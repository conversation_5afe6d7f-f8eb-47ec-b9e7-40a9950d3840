<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.rta.mapper.RtaConfigProductMapper">

    <resultMap id="BaseResultMap" type="com.coohua.rta.domain.RtaConfigProduct">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="os" column="os" jdbcType="VARCHAR"/>
            <result property="product" column="product" jdbcType="VARCHAR"/>
            <result property="rtaId" column="rta_id" jdbcType="BIGINT"/>
            <result property="tag" column="tag" jdbcType="VARCHAR"/>
            <result property="tagStrategy" column="tag_strategy" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,os,product,
        rta_id,tag,tag_strategy,
        create_time,update_time
    </sql>
    <insert id="insert">
        insert into ${table} (os,product,
        rta_id,tag,tag_strategy,
        create_time,update_time)
        values (#{record.os,jdbcType=VARCHAR},#{record.product,jdbcType=VARCHAR},
        #{record.rtaId,jdbcType=BIGINT},#{record.tag,jdbcType=VARCHAR},#{record.tagStrategy,jdbcType=VARCHAR},
        #{record.createTime,jdbcType=TIMESTAMP},#{record.updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertBatch">
        insert into ${table} (os,product,
        rta_id,tag,tag_type,tag_strategy,
        create_time,update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.os,jdbcType=VARCHAR},#{item.product,jdbcType=VARCHAR},
            #{item.rtaId,jdbcType=BIGINT},#{item.tag,jdbcType=VARCHAR}, #{item.tagType,jdbcType=VARCHAR},#{item.tagStrategy,jdbcType=VARCHAR},
             #{item.createTime,jdbcType=TIMESTAMP},#{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <select id="selectAll" resultType="com.coohua.rta.entity.RtaByteConfigProduct">
        select
            os,product,
            rta_id,tag
        from ${table}
        where is_enabled = 1
    </select>


</mapper>
