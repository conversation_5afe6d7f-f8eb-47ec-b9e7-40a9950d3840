<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.rta.mapper.ToutiaoClickDistMapper">

    <resultMap id="BaseResultMap" type="com.coohua.rta.domain.ToutiaoClickDist">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="product" column="product" jdbcType="VARCHAR"/>
            <result property="os" column="os" jdbcType="VARCHAR"/>
            <result property="oaid" column="oaid" jdbcType="VARCHAR"/>
            <result property="caid" column="caid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product,os,
        oaid,caid, pv,logday,min_create_time
    </sql>
    <select id="selectListByPage" resultType="com.coohua.rta.domain.ToutiaoClickDist">
        select
            <include refid="Base_Column_List"/>
            from toutiao_click_dist
        where id > #{lastId}
        limit #{pageSize}
    </select>
</mapper>
