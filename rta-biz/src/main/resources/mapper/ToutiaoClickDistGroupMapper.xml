<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.coohua.rta.mapper.ToutiaoClickDistGroupMapper">

    <resultMap id="BaseResultMap" type="com.coohua.rta.domain.ToutiaoClickDistGroup">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="product" column="product" jdbcType="VARCHAR"/>
            <result property="os" column="os" jdbcType="VARCHAR"/>
            <result property="oaid" column="oaid" jdbcType="VARCHAR"/>
            <result property="caid" column="caid" jdbcType="VARCHAR"/>
            <result property="groupId" column="group_id" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,product,os,
        oaid,caid,group_id
    </sql>
</mapper>
