package com.coohua.rta.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.coohua.rta.constant.RedisKeyConstants;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.service.cache.RtaBinaryDictCache;
import com.coohua.rta.service.cache.RtaLocalCacheService;
import com.coohua.rta.service.shard.ABShardingService;
import com.coohua.rta.strategy.TagBidStrategy;
import com.coohua.rta.utils.BinaryUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.JedisCluster;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.LongAdder;

import static com.coohua.rta.constant.RedisKeyKsConstants.*;

@Slf4j
@Component
public class RtaCheckBidHandler {

    private final Map<String, TagBidStrategy> tagBidStrategyMap;

    private static final LongAdder blackDeviceCount = new LongAdder();

    @ApolloJsonValue("${rta.byte.sharding.weights:[50, 50]}")
    private int[] WEIGHTS;
    @Value("${rta.ks.bid.times.apollo:false}")
    private Boolean rtaKsBidTimesApollo;
    @Value("${rta.ks.log.server:false}")
    private Boolean rtaKsLogServer;
    @Autowired
    @Qualifier("rta-redis-newJedisClusterClient")
    private JedisClusterClient rtaRedis;
    @ApolloJsonValue("${rta.all.not.bid.tag.set:[]}")
    private Set<String> rtaAllNotBidTagSet;
    @Value("${rta.all.not.bid.switch:false}")
    private Boolean rtaAllNotBidSwitch;

    @Autowired
    public RtaCheckBidHandler(Map<String, TagBidStrategy> tagBidStrategyMap) {
        this.tagBidStrategyMap = tagBidStrategyMap;
    }


    /**
     * 根据人群包位图判断是否竞价rtaId
     *
     * @param unionTagMap         设备的人群包位图map
     * @param tagRtaMap           配置的tag - rtaId
     * @param configRtaProductMap rtaId - rtaByteConfigProduct
     * @param userEventReq        请求来源设备信息封装
     * @return
     */
    public List<Long> chooseNoBidRtaIdList(Map<String, String> unionTagMap, Map<String, Set<Long>> tagRtaMap, Map<Long, RtaByteConfigProduct> configRtaProductMap, UserEventReq userEventReq) {
        Set<Long> needCheckBitRtaIdSet = new HashSet<>();
        try {

            Set<String> tagSet = convertBitMap(unionTagMap, userEventReq.getOs());

            if (rtaAllNotBidSwitch && CollectionUtils.isNotEmpty(rtaAllNotBidTagSet) && tagSet.stream().anyMatch(rtaAllNotBidTagSet::contains)){
                try {
                    recordProductBidTimes(userEventReq.getOs(), configRtaProductMap.keySet(), configRtaProductMap, true);
                    rtaRedis.incrBy("rta:ks:all:not:bid:blacklist:count", 1);
                } catch (Exception e) {
                    log.error("全局拉黑人群异常", e);
                }
                return new ArrayList<>(configRtaProductMap.keySet());
            }

            // 根据tag获取需要判断是否竞价rtaId
            for (String tag : tagSet) {
                Set<Long> rtaIdSet = tagRtaMap.get(tag);
                if (CollUtil.isNotEmpty(rtaIdSet)) {
                    needCheckBitRtaIdSet.addAll(rtaIdSet);
                    recordProductBidTimes(userEventReq.getOs(), rtaIdSet, configRtaProductMap, true);
                }
            }
            recordProductBidTimes(userEventReq.getOs(), new HashSet<>(), configRtaProductMap, false);
        } catch (Exception e) {
            log.error("人群包位图转tag错误", e);
        }

        return new ArrayList<>(needCheckBitRtaIdSet);
    }

    //public static final int[] WEIGHTS = {20, 20, 60};
    public List<Long> chooseNoBidRtaIdListForBlackDeviceAbTest(Map<String, String> unionTagMap, Map<String, Set<Long>> tagRtaMap, Map<Long, RtaByteConfigProduct> configRtaProductMap, UserEventReq userEventReq, String shardingKey) {
        Set<Long> needCheckBitRtaIdSet = new HashSet<>();
        try {

            Set<String> tagSet = convertBitMap(unionTagMap, userEventReq.getOs());
// 先读取本地缓存，获取不到则重新分流
            Integer shard = RtaLocalCacheService.getSharding(shardingKey);
            if (shard == null) {
                shard = ABShardingService.shard(shardingKey, ABShardingService.TOTAL_WEIGHT, WEIGHTS);
                RtaLocalCacheService.setSharding(shardingKey, shard);
                ABShardingService.count(shard);
            }

            if (shard == 0) {
                boolean isBlackDevice = false;
                for (String tag : tagSet) {
                    if ("BLACKDEVICE".equals(tag) || tag.contains("_b")) {
                        isBlackDevice = true;

                        blackDeviceCount.increment();


                        if (blackDeviceCount.sum() % 100 == 0) {
                            log.info("黑名单数量:{}", blackDeviceCount.sum());
                        }

                        if (blackDeviceCount.sum() > 10000) {
                            log.info("黑名单数量超过10000,重置统计");
                            blackDeviceCount.reset();
                        }

                        break;
                    }
                }
                if (isBlackDevice) {
                    // 黑名单设备不竞价
                    return new ArrayList<>(configRtaProductMap.keySet());
                }
            }

            // 根据tag获取需要判断是否竞价rtaId
            for (String tag : tagSet) {
                Set<Long> rtaIdSet = tagRtaMap.get(tag);
                if (CollUtil.isNotEmpty(rtaIdSet)) {
                    needCheckBitRtaIdSet.addAll(rtaIdSet);
                }
            }
        } catch (Exception e) {
            log.error("人群包位图转tag错误", e);
        }

        return new ArrayList<>(needCheckBitRtaIdSet);

    }

    private Set<String> convertBitMap(Map<String, String> unionTagMap, String os) {
        // 1 获取人群包类型并将位图编码转为位图, 并根据位图表转为对应tag
        Set<String> tagSet = new HashSet<>();
        for (String type : unionTagMap.keySet()) {
            String binaryCode = unionTagMap.get(type);
            String binary = BinaryUtil.decryptZipCodeToBinary(binaryCode);

            if (StrUtil.isBlank(binary)) {
                continue;
            }
            for (int i = 0; i < binary.length(); i++) {
                if (binary.charAt(i) == '1') {
                    String tag = null;
                    if (PlatformEnum.IOS.getPlatform().equals(os)) {
                        tag = RtaBinaryDictCache.binaryDictIosMap.get(type + ":" + i);
                    } else {
                        tag = RtaBinaryDictCache.binaryDictAndroidMap.get(type + ":" + i);
                    }
                    if (tag != null) tagSet.add(tag);
                }
            }
        }

        return tagSet;
    }



    private void recordProductBidTimes(String os, Set<Long> rtaIdSet, Map<Long, RtaByteConfigProduct> configRtaProductMap, Boolean isSingleProduct) {
        if (rtaKsLogServer && rtaKsBidTimesApollo) {
            CompletableFuture.runAsync(() -> {
                try {
                    if (isSingleProduct){
                        rtaIdSet.forEach(rtaId -> {
                            String product = configRtaProductMap.get(rtaId).getProduct();
                            rtaRedis.incrBy(getRtaKsProductBidTimes(os, product), 1);
                        });
                    }else {
                        if (PlatformEnum.ANDROID.getPlatform().equals(os)) {
                            rtaRedis.incrBy(RTA_KS_PRODUCT_BID_TIMES_TOTAL_ANDROID, 1);
                        } else {
                            rtaRedis.incrBy(RTA_KS_PRODUCT_BID_TIMES_TOTAL_IOS, 1);
                        }
                    }
                } catch (Exception e) {
                    log.error("记录参竞数异常", e);
                }
            });
        }
    }
}
