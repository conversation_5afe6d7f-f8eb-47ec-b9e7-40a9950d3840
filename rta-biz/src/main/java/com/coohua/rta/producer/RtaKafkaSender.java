package com.coohua.rta.producer;

import cn.hutool.core.thread.ThreadUtil;
import com.pepper.metrics.integration.custom.Profile;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
public class RtaKafkaSender implements InitializingBean {

    private KafkaProducer<String, String> producer;

    private AtomicLong counter = new AtomicLong(0);

    private String rtaToutiaoClick = "rta_toutiao_click";
    static ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("req_tt_click",null,false);
    public  static int sendPd = 30;
    ExecutorService toutiaoReuserExecutor = new ThreadPoolExecutor(
            5
            , 30,
            60,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2000),
            threadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Profile
    public void sendToutiaoClick(String reqStr){
        toutiaoReuserExecutor.submit(()->{
            try {
                ProducerRecord<String, String> kafkaMessage = new ProducerRecord<>(rtaToutiaoClick, reqStr);
                producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
            }catch (Exception e){
                log.error("",e);
            }
        });
    }
    @Override
    public void afterPropertiesSet() throws Exception {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "172.16.200.5:9092,172.16.200.6:9092,172.16.200.7:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        props.put(ProducerConfig.RETRIES_CONFIG, 3);

        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 80*1024);// 160kb
        props.put(ProducerConfig.LINGER_MS_CONFIG, 1000);// 当linger.ms>0时，延时性会增加，但会提高吞吐量，因为会减少消息发送频率

//        props.put(ProducerConfig.ACKS_CONFIG, "1");
        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        this.producer = new KafkaProducer<>(props);

        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    try {
                        TimeUnit.SECONDS.sleep(5);
                        long sec = counter.getAndSet(0);
                        log.info("MSG.SEC={}", sec);
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
            }
        }).start();
    }
}
