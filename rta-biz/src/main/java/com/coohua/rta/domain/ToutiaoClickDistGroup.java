package com.coohua.rta.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @TableName toutiao_click_dist_group
 */
@TableName(value ="toutiao_click_dist_group")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ToutiaoClickDistGroup implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String product;
    private String logday;

    /**
     * 
     */
    private String os;

    /**
     * oaid/caid
     */
    private String oaid;

    /**
     * oaid/caid
     */
    private String caid;

    /**
     * 
     */
    private Integer groupId;

    private Integer pv;
    private LocalDateTime minCreateTime;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}