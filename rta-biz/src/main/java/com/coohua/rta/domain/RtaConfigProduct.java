package com.coohua.rta.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * rtabyte配置的产品-rtaId
 * @TableName rta_config_product_byte
 */
@TableName(value ="rta_config_product_byte")
@Data
public class RtaConfigProduct implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private String os;

    /**
     * 产品打点名
     */
    private String product;

    /**
     * rtaId
     */
    private Long rtaId;

    /**
     * 
     */
    private String tag;

    /**
     * 
     */
    private String tagStrategy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}