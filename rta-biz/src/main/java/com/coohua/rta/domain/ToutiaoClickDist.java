package com.coohua.rta.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * @TableName toutiao_click_dist
 */
@TableName(value = "toutiao_click_dist")
@Data
public class ToutiaoClickDist implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    private String logday;

    /**
     *
     */
    private String product;

    /**
     *
     */
    private String os;

    /**
     * oaid/caid
     */
    private String oaid;
    private String caid;
    private Integer pv;
    private LocalDateTime minCreateTime;
    @TableField(exist = false)
    private Integer groupId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}