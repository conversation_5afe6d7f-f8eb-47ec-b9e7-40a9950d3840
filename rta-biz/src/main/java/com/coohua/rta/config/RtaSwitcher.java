package com.coohua.rta.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class RtaSwitcher {
    @Value("${rta.swt.arpu.days:4}")
    public Integer redisSaveDay;

    @Value("${rta.swt.max.flt:5}")
    public Double rtaMaxFlt;
    @Value("${rta.swt.strgy:1}")
    public Integer rtaStrgy;
    @Value("${rta.fl.rate:1}")
    public Double flRate;

    @Value("${rta.send.norate:true}")
    public Boolean isSendNoBidRsp;


    @Value("${rta.pvlower.rate:2}")
    public Double pvLowerRate;
    @Value("${rta.arpugt.rate:5}")
    public Double arpuGrBeishu;

    @ApolloJsonValue("${rta.account.ids:[]}")
    public Set<String> rtaAccountIds;

    @ApolloJsonValue("${rta.tx.itst:true}")
    public Boolean isTxRtaTest;
    @ApolloJsonValue("${rta.tx.jjoaids:[]}")
    public Set<String> jingjiaOaids;
    @ApolloJsonValue("${rta.tx.exlds:[]}")
    public Set<String> exldOaids;

    @Value("${rta.tx.priceft:true}")
    public Boolean priceFt;
    @Value("${rta.tx.rtaclog:true}")
    public Boolean rtaclog;

    @Value("${rta.fl.boundrate:6}")
    public Double boundrate;

    @Value("${rta.fl.repeatflg:false}")
    public Boolean repeatFitFlag;


    @Value("${rta.tx.saveDb:false}")
    public Boolean saveDbFlg;

}
