package com.coohua.rta.enums;

/**
 * when s4.tag = '点消' then 1
 * when s4.tag = '工具' then 2
 * when s4.tag = '合成' then 3
 * when s4.tag = '短视频' then 4
 * when s4.tag = '模拟经营' then 5
 * when s4.tag = '付费' then 6
 * when s4.tag = '益智' then 7
 * when s4.tag = '其他' then 8
 * when s4.tag = '答题' then 9
 */
public enum ProductType {

    dian<PERSON><PERSON>(1,  "点消"),
    gong<PERSON>(2, "工具"),
    he<PERSON>(3, "合成"),
    duanship<PERSON>(4, "短视频"),
    moniji<PERSON><PERSON>(5, "模拟经营"),
    f<PERSON>ei(6, "付费"),
    y<PERSON><PERSON>(7, "益智"),
    qita(8, "其他"),
    dati(9, "答题"),
    ;

    public final Integer code;
    public final String desc;

    ProductType(Integer code,  String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductType parse(Integer eventType) {
        for (ProductType item : values()) {
            if (item.code.equals(eventType)) {
                return item;
            }
        }
        return null;
    }
}
