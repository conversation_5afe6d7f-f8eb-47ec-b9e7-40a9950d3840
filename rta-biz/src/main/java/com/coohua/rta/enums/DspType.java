package com.coohua.rta.enums;

/**
 * 平台类型和处理方法
 */
public enum DspType {
    TOUTIAO("toutiao","toutiao",1, "头条"),//	         30天内第一次打开APP
    TTWX("ttwx","ttwx",19, "头条微信小游戏"),//	         30天内第一次打开APP
    BAIDU("baidu","baidu",2, "百度"),
    KUAISHOU("kuaishou","kuaishou",3, "快手"),////   用户点击广告后注册成为APP的新用户
    TIANWEI("tianwei","tianwei",4, "天威"),
    GUANGDIANTONG("guangdiantong","guangdiantong",5, "广点通"),//	         用户点击广告后注册成为APP的新用户
    SOGOU("sogou","sogou",6, "搜狗"),
    //内拉新
    INNER_OLD_PULL_NEW("INNER_OLD_PULL","INNER_OLD_PULL",7, "内拉新"),
    //自家视频
    INNER_VIDEO("INNER_VIDEO","INNER_VIDEO",8, "自家视频"),
    //内部引流
    INNER_DRAINAGE("INNER_DRAINAGE","INNER_DRAINAGE",9, "内部引流"),
    //快手 磁力聚星
    KUAISHOU_CLJX("kuaishou_cljx","kuaishou_cljx", 10, "快手磁力聚星"),
    OPPO("oppo","oppo",11, "OPPO"),
    vivo("vivo","vivo",12, "VIVO"),
    // 百度搜索
    BAIDUSEM("baidusem","baidusem",13, "百度搜索"),
    // 百度信息流
    BAIDUFEED("baidufeed","baidufeed",14, "百度信息流"),
    // 爱奇艺
    IQIYI("iqiyi","iqiyi",15, "爱奇艺"),
    XIAOMI("xiaomi","xiaomi",16, "小米"),
    XIMALAYA("ximalaya","ximalaya",17, "喜马拉雅"),
    ALI("ali","ali",18, "阿里"),
    BYTEMIN("bytemin","bytemin",20, "字节小游戏"),//	         30天内第一次打开APP
    TTWMINDR("ttwmindaren","ttwmindaren",21, "头条微信小游戏"),//	         30天内第一次打开APP
    KSMIN("ksmin","ksmin",22, "快手小游戏"),//

    SIGMOB("sigmob","sigmob",23,"Sigmob"),

    ;
    public String value;
    public String name;
    public int platformId;
    public String cnName;

    DspType(String value, String name, int platformId, String cnName) {
        this.value = value;
        this.name = name;
        this.platformId = platformId;
        this.cnName = cnName;
    }

    public static DspType getDspType(String value) {
        if (value != null) {
            DspType[] otypes = DspType.values();
            for (DspType memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }

    public static boolean isBaidu(String value) {
        DspType dspType = getDspType(value);
        if (dspType == null) {
            return false;
        }
        return dspType == BAIDU || dspType == BAIDUSEM || dspType == BAIDUFEED;
    }

    public static boolean needCallbackDeviceId(String dsp) {
        return DspType.OPPO.value.equals(dsp)
                || DspType.vivo.value.equals(dsp)
                || DspType.XIAOMI.value.equals(dsp)
                ;
    }

    /**
     * 计算次留时，是否以转化的第二天才算次留
     */
    public static boolean ciLiuByKeyEvent(String dsp) {
        return DspType.OPPO.value.equals(dsp)
                || DspType.vivo.value.equals(dsp)
                ;
    }
}
