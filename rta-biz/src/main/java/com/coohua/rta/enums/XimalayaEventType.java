package com.coohua.rta.enums;

/**
 * <AUTHOR>
 * @date 2022/4/26 14:28
 */
public enum XimalayaEventType {

    ACTIVATE_APP(0, "act", "激活"),

    START_APP(6, "leave", "次留"),

    KEY_EVENT(25, "register", "关键行为-回传到注册"),

    ;

    public final Integer val;
    public final String code;
    public final String desc;

    XimalayaEventType(Integer val, String code, String desc) {
        this.val = val;
        this.code = code;
        this.desc = desc;
    }

    public static XimalayaEventType parse(Integer eventType) {
        for (XimalayaEventType item : values()) {
            if (item.val.equals(eventType)) {
                return item;
            }
        }
        return null;
    }
}
