package com.coohua.rta.enums;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR>
 */
public class BaseConstants {

    /**
     * 字符串0的md5
     */
    public static final String ZERO_MD5_HEX = DigestUtils.md5Hex("0");

    /**
     * 空字符串的md5
     */
    public static final String EMPTY_MD5 = "d41d8cd98f00b204e9800998ecf8427e";

    /**
     * 多ecpm配置分隔符
     */
    public static final String MULTI_ECPM_SEPARATOR = "/";

    /**
     * 下划线
     */
    public static final String UNDERLINE = "_";

    /**
     * char常量-0
     */
    public static final char CHAR_ZERO = '0';

    /**
     * char常量-9
     */
    public static final char CHAR_NINE = '9';

    /**
     * 逗号
     */
    public static final String COMMA = ",";

    public static final String LOCK_SUCCESS = "OK";

    public static final String SET_IF_NOT_EXIST = "NX";

    public static final String SET_WITH_EXPIRE_TIME = "PX";

    /**
     * 次数,arpu 配置
     */
    public static final String NUM_ARPU_RULE = "1,2";

    /**
     * 次数,ecpm 配置
     */
    public static final String NUM_ECPM_RULE = "1,3";

    /**
     * 将请求数据存入req_url字段时，请求的url与body之间的分隔符
     */
    public static final String DELIMITER_URL_BODY = " ; ";

    /**
     * 额外请求时保存多个请求url的分隔符
     */
    public static final String DELIMITER_MULTI_URL = " ;; ";

    /**
     * 头条回传v2版本 未识别的event_type
     */
    public static final String UnknownEventTypeV2 = "unknown_event_type_v2";

    /**
     * 数据库中ip字段的最大长度
     */
    public static final int IpMaxLength = 256;

    /**
     * 数据库中ua字段的最大长度
     */
    public static final int UaMaxLength = 500;

    /**
     * 数据库中model字段的最大长度
     */
    public static final int ModelMaxLength = 256;

}
