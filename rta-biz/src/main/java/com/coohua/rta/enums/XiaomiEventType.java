package com.coohua.rta.enums;

/**
 * <AUTHOR>
 * @date 2022/4/26 14:28
 */
public enum XiaomiEventType {

    ACTIVATE_APP(0, "APP_ACTIVE_NEW", "自定义新增激活"),

    /** 自定义注册 即 关键行为 */
    KEY_EVENT(25, "APP_ADDICTION", "关键行为"),

    START_APP(6, "APP_RETENTION", "留存"),

    ;

    public final Integer val;
    public final String code;
    public final String desc;

    XiaomiEventType(Integer val, String code, String desc) {
        this.val = val;
        this.code = code;
        this.desc = desc;
    }

    public static XiaomiEventType parse(Integer eventType) {
        for (XiaomiEventType item : values()) {
            if (item.val.equals(eventType)) {
                return item;
            }
        }
        return null;
    }
}
