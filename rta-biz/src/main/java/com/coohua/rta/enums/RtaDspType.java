package com.coohua.rta.enums;

/**
 * when s4.tag = '点消' then 1
 * when s4.tag = '工具' then 2
 * when s4.tag = '合成' then 3
 * when s4.tag = '短视频' then 4
 * when s4.tag = '模拟经营' then 5
 * when s4.tag = '付费' then 6
 * when s4.tag = '益智' then 7
 * when s4.tag = '其他' then 8
 * when s4.tag = '答题' then 9
 */
public enum RtaDspType {

    chuanshanjia("csj",  "chuanshanjia"),
    ;

    public final String code;
    public final String desc;

    RtaDspType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RtaDspType getRtaDsp(String eventType) {
        for (RtaDspType item : values()) {
            if (item.code.equals(eventType)) {
                return item;
            }
        }
        return null;
    }
}
