package com.coohua.rta.enums;

public enum PlatFmType {
    android("1","1"),//	        用户id
    ios("2","2"),//	         30天内第一次打开APP
            ;
    public String value;
    public String name;

    PlatFmType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static PlatFmType getDspType(String value) {
        if (value != null) {
            PlatFmType[] otypes = PlatFmType.values();
            for (PlatFmType memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
