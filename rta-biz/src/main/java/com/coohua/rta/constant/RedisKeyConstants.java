package com.coohua.rta.constant;

/**
 * redis key常量
 */
public class RedisKeyConstants {

    public static final String RTA_BYTE_OAID_UNION = "{rta:byte:oaid:%s}:%s";
    public static final String RTA_BYTE_CAID_UNION = "{rta:byte:caid:%s}:%s";
    public static final String RTA_BYTE_RTAID_PRODUCT = "{rta:byte:product}:%s:%s";
    public static final String RTA_BYTE_OS_PRODUCT = "{rta:byte:product}:%s";
    public static final String RTA_BYTE_RTA_TAG = "rta:byte:tag";
    public static final String RTA_BAIDU_RTA_TAG = "rta:baidu:tag";
    public static final String RTA_KS_RTA_TAG = "rta:ks:tag";
    public static final String RTA_BINARY_DICT_IOS = "rta:binary:dict:ios";
    public static final String RTA_BINARY_DICT_ANDROID = "rta:binary:dict:android";
    public static final String RTA_OAID_UNION = "{rta:o:%s}:%s";
    public static final String RTA_OAID_MD5_UNION = "{rta:o:md5:%s}:%s";
    public static final String RTA_CAID_UNION = "{rta:c:%s}:%s";

    public static String getRtaByteOaidUnion(String slot, String oaid) {
        return String.format(RTA_BYTE_OAID_UNION, slot, oaid);
    }

    public static String getRtaByteCaidUnion(String slot, String caid) {
        return String.format(RTA_BYTE_CAID_UNION, slot, caid);
    }

    public static String getProductByRtaId(Long rtaId, String os) {
        return String.format(RTA_BYTE_RTAID_PRODUCT, os, rtaId);
    }

    public static String getProductByOs(String os) {
        return String.format(RTA_BYTE_OS_PRODUCT, os);
    }

    public static String getRtaByteTagKey() {
        return RTA_BYTE_RTA_TAG;
    }
    public static String getRtaBinaryDictIos() {
        return RTA_BINARY_DICT_IOS;
    }
    public static String getRtaBinaryDictAndroid() {
        return RTA_BINARY_DICT_ANDROID;
    }

    public static String getRtaOaidUnion(String slot, String oaid) {
        return String.format(RTA_OAID_UNION, slot, oaid);
    }

    public static String getRtaCaidUnion(String slot, String caid) {
        return String.format(RTA_CAID_UNION, slot, caid);
    }

    public static String getRtaBaiduTagKey() {
        return RTA_BAIDU_RTA_TAG;
    }

    public static String getRtaKsTagKey() {
        return RTA_KS_RTA_TAG;
    }

    public static String getRtaOaidMd5Union(String slot, String oaidMd5) {
        return String.format(RTA_OAID_MD5_UNION, slot, oaidMd5);
    }
}
