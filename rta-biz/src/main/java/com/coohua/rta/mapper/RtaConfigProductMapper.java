package com.coohua.rta.mapper;

import com.coohua.rta.entity.RtaByteConfigProduct;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【rta_config_product_byte(rtabyte配置的产品-rtaId)】的数据库操作Mapper
* @createDate 2025-01-13 10:39:20
* @Entity com.coohua.rta.domain.RtaConfigProduct
*/
public interface RtaConfigProductMapper<T> {
    int insert(@Param("table") String table, @Param("record") T record);

    List<T> selectAll(@Param("table") String table);

    int insertBatch(@Param("table") String table, @Param("list") List<T> rtaByteConfigProductList);
}




