package com.coohua.rta.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.coohua.rta.domain.ToutiaoClickDist;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【toutiao_click_dist】的数据库操作Mapper
* @createDate 2025-02-19 11:50:41
* @Entity com.coohua.rta.domain.ToutiaoClickDist
*/
public interface ToutiaoClickDistMapper extends BaseMapper<ToutiaoClickDist> {

    List<ToutiaoClickDist> selectListByPage(@Param("pageSize") int pageSize, @Param("lastId") long lastId);
}




