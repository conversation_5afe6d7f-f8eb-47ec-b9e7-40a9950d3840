package com.coohua.rta.service.strategy;

//import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
//import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
//import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaCfg;
        import com.coohua.rta.enums.PlatFmType;
import com.coohua.rta.service.RtaCounterPrint;
        import com.google.protobuf.Int32Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class StrRtaNorepeatService {
//    @Autowired
//    OcpcSwitcher ocpcSwitcher;
//    @Autowired
//    RtaCacheSch rtaCacheSch;
//    @Autowired
//    RedisEventService redisEventService;
    @Autowired
    RtaReqConverService rtaReqConverService;

    public RtaApi.Rsp getRtaRsp(RtaApi.Req request, Integer strgNum, String reqId){
        List<Long> rtaIdList = request.getRtaIdsList();
        PlatFmType platFmType = PlatFmType.android;
        List<Long> bidRtaList = new ArrayList<>();
        RtaApi.PlatformType platformType = request.getPlatform();
        if(RtaApi.PlatformType.IOS.equals(platformType)){
            platFmType = PlatFmType.ios;
        }
        for(Long rtaId : rtaIdList){
//            RtaCfg rtaCfg = rtaCacheSch.getRtaCfg(rtaId+"",platFmType, DspType.TOUTIAO);
//
//            boolean isNeedReq = needReqRtaId(request,rtaCfg);
//            if(isNeedReq){
//                bidRtaList.add(rtaId);
//            }
        }

        RtaApi.Rsp.Builder  builderRsp = RtaApi.Rsp.newBuilder();
        if(bidRtaList.size()==0){
            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
        }else{
            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(0).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            for(Long bidRtaId : bidRtaList){
                RtaApi.UserInfo.Builder ubuilder = RtaApi.UserInfo.newBuilder();
                ubuilder.setIsInterested(true);
                ubuilder.setRtaId(bidRtaId);

                RtaApi.UserInfo uinfo = ubuilder.build();
                builderRsp.addUserInfos(uinfo);
            }
        }
        builderRsp.setRtaVid(strgNum+"");
        builderRsp.setStatusCode(0);
        builderRsp.setReqId(reqId);

        RtaApi.Rsp rsp = builderRsp.build();
        return rsp;
    }
    @Autowired
    RtaCounterPrint rtaCounterPrint;
    public boolean needReqRtaId(RtaApi.Req request, RtaCfg rtaCfg){
//        if(ocpcSwitcher.isAllJ){
//            return true;
//        }
        UserEventReq userEventReq = rtaReqConverService.getUserReq(request,rtaCfg);

        if(userEventReq==null){
            return false;
        }
        long ctime = System.currentTimeMillis();
//        OcpcEvent activeEvt = redisEventService.getActiveEventByRedis(userEventReq);
//        if(activeEvt!=null && rtaCounterPrint.isPrt("needReqRtaId")){
//            log.info("rta归因成功 "+ JSON.toJSONString(userEventReq)+" cost "+(System.currentTimeMillis()-ctime));
//        }
//        return activeEvt==null;
        return false;
    }

    private String imeiMd5Bid = "B0EEB5005B07B331A94CAD35A28FDCA3";
    private String imeiMd5NoBid = "B5A2E0E38B53F8BCCABD94349E4CCC6B";
    private String idfaBid = "2216916B-5327-463A-802B-DE167ACCB84";
    private String idfaNoBid = "A53CB275-88C0-4D11-BE64-8E1C333B633C";

    public RtaApi.Rsp getTestRsp(RtaApi.Req request, Integer strgNum, String reqId) {
        List<Long> rtaIdList = request.getRtaIdsList();
        PlatFmType platFmType = PlatFmType.android;
        List<Long> bidRtaList = new ArrayList<>();
        RtaApi.PlatformType platformType = request.getPlatform();
        if (RtaApi.PlatformType.IOS.equals(platformType)) {
            platFmType = PlatFmType.ios;
        }

        RtaCfg rtaCfg = new RtaCfg();
        rtaCfg.setProduct("testt");
        UserEventReq userEventReq = rtaReqConverService.getUserReq(request, rtaCfg);

        for (Long rtaId : rtaIdList) {
            if (rtaId.equals(123456L)) {
                if (imeiMd5Bid.equals(userEventReq.getSourceDeviceId())) {
                    bidRtaList.add(rtaId);
                } else if (idfaBid.equals(userEventReq.getIdfa())) {
                    bidRtaList.add(rtaId);
                }
            }
        }

        RtaApi.Rsp.Builder builderRsp = RtaApi.Rsp.newBuilder();
        if (bidRtaList.size() == 0) {
            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
        } else {
            builderRsp.setBidType(Int32Value.newBuilder().setValue(0).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            for (Long bidRtaId : bidRtaList) {
                RtaApi.UserInfo.Builder ubuilder = RtaApi.UserInfo.newBuilder();
                ubuilder.setIsInterested(true);
                ubuilder.setRtaId(bidRtaId);

                RtaApi.UserInfo uinfo = ubuilder.build();
                builderRsp.addUserInfos(uinfo);
            }
        }
        builderRsp.setStatusCode(0);
        builderRsp.setReqId(reqId);

        RtaApi.Rsp rsp = builderRsp.build();
        return rsp;
    }
}
