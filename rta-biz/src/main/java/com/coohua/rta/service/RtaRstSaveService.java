package com.coohua.rta.service;

import cn.hutool.core.thread.NamedThreadFactory;

import com.coohua.rta.entity.RtaDevice;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.enums.GuiyingType;
import com.coohua.rta.utils.DoubleUtil;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RtaRstSaveService {
    Executor rtaRstToDbPool = new ThreadPoolExecutor(80,
            80, DateTimeConstants.SECONDS_PER_MINUTE * 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(3000),
            new NamedThreadFactory("deviceToRedisPool",false),
            new ThreadPoolExecutor.DiscardOldestPolicy()
    );

    @Autowired
    RtaTableService rtaTableService;
    @Autowired
    RtaUppriceService rtaUppriceService;
    @Autowired
    RtaUppriceSubService rtaUppriceSubService;
    @Autowired
    RtaCounterPrint rtaCounterPrint;
    public void savePriceFl(RtaUpprice rtaUpprice, List<RtaUppriceSub> subList){
        rtaRstToDbPool.execute(()->{
            rtaTableService.createIfNot(RtaTableService.rtaSubUpTableName);
            rtaTableService.createIfNot(RtaTableService.rtaUpTableName);

            rtaUppriceService.save(rtaUpprice);
            subList.forEach(rtaUppriceSub -> {
                rtaUppriceSub.setUppriceId(rtaUpprice.getId());
            });
            rtaUppriceSubService.saveBatch(subList);
            if(rtaCounterPrint.isPrt("savePriceFl")){
                log.info("保存数据成功 "+rtaUpprice.getRtaId()+"@"+subList.size());
            }
        });

    }

    public RtaUppriceSub getRtaSub(RtaDevice rtaDevice, String rtaId, GuiyingType guiyingType){
        RtaUppriceSub rtaUppriceSub = new RtaUppriceSub();
//                rtaUppriceSub.setUppriceId();
        rtaUppriceSub.setRtaId(rtaId);
        rtaUppriceSub.setArpu(rtaDevice.getArpu());
        rtaUppriceSub.setCaid(rtaDevice.getCaid());
        rtaUppriceSub.setCpa(rtaDevice.getCpa());
        rtaUppriceSub.setGuiyType(guiyingType.value);
//                rtaUppriceSub.setIdfa();
        rtaUppriceSub.setImei(rtaDevice.getImei());
        rtaUppriceSub.setLogDay(rtaDevice.getLogday());
        rtaUppriceSub.setOaid(rtaDevice.getOaid());
        rtaUppriceSub.setOs(rtaDevice.getOs());
        rtaUppriceSub.setProduct(rtaDevice.getProduct());
        rtaUppriceSub.setPv(DoubleUtil.getIntegerNoF(rtaDevice.getPv()));
        rtaUppriceSub.setWithdraw(DoubleUtil.getIntegerNoF(rtaDevice.getWithdrawAmount()));

        rtaUppriceSub.setCreateTime(new Date());
        rtaUppriceSub.setUpdateTime(new Date());
        return rtaUppriceSub;
    }
}
