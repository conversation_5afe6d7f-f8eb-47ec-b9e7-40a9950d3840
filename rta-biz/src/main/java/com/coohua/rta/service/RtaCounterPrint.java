package com.coohua.rta.service;

//import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
//import com.coohua.core.caf.dispense.kafka.LogKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Component
@Slf4j
public class RtaCounterPrint {
    public static AtomicLong jingjiaNUm = new AtomicLong(0);
    public static  AtomicLong bujinjiaNum = new AtomicLong(0);

//    @PostConstruct
    public void startCount(){
//        new Thread(new Runnable() {
//            @Override
//            public void run() {
//                while (true) {
//                    try {
//                        TimeUnit.SECONDS.sleep(LogKafkaSender.sendPd);
//                        long jjia = jingjiaNUm.getAndSet(0);
//                        long bujijia = bujinjiaNum.getAndSet(0);
//                        log.info("竞价数为 "+jjia+" 不竞价数为 "+bujijia);
//                    } catch (InterruptedException e) {
//                        log.error("", e);
//                    }
//                }
//            }
//        }).start();
    }
//    @Autowired
//    OcpcSwitcher ocpcSwitcher;
    private static Map<String,AtomicInteger> icaMap = new ConcurrentHashMap<>();

    public  boolean isPrt(String reqType){
        AtomicInteger ica = icaMap.get(reqType);
        if(ica==null){
            ica = new AtomicInteger(0);
            icaMap.put(reqType,ica);
        }
        Integer icds = ica.addAndGet(1);
//        if(icds>ocpcSwitcher.writeLogNUM){
//            ica.set(0);
//            return true;
//        }
        return  false;
    }
}
