package com.coohua.rta.service.strategy;

//import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
//import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
//import com.coohua.core.caf.dispense.ocpc.service.CaidService;
//import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.enums.PlatFmType;
import com.coohua.rta.service.RtaCounterPrint;
        import com.google.protobuf.Int32Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class StrRtaSkipNorepeatService {
//    @Autowired
//    OcpcSwitcher ocpcSwitcher;
//    @Autowired
//    RtaCacheSch rtaCacheSch;
//    @Autowired
//    RedisEventService redisEventService;
//    @Autowired
//    RtaReqConverService rtaReqConverService;
//    @Autowired
//    CaidService caidService;

    public RtaApi.Rsp getRtaRsp(RtaApi.Req request, Integer strgNum, String reqId){
        List<Long> rtaIdList = request.getRtaIdsList();
        PlatFmType platFmType = PlatFmType.android;
        List<Long> bidRtaList = new ArrayList<>();
        RtaApi.PlatformType platformType = request.getPlatform();
        if(RtaApi.PlatformType.IOS.equals(platformType)){
            platFmType = PlatFmType.ios;
        }
        for(Long rtaId : rtaIdList){
//            RtaCfg rtaCfg = rtaCacheSch.getRtaCfg(rtaId+"",platFmType, DspType.TOUTIAO);
//
//            boolean isNeedReq = needReqRtaId(request,rtaCfg);
//            if(isNeedReq){
//                bidRtaList.add(rtaId);
//            }
        }

        RtaApi.Rsp.Builder  builderRsp = RtaApi.Rsp.newBuilder();
        if(bidRtaList.size()==0){
            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
//        } else if (caidService.isNoActiveCaid(request.getDevice().getCaid())) {
//            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  指定caid不参竞
        }
        else{
            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(0).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            for(Long bidRtaId : bidRtaList){
                RtaApi.UserInfo.Builder ubuilder = RtaApi.UserInfo.newBuilder();
                ubuilder.setIsInterested(true);
                ubuilder.setRtaId(bidRtaId);

                RtaApi.UserInfo uinfo = ubuilder.build();
                builderRsp.addUserInfos(uinfo);
            }
        }
        builderRsp.setRtaVid(strgNum+"");
        builderRsp.setStatusCode(0);
        builderRsp.setReqId(reqId);

        RtaApi.Rsp rsp = builderRsp.build();
        return rsp;
    }
    @Autowired
    RtaCounterPrint rtaCounterPrint;
    /*public boolean needReqRtaId(RtaApi.Req request, RtaCfg rtaCfg){
        if(ocpcSwitcher.isAllJ){
            return true;
        }
        UserEventReq userEventReq = rtaReqConverService.getUserReq(request,rtaCfg);

        if(userEventReq==null){
            return false;
        }
        long ctime = System.currentTimeMillis();
        OcpcEvent activeEvt = redisEventService.getActiveEventByRedis(userEventReq);
        if(activeEvt!=null && rtaCounterPrint.isPrt("needReqRtaId")){
            log.info("rta归因成功 "+ JSON.toJSONString(userEventReq)+" cost "+(System.currentTimeMillis()-ctime));
        }
        return activeEvt==null;
    }*/

}
