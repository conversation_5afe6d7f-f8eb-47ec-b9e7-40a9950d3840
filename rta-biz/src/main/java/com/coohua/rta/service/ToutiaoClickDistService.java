package com.coohua.rta.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.rta.domain.ToutiaoClickDist;
import com.coohua.rta.domain.ToutiaoClickDistGroup;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.mapper.ToutiaoClickDistMapper;
//import com.coohua.rta.producer.RtaKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.LongAdder;
import java.util.regex.Pattern;

@Service
@Slf4j
public class ToutiaoClickDistService extends ServiceImpl<ToutiaoClickDistMapper, ToutiaoClickDist> {

    private static final Map<Integer, LongAdder> groupCounters = new ConcurrentHashMap<>();

    static  {
        for (int i = 0; i < 3; i++) {
            groupCounters.put(i, new LongAdder());
        }
    }

    // 正则表达式模式
    private static final Pattern OAID_PATTERN = Pattern.compile("^[a-fA-F0-9]{32,64}$|^[a-fA-F0-9-]{36}$");
    private static final Pattern CAID_PATTERN = Pattern.compile("^[a-fA-F0-9]{32}$");

    public static boolean isValidOaid(String oaid) {
        return OAID_PATTERN.matcher(oaid).matches();
    }

    public static boolean isValidCaid(String caid) {
        return CAID_PATTERN.matcher(caid).matches();
    }

    @Resource
    private ToutiaoClickDistMapper toutiaoClickDistMapper;
    @Resource
    private ToutiaoClickDistGroupService toutiaoClickDistGroupService;
//    @Autowired
//    private RtaKafkaSender rtaKafkaSender;

//    @PostConstruct
    public void shardClick() {
        // 分页批量拉取click表中的数据
        int currentPage = 1;
        int pageSize = 100000;
        long start = System.currentTimeMillis();
        long lastId = 0;
        int recordsCount = 0;
        while (true) {
            List<ToutiaoClickDist> records = getClicksByPage(currentPage, pageSize, lastId);

            if (records.isEmpty()) {
                break;
            }

            recordsCount += records.size();

            processRecords(records);

            lastId = records.get(records.size() - 1).getId();
        }

        log.info("shard click cost:{}", System.currentTimeMillis() - start);
        log.info("Total records:{}", recordsCount);

        for (Map.Entry<Integer, LongAdder> entry : groupCounters.entrySet()) {
            long sum = entry.getValue().sum();
            log.info("Click Group {} : {}  users", entry.getKey(), sum);
        }

    }

    private void processRecords(List<ToutiaoClickDist> records) {
        long time = System.currentTimeMillis();
        List<ToutiaoClickDistGroup> insertList = new ArrayList<>();
        AtomicReference<Integer> count = new AtomicReference<>(0);
        records.stream().forEach(record -> {

            if (record.getOs().equals("android")) {
                String oaid = record.getOaid();
                if (StrUtil.isBlank(oaid) || oaid.contains("oaid") || oaid.contains("OAID") || oaid.contains("00000000")) {
                    log.error("oaid is abnormal, record:{}", record);
                    return;
                }
            } else if (record.getOs().equals("ios")) {
                String caid = record.getCaid();
                if (StrUtil.isBlank(caid) || caid.contains("caid") || caid.contains("CAID") || caid.contains("00000000")) {
                    log.error("caid is abnormal, record:{}", record);
                    return;
                }
            } else {
                log.error("unknown os:{}", record);
                return;
            }

            String key = buildShardKey(record);
            if (key == null) {
                log.error("key is null, record:{}", record);
                return;
            }
            count.getAndSet(count.get() + 1);

//            rtaKafkaSender.sendToutiaoClick(JSONObject.toJSONString(record));

        });

        log.info("Processed {} records, cost:{}", count, System.currentTimeMillis() - time);
    }


    private ToutiaoClickDistGroup buildGroupEntity(int shard, ToutiaoClickDist record) {
        return ToutiaoClickDistGroup
                .builder()
                .groupId(shard)
                .os(record.getOs())
                .product(record.getProduct())
                .oaid(record.getOaid())
                .caid(record.getCaid())
                .pv(record.getPv())
                .build();
    }

    private String buildShardKey(ToutiaoClickDist record) {
        if (record.getOs().equals("android")) {
            return PlatformEnum.ANDROID.getPlatform() + record.getOaid();
        } else if (record.getOs().equals("ios")) {
            return PlatformEnum.IOS.getPlatform() + record.getCaid();
        }

        return null;
    }

    public List<ToutiaoClickDist> getClicksByPage(int currentPage, int pageSize, long lastId) {
        return toutiaoClickDistMapper.selectListByPage(pageSize, lastId);
    }
}
