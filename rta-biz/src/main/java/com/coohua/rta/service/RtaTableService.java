package com.coohua.rta.service;

import com.coohua.rta.entity.Tables;
import com.coohua.rta.mapper.RtaTableMapper;
import com.coohua.rta.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class RtaTableService {
    @Autowired
    RtaTableMapper rtaTableMapper;


    public static   String rtaUpTableName = "rta_upprice";
    public static   String rtaSubUpTableName = "rta_upprice_sub";
    public static   String rtaDevice = "rta_device";
    public void createIfNot(String tablePre) {
        try {
            rtaTableMapper.createOrderUnionTable(tablePre);
//            log.info("agentck create " + tablePre + " 成功");
        } catch (Exception e) {
            log.warn("",e);
        }
    }
    /**
     *
     * @param tablePre gpt_chat_info
     */
    public void renameTable(String tablePre) {
        String dateStr = DateUtils.formatDateForYMDSTR(new Date());
        List<Tables> tbs = rtaTableMapper.queryTablesByEnd(tablePre,dateStr);
        if(tbs.size()==0){
            try {
                rtaTableMapper.renameTable(tablePre,dateStr);
                log.info("agentck rename " + tablePre+dateStr + " 成功");
            } catch (Exception e) {
                log.warn("",e);
            }
            try {
                rtaTableMapper.createOrderUnionTable(tablePre);
                log.info("agentck create " + tablePre+dateStr + " 成功");
            } catch (Exception e) {
                log.warn("",e);
            }
        }
        try {
            String delDateStr = DateUtils.formatDateForYMDSTR(new Date(System.currentTimeMillis() - 4 * DateTimeConstants.MILLIS_PER_DAY));
            List<Tables>  delTabs = rtaTableMapper.queryTablesByEnd(tablePre,delDateStr);
            if(delTabs.size()>0){
                rtaTableMapper.truncateOrderUnionTable(tablePre,delDateStr);
                rtaTableMapper.dropOrderUnionTable(tablePre,delDateStr);
                log.info("agentck 删除表 " + delDateStr + " 成功");
            }
        } catch (Exception e) {
            log.warn("",e);
        }
    }
}
