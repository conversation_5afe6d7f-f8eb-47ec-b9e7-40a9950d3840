package com.coohua.rta.service;

import cn.hutool.core.collection.CollUtil;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.handler.RtaCheckBidHandler;
import com.coohua.rta.redis.RtaRedisService;
import com.coohua.rta.service.cache.RtaByteProductRtaCache;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * byte 模板实现
 */
@Service("rtaByteTemplate")
public class RtaByteTemplateServiceImp extends AbstractRtaTemplateService<RtaApi.Req> {

    @Resource
    private RtaCheckBidHandler rtaCheckBidHandler;
    @Resource
    private RtaRedisService rtaRedisService;
    @ApolloJsonValue("${rta.bid.rtaId.list:[22902]}")
    private List<Long> bidRtaConfigList;

//    @Autowired
//    public RtaByteTemplateServiceImp(RtaCheckBidHandler rtaCheckBidHandler, RtaRedisService rtaRedisService) {
//        super(rtaCheckBidHandler, rtaRedisService);
//        this.rtaCheckBidHandler = rtaCheckBidHandler;
//        this.rtaRedisService = rtaRedisService;
//    }

    @Override
    protected void setRtaReq() {
        if (super.rtaRedisService == null) {
            super.rtaRedisService = rtaRedisService;
        }
        if (super.rtaCheckBidHandler == null) {
            super.rtaCheckBidHandler = rtaCheckBidHandler;
        }
    }

    @Override
    protected UserEventReq getRequestDeviceInfo(RtaApi.Req request) {
        UserEventReq userEventReq = new UserEventReq();
        RtaApi.PlatformType platformType = request.getPlatform();

        if (RtaApi.PlatformType.IOS.equals(platformType)) {
            userEventReq.setOs(PlatformEnum.IOS.getPlatform());
            if (StringUtils.isNotBlank(request.getDevice().getCaid1())) {
                userEventReq.setCaid1(request.getDevice().getCaid1());
            }

            if (StringUtils.isNotBlank(request.getDevice().getCaid2())) {
                userEventReq.setCaid2(request.getDevice().getCaid2());
            }

            if (StringUtils.isNotBlank(request.getDevice().getCaid())) {
                userEventReq.setCaid(request.getDevice().getCaid());
            }
        } else {
            userEventReq.setOs(PlatformEnum.ANDROID.getPlatform());
            if (StringUtils.isNotBlank(request.getDevice().getOaid())) {
                userEventReq.setOaid(request.getDevice().getOaid());
                userEventReq.setOaid2(request.getDevice().getOaid());
            }
        }
        return userEventReq;
    }

    @Override
    protected Map<Long, RtaByteConfigProduct> getConfigRtaTagMap(String os) {
        if (PlatformEnum.IOS.getPlatform().equals(os)) {
            return RtaByteProductRtaCache.byteRtaIdTagIosMap;
        } else {
            return RtaByteProductRtaCache.byteRtaIdTagAndroidMap;
        }
    }

    @Override
    protected Map<String, Set<Long>> getTagRtaMap(String os) {
        if (PlatformEnum.IOS.getPlatform().equals(os)) {
            return RtaByteProductRtaCache.byteTagRtaIosMap;
        } else {
            return RtaByteProductRtaCache.byteTagRtaAndroidMap;
        }
    }

    @Override
    protected List<Long> getConfigBidRtaList() {
        return bidRtaConfigList;
    }

    @Override
    protected List<Long> getNotBidRtaIdList(List<Long> bidRtaList, Map<String, String> unionTagMap, Map<String, Set<Long>> tagRtaMap, Map<Long, RtaByteConfigProduct> configRtaProductMap, UserEventReq userEventReq) {
        List<Long> notBidRtaList = new ArrayList<>();

        // 未命中人群包，返回所有rtaId
        if (CollUtil.isEmpty(unionTagMap)) {
            Set<Long> tmp = new HashSet<>();
            // 22902 yes
            tmp.addAll(getConfigBidRtaList());
            tmp.addAll(configRtaProductMap.keySet());
            tmp.remove(22903L);
            bidRtaList.addAll(tmp);
            return notBidRtaList;
        }

        // 获取命中tag的rtaId
        notBidRtaList = getHitRtaIdList(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);

        // 只返回已配置账户的rtaId
        List<Long> finalNotBidRtaList = notBidRtaList;
        List<Long> tmpBidList = configRtaProductMap.keySet().stream().filter(rtaId -> !finalNotBidRtaList.contains(rtaId)).collect(Collectors.toList());
        bidRtaList.addAll(tmpBidList);
        // 22903 no
        bidRtaList.remove(22903L);
        // 22902 yes
        bidRtaList.addAll(getConfigBidRtaList());

        return notBidRtaList;
    }
}
