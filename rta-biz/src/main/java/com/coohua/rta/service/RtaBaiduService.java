package com.coohua.rta.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.coohua.rta.dto.req.BaiduRtaRequest;
import com.coohua.rta.dto.req.BaiduRtaResponse;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.handler.RtaCheckBidHandler;
import com.coohua.rta.redis.RtaRedisService;
import com.coohua.rta.service.cache.RtaByteProductRtaCache;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.protobuf.ByteString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RtaBaiduService {

    @Value("${rta.test.baidu.strategy:2}")
    private Integer testStrategy;
    @Value("${rta.baidu.log.server:false}")
    private Boolean rtaLogServer;
    @Value("${rta.baidu.log.apollo:false}")
    private Boolean rtaLogServerApollo;
    @ApolloJsonValue("${rta.baidu.bid.list:[]}")
    private List<Long> baiduRtaBidList;
    @Resource
    private RtaCheckBidHandler rtaCheckBidHandler;
    @Resource
    private RtaRedisService rtaRedisService;

    public BaiduRtaResponse.RtaApiResponse getRtaRsp(BaiduRtaRequest.RtaApiRequest request) {

        List<Long> bidRtaList = CollUtil.newArrayList();

        List<Long> notBidRtaList = chooseUnionBidRta(request, bidRtaList);

        boolean isAllBidFlag = CollUtil.isEmpty(notBidRtaList);


        if (rtaLogServer && rtaLogServerApollo) {
            if (CollUtil.isNotEmpty(notBidRtaList)) {
                log.info("rtaBaidu: qid:{} oaid:{} notBidRtaList:{} ", request.getQid(), request.getDeviceInfo().getOaid(), notBidRtaList);
            } else {
                log.info("rtaBaidu: qid:{} oaid:{} bidRtaList:{} ", request.getQid(), request.getDeviceInfo().getOaid(), bidRtaList);
            }
        }
        // 封装rsp
        return buildRsp(request, bidRtaList, isAllBidFlag);
    }

    private BaiduRtaResponse.RtaApiResponse buildRsp(BaiduRtaRequest.RtaApiRequest request, List<Long> bidRtaList, boolean isAllBidFlag) {
        BaiduRtaResponse.RtaApiResponse.Builder builder = BaiduRtaResponse.RtaApiResponse.newBuilder();
        builder.setQid(request.getQid());

        if (isAllBidFlag) {
            builder.setRes(BaiduRtaResponse.ResType.ALL);
        } else if (CollUtil.isNotEmpty(bidRtaList)) {
            builder.setRes(BaiduRtaResponse.ResType.PART);
            // 策略
            for (Long rtaId : bidRtaList) {
                BaiduRtaResponse.RtaStrategyAdResult.Builder strategyBuild = BaiduRtaResponse.RtaStrategyAdResult.newBuilder();
                strategyBuild.setRtaId(rtaId);
                builder.addStrategyResults(strategyBuild);
            }

        } else {
            builder.setRes(BaiduRtaResponse.ResType.NONE);
        }

        return builder.build();
    }

    private List<Long> chooseUnionBidRta(BaiduRtaRequest.RtaApiRequest request, List<Long> bidRtaList) {
        List<Long> notBidRtaList = new ArrayList<>();
        UserEventReq userEventReq = getRequestDeviceInfo(request);

        Map<String, String> unionTagMap = rtaRedisService.getUnionFromRedisHash(userEventReq);
        // rtaid -> tag
        Map<Long, RtaByteConfigProduct> configRtaProductMap = getConfigRtaTagMap(userEventReq.getOs());
        // tag -> rtaId
        Map<String, Set<Long>> tagRtaMap = getConfigTagRtaMapByOs(request);

        bidRtaList.addAll(baiduRtaBidList);

        // 未命中人群包，返回所有rtaId
        if (CollUtil.isEmpty(unionTagMap)) {
            Set<Long> tmp = new HashSet<>();
            tmp.addAll(configRtaProductMap.keySet());
            bidRtaList.addAll(tmp);
            return notBidRtaList;
        }
        // 获取未命中人群包rtaId
        notBidRtaList = rtaCheckBidHandler.chooseNoBidRtaIdList(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);

        // 只返回已配置账户的rtaId
        List<Long> finalNotBidRtaList = notBidRtaList;
        List<Long> tmpBidList = configRtaProductMap.keySet().stream().filter(rtaId -> !finalNotBidRtaList.contains(rtaId)).collect(Collectors.toList());
        bidRtaList.addAll(tmpBidList);

        return notBidRtaList;
    }

    private Map<String, Set<Long>> getConfigTagRtaMapByOs(BaiduRtaRequest.RtaApiRequest request) {
        if (BaiduRtaRequest.OsType.ANDROID.equals(request.getOsType())) {
            return RtaByteProductRtaCache.baiduTagRtaAndroidMap;
        } else {
            return RtaByteProductRtaCache.baiduTagRtaIosMap;
        }
    }

    private Map<Long, RtaByteConfigProduct> getConfigRtaTagMap(String os) {
        if (PlatformEnum.IOS.getPlatform().equals(os)) {
            return RtaByteProductRtaCache.baiduRtaIdTagIosMap;
        } else {
            return RtaByteProductRtaCache.baiduRtaIdTagAndroidMap;
        }
    }

    private UserEventReq getRequestDeviceInfo(BaiduRtaRequest.RtaApiRequest request) {
        UserEventReq userEventReq = new UserEventReq();
        BaiduRtaRequest.OsType osType = request.getOsType();
        if (BaiduRtaRequest.OsType.ANDROID.equals(osType)) {
            userEventReq.setOs(PlatformEnum.ANDROID.getPlatform());
            ByteString oaid = request.getDeviceInfo().getOaid();
            if (ObjectUtil.isNotEmpty(oaid) && StrUtil.isNotBlank(oaid.toStringUtf8())) {
                userEventReq.setOaid(oaid.toStringUtf8());
            }
        }

        return userEventReq;
    }
}
