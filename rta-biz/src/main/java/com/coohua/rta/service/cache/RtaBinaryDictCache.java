package com.coohua.rta.service.cache;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.coohua.rta.constant.RedisKeyConstants;
import com.coohua.rta.entity.RtaBinaryDict;
import com.coohua.rta.redis.RtaRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class RtaBinaryDictCache {
    @Autowired
    @Qualifier("rta-redis-newJedisClusterClient")
    private JedisClusterClient rtaRedis;

    @Resource
    private RtaRedisService rtaRedisService;

    public static Map<String, String> binaryDictIosMap = new HashMap<>();
    public static Map<String, String> binaryDictAndroidMap = new HashMap<>();

    @PostConstruct
    @Scheduled(cron = "0 0/10 * * * ?")
    public void refreshBinaryDictMap() {
        try {
            Set<String> iosSet = rtaRedisService.getSetFromRedisBatch(RedisKeyConstants.getRtaBinaryDictIos());
            Set<String> androidSet = rtaRedisService.getSetFromRedisBatch(RedisKeyConstants.getRtaBinaryDictAndroid());

            if (CollUtil.isNotEmpty(iosSet)) {
                iosSet.stream()
                        .map(jsonStr -> JSONObject.parseObject(jsonStr, RtaBinaryDict.class))
                        .forEach(dict -> {
                            binaryDictIosMap.put(dict.getRtaType() + ":" + dict.getBitmapDigit(), dict.getRtaTag());
                        });
            } else {
                log.info("binaryDictIos Redis data is null");
            }

            if (CollUtil.isNotEmpty(androidSet)) {
                androidSet.stream()
                        .map(jsonStr -> JSONObject.parseObject(jsonStr, RtaBinaryDict.class))
                        .forEach(dict -> {
                            binaryDictAndroidMap.put(dict.getRtaType() + ":" + dict.getBitmapDigit(), dict.getRtaTag());
                        });
            } else {
                log.info("binaryDictAndroid Redis data is null");
            }

        } catch (Exception e) {
            log.info("refreshBinaryDictMap error", e);
        }
    }

}
