package com.coohua.rta.service;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Paths;

@Slf4j
public class FileDownloadUtil {
 
    /**
     * SIZE
     */
    private static final int size = 1024;
 
    /**
     * File url
     *
     * @param urlAddress url address
     * @param localFileName local file name
     * @param destinationDir destination dir
     */
    public static String fileUrl(String urlAddress, String localFileName, String destinationDir) {
        OutputStream outputStream = null;
        URLConnection urlConnection = null;
        InputStream inputStream = null;
        String fname = destinationDir + File.separator + localFileName;
        File filef = new File(fname);
        if(!filef.getParentFile().exists()){
            filef.getParentFile().mkdirs();
        }
        try {
            URL url = new URL(urlAddress);
            outputStream = new BufferedOutputStream(Files.newOutputStream(Paths.get(fname)));
            urlConnection = url.openConnection();
            inputStream = urlConnection.getInputStream();
 
            byte[] buf = new byte[size];
            int byteRead, byteWritten = 0;
            while ((byteRead = inputStream.read(buf)) != -1) {
                outputStream.write(buf, 0, byteRead);
                outputStream.flush();
                byteWritten += byteRead;
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            try {
                inputStream.close();
                outputStream.close();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return fname;
    }
 
    /**
     * File download
     *
     * @param urlAddress url address
     * @param destinationDir destination dir
     */
    public static String fileDownload(String urlAddress, String destinationDir) {
        String fname = urlAddress.split("/")[urlAddress.split("/").length-1];
        return fileUrl(urlAddress, fname, destinationDir);
    }
}