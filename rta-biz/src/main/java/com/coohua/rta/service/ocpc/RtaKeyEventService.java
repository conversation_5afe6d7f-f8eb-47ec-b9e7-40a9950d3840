package com.coohua.rta.service.ocpc;

import com.alibaba.fastjson.JSON;

import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaOcpcCfg;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.enums.DspType;
import com.coohua.rta.service.cache.RtaCacheSch;
import com.coohua.rta.service.cache.RtaOcpcCache;
import com.coohua.rta.service.strategy.price.StrRtaDirPriceService;
import com.google.common.collect.Lists;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class RtaKeyEventService {
    @Autowired
    RtaOcpcCache rtaOcpcCache;
//    @Autowired
//    HbaseUserActiveService hbaseUserActiveService;
//    @Autowired
//    RtaUpPriceRedisService rtaUpPriceRedisService;

//    private RtaUpprice getMaxUpprice(List<RtaUpprice> rtaUppriceList, DspType dspType){
//        RtaUpprice rtaUpprice = null;
//        if(rtaUppriceList!=null && rtaUppriceList.size()>0){
//            for(RtaUpprice rfprice : rtaUppriceList){
//                DspType dspTypeF = DspType.getDspType(rfprice.getDspType());
//                if(dspTypeF.equals(dspType)){
//                    if(rtaUpprice == null){
//                        rtaUpprice = rfprice;
//                    }else{
//                        if(rfprice.getUppriceFloat()>rtaUpprice.getUppriceFloat()){
//                            rtaUpprice = rfprice;
//                        }
//                    }
//                }
//            }
//
//        }
//        return rtaUpprice;
//    }
    @Autowired
    RtaUserArpuService rtaUserArpuService;

    public  boolean isRtaUprice(String product,String os){
        RtaProductArpu rtaProductArpu = rtaCacheSch.getProductArpuCfg(product,os,DspType.TOUTIAO);
        if(rtaProductArpu==null){
            rtaProductArpu = rtaCacheSch.getProductArpuCfg(product,os,DspType.GUANGDIANTONG);
        }
        if(rtaProductArpu==null){
            return false;
        }else{
            return true;
        }
    }

    public  boolean isRtaUprice(UserEventReq userEventReq){
        boolean isRta = false;
//        ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEventReq.getEventType());
//        if(ToutiaoEventTypeEnum.KEY_EVENT.equals(toutiaoEventTypeEnum) && isRtaUprice(userEventReq.getProduct(),userEventReq.getOs())){
//            UserEvent userActiveEvent = hbaseUserActiveService.queryByHbaseActiveEvent(userEventReq.getUserId()+"",userEventReq.getProduct());
//            if(userActiveEvent!=null){
//                isRta = isRtaUprice(userActiveEvent.getDsp(),userActiveEvent.getProduct(),userActiveEvent.getOs(),userActiveEvent.getAccountId());
//            }
//        }
        return isRta;
    }
    public  boolean isRtaUprice(String dsp,String product,String os,String accountId){
        DspType dspType = DspType.getDspType(dsp);

        RtaProductArpu rtaProductArpu = rtaCacheSch.getProductArpuCfg(product,os,dspType);
        RtaOcpcCfg rtaOcpcCfg = rtaOcpcCache.getRtaOcpcCfg(dspType,os,product);

        boolean isRta = false;

        if (rtaProductArpu!=null && rtaOcpcCfg!=null) {
           if(StringUtils.isNotBlank(rtaOcpcCfg.getAccountIds())){
               List<String> accountIds = Lists.newArrayList(rtaOcpcCfg.getAccountIds().split(","));
               if(accountIds.contains(accountId)){
                   isRta = true;
                   log.info("isrta 开始rta 出价策略 "+ product+"@"+accountId);
               }
           }
        }
        return isRta;
    }
    @Autowired
    StrRtaDirPriceService strRtaDirPriceService;
    @Autowired
    RtaCacheSch rtaCacheSch;

//    public Pair<String,Boolean> isManju(UserEvent userActiveEvent){
//        boolean isManju = false;
//
//        String product = userActiveEvent.getProduct();
//        String accountId = userActiveEvent.getAccountId();
//        String remark = "";
//        String os = userActiveEvent.getOs();
//        String userId = userActiveEvent.getUserId();
//        DspType dspType = DspType.getDspType(userActiveEvent.getDsp());
//        UserEventReq userEventReq = new UserEventReq();
//        userEventReq.setOs(userActiveEvent.getOs());
//        userEventReq.setProduct(userActiveEvent.getProduct());
//        userEventReq.setOaid(userActiveEvent.getOaid());
//        userEventReq.setOaid2(userActiveEvent.getOaid2());
//        userEventReq.setCaid(userActiveEvent.getCaid());
//        userEventReq.setOcpcDeviceId(userActiveEvent.getOcpcDeviceId());
//
//        RtaProductArpu rtaProductArpu = rtaCacheSch.getProductArpuCfg(userEventReq.getProduct(),userActiveEvent.getOs(), dspType);
//        Pair<GuiyingType, List<RtaUpprice>> rtaUpPriceLt = rtaUpPriceRedisService.queryRtaUpPrice(userEventReq);
//        Double chujiaPrice = rtaProductArpu.getChujia();
//
//        long curtime = System.currentTimeMillis() - userActiveEvent.getCreateTime().getTime();
//        long cd = curtime/ DateTimeConstants.MILLIS_PER_DAY;
//
//        if(rtaUpPriceLt!=null && rtaUpPriceLt.getValue()!=null && rtaUpPriceLt.getValue().size()>0){
//            List<RtaUpprice> rtaUppriceList = rtaUpPriceLt.getValue();
//            RtaUpprice rtaUpprice = getMaxUpprice(rtaUppriceList,DspType.getDspType(userActiveEvent.getDsp()));
//
//            chujiaPrice = rtaUpprice.getChujiaPrice();
//            if(chujiaPrice==null){
//                chujiaPrice = rtaProductArpu.getChujia();
//
//                remark = remark+" rtaprice 出价 "+chujiaPrice;
//            }
//
//        }
//
//
//        if(chujiaPrice!=null && chujiaPrice>0 && rtaProductArpu!=null){
//            Double huichuanArpu = StrRtaDirPriceService.getEventArpu(chujiaPrice,rtaProductArpu.getPriceCvr(),rtaProductArpu.getPriceLt(),rtaProductArpu.getPriceOtherRate(),rtaProductArpu.getPriceUpfloat());
//            RtaOcpcCfg rtaOcpcCfg = rtaOcpcCache.getRtaOcpcCfg(dspType,os,product);
//            if(rtaOcpcCfg==null){
//                log.error("rtaocpc错误 无配置 "+dspType+" "+os+" "+product+" "+ JSON.toJSONString(userActiveEvent));
//            }
//            UserArpuEntity userArpuEntity = rtaUserArpuService.getUserArpu(product,userId);
//            if(userArpuEntity!=null){
//                Double spArpu = DoubleUtil.divideDouble(userArpuEntity.getSpArpu(),1000d);
//                Double cpArpu = DoubleUtil.divideDouble(userArpuEntity.getCpArpu(),1000d);
//                Integer spPv = userArpuEntity.getSpPV();
//                Integer cpPv = userArpuEntity.getCpPV();
//
//                Double yusuanjia = DoubleUtil.addDouble(spArpu,cpArpu);
//                if(yusuanjia>=chujiaPrice){
//                    isManju = true;
//                    remark = remark+yusuanjia+">="+chujiaPrice+" 总arpu大于出价 天数->"+cd+" arpuEt="+JSON.toJSONString(userArpuEntity);
//
//                    log.info("满足回传条件 "+userId+" "+remark+" arpuEt="+JSON.toJSONString(userArpuEntity));
//                }else{
//                    if(spPv >= rtaOcpcCfg.getPvLow()){
//                        Double carpu = DoubleUtil.divideDouble(spArpu+cpArpu,(cd+1)*1.0d);
//                        if(carpu>=huichuanArpu){
//                            isManju = true;
//                            remark = remark+carpu+">="+huichuanArpu+" chujiaPrice="+chujiaPrice+" pv大于arpu合格 "+spPv+">"+rtaOcpcCfg.getPvLow()+" 天数->"+cd+" arpuEt="+JSON.toJSONString(userArpuEntity);
//                            log.info("满足回传条件 "+userId+" "+remark+" arpuEt="+JSON.toJSONString(userArpuEntity));
//                        }
//                    }
//                }
//            }
//        }
//        return   new Pair<>(remark,isManju);
//
//    }

}
