package com.coohua.rta.service;

import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.mapper.RtaUppriceMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.rta.redis.RtaDeviceRedisService;
import com.coohua.rta.service.strategy.entity.ChujiaEnt;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-02-05
*/
@Service
@Slf4j
public class RtaUppriceService extends ServiceImpl<RtaUppriceMapper, RtaUpprice> {
}
