package com.coohua.rta.service.cache;

import com.alibaba.fastjson.JSON;
import com.coohua.rta.entity.RtaOcpcCfg;
import com.coohua.rta.enums.DspType;
import com.coohua.rta.service.RtaOcpcCfgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class RtaOcpcCache {
    public static Map<String, RtaOcpcCfg> rtaOcpcMap = new HashMap();

//    @Scheduled(cron = "0 0/3 * * * ?")
//    @PostConstruct
    public void initPkgToProductMap() {
        log.info("开始初始化 rtaocpc 配置");
        initOcpcCfg();
        log.info("初始化 rtaocpc 配置 完成 " + JSON.toJSONString(rtaOcpcMap));
    }

    @Autowired
    RtaOcpcCfgService rtaOcpcCfgService;

    private void initOcpcCfg() {
        List<RtaOcpcCfg> dlist = rtaOcpcCfgService.list();
        for (RtaOcpcCfg rtaOcpcCfg : dlist) {
            try {
                DspType rtaDspType = DspType.getDspType(rtaOcpcCfg.getDsp());
                rtaOcpcMap.put(getCfgKey(rtaDspType, rtaOcpcCfg.getOs(), rtaOcpcCfg.getProduct()), rtaOcpcCfg);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    public static String getCfgKey(DspType dspType, String os, String product) {
        String key = dspType.value + "@" + product + "@" + os;
        return key;
    }

    public RtaOcpcCfg getRtaOcpcCfg(DspType dspType, String os, String product) {
        String rkey = getCfgKey(dspType, os, product);
        RtaOcpcCfg rtaOcpcCfg = rtaOcpcMap.get(rkey);
        return rtaOcpcCfg;
    }
}
