package com.coohua.rta.service.sharding;

import com.coohua.rta.redis.RtaRedisService;
import com.coohua.rta.utils.ABShardingUtil;
import com.coohua.rta.utils.ConsistentHashingShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RtaShardingService {

    @Resource
    private RtaRedisService rtaRedisService;

    public void testHash() {
        Map<String, String> map = rtaRedisService.getMapFromRedisBatch("rta:check:bit:map");

        if (map.size() < 5000) {
            List<String> deviceList = ABShardingUtil.getDeviceList();
            for (String s : deviceList) {
                if (map.size() < 5000) {
                    map.put(s, "1");
                } else {
                    break;
                }
            }
        }

        map.forEach((k, v) -> {
            ABShardingUtil.shard(k+"os");
        });

        ABShardingUtil.printShardingStatistics(ABShardingUtil.getShardingStatistics());

        // 初始化一致性哈希分流器，设置虚拟节点数为100
        ConsistentHashingShardingUtil sharding = new ConsistentHashingShardingUtil(1000);

        // 添加10个组，每组权重为10
        for (int i = 0; i < 10; i++) {
            sharding.addGroup(i, 10);
        }

        int[] counts = new int[10];
        map.forEach((k, v) -> {
            int group = sharding.shard(k+"os");
            counts[group]++;
        });

        // 输出分流结果
        for (int i = 0; i < counts.length; i++) {
            System.out.println("Group " + i + ": " + counts[i] + " users");
        }

    }
}
