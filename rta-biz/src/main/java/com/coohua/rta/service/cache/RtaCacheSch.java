package com.coohua.rta.service.cache;

import cn.hutool.core.thread.NamedThreadFactory;
import com.coohua.rta.entity.RtaCfg;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.enums.DspType;
import com.coohua.rta.enums.PlatFmType;
import com.coohua.rta.service.RtaCfgService;
import com.coohua.rta.service.RtaProductArpuService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RtaCacheSch {

    public static Map<String, RtaCfg> rtaidPrductMap = new HashMap();
    public static Map<String, RtaProductArpu> rtaProductArpuMap = new HashMap();


    Executor deviceToRedisPool = new ThreadPoolExecutor(2,
            2, DateTimeConstants.SECONDS_PER_MINUTE * 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(3),
            new NamedThreadFactory("deviceToRedisPool",false),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );


//    @Scheduled(cron = "0 0/3 * * * ?")
//    @PostConstruct
    public void initPkgToProductMap() {
        initPKgCfg();
        initProductArpu();
    }

    @Autowired
    RtaProductArpuService  rtaProductArpuService;
    private void initProductArpu(){
        try {
            List<RtaProductArpu> rtaList = rtaProductArpuService.lambdaQuery().list();
            rtaList.forEach(rtaCfg -> {
                rtaProductArpuMap.put(rtaCfg.getProduct()+"@"+rtaCfg.getOs()+"@"+rtaCfg.getToufangQudao(),rtaCfg);
            });
        }catch (Exception e){
            log.error("",e);
        }
    }
    @Autowired
    RtaCfgService rtaCfgService;
    private void initPKgCfg(){
        try {
            List<RtaCfg> rtaList = rtaCfgService.lambdaQuery().list();
            rtaList.forEach(rtaCfg -> {
                rtaidPrductMap.put(rtaCfg.getRtaId()+"@"+rtaCfg.getPlatType()+"@"+rtaCfg.getDsp(),rtaCfg);
            });
        }catch (Exception e){
            log.error("",e);
        }
    }
    public RtaCfg getRtaCfg(String rtaId, PlatFmType platFmType, DspType dspType){
        if(rtaidPrductMap.size()==0){
            deviceToRedisPool.execute(new Thread(){
                @Override
                public void run() {
                    initPkgToProductMap();
                    log.info("开始扫描数据"+rtaidPrductMap.size());
                }
            });
        }
        return rtaidPrductMap.get(rtaId+"@"+platFmType.value+"@"+dspType.value);
    }


    public List<RtaCfg> getRtaCfg(PlatFmType platFmType, DspType dspType){
        List<RtaCfg> dlist = new ArrayList<>();

        if(rtaidPrductMap.size()==0){
            deviceToRedisPool.execute(new Thread(){
                @Override
                public void run() {
                    initPkgToProductMap();
                    log.info("开始扫描数据"+rtaidPrductMap.size());
                }
            });
        }

        rtaidPrductMap.forEach((k,v)->{
            String endfx = "@"+platFmType.value+"@"+dspType.value;
            if(k.endsWith(endfx)){
                dlist.add(v);
            }
        });

        return dlist;
    }


    public RtaProductArpu getProductArpuCfg(String product,String os, DspType dspType){
        if(rtaProductArpuMap.size()==0){
            deviceToRedisPool.execute(new Thread(){
                @Override
                public void run() {
                    initProductArpu();
                    log.info("开始扫描数据"+rtaProductArpuMap.size());
                }
            });
        }
        return rtaProductArpuMap.get(product+"@"+os+"@"+dspType.value);
    }
}
