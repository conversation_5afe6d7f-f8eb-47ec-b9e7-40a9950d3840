package com.coohua.rta.service;

import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.handler.RtaCheckBidHandler;
import com.coohua.rta.redis.RtaRedisService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * rta竞价流程模版类
 */
public abstract class AbstractRtaTemplateService<T> {
    protected RtaCheckBidHandler rtaCheckBidHandler;
    protected RtaRedisService rtaRedisService;


    protected abstract void setRtaReq ();


    /**
     * 转换设备信息
     * @return 设备信息实体
     */
    protected abstract UserEventReq getRequestDeviceInfo(T request);

    /**
     * 获取人群包
     * @param userEventReq
     * @return 人群包位图
     */
    private Map<String, String> getUnionTagMap(UserEventReq userEventReq) {

        return rtaRedisService.getUnionFromRedisHash(userEventReq);
    }

    /**
     * rtaId-tag 配置（正排）
     * @return
     */
    protected abstract Map<Long, RtaByteConfigProduct> getConfigRtaTagMap(String os);

    /**
     * tag-rtaId 配置（倒排）
     * @param os
     * @return
     */
    protected abstract Map<String, Set<Long>> getTagRtaMap(String os);

    /**
     * 获取命中人群包tag的rtaId
     * @param unionTagMap
     * @param tagRtaMap
     * @param configRtaProductMap
     * @param userEventReq
     * @return
     */
    protected List<Long> getHitRtaIdList(Map<String, String> unionTagMap, Map<String, Set<Long>> tagRtaMap, Map<Long, RtaByteConfigProduct> configRtaProductMap, UserEventReq userEventReq) {
        return rtaCheckBidHandler.chooseNoBidRtaIdList(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);
    }

    /**
     * 获取配置的需要竞价的rtaId
     * @return
     */
    protected List<Long> getConfigBidRtaList() {
        return new ArrayList<>();
    }

    /**
     * 选出不竞价的rtaId
     *
     * @param bidRtaList
     * @param unionTagMap
     * @param tagRtaMap
     * @param configRtaProductMap
     * @param userEventReq
     * @return
     */
    protected abstract List<Long> getNotBidRtaIdList(List<Long> bidRtaList, Map<String, String> unionTagMap, Map<String, Set<Long>> tagRtaMap, Map<Long, RtaByteConfigProduct> configRtaProductMap, UserEventReq userEventReq);


    protected List<Long> execute(T request, List<Long> bidRtaList) {

        this.setRtaReq();

        UserEventReq userEventReq = getRequestDeviceInfo(request);

        Map<String, String> unionTagMap = getUnionTagMap(userEventReq);

        Map<Long, RtaByteConfigProduct> configRtaProductMap = getConfigRtaTagMap(userEventReq.getOs());

        Map<String, Set<Long>> tagRtaMap = getTagRtaMap(userEventReq.getOs());

        return getNotBidRtaIdList(bidRtaList, unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);
    }


}
