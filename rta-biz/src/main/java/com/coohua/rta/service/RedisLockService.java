package com.coohua.rta.service;

import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

public class RedisLockService {
    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME = "PX";
    private static final Long RELEASE_SUCCESS = 1L;

    public static boolean tryGetDistributedLock(JedisClusterClient coreOptJedisClusterClient, String lockKey, String requestId, int millsExpireTime) {
        String result = coreOptJedisClusterClient.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, millsExpireTime);
        if (LOCK_SUCCESS.equals(result)) {
            return true;
        }
        return false;
    }
}
