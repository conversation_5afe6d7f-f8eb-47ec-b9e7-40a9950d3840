package com.coohua.rta.service;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.distribution.EnumeratedIntegerDistribution;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;

@Service
@Slf4j
public class WeightRandomService {

    private volatile EnumeratedIntegerDistribution distribution;

    @ApolloJsonValue("${rta.cache.weight.config:{\"1\":10,\"2\":10,\"3\":5,\"4\":5,\"5\":5,\"6\":5,\"7\":5,\"8\":5,\"9\":5,\"10\":5,\"11\":5,\"12\":5,\"13\":5,\"14\":5,\"15\":16,\"16\":16,\"17\":16,\"18\":16,\"19\":16,\"20\":16,\"21\":16,\"22\":16,\"23\":16,\"24\":16}}")
    private Map<String, Double> weightConfig;


    private final ConcurrentHashMap<Integer, LongAdder> numberCounters = new ConcurrentHashMap<>();
    // 总调用次数计数器
    private final LongAdder totalCounter = new LongAdder();

    @PostConstruct
    public void init() {
        updateDistribution();
        initCounters();
    }

    private void initCounters() {
        weightConfig.keySet().forEach(key -> {
            int number = Integer.parseInt(key);
            numberCounters.put(number, new LongAdder());
        });
    }

    @ApolloConfigChangeListener
    private void onConfigChange(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged("rta.cache.weight.config")) {
            log.info("Weight config changed, updating distribution");
            try {
                // 睡一下等刷新
                Thread.sleep(200);
                log.info("weightConfig: {}", weightConfig);
                updateDistribution();
                // 重置计数器
                resetCounters();
            } catch (Exception e) {
                log.error("Failed to parse new weight config", e);
            }
        }
    }

    private synchronized void updateDistribution() {
        try {
            int size = weightConfig.size();
            int[] numbers = new int[size];
            double[] weights = new double[size];

            int i = 0;
            for (Map.Entry<String, Double> entry : weightConfig.entrySet()) {
                numbers[i] = Integer.parseInt(entry.getKey());
                weights[i] = entry.getValue();
                i++;
            }

            this.distribution = new EnumeratedIntegerDistribution(numbers, weights);
            log.info("Weight distribution updated successfully");
        } catch (Exception e) {
            log.error("Failed to update weight distribution", e);
        }
    }

    public int getRandomNumber() {
        try {
            int number = distribution.sample();
            // 增加计数
            totalCounter.increment();
            LongAdder counter = numberCounters.get(number);
            if (counter != null) {
                counter.increment();
            }
            return number;
        } catch (Exception e) {
            log.error("Failed to generate random number", e);
            return 1; // 返回默认值
        }
    }

    private void resetCounters() {
        numberCounters.values().forEach(LongAdder::reset);
        totalCounter.reset();
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void logStatistics() {
        try {
            long total = totalCounter.sum();
            if (total == 0) {
                return;
            }

            StringBuilder stats = new StringBuilder("\nRandom Number Distribution Statistics:\n");
            stats.append(String.format("Total samples: %d\n", total));
            stats.append("Number distributions:\n");

            numberCounters.forEach((number, counter) -> {
                long count = counter.sum();
                double percentage = (count * 100.0) / total;
                stats.append(String.format("Number %2d: count=%d, percentage=%.2f%%\n",
                        number, count, percentage));
            });

            log.info(stats.toString());

            // 统计完成后重置计数器
            resetCounters();
        } catch (Exception e) {
            log.error("Failed to log statistics", e);
        }
    }
}
