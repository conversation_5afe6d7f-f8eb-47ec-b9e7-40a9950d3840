package com.coohua.rta.service.strategy;

import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.redis.RtaDeviceRedisService;
import com.coohua.rta.redis.RtaSetSigleService;
import com.coohua.rta.service.RtaCounterPrint;
import com.coohua.rta.service.RtaRstSaveService;
import com.coohua.rta.service.RtaUppriceService;
import com.coohua.rta.service.cache.RtaCacheSch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class StrRtaPriceUpflService {
    @Autowired
    RtaSetSigleService rtaSetSigleService;
    @Autowired
    RtaCacheSch rtaCacheSch;
    @Autowired
    RtaSwitcher rtaSwitcher;
    @Autowired
    RtaUppriceService rtaUppriceService;
    @Autowired
    RtaCounterPrint rtaCounterPrint;
    @Autowired
    RtaRstSaveService rtaRstSaveService;

    private void saveRst(RtaUpprice rtaUpprice, List<RtaUppriceSub> subList) {
        rtaRstSaveService.savePriceFl(rtaUpprice, subList);
    }

    @Autowired
    RtaDeviceRedisService rtaDeviceRedisService;


}
