package com.coohua.rta.service.strategy;

//import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
//import com.coohua.core.caf.dispense.ocpc.service.CaidService;
//import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.dto.req.kuaishou.OsType;
import com.coohua.rta.dto.req.kuaishou.Req;
import com.coohua.rta.entity.RtaCfg;
import com.coohua.rta.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RtaReqConverService {
//    @Autowired
//    OcpcSwitcher ocpcSwitcher;
//    @Autowired
//    RedisEventService redisEventService;

    public UserEventReq getUserReq(RtaApi.Req request, RtaCfg rtaCfg){
        UserEventReq userEventReq = new UserEventReq();
        RtaApi.PlatformType platformType = request.getPlatform();

        if(rtaCfg!=null){
            userEventReq.setProduct(rtaCfg.getProduct());
        }else {
            return null;
        }
        if(RtaApi.PlatformType.IOS.equals(platformType)){
            userEventReq.setOs("ios");
            if(StringUtils.isNotBlank(request.getDevice().getCaid())){
                userEventReq.setCaid(request.getDevice().getCaid2());
            }

            if(StringUtils.isNotBlank(request.getDevice().getIdfa())){
                userEventReq.setIdfa(request.getDevice().getIdfa());
                userEventReq.setIdfa2(MD5Utils.getMd5Sum(request.getDevice().getIdfa()));
            }
        }else{
            userEventReq.setOs("android");
            if(StringUtils.isNotBlank(request.getDevice().getOaid())){
                userEventReq.setOaid(request.getDevice().getOaid());
                userEventReq.setOaid2(request.getDevice().getOaid());
            }

            if(StringUtils.isNotBlank(request.getDevice().getImeiMd5())){
                userEventReq.setOcpcDeviceId(request.getDevice().getImeiMd5());
                userEventReq.setSourceDeviceId(request.getDevice().getImeiMd5());
            }
        }
        return userEventReq;
    }


//    public UserEventReq getTxUserReq(RtaProtos.RtaRequest request, RtaCfg rtaCfg){
//        UserEventReq userEventReq = new UserEventReq();
//
//        RtaProtos.RtaRequest.Device device = request.getDevice();
//
//        RtaProtos.RtaRequest.OperatingSystem platformType = device.getOs();
//        if(rtaCfg!=null){
//            userEventReq.setProduct(rtaCfg.getProduct());
//        }else {
//            return null;
//        }
//        if(RtaProtos.RtaRequest.OperatingSystem.OS_IOS.equals(platformType)){
//            userEventReq.setOs("ios");
//            //CaidService
//            java.util.List<RtaProtos.RtaRequest.Device.QaidInfo>  qaidInfos = request.getDevice().getQaidInfosList();
//            RtaProtos.RtaRequest.Device.QaidInfo qaidInfo = null;
//            if(qaidInfos!=null && qaidInfos.size()>0){
//                qaidInfo = qaidInfos.get(0);
//                for(RtaProtos.RtaRequest.Device.QaidInfo qaidInfof : qaidInfos){
////                    if(StringUtils.equalsIgnoreCase(CaidService.caidVersion,qaidInfof.getOriginVersion())){
////                        qaidInfo = qaidInfof;
////                    }
//                }
//
//            }
//            if(StringUtils.isNotBlank(qaidInfo.getQaid())){
//                userEventReq.setCaid(qaidInfo.getQaid());
//            }
//
//            if(StringUtils.isNotBlank(request.getDevice().getIdfaMd5Sum())){
//                userEventReq.setIdfa(request.getDevice().getIdfaMd5Sum());
//                userEventReq.setIdfa2(request.getDevice().getIdfaMd5Sum());
//            }
//        }else{
//            userEventReq.setOs("android");
//            if(StringUtils.isNotBlank(request.getDevice().getOaid())){
//                userEventReq.setOaid(request.getDevice().getOaid());
//                userEventReq.setOaid2(request.getDevice().getOaid());
//            }
//
//            if(StringUtils.isNotBlank(request.getDevice().getImeiMd5Sum())){
//                userEventReq.setOcpcDeviceId(request.getDevice().getImeiMd5Sum());
//                userEventReq.setSourceDeviceId(request.getDevice().getImeiMd5Sum());
//            }
//        }
//        return userEventReq;
//    }

    public UserEventReq getKsUserReq(Req request, RtaCfg rtaCfg){
        UserEventReq userEventReq = new UserEventReq();

        OsType osType = request.getOsType();

        if(rtaCfg!=null){
            userEventReq.setProduct(rtaCfg.getProduct());
        }else {
            return null;
        }
        if(OsType.IOS.equals(osType)){
            userEventReq.setOs("ios");
            //CaidService
            String caid = request.getCurrentCaid();
//            if(StringUtils.equalsIgnoreCase(CaidService.caidVersion,request.getCurrentCaidVersion())){
//                caid = request.getCurrentCaid();
//            }else{
//                caid = request.getLastCaid();
//            }
            userEventReq.setCaid(caid);
        }else{
            userEventReq.setOs("android");
            if(StringUtils.isNotBlank(request.getOaid())){
                userEventReq.setOaid(request.getOaid());
                userEventReq.setOaid2(request.getOaidMd5());
            }

            if(StringUtils.isNotBlank(request.getDidMd5())){
                userEventReq.setOcpcDeviceId(request.getDidMd5());
            }
        }
        return userEventReq;
    }
}
