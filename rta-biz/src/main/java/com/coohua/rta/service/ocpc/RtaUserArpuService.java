package com.coohua.rta.service.ocpc;

import com.alibaba.fastjson.JSON;
//import com.coohua.core.caf.dispense.ocpc.entity.kafka.VideoAdReportEntitiy;
import com.coohua.rta.entity.UserArpuEntity;
import com.coohua.rta.service.ocpc.RtaKeyEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import redis.clients.jedis.Response;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClientRefer;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/2/18
 */
@Slf4j
@Service
public class RtaUserArpuService {

    @JedisClusterClientRefer(namespace = "rta-redis-new")
    private JedisClusterClient rtaRedis;

    public void saveBatch(List<String> flist, boolean needSave){
//        Map<String, List<VideoAdReportEntitiy>> videoMap = flist.stream()
//                .map(r -> JSON.parseObject(r,VideoAdReportEntitiy.class))
//                .collect(Collectors.groupingBy(r -> buildRedisKey(r.getProduct(),r.getUserId())));
//
//        Map<String, UserArpuEntity> saveMap = new HashMap<>();
//        videoMap.forEach((key,vList) -> {
//            UserArpuEntity userArpuEntity = new UserArpuEntity();
//            userArpuEntity.setCpArpu(0d);
//            userArpuEntity.setSpArpu(0d);
//            userArpuEntity.setCpPV(0);
//            userArpuEntity.setSpPV(0);
//            VideoAdReportEntitiy first = vList.get(0);
//            userArpuEntity.setUserId(Long.valueOf(first.getUserId()));
//            userArpuEntity.setProduct(first.getProduct());
//            userArpuEntity.setOs(first.getOs());
//
//            for (VideoAdReportEntitiy videoAdReportEntitiy : vList){
//                if (videoAdReportEntitiy.getAdType() == 3){
//                    userArpuEntity.setCpArpu(userArpuEntity.getCpArpu() + Double.parseDouble(videoAdReportEntitiy.getPrice()));
//                    userArpuEntity.setCpPV(userArpuEntity.getCpPV() + 1);
//                }
//                if (videoAdReportEntitiy.getAdType() == 4){
//                    userArpuEntity.setSpArpu(userArpuEntity.getSpArpu() + Double.parseDouble(videoAdReportEntitiy.getPrice()));
//                    userArpuEntity.setSpPV(userArpuEntity.getSpPV() + 1);
//                }
//            }
//            saveMap.put(key,userArpuEntity);
//        });
//
//        if (needSave){
//            updateSave(saveMap);
//        }else {
//            // 查询
//            pushRtaEvent(saveMap);
//        }
    }

    public UserArpuEntity getUserArpu(String product,String userId){
        String key = buildRedisKey(product,userId);
        String result = rtaRedis.get(key);
        if (StringUtils.isNotEmpty(result)){
            return JSON.parseObject(result,UserArpuEntity.class);
        }
        return null;
    }

    // RTA回传触发
    public void pushRtaEvent(Map<String, UserArpuEntity> saveMap){
        try (Jedis jedis =  rtaRedis.getResource("{rta:ad}:")){
            Pipeline pipeline = jedis.pipelined();
            saveMap.forEach((key,v)->{
                Response<String> rs = pipeline.get(key);
                pipeline.sync();
                if (rs != null){
                    String result = rs.get();
                    if (StringUtils.isNotEmpty(result)){
                        UserArpuEntity userArpuEntity = JSON.parseObject(result,UserArpuEntity.class);
                        v.setCpPV(userArpuEntity.getCpPV() == null?v.getCpPV():userArpuEntity.getCpPV()+v.getCpPV());
                        v.setSpPV(userArpuEntity.getSpPV() == null?v.getSpPV():userArpuEntity.getSpPV()+v.getSpPV());
                        v.setCpArpu(userArpuEntity.getCpArpu() == null?v.getCpArpu():userArpuEntity.getCpArpu()+v.getCpArpu());
                        v.setSpArpu(userArpuEntity.getSpArpu() == null?v.getSpArpu():userArpuEntity.getSpArpu()+v.getSpArpu());
                    }
                }
//                rtaEventGuiyService.sendRtaKeyEvent(v);
            });
            pipeline.close();
        }catch (Exception e){
            log.error("查询Redis  ex:",e);
        }
    }

    @Autowired
    RtaEventGuiyService rtaEventGuiyService;
    private void updateSave(Map<String, UserArpuEntity> saveMap){
        try (Jedis jedis =  rtaRedis.getResource("{rta:ad}:")){
            Pipeline pipeline = jedis.pipelined();
            saveMap.forEach((key,v)->{
                Response<String> rs = pipeline.get(key);
                pipeline.sync();
                if (rs != null){
                    String result = rs.get();
                    if (StringUtils.isNotEmpty(result)){
                        UserArpuEntity userArpuEntity = JSON.parseObject(result,UserArpuEntity.class);
                        v.setCpPV(userArpuEntity.getCpPV() == null?v.getCpPV():userArpuEntity.getCpPV()+v.getCpPV());
                        v.setSpPV(userArpuEntity.getSpPV() == null?v.getSpPV():userArpuEntity.getSpPV()+v.getSpPV());
                        v.setCpArpu(userArpuEntity.getCpArpu() == null?v.getCpArpu():userArpuEntity.getCpArpu()+v.getCpArpu());
                        v.setSpArpu(userArpuEntity.getSpArpu() == null?v.getSpArpu():userArpuEntity.getSpArpu()+v.getSpArpu());
                    }
                }
                pipeline.set(key, JSON.toJSONString(v));
                pipeline.expire(key, 60 * 60 * 24 * 3);
            });
            pipeline.sync();
            pipeline.close();
        }catch (Exception e){
            log.error("保存Redis异常  ex:",e);
        }
    }

    public String buildRedisKey(String product,String userId){
        return String.format("{rta:ad}:sum:arpu:%s:%s",product,userId);
    }
}
