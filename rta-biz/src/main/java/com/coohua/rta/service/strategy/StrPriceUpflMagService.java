package com.coohua.rta.service.strategy;

//import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.dto.resp.PChildsClssT;
import com.coohua.rta.entity.RtaCfg;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.enums.DspType;
import com.coohua.rta.enums.PlatFmType;
import com.coohua.rta.service.RtaCounterPrint;
import com.coohua.rta.service.RtaRspService;
import com.coohua.rta.service.cache.RtaCacheSch;
import com.coohua.rta.service.strategy.price.StrRtaDirPriceService;
import com.google.protobuf.Int32Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class StrPriceUpflMagService{
    @Autowired
    RtaSwitcher rtaSwitcher;
    @Autowired
    RtaCacheSch rtaCacheSch;
//    @Autowired
//    RedisEventService redisEventService;
    @Autowired
    RtaReqConverService rtaReqConverService;
    @Autowired
    StrRtaNorepeatService strRtaNorepeatService;
    @Autowired
    RtaRspService rtaRspService;
    @Autowired
    RtaCounterPrint rtaCounterPrint;
    @Autowired
    StrRtaDirPriceService strRtaDirPriceService;
    public RtaApi.Rsp getRtaRsp(RtaApi.Req request){
        List<Long> rtaIdList = request.getRtaIdsList();
        PlatFmType platFmType = PlatFmType.android;

        RtaApi.PlatformType platformType = request.getPlatform();
        if(RtaApi.PlatformType.IOS.equals(platformType)){
            platFmType = PlatFmType.ios;
        }

        RtaApi.Rsp.Builder  builderRsp = RtaApi.Rsp.newBuilder();
        builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(0).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用

        RtaApi.UserInfo.Builder ubuilder = RtaApi.UserInfo.newBuilder();
        ubuilder.setIsInterested(true);

        String remark = "";
        for(Long rtaId : rtaIdList){
            RtaCfg rtaCfg = rtaCacheSch.getRtaCfg(rtaId+"",platFmType, DspType.TOUTIAO);
            if(rtaCfg!=null){
                //rtaId 代表product
                boolean isNeedReq = true;
                if(rtaSwitcher.repeatFitFlag){
                    isNeedReq = strRtaNorepeatService.needReqRtaId(request,rtaCfg);
                }
                if(isNeedReq){
                    UserEventReq userEventReq = rtaReqConverService.getUserReq(request,rtaCfg);
                    try {
                        PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> frtaPricePair = strRtaDirPriceService.getChujiaPrice(userEventReq,request.getReqId(), DspType.TOUTIAO);
                        ubuilder.setRtaId(Long.parseLong(rtaCfg.getRtaId()));
                        ubuilder.setIsInterested(true);

                        RtaApi.UserInfo.UserQuality.Builder builder =  RtaApi.UserInfo.UserQuality.newBuilder();
                        builder.setUserType(RtaApi.UserInfo.UserType.BOOST_COEF);
                        builder.setQuality(frtaPricePair.t.getUppriceFloat());
                        if(rtaCounterPrint.isPrt("uqu")){
                            log.info("rta出价 -"+builder.getUserType()+" 系数为-"+builder.getQuality());
                        }
                        RtaApi.UserInfo.UserQuality userQuality = builder.build();
                        ubuilder.addUserScores(userQuality);
                        RtaApi.UserInfo uinfo = ubuilder.build();
                        builderRsp.addUserInfos(uinfo);
                        if(frtaPricePair!=null && rtaSwitcher.isSendNoBidRsp){
                            rtaRspService.sysSaveRtaRsp(request,frtaPricePair,builderRsp,remark);
                        }
                    }catch (Exception e){
                        log.error("",e);
                    }
                }else{
                    remark = "isneed "+isNeedReq;
                    ubuilder.setIsInterested(false);
                    ubuilder.setRtaId(rtaId);

                    RtaApi.UserInfo uinfo = ubuilder.build();
                    builderRsp.addUserInfos(uinfo);

                }
            }else{
                //无配置 不竞价
                remark = "nocfg";
                ubuilder.setIsInterested(false);
                ubuilder.setRtaId(rtaId);

                RtaApi.UserInfo uinfo = ubuilder.build();
                builderRsp.addUserInfos(uinfo);

            }

        }

        if(builderRsp.getUserInfosList()==null || builderRsp.getUserInfosList().size()==0){
            builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
        }
        builderRsp.setRtaVid(request.getEnableStrategy().getNumber()+"");
        builderRsp.setStatusCode(0);
        builderRsp.setReqId(request.getReqId());


        request.getSource();//local 站内 union穿山甲
        RtaApi.Rsp rsp = builderRsp.build();
        return rsp;

    }

}
