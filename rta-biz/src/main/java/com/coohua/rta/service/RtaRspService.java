package com.coohua.rta.service;

import cn.hutool.core.thread.NamedThreadFactory;
//import com.coohua.core.caf.dispense.kafka.RtaKafkaSender;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.resp.PChildsClssT;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.entity.RtaRsp;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.mapper.RtaRspMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-02-05
*/
@Service
@Slf4j
public class RtaRspService extends ServiceImpl<RtaRspMapper, RtaRsp> {
//    @Autowired
//    RtaKafkaSender rtaKafkaSender;


    Executor rtaRstToDbPool = new ThreadPoolExecutor(80,
            80, DateTimeConstants.SECONDS_PER_MINUTE * 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(3000),
            new NamedThreadFactory("deviceToRedisPool",false),
            new ThreadPoolExecutor.DiscardOldestPolicy()
    );

    public void sysSaveRtaRsp(RtaApi.Req request, Pair<PChildsClssT<RtaUpprice, RtaUppriceSub,RtaProductArpu>,Double> flRatePair, RtaApi.Rsp.Builder  builderRsp, String remark){
        rtaRstToDbPool.execute(()->{
            saveRtaRsp(request,flRatePair,builderRsp,remark);
        });
    }



    public void sysSaveRtaRsp(RtaApi.Req request,PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> pChildsClssT,RtaApi.Rsp.Builder  builderRsp,String remark){
        Pair<PChildsClssT<RtaUpprice, RtaUppriceSub,RtaProductArpu>,Double> flRatePair = new Pair<>(pChildsClssT,pChildsClssT.t.getChujiaPrice());
        rtaRstToDbPool.execute(()->{
            saveRtaRsp(request,flRatePair,builderRsp,remark);
        });
    }

    public void saveRtaRsp(RtaApi.Req request, Pair<PChildsClssT<RtaUpprice, RtaUppriceSub,RtaProductArpu>,Double> flRatePair,RtaApi.Rsp.Builder  builderRsp,String remark){
        try {
            RtaRsp rtaRsp = new RtaRsp();
            rtaRsp.setReqId(request.getReqId());

            rtaRsp.setOaid(request.getDevice().getOaid());
            rtaRsp.setImei(request.getDevice().getImeiMd5());
            rtaRsp.setCaid(request.getDevice().getCaid());

            rtaRsp.setBidType(builderRsp.getBidType().getValue());
            rtaRsp.setOs(request.getPlatform().name());

            if(flRatePair!=null){
                PChildsClssT<RtaUpprice, RtaUppriceSub,RtaProductArpu> pChildsClssT = flRatePair.getKey();
                if(pChildsClssT!=null){
                    if(pChildsClssT.t!=null){
                        rtaRsp.setUpFloat(pChildsClssT.t.getUppriceFloat());
                        rtaRsp.setProduct(pChildsClssT.t.getProduct());
                        rtaRsp.setPpv(pChildsClssT.t.getHpv());
                        rtaRsp.setParpu(pChildsClssT.t.getHarpu());
                        rtaRsp.setPwithdraw(pChildsClssT.t.getHwithdraw());
                        rtaRsp.setPcpa(pChildsClssT.t.getHcpa());
                    }
                }
            }

            rtaRsp.setStrgyId(1);
            rtaRsp.setExtend1(builderRsp.getUserInfosList().size()+"");
            List<RtaApi.UserInfo> rlist = builderRsp.getUserInfosList();
            if(rlist!=null && rlist.size()>0 && rlist.get(0).getUserScoresList()!=null && rlist.get(0).getUserScoresList().size()>0){
                RtaApi.UserInfo.UserQuality  userQuality  = rlist.get(0).getUserScores(0);
                rtaRsp.setExtend2(userQuality.getUserType().getNumber()+"");
                rtaRsp.setExtend3(userQuality.getQuality()+"");
            }

            rtaRsp.setExtend4(remark);
//        rtaRsp.setExtend1();
//        rtaRsp.setExtend2();
//        rtaRsp.setExtend3();
//        rtaRsp.setExtend4();
            rtaRsp.setCreateTime(new Date());
            rtaRsp.setUpdateTime(new Date());

//            rtaKafkaSender.sendMsg(JSON.toJSONString(rtaRsp));

            if(rtaCounterPrint.isPrt("saveTOkAFA")){
                log.info("成功发送数据成功  "+rtaRsp.getProduct()+" "+rtaRsp.getCaid());
            }
        }catch (Exception e){
            log.error("",e);
        }

    }
    @Autowired
    RtaCounterPrint rtaCounterPrint;
}
