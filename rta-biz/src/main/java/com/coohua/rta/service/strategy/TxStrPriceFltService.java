package com.coohua.rta.service.strategy;

import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.dto.req.kuaishou.Req;
import com.coohua.rta.dto.resp.PChildsClssT;
import com.coohua.rta.entity.RtaCfg;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.enums.DspType;
import com.coohua.rta.service.strategy.price.StrRtaDirPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TxStrPriceFltService {
    @Autowired
    StrRtaDirPriceService strRtaDirPriceService;
    @Autowired
    RtaReqConverService rtaReqConverService;
//    public PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> getTxFlt(RtaProtos.RtaRequest request, RtaCfg rtaCfg){
//        UserEventReq userEventReq = rtaReqConverService.getTxUserReq(request,rtaCfg);
//        PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> frtaPricePair = strRtaDirPriceService.getChujiaPrice(userEventReq,request.getId(), DspType.GUANGDIANTONG);
//        return frtaPricePair;
//    }


    public PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> getKsFlt(Req request, RtaCfg rtaCfg){
        UserEventReq userEventReq = rtaReqConverService.getKsUserReq(request,rtaCfg);
        PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> frtaPricePair = strRtaDirPriceService.getChujiaPrice(userEventReq,request.getId(), DspType.KUAISHOU);
        return frtaPricePair;
    }

}
