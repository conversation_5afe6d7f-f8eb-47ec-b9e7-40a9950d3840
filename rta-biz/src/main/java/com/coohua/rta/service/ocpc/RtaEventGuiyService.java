package com.coohua.rta.service.ocpc;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson.JSON;
//import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
//import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
//import com.coohua.core.caf.dispense.dto.req.UserEventReq;
//import com.coohua.core.caf.dispense.hbase.HbaseRtaService;
//import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
//import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
//import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
//import com.coohua.core.caf.dispense.redis.RedisEventService;
//import com.coohua.core.caf.dispense.rta.entity.UserArpuEntity;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RtaEventGuiyService {
    @Autowired
    RtaKeyEventService rtaKeyEventService;
//    @Autowired
//    HbaseUserActiveService hbaseUserActiveService;
//    @Autowired
//    OcpcEventService ocpcEventService;

    public static Executor deviceToRedisPool = null;
//    @Autowired
//    HbaseRtaService hbaseRtaService;
//    public void sendRtaKeyEvent(UserArpuEntity v){
    public void sendRtaKeyEvent(){
        try {
            if(deviceToRedisPool==null){
                deviceToRedisPool = new ThreadPoolExecutor(50,
                        50, DateTimeConstants.SECONDS_PER_MINUTE * 60,
                        TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
                        new NamedThreadFactory("deviceToRedisPool",false),
                        new ThreadPoolExecutor.CallerRunsPolicy()
                );
            }

//            deviceToRedisPool.execute(()->{
//                if(rtaKeyEventService.isRtaUprice(v.getProduct(),v.getOs())){
//                    UserEvent userActiveEvent = hbaseUserActiveService.queryByHbaseActiveEvent(v.getUserId()+"",v.getProduct());
//                    if(userActiveEvent!=null){
//                        if(rtaKeyEventService.isRtaUprice(userActiveEvent.getDsp(),userActiveEvent.getProduct(),userActiveEvent.getOs(),userActiveEvent.getAccountId())){
//                            OcpcEvent hbaseOcpcEvent = hbaseRtaService.getUserKeyEvent(userActiveEvent.getProduct(),userActiveEvent.getOs(),userActiveEvent.getUserId());
//                            if(hbaseOcpcEvent!=null){
//                                log.info("ocpc rta已经回传 "+hbaseOcpcEvent.getProduct()+" "+hbaseOcpcEvent.getAccountId()+" "+ JSON.toJSONString(hbaseOcpcEvent));
//                                return;
//                            }
//                            Pair<String,Boolean> isManjuPair = rtaKeyEventService.isManju(userActiveEvent);
//
//                            if(isManjuPair!=null && isManjuPair.getValue()){
//                                log.info("ocpc rta开始回传 "+v.getProduct()+" "+userActiveEvent.getAccountId()+" "+userActiveEvent.getUserId()+" "+ JSON.toJSONString(userActiveEvent));
//                                OcpcEvent ocpcEvent = new OcpcEvent();
//                                BeanUtils.copyProperties(userActiveEvent,ocpcEvent);
//                                ocpcEvent.setUserId(userActiveEvent.getUserId());
//                                ocpcEvent.setProduct(userActiveEvent.getProduct());
//                                ocpcEvent.setOaid(userActiveEvent.getOaid());
//                                ocpcEvent.setCallbackUrl(userActiveEvent.getCallbackUrl());
//                                ocpcEvent.setCaid(userActiveEvent.getCaid());
//                                ocpcEvent.setOs(userActiveEvent.getOs());
//                                ocpcEvent.setDsp(userActiveEvent.getDsp());
//                                ocpcEvent.setRemark(isManjuPair.getKey());
//                                ToutiaoClick toutiaoClick = RedisEventService.convertEventToClick(ocpcEvent);
//
//                                toutiaoClick.setAidName(isManjuPair.getKey());
//
//                                UserEventReq userEventReq = new UserEventReq();
//                                BeanUtils.copyProperties(userActiveEvent,userEventReq);
//
//                                userEventReq.setUserId(userActiveEvent.getUserId());
//                                userEventReq.setProduct(userActiveEvent.getProduct());
//                                userEventReq.setOaid(userActiveEvent.getOaid());
//                                userEventReq.setCaid(userActiveEvent.getCaid());
//                                userEventReq.setOs(userActiveEvent.getOs());
//
//                                userEventReq.setEventType(25);
//                                boolean isAct = ocpcEventService.guiyingDspClick(toutiaoClick, userEventReq, true);
//                                log.info("ocpc rta回传成功 ");
//                            }
//                        }
//                    }
//                }
//            });
        }catch (Exception e){
            log.error("",e);
        }
    }
}
