package com.coohua.rta.service;

import cn.hutool.core.collection.CollUtil;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.handler.RtaCheckBidHandler;
import com.coohua.rta.redis.RtaRedisService;
import com.coohua.rta.service.cache.RtaByteProductRtaCache;
import com.coohua.rta.service.cache.RtaLocalCacheService;
import com.coohua.rta.service.shard.ABShardingService;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.protobuf.Int32Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RtaByteService {

    @Value("${rta.test.strategy:1}")
    private Integer testStrategy;

    @Value("${rta.cache.time:14400}")
    private Integer rtaCacheTime;
    @Value("${rta.not.bid.cache.time:86400}")
    private Integer rtaNotBidCacheTime;
    @ApolloJsonValue("${rta.bid.rtaId.list:[22902]}")
    private List<Long> bidRtaConfigList;
    @ApolloJsonValue("${rta.choose.bid.rtaId.list:[22904]}")
    private List<Long> chooseBidRtaConfigList;
    @ApolloJsonValue("${rta.ab.not.bid.rtaId.list:[]}")
    private List<Long> abNotBidRtaIdList;
    @Value("${rta.byte.log.server:false}")
    private Boolean rtaLogServer;
    @Value("${rta.byte.log.apollo:false}")
    private Boolean rtaLogServerApollo;
    @ApolloJsonValue("${rta.byte.ab.test.Map:{}}")
    private Map<Integer, List<Long>> abTestRtaMap;
    @Value("${rta.subsides.recycle.ab.switch:false}")
    private Boolean subsidesRecycleAbSwitch;
    @Value("${rta.byte.ab.subsides.recycle:5}")
    private Integer subsidesRecycleAb;
    @ApolloJsonValue("${rta.byte.sharding.weights:[50, 50]}")
    private int[] WEIGHTS;
    @ApolloJsonValue("${rta.byte.new.framework.sharding.weights:[50, 50]}")
    private int[] NEW_FRAMEWOTK_WEIGHTS;
    @Resource
    private RtaCheckBidHandler rtaCheckBidHandler;
    @Resource
    private RtaRedisService rtaRedisService;
    @Resource(name = "rtaByteTemplate")
    private AbstractRtaTemplateService rtaTemplateService;
    @Resource
    private WeightRandomService weightRandomService;
    @Value("${rta.new.framework.test.switch:false}")
    private Boolean newFrameWorkTestSwitch;
    @ApolloJsonValue("${rta.new.framework.a.test.id.list:[]}")
    private List<Long> newFrameWorkATestIdList;
    @ApolloJsonValue("${rta.new.framework.b.test.id.list:[]}")
    private List<Long> newFrameWorkBTestIdList;
    List<Integer> testIdList = new ArrayList<>(Collections.nCopies(11, 0));

    public RtaApi.Rsp getRtaRsp(RtaApi.Req request) {
        List<Long> bidRtaList = new ArrayList<>();
        List<Long> notBidRtaList = new ArrayList<>();
        RtaApi.PlatformType platformType = request.getPlatform();

        // 获取配置的rta
        List<Long> configBidRtaList = getConfigBidRtaList();


//        if (testStrategy == 4) {
        // ab 实验
//            notBidRtaList = chooseBidRtaByAbTest(request, bidRtaList, configBidRtaList, notBidRtaList);
//        } else {
        notBidRtaList = chooseUnionBidRta(request, bidRtaList, configBidRtaList, notBidRtaList);

//            notBidRtaList = rtaTemplateService.execute(request, bidRtaList);
//        }

        rtaBidOrNotAbTest(request, bidRtaList);

//        log.info("chooseUnionBidRta notBidRtaList {} bidRtaList {}", notBidRtaList, bidRtaList);

        // 封装rsp
        RtaApi.Rsp rsp;
//        if (testStrategy == 7) {
//            rsp = buildRtaRspForAbTest(request, bidRtaList);
//        } else {
            rsp = buildRtaRspWithRandomCacheTime(request, bidRtaList);
//        }
        printBidLog(rsp, bidRtaList, request, notBidRtaList);
        return rsp;
    }


    private void rtaBidOrNotAbTest(RtaApi.Req request, List<Long> bidRtaList) {
        UserEventReq userEventReq = getRequestDeviceInfo(request);
        if(PlatformEnum.IOS.getPlatform().equals(userEventReq.getOs())) {
            return;
        }

        try {
            //主分流
            String shardingKey = buildShardingKey(userEventReq);
            // 先读取本地缓存，获取不到则重新分流
            Integer shard = RtaLocalCacheService.getSharding(shardingKey);
            if (shard == null) {
                shard = ABShardingService.shard(shardingKey, ABShardingService.TOTAL_WEIGHT, WEIGHTS);
                RtaLocalCacheService.setSharding(shardingKey, shard);
                ABShardingService.count(shard);
            }
            //新分流
//        String newShardKey = buildNewShardingKey(userEventReq, RTA_NEW_FRAMEWORK_AB_TEST);
//        Integer newShard = RtaLocalCacheService.getSharding(newShardKey);
//        if (newShard == null) {
//            newShard = ABShardingService.shard(newShardKey, ABShardingService.TOTAL_WEIGHT, NEW_FRAMEWOTK_WEIGHTS);
//            RtaLocalCacheService.setSharding(newShardKey, newShard);
//            ABShardingService.count(newShard);
//        }


            if (newFrameWorkTestSwitch) {
                if (shard == 0 && CollectionUtils.isNotEmpty(newFrameWorkATestIdList)) {
                    bidRtaList.removeAll(newFrameWorkATestIdList);
                } else if (shard == 1 && CollectionUtils.isNotEmpty(newFrameWorkBTestIdList)) {
                    bidRtaList.removeAll(newFrameWorkBTestIdList);
                }
            }

            //        先不上
//        if (subsidesRecycleAbSwitch) {
//            if (subsidesRecycleAb.equals(newShard) && !abNotBidRtaIdList.isEmpty()) {
//                for (Long notBidId : abNotBidRtaIdList) {
//                    bidRtaList.remove(notBidId);
//                }
//            }
//        }
        } catch (Exception e) {
            log.error("新框架分流试验异常");
        }




    }


    private void printBidLog(RtaApi.Rsp rsp, List<Long> bidRtaList, RtaApi.Req request, List<Long> notBidRtaList) {
        // 异步去打印
        CompletableFuture.runAsync(() -> {
            if (rtaLogServer && rtaLogServerApollo) {
//                if (CollUtil.isNotEmpty(notBidRtaList)) {
                // 创建一个 JsonPrinter 对象，并配置为包含默认值
//                    JsonFormat.Printer printer = JsonFormat.printer().includingDefaultValueFields();
//                    String jsonString = null;
//
//                    try {
//                        // 将 protobuf 对象转换为 JSON 字符串
//                        jsonString = printer.print(rsp);
//                    } catch (InvalidProtocolBufferException e) {
//                        log.error("rsp to json error");
//
//                    }

//                }
                // todo
                if (CollUtil.isNotEmpty(notBidRtaList)) {
                    log.info("rtaByte reqId:{} oaid:{} caid:{} caid1:{} caid2:{},notBidList:{}", request.getReqId(), request.getDevice().getOaid(), request.getDevice().getCaid(), request.getDevice().getCaid1(), request.getDevice().getCaid2(), notBidRtaList);
                }
                log.info("rtaByte request {}", request);

            }
        });

    }

    private List<Long> getConfigChooseBidRtaList() {
        return chooseBidRtaConfigList;
    }

    private List<Long> getConfigBidRtaList() {
        return bidRtaConfigList;
    }

    /**
     * 根据人群包判断当前rta是否是竞价的rta
     *
     * @param request
     * @param bidRtaList       竞价rta列表
     * @param configBidRtaList 竞价rtaId
     * @param notBidRtaList
     */
    private List<Long> chooseUnionBidRta(RtaApi.Req request, List<Long> bidRtaList, List<Long> configBidRtaList, List<Long> notBidRtaList) {

        // 获取设备信息
        UserEventReq userEventReq = getRequestDeviceInfo(request);

        Map<String, String> unionTagMap = rtaRedisService.getUnionFromRedisHash(userEventReq);
        // rtaid -> tag
        Map<Long, RtaByteConfigProduct> configRtaProductMap = getConfigRtaTagMap(userEventReq.getOs());
        // tag -> rtaId
        Map<String, Set<Long>> tagRtaMap = getConfigTagRtaMapByOs(request);
        // 未命中人群包，返回所有rtaId
        if (CollUtil.isEmpty(unionTagMap)) {
            Set<Long> tmp = new HashSet<>();
            // 22902 yes
            tmp.addAll(configBidRtaList);
            tmp.addAll(configRtaProductMap.keySet());
            tmp.remove(22903L);
            bidRtaList.addAll(tmp);
            return notBidRtaList;
        }

        // 拉黑设备实验
//        if (testStrategy == 6 && PlatformEnum.ANDROID.getPlatform().equals(userEventReq.getOs())) {
//            String shardingKey = buildShardingKey(userEventReq);
//            notBidRtaList = rtaCheckBidHandler.chooseNoBidRtaIdListForBlackDeviceAbTest(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq, shardingKey);
//        } else {
        // 获取命中tag的rtaId
        notBidRtaList = rtaCheckBidHandler.chooseNoBidRtaIdList(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);
//        }

        // 只返回已配置账户的rtaId
        List<Long> finalNotBidRtaList = notBidRtaList;
        List<Long> tmpBidList = configRtaProductMap.keySet().stream().filter(rtaId -> !finalNotBidRtaList.contains(rtaId)).collect(Collectors.toList());
        bidRtaList.addAll(tmpBidList);
        // 22903 no
        bidRtaList.remove(22903L);
        // 22902 yes
        bidRtaList.addAll(configBidRtaList);

        return notBidRtaList;
    }

    private Map<Long, RtaByteConfigProduct> getConfigRtaTagMap(String os) {
        if (PlatformEnum.IOS.getPlatform().equals(os)) {
            return RtaByteProductRtaCache.byteRtaIdTagIosMap;
        } else {
            return RtaByteProductRtaCache.byteRtaIdTagAndroidMap;
        }
    }

    private List<Long> chooseBidRtaByAbTest(RtaApi.Req request, List<Long> bidRtaList, List<Long> configBidRtaList, List<Long> notBidRtaList) {
        // 获取设备信息
        UserEventReq userEventReq = getRequestDeviceInfo(request);

        Map<String, String> unionTagMap = rtaRedisService.getUnionFromRedisHash(userEventReq);
        // rtaid -> tag
        Map<Long, RtaByteConfigProduct> configRtaProductMap = getConfigRtaTagMap(userEventReq.getOs());
        // tag -> rtaId
        Map<String, Set<Long>> tagRtaMap = getConfigTagRtaMapByOs(request);

        String shardingKey = buildShardingKey(userEventReq);
        // 先读取本地缓存，获取不到则重新分流
        Integer shard = RtaLocalCacheService.getSharding(shardingKey);
        if (shard == null) {
            shard = ABShardingService.shard(shardingKey, ABShardingService.TOTAL_WEIGHT, ABShardingService.WEIGHTS);
            RtaLocalCacheService.setSharding(shardingKey, shard);
            ABShardingService.count(shard);
        }
        // 未命中人群包，返回所有rtaId
        if (CollUtil.isEmpty(unionTagMap)) {
            Set<Long> tmp = new HashSet<>();
            // 22902 yes
            tmp.addAll(configBidRtaList);
            tmp.addAll(configRtaProductMap.keySet());
            tmp.remove(22903L);
            bidRtaList.addAll(tmp);
            return notBidRtaList;
        }

        // 获取命中tag的rtaId
        notBidRtaList = rtaCheckBidHandler.chooseNoBidRtaIdList(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);

        if (abTestRtaMap.containsKey(shard) && PlatformEnum.ANDROID.getPlatform().equals(userEventReq.getOs())) {
//            List<Long> abTestRtaList = abTestRtaMap.get(shard);
            notBidRtaList.clear();
        }

        // 只返回已配置账户的rtaId
        List<Long> finalNotBidRtaList = notBidRtaList;
        List<Long> tmpBidList = configRtaProductMap.keySet().stream().filter(rtaId -> !finalNotBidRtaList.contains(rtaId)).collect(Collectors.toList());
        bidRtaList.addAll(tmpBidList);
        // 22903 no
        bidRtaList.remove(22903L);
        // 22902 yes
        bidRtaList.addAll(configBidRtaList);

        return notBidRtaList;
    }

    private String buildShardingKey(UserEventReq userEventReq) {
        if (PlatformEnum.IOS.getPlatform().equals(userEventReq.getOs())) {
            return PlatformEnum.IOS.getPlatform() + userEventReq.getCaid2();
        } else {
            return PlatformEnum.ANDROID.getPlatform() + userEventReq.getOaid();

        }
    }

    private String buildNewShardingKey(UserEventReq userEventReq, String testType) {
        if (PlatformEnum.IOS.getPlatform().equals(userEventReq.getOs())) {
            return PlatformEnum.IOS.getPlatform() + userEventReq.getCaid2() + testType;
        } else {
            return PlatformEnum.ANDROID.getPlatform() + userEventReq.getOaid() + testType;

        }
    }


    /**
     * 根据os获取配置的 tag-rtaId map
     *
     * @param request
     * @return
     */
    private Map<String, Set<Long>> getConfigTagRtaMapByOs(RtaApi.Req request) {
        if (RtaApi.PlatformType.IOS.equals(request.getPlatform())) {
            return RtaByteProductRtaCache.byteTagRtaIosMap;
        } else {
            return RtaByteProductRtaCache.byteTagRtaAndroidMap;
        }
    }


    private UserEventReq getRequestDeviceInfo(RtaApi.Req request) {
        UserEventReq userEventReq = new UserEventReq();
        RtaApi.PlatformType platformType = request.getPlatform();

        if (RtaApi.PlatformType.IOS.equals(platformType)) {
            userEventReq.setOs(PlatformEnum.IOS.getPlatform());
            if (StringUtils.isNotBlank(request.getDevice().getCaid1())) {
                userEventReq.setCaid1(request.getDevice().getCaid1());
            }

            if (StringUtils.isNotBlank(request.getDevice().getCaid2())) {
                userEventReq.setCaid2(request.getDevice().getCaid2());
            }

            if (StringUtils.isNotBlank(request.getDevice().getCaid())) {
                userEventReq.setCaid(request.getDevice().getCaid());
            }
        } else {
            userEventReq.setOs(PlatformEnum.ANDROID.getPlatform());
            if (StringUtils.isNotBlank(request.getDevice().getOaid())) {
                userEventReq.setOaid(request.getDevice().getOaid());
                userEventReq.setOaid2(request.getDevice().getOaid());
            }
        }
        return userEventReq;
    }

    private RtaApi.Rsp buildRtaRspForAbTest(RtaApi.Req request, List<Long> bidRtaList) {
        UserEventReq userEventReq = getRequestDeviceInfo(request);
        RtaApi.Rsp.Builder builderRsp = RtaApi.Rsp.newBuilder();
        if (bidRtaList.isEmpty()) {
            builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            builderRsp.setCacheTimeSecs(rtaNotBidCacheTime);
        } else {
            builderRsp.setBidType(Int32Value.newBuilder().setValue(0).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            builderRsp.setCacheTimeSecs(rtaCacheTime);
            for (Long bidRtaId : bidRtaList) {
                RtaApi.UserInfo.Builder ubuilder = RtaApi.UserInfo.newBuilder();
                ubuilder.setIsInterested(true);
                ubuilder.setRtaId(bidRtaId);

                RtaApi.UserInfo uinfo = ubuilder.build();
                builderRsp.addUserInfos(uinfo);
            }
        }

        String shardingKey = buildShardingKey(userEventReq);
        // 先读取本地缓存，获取不到则重新分流
        Integer shard = RtaLocalCacheService.getSharding(shardingKey);
        if (shard == null) {
            shard = ABShardingService.shard(shardingKey, ABShardingService.TOTAL_WEIGHT, WEIGHTS);
            RtaLocalCacheService.setSharding(shardingKey, shard);
            ABShardingService.count(shard);
        }

        int randomNumber;
        int cacheTime;
        if (abTestRtaMap.containsKey(shard) && PlatformEnum.ANDROID.getPlatform().equals(userEventReq.getOs())) {
            randomNumber = weightRandomService.getRandomNumber();
            // 根据随机数计算对应范围内的随机秒数
            Random random = new Random();
            // randomNumber对应的小时区间为 [randomNumber-1, randomNumber)
            // 转换为秒数区间为 [(randomNumber-1)*3600, randomNumber*3600)
            int startSeconds = (randomNumber - 1) * 3600;
            int endSeconds = randomNumber * 3600;

            cacheTime = startSeconds + random.nextInt(endSeconds - startSeconds);
        } else {
            Random random = new Random();
            randomNumber = random.nextInt(24) + 1;
            cacheTime = randomNumber * 3600;
        }

        builderRsp.setCacheTimeSecs(cacheTime);

        builderRsp.setStatusCode(0);
        builderRsp.setReqId(request.getReqId());

        return builderRsp.build();
    }

    private RtaApi.Rsp buildRtaRspWithRandomCacheTime(RtaApi.Req request, List<Long> bidRtaList) {
        RtaApi.Rsp.Builder builderRsp = RtaApi.Rsp.newBuilder();
        if (bidRtaList.isEmpty()) {
            builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            builderRsp.setCacheTimeSecs(rtaNotBidCacheTime);
        } else {
            builderRsp.setBidType(Int32Value.newBuilder().setValue(0).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            builderRsp.setCacheTimeSecs(rtaCacheTime);
            for (Long bidRtaId : bidRtaList) {
                RtaApi.UserInfo.Builder ubuilder = RtaApi.UserInfo.newBuilder();
                ubuilder.setIsInterested(true);
                ubuilder.setRtaId(bidRtaId);

                RtaApi.UserInfo uinfo = ubuilder.build();
                builderRsp.addUserInfos(uinfo);
            }
        }

        Random random = new Random();
        int randomNumber = random.nextInt(24) + 1;
        builderRsp.setCacheTimeSecs(randomNumber * 3600);

        builderRsp.setStatusCode(0);
        builderRsp.setReqId(request.getReqId());

        return builderRsp.build();
    }

    /**
     * 封装rta rsp
     *
     * @param request
     * @param bidRtaList
     * @return
     */
    private RtaApi.Rsp buildRtaRsp(RtaApi.Req request, List<Long> bidRtaList) {

        RtaApi.Rsp.Builder builderRsp = RtaApi.Rsp.newBuilder();
        if (bidRtaList.isEmpty()) {
            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(1).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            builderRsp.setCacheTimeSecs(rtaNotBidCacheTime);
        } else {
            builderRsp = builderRsp.setBidType(Int32Value.newBuilder().setValue(0).build());//  0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
            builderRsp.setCacheTimeSecs(rtaCacheTime);
            for (Long bidRtaId : bidRtaList) {
                RtaApi.UserInfo.Builder ubuilder = RtaApi.UserInfo.newBuilder();
                ubuilder.setIsInterested(true);
                ubuilder.setRtaId(bidRtaId);

                RtaApi.UserInfo uinfo = ubuilder.build();
                builderRsp.addUserInfos(uinfo);
            }
        }

        builderRsp.setStatusCode(0);
        builderRsp.setReqId(request.getReqId());

        return builderRsp.build();
    }

    public void testTemplate(RtaApi.Req build) {
        List<Long> bidRtaList1 = new ArrayList<>();
        List<Long> bidRtaList2 = new ArrayList<>();
        List<Long> notBidRtaList = new ArrayList<>();
        // 获取配置的rta
        List<Long> configBidRtaList = getConfigBidRtaList();
        List<Long> res1 = chooseUnionBidRta(build, bidRtaList1, configBidRtaList, notBidRtaList);


        List<Long> res2 = rtaTemplateService.execute(build, bidRtaList2);

        if (!CollUtil.containsAll(res1, res2) || !CollUtil.containsAll(res2, res1)) {
            log.error("校验失败notBid {}", build.getDevice().getOaid());
        }
        if (!CollUtil.containsAll(bidRtaList1, bidRtaList2) || !CollUtil.containsAll(bidRtaList2, bidRtaList1)) {
            log.error("校验失败bid {}", build.getDevice().getOaid());
        }

    }
}
