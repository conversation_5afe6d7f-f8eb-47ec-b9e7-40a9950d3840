package com.coohua.rta.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.dto.req.kuaishou.OsType;
import com.coohua.rta.dto.req.kuaishou.Req;
import com.coohua.rta.dto.req.kuaishou.Resp;
import com.coohua.rta.dto.req.kuaishou.RtaResult;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.handler.RtaCheckBidHandler;
import com.coohua.rta.redis.RtaRedisService;
import com.coohua.rta.service.cache.RtaByteProductRtaCache;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.coohua.rta.service.RtaByteConfigProductService.parseCsv;

@Slf4j
@Service
public class RtaKuaishouService {

    @Value("${rta.ks.log.server:false}")
    private Boolean rtaKsLogServer;
    @Value("${rta.ks.log.apollo:false}")
    private Boolean rtaKsLogApollo;
    @Value("${rta.test.kuaishou.strategy:3}")
    private Integer kuaishouStrategy;
    @Resource
    private RtaRedisService rtaRedisService;
    @ApolloJsonValue("${rta.ks.bid.list:[39016]}")
    private List<Long> ksRtaBidList;
    @Resource
    private RtaCheckBidHandler rtaCheckBidHandler;
    @Value("${rta.ks.bid.cache.time:14400}")
    private Integer rtaCacheTime;
    @Value("${rta.ks.nBid.cache.time:86400}")
    private Integer rtaNotBidCacheTime;
    @Resource(name = "rtaKsTemplate")
    private AbstractRtaTemplateService abstractRtaTemplateService;

    public Resp getRtaRsp(Req request) {

        List<Long> bidRtaList = CollUtil.newArrayList();

        //List<Long> notBidRtaListOld = chooseUnionBidRta(request, bidRtaList);

        List<Long> notBidRtaList = abstractRtaTemplateService.execute(request, bidRtaList);

        boolean isAllBidFlag = CollUtil.isEmpty(notBidRtaList);

        Resp resp = buildRsp(request, bidRtaList, isAllBidFlag);

        printLogAsync(request, resp, notBidRtaList, bidRtaList);

        return resp;
    }

    private void printLogAsync(Req request, Resp resp, List<Long> notBidRtaList, List<Long> bidRtaList) {
        CompletableFuture.runAsync(() -> {
            // todo
            if (rtaKsLogServer && rtaKsLogApollo) {
//            if (rtaKsLogApollo) {
//                log.info("rtaKuaishou: requestId:{} oaid:{} oaidMd5:{} caid1:{} caid2:{}", request.getId(), request.getOaid(), request.getOaidMd5(), request.getCurrentCaid(), request.getLastCaid());
                if (CollUtil.isNotEmpty(notBidRtaList)) {
                    log.info("rtaKs notBidRtaList:{} ", notBidRtaList);
                    log.info("rtaKsReq {}, rtaKsRsp {}", request, resp);
                }
                if (CollUtil.isNotEmpty(bidRtaList)){
                    log.info("rtaKs bidRtaList:{} ", bidRtaList);
                    log.info("rtaKsReq {}, rtaKsRsp {}", request, resp);
                }
                //log.info("rtaKsReq {}, rtaKsRsp {}", request, resp);

            }
        });
    }

    private List<Long> chooseUnionBidRta(Req request, List<Long> bidRtaList) {
        List<Long> notBidRtaList = new ArrayList<>();
        UserEventReq userEventReq = getRequestDeviceInfo(request);

        Map<String, String> unionTagMap = rtaRedisService.getUnionFromRedisHash(userEventReq);
        // rtaid -> tag
        Map<Long, RtaByteConfigProduct> configRtaProductMap = getConfigRtaTagMap(userEventReq.getOs());
        // tag -> rtaId
        Map<String, Set<Long>> tagRtaMap = getConfigTagRtaMapByOs(request);

        bidRtaList.addAll(ksRtaBidList);

        // 未命中人群包，返回所有rtaId
        if (CollUtil.isEmpty(unionTagMap)) {
            Set<Long> tmp = new HashSet<>();
            tmp.addAll(configRtaProductMap.keySet());
            bidRtaList.addAll(tmp);
            return notBidRtaList;
        }
        if (rtaKsLogServer && rtaKsLogApollo) {
            log.info("rtaKsUnion map:{}", unionTagMap);
        }
        // 获取未命中人群包rtaId
        notBidRtaList = rtaCheckBidHandler.chooseNoBidRtaIdList(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);

        // 只返回已配置账户的rtaId
        List<Long> finalNotBidRtaList = notBidRtaList;
        List<Long> tmpBidList = configRtaProductMap.keySet().stream().filter(rtaId -> !finalNotBidRtaList.contains(rtaId)).collect(Collectors.toList());
        bidRtaList.addAll(tmpBidList);

        return notBidRtaList;
    }

    private Map<String, Set<Long>> getConfigTagRtaMapByOs(Req request) {
        if (OsType.ANDROID.equals(request.getOsType())) {
            return RtaByteProductRtaCache.ksTagRtaAndroidMap;
        } else {
            return RtaByteProductRtaCache.ksTagRtaIosMap;
        }
    }

    private Map<Long, RtaByteConfigProduct> getConfigRtaTagMap(String os) {
        if (PlatformEnum.IOS.getPlatform().equals(os)) {
            return RtaByteProductRtaCache.ksRtaIdTagIosMap;
        } else {
            return RtaByteProductRtaCache.ksRtaIdTagAndroidMap;
        }
    }

    private UserEventReq getRequestDeviceInfo(Req request) {
        UserEventReq userEventReq = new UserEventReq();
        OsType osType = request.getOsType();
        if (OsType.ANDROID.equals(osType)) {
            userEventReq.setOs(PlatformEnum.ANDROID.getPlatform());
//            String didMd5 = request.getDidMd5();
            String oaidMd5 = request.getOaidMd5();
            String oaid = request.getOaid();
            if (StrUtil.isNotBlank(oaid)) {
                userEventReq.setOaid(oaid);
            } else if (StrUtil.isNotBlank(oaidMd5)) {
                userEventReq.setOaidMd5(oaidMd5);
            }
//            } else if (StrUtil.isNotBlank(didMd5)) {
//                userEventReq.setOaid(DigestUtils.md5Hex(didMd5));
//            }

        } else if (OsType.IOS.equals(osType)) {
            userEventReq.setOs(PlatformEnum.IOS.getPlatform());
            if (StrUtil.isNotBlank(request.getCurrentCaid())) {
                userEventReq.setCaid1(request.getCurrentCaid());
            }

            if (StrUtil.isNotBlank(request.getLastCaid())) {
                userEventReq.setCaid2(request.getLastCaid());
            }
        }

        return userEventReq;
    }


    private Resp buildRsp(Req request, List<Long> bidRtaList, boolean isAllBidFlag) {
        Resp.Builder builder = Resp.newBuilder();
        builder.setId(request.getId());

//        builder.setCacheTime(rtaCacheTime);
        if (isAllBidFlag) {
            builder.setResultType(Resp.ResultType.ALL);
        } else if (CollUtil.isNotEmpty(bidRtaList)) {
            builder.setResultType(Resp.ResultType.PART);
            // 策略
            for (Long rtaId : bidRtaList) {
                RtaResult.Builder resultBuilder = RtaResult.newBuilder();
                resultBuilder.setStrategyId(rtaId);
                builder.addResult(resultBuilder);
            }

        } else {
            builder.setResultType(Resp.ResultType.NONE);
//            builder.setCacheTime(rtaNotBidCacheTime);
        }

        return builder.build();
    }
}
