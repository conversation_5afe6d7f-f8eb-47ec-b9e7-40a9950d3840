package com.coohua.rta.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.dto.req.kuaishou.OsType;
import com.coohua.rta.dto.req.kuaishou.Req;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.handler.RtaCheckBidHandler;
import com.coohua.rta.redis.RtaRedisService;
import com.coohua.rta.service.cache.RtaByteProductRtaCache;
import com.coohua.rta.service.cache.RtaLocalCacheService;
import com.coohua.rta.service.shard.ABShardingService;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * byte 模板实现
 */
@Service("rtaKsTemplate")
public class RtaKsTemplateServiceImpl extends AbstractRtaTemplateService<Req> {

    @Resource
    private RtaCheckBidHandler rtaCheckBidHandler;
    @Resource
    private RtaRedisService rtaRedisService;
    @ApolloJsonValue("${rta.ks.bid.rtaId.list:[]}")
    private List<Long> ksBidRtaConfigList;
    @Value("${rta.test.kuaishou.strategy:1}")
    private Integer testStrategy;


    @Override
    protected void setRtaReq() {
        if (super.rtaRedisService == null) {
            super.rtaRedisService = rtaRedisService;
        }
        if (super.rtaCheckBidHandler == null) {
            super.rtaCheckBidHandler = rtaCheckBidHandler;
        }
    }

    @Override
    protected UserEventReq getRequestDeviceInfo(Req request) {
        UserEventReq userEventReq = new UserEventReq();
        OsType osType = request.getOsType();
        if (OsType.ANDROID.equals(osType)) {
            userEventReq.setOs(PlatformEnum.ANDROID.getPlatform());
            String oaidMd5 = request.getOaidMd5();
            String oaid = request.getOaid();
            if (StrUtil.isNotBlank(oaid)) {
                userEventReq.setOaid(oaid);
            } else if (StrUtil.isNotBlank(oaidMd5)) {
                userEventReq.setOaidMd5(oaidMd5);
            }

        } else if (OsType.IOS.equals(osType)) {
            userEventReq.setOs(PlatformEnum.IOS.getPlatform());
            if (StrUtil.isNotBlank(request.getCurrentCaid())) {
                userEventReq.setCaid1(request.getCurrentCaid());
            }

            if (StrUtil.isNotBlank(request.getLastCaid())) {
                userEventReq.setCaid2(request.getLastCaid());
            }
        }

        return userEventReq;
    }

    @Override
    protected Map<Long, RtaByteConfigProduct> getConfigRtaTagMap(String os) {
        if (PlatformEnum.IOS.getPlatform().equals(os)) {
            return RtaByteProductRtaCache.ksRtaIdTagIosMap;
        } else {
            return RtaByteProductRtaCache.ksRtaIdTagAndroidMap;
        }
    }

    @Override
    protected List<Long> getConfigBidRtaList() {
        return ksBidRtaConfigList;
    }

    @Override
    protected Map<String, Set<Long>> getTagRtaMap(String os) {
        if (PlatformEnum.IOS.getPlatform().equals(os)) {
            return RtaByteProductRtaCache.ksTagRtaIosMap;
        } else {
            return RtaByteProductRtaCache.ksTagRtaAndroidMap;
        }
    }

    @Override
    protected List<Long> getNotBidRtaIdList(List<Long> bidRtaList, Map<String, String> unionTagMap, Map<String, Set<Long>> tagRtaMap, Map<Long, RtaByteConfigProduct> configRtaProductMap, UserEventReq userEventReq) {
        List<Long> notBidRtaList = new ArrayList<>();


        String shardingKey = buildShardingKey(userEventReq);
        // 先读取本地缓存，获取不到则重新分流
        Integer shard = RtaLocalCacheService.getSharding(shardingKey);
        if (shard == null) {
            shard = ABShardingService.shard(shardingKey, ABShardingService.TOTAL_WEIGHT, ABShardingService.WEIGHTS);
            RtaLocalCacheService.setSharding(shardingKey, shard);
            ABShardingService.count(shard);
        }

        // 未命中人群包，返回所有rtaId
        if (CollUtil.isEmpty(unionTagMap)) {
            Set<Long> tmp = new HashSet<>();
            tmp.addAll(getConfigBidRtaList());
            tmp.addAll(configRtaProductMap.keySet());
            bidRtaList.addAll(tmp);
            return notBidRtaList;
        }

        // 获取命中tag的rtaId
        notBidRtaList = getHitRtaIdList(unionTagMap, tagRtaMap, configRtaProductMap, userEventReq);

        if (shard == 0  && testStrategy == 2 && PlatformEnum.ANDROID.getPlatform().equals(userEventReq.getOs())) {
            notBidRtaList.clear();
        }

        // 只返回已配置账户的rtaId
        List<Long> finalNotBidRtaList = notBidRtaList;
        List<Long> tmpBidList = configRtaProductMap.keySet().stream().filter(rtaId -> !finalNotBidRtaList.contains(rtaId)).collect(Collectors.toList());
        bidRtaList.addAll(tmpBidList);
        bidRtaList.addAll(getConfigBidRtaList());
        return notBidRtaList;
    }

    private String buildShardingKey(UserEventReq userEventReq) {
        if (PlatformEnum.IOS.getPlatform().equals(userEventReq.getOs())) {
            return PlatformEnum.IOS.getPlatform() + userEventReq.getCaid2();
        } else {
            return PlatformEnum.ANDROID.getPlatform() + userEventReq.getOaid();

        }
    }
}
