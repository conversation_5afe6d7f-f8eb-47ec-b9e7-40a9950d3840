package com.coohua.rta.service.strategy.price;

import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.dto.resp.PChildsClssT;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.enums.DspType;
import com.coohua.rta.redis.RtaDeviceRedisService;
import com.coohua.rta.redis.RtaSetSigleService;
import com.coohua.rta.service.RtaCounterPrint;
import com.coohua.rta.service.RtaRstSaveService;
import com.coohua.rta.service.cache.RtaCacheSch;
import com.coohua.rta.service.strategy.entity.ChujiaEnt;
import com.coohua.rta.utils.DoubleUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class StrRtaDirPriceService {
    @Autowired
    RtaSetSigleService rtaSetSigleService;
    @Autowired
    RtaCacheSch rtaCacheSch;
    @Autowired
    RtaSwitcher rtaSwitcher;
    @Autowired
    RtaDeviceRedisService rtaDeviceRedisService;
    @Autowired
    RtaRstSaveService rtaRstSaveService;
    @Autowired
    RtaCounterPrint rtaCounterPrint;

    public PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> getChujiaPrice(UserEventReq request, String reqId, DspType dspType) {

        Pair<ChujiaEnt, List<RtaUppriceSub>> chujiaEntListPair = rtaSetSigleService.getChujiaEnt(request, reqId);

        PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> pChildsClssT = new PChildsClssT<>();
        RtaProductArpu rtaProductArpu = rtaCacheSch.getProductArpuCfg(request.getProduct(),request.getOs(), dspType);

        Double chujia = 0d;
        String remark = "";
        RtaUpprice rtaUpprice = new RtaUpprice();

        rtaUpprice.setProduct(request.getProduct());
        rtaUpprice.setOs(request.getOs());

        if (chujiaEntListPair != null && chujiaEntListPair.getValue() != null && chujiaEntListPair.getValue().size() > 0) {
            ChujiaEnt chujiaEnt = chujiaEntListPair.getKey();

            rtaUpprice.setHcpa(chujiaEnt.getHcpa());
            rtaUpprice.setHarpu(chujiaEnt.getHarpu());
            rtaUpprice.setHpv(chujiaEnt.getHpv());
            rtaUpprice.setHwithdraw(chujiaEnt.getHwithDraw());
            rtaUpprice.setGuiyType(chujiaEnt.getGuiyingType().value);

            pChildsClssT.childList = chujiaEntListPair.getValue();


            pChildsClssT.configProductArpu = rtaProductArpu;
            if (rtaProductArpu != null) {
                Double priceLt = rtaProductArpu.getPriceLt();//投放看板 arpu值
                Double priceCvr = rtaProductArpu.getPriceCvr();
                Double priceUpfloat = rtaProductArpu.getPriceUpfloat();

                if (priceUpfloat == null) {
                    priceUpfloat = 1d;
                } else if (priceUpfloat > 5) {
                    priceUpfloat = 5d;
                    log.error(rtaProductArpu.getProduct() + " 配置错误 出价上浮系数太高" + rtaProductArpu.getPriceUpfloat());
                }

                if (priceLt > 5) {
                    log.error(rtaProductArpu.getProduct() + " 配置错误 出价getPriceLt太高" + rtaProductArpu.getPriceLt());
                }
                if (priceCvr > 0.6) {
                    log.error(rtaProductArpu.getProduct() + " 配置错误 出价getPriceCvr太高" + rtaProductArpu.getPriceCvr());
                }

                chujia = getPrice(chujiaEnt.getHarpu(),priceCvr,priceLt,rtaProductArpu.getPriceOtherRate(),priceUpfloat);

//                if(chujia<rtaProductArpu.getChujia()){
//                    remark = "出账户价"+chujia+"@";
//                    chujia = rtaProductArpu.getChujia();//不起量 直接出账户价格
//                }

                if(rtaProductArpu.getLowerPriceFlg()!=null && rtaProductArpu.getLowerPriceFlg()==2 && chujia<rtaProductArpu.getChujia()){
                    remark = "出账户价"+chujia+"@";
                    chujia = rtaProductArpu.getChujia();//不起量 直接出账户价格
                }

                Double chujiaBud = DoubleUtil.getBoundDouble(chujia,rtaProductArpu.getChujia(),rtaProductArpu.getPriceMax(),rtaSwitcher.boundrate);

                if(rtaCounterPrint.isPrt("priceCt")){
                    log.info("汇算前 "+chujia+" 汇算后 "+chujiaBud +" ("+rtaProductArpu.getChujia()+","+rtaProductArpu.getPriceMax()+")");
                }

                chujia = chujiaBud;

                if (chujia > rtaProductArpu.getPriceMax()) {
                    remark = "出最高价"+chujia+"@";
                    chujia = rtaProductArpu.getPriceMax();
                }

                remark = remark +" ("+chujiaEnt.getHarpu() + "*" + priceLt + "*" + priceCvr + "+(" + chujiaEnt.getHarpu() + "/" + 2 + ")*" + priceLt + "*(1-" + priceCvr + ")) *"+priceUpfloat;
                if(rtaCounterPrint.isPrt("rta出价最终")){
                    log.info("rta出价最终  " + rtaUpprice.getProduct() + " " + chujia + " harpu:" + chujiaEnt.getHarpu() + " hpv:" + chujiaEnt.getHpv());
                }
            }
        }else{
            chujia = rtaProductArpu.getChujia();//若无匹配直接出价 待优化  每次出价倍数递增
        }

        rtaUpprice.setRtaId(reqId);
        rtaUpprice.setCaid(request.getCaid());
//                    rtaUpprice.setIdfa("");
        rtaUpprice.setDspType(DspType.TOUTIAO.value);
        rtaUpprice.setOaid(request.getOaid());
        rtaUpprice.setImei(request.getOcpcDeviceId());
        rtaUpprice.setCreateTime(new Date());
        rtaUpprice.setUpdateTime(new Date());

        rtaUpprice.setUppriceFloat(DoubleUtil.getDoubleByTwo(chujia / rtaProductArpu.getChujia()));
        rtaUpprice.setChujiaPrice(chujia);
        rtaUpprice.setRemark(remark);
        pChildsClssT.t = rtaUpprice;

        if (rtaUpprice.getUppriceFloat() >1 && rtaSwitcher.saveDbFlg) {
            rtaDeviceRedisService.saveUpPrice(rtaUpprice);
            rtaRstSaveService.savePriceFl(rtaUpprice, chujiaEntListPair.getValue());
        }
        return pChildsClssT;
    }

    /**
     * 根据arpu算出价
     * @param arpu
     * @param cvr
     * @param lt
     * @param otherRate
     * @param priceFlt
     * @return
     */
    public static double getPrice(Double arpu,Double cvr,Double lt,Double otherRate,Double priceFlt){
        Double chujia = (arpu * lt * cvr + (arpu * otherRate) * lt * (1 - cvr))/cvr;
        chujia = DoubleUtil.multiplicationDouble(chujia, priceFlt);
        return chujia;
    }

    /**
     * 根据出价算可回收的arpu
     * @param chujia
     * @param cvr
     * @param lt
     * @param otherRate
     * @param priceFlt
     * @return
     */
    public static double getEventArpu(Double chujia,Double cvr,Double lt,Double otherRate,Double priceFlt){
        Double arpu = (chujia*cvr)/((lt*cvr+otherRate*lt*(1-cvr))/priceFlt);
        return DoubleUtil.getDoubleByTwo(arpu);
    }

    public static void main(String[] args){
//        System.out.println(1d+"->"+getPrice(4d,0.25d,2.5d,0.5d,1d));
        for(int i=100;i>10;i--){
            System.out.println(i+"  "+getEventArpu(i*1d,0.25d,2.5d,0.5d,1d));
        }
    }


}
