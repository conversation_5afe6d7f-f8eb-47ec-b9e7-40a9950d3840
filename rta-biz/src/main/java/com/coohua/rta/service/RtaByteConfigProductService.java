package com.coohua.rta.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.mapper.RtaByteConfigProductMapper;
import com.coohua.rta.mapper.RtaConfigProductMapper;
import com.coohua.rta.vo.RtaByteConfigProductVo;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvValidationException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class RtaByteConfigProductService extends ServiceImpl<RtaByteConfigProductMapper, RtaByteConfigProduct> {

    @Autowired
    @Qualifier("rta-redis-newJedisClusterClient")
    private JedisClusterClient rtaRedis;

    @Resource
    private RtaConfigProductMapper rtaConfigProductMapper;

    /**
     * 批量保存rtaId和product
     *
     * @param rtaByteConfigProductVo
     */
    @Transactional
    public void saveRtaIdAndProductBatch(RtaByteConfigProductVo rtaByteConfigProductVo) {
        for (RtaByteConfigProduct rtaByteConfigProduct : rtaByteConfigProductVo.getRtaByteConfigProductList()) {
            if ("z".equals(rtaByteConfigProduct.getTagType())) {
                rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct());
            } else if ("c".equals(rtaByteConfigProduct.getTagType())) {
                rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct() + "," + rtaByteConfigProduct.getProduct() + "#");
            } else if ("cl".equals(rtaByteConfigProduct.getTagType())) {
                rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct() + "," + rtaByteConfigProduct.getProduct() + "#" + ",LTV");
            } else if ("zl".equals(rtaByteConfigProduct.getTagType())) {
                rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct() + "," + "LTV");
            }
            rtaByteConfigProduct.setCreateTime(LocalDateTime.now());
            rtaByteConfigProduct.setUpdateTime(LocalDateTime.now());
        }
        System.out.println(111);
//        saveBatch(rtaByteConfigProductVo.getRtaByteConfigProductList());
        rtaConfigProductMapper.insertBatch(rtaByteConfigProductVo.getTable(), rtaByteConfigProductVo.getRtaByteConfigProductList());
//        Map<String, String> rtaProductMap = new HashMap<>();
//        for (RtaByteConfigProduct rtaByteConfigProduct : rtaByteConfigProductVo.getRtaByteConfigProductList()) {
//            rtaProductMap.put(rtaByteConfigProduct.getRtaId().toString(), JSON.toJSONString(rtaByteConfigProduct));
//        }
//        if (CollUtil.isNotEmpty(rtaProductMap)) {
//            rtaRedis.hmset(RedisKeyConstants.getRtaTagKey(), rtaProductMap);
//            rtaRedis.expire(RedisKeyConstants.getRtaTagKey(), 3600);
//        }

    }

    public void convertAndSaveRtaIdAndProductBatch(RtaByteConfigProductVo rtaByteConfigProductVo) {

        //头条
        List<RtaByteConfigProduct> rtaByteConfigProducts = parseCsv(rtaByteConfigProductVo.getFilePath());
        rtaConfigProductMapper.insertBatch("rta_config_product_byte", rtaByteConfigProducts);

        //快手
//        List<RtaConfigProductKs> rtaConfigProductKsList = new ArrayList<>();
//        rtaByteConfigProducts.forEach(rtaByteConfigProduct -> {
//            RtaConfigProductKs rtaConfigProductKs = new RtaConfigProductKs();
//            BeanUtils.copyProperties(rtaByteConfigProduct, rtaConfigProductKs);
//            rtaConfigProductKs.setRtaId();
//
//        });
//        rtaConfigProductMapper.insertBatch("rta_config_product_ks", rtaConfigProductKsList);

        System.out.println("rta配置写入完毕");

    }

    public static List<RtaByteConfigProduct> parseCsv(String filePath) {
        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            return parseCsvInternal(reader);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (CsvValidationException e) {
            throw new RuntimeException(e);
        }
    }

    public static List<RtaByteConfigProduct> parseCsv(InputStream inputStream) {
        try (CSVReader reader = new CSVReader(new InputStreamReader(inputStream))) {
            return parseCsvInternal(reader);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (CsvValidationException e) {
            throw new RuntimeException(e);
        }
    }

    private static List<RtaByteConfigProduct> parseCsvInternal(CSVReader reader) throws IOException, CsvValidationException {
        List<RtaByteConfigProduct> adDataList = new ArrayList<>();
        String[] nextLine;
        reader.readNext(); // 跳过表头
        while ((nextLine = reader.readNext()) != null) {
            long rtaId = Long.parseLong(nextLine[0]);
            String productInfo = nextLine[1];
            String product = nextLine[2];

            // 解析产品名称、操作系统和策略
            String productName = productInfo.replaceAll("[a-zA-Z]+", ""); // 去掉字母
            String os = productInfo.contains("ios") ? "ios" : "android";
            String tagType = String.valueOf(productInfo.charAt(productInfo.length() - 1)); // 提取字母

            // 转换为目标格式
            RtaByteConfigProduct adData = new RtaByteConfigProduct();
            adData.setProduct(product);
            adData.setRtaId(rtaId);
            adData.setOs(os);
            adData.setTagType(tagType);
            adData.setTagStrategy("includesTagsBidStrategy");

            adDataList.add(adData);
            convertTag(adData);
        }
        return adDataList;
    }

    private static void convertTag(RtaByteConfigProduct rtaByteConfigProduct) {
        if ("z".equals(rtaByteConfigProduct.getTagType())) {
            rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct());
        } else if ("c".equals(rtaByteConfigProduct.getTagType())) {
            rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct() + "," + rtaByteConfigProduct.getProduct() + "#");
        } else if ("cl".equals(rtaByteConfigProduct.getTagType())) {
            rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct() + "," + rtaByteConfigProduct.getProduct() + "#" + ",LTV");
        } else if ("zl".equals(rtaByteConfigProduct.getTagType())) {
            rtaByteConfigProduct.setTag(rtaByteConfigProduct.getProduct() + "," + "LTV");
        }
        rtaByteConfigProduct.setCreateTime(LocalDateTime.now());
        rtaByteConfigProduct.setUpdateTime(LocalDateTime.now());
    }
}