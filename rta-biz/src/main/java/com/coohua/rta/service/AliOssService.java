package com.coohua.rta.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import com.aliyun.oss.model.ObjectListing;
import com.coohua.rta.utils.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class AliOssService {
    public static OSS ossClient;
    public static String endpoint = "https://oss-cn-beijing-internal.aliyuncs.com";
    public static String accessKeyId = "LTAI5t5kEg8uUXpMZxiQrxfU";
    public static String accessKeySecret = "******************************";
    // 填写Bucket名称，例如examplebucket。
    public static String bucketName = "shinet-gpt";

    public static String getFolder(){
        String osName = System.getProperty("os.name");
        String lowerCaseOS = osName.toLowerCase().replaceAll("\\s", "");
        String fname = "d:\\tmp";
        if (lowerCaseOS.contains("win")) {
        } else  {
            fname = "/data/rta/tmp/";
        }

        File file = new File(fname);
        if(!file.exists()){
            file.mkdirs();
        }
        return fname;
    }

    public static void downloadFile(String dateStr){
        try {
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

            ObjectListing objectListing = ossClient.listObjects(bucketName, dateStr);
            List<OSSObjectSummary> sums = objectListing.getObjectSummaries();
            for (OSSObjectSummary s : sums) {
                if(s.getSize()>1000){
                    String fileName = s.getKey();
                    GetObjectRequest request = new GetObjectRequest(bucketName, fileName);
                    // 下载OSS文件到本地文件。
                    File file = new File(getFolder()+File.separator+fileName);
                    file = new File(getFolder()+File.separator+fileName);
                    long ctime = System.currentTimeMillis();
                    XxlJobLogger.log("开始下载文件 "+file.getAbsolutePath()+" "+s.getSize());
                    File newFile = new File(getFolder()+File.separator+fileName);
                    if(!newFile.getParentFile().exists()){
                        newFile.getParentFile().mkdirs();
                    }
                    ossClient.getObject(request, new File(getFolder()+File.separator+fileName));
                    XxlJobLogger.log("文件完成 "+file.getAbsolutePath()+" "+s.getSize()+" 耗时 "+(System.currentTimeMillis()-ctime));
                }
            }
        } catch (Exception oe) {
            log.error("",oe);
        }finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


    public static void main(String[] args) throws Exception {
        String dateStr = DateUtils.formatDateForYMDSTR(new Date());
        downloadFile(dateStr);
    }
}