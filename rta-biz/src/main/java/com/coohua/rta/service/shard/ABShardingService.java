package com.coohua.rta.service.shard;

import com.coohua.rta.utils.ABShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;

@Service
@Slf4j
public class ABShardingService {

    private static final Map<Integer, LongAdder> groupCounters = new ConcurrentHashMap<>();

    public static final int[] WEIGHTS = {10, 10, 10, 10, 10, 10, 10, 10, 10, 10};
    public static final Integer TOTAL_WEIGHT = 100;

    static {
        for (int i = 0; i < WEIGHTS.length; i++) {
            groupCounters.put(i, new LongAdder());
        }
    }

    public static int shard(String deviceId, Integer totalWeight, int[] weights) {

        return ABShardingUtil.shard(deviceId, totalWeight, weights);
    }

    /**
     * 计数
     */
    public static void count(Integer shard) {

        groupCounters.get(shard).increment();
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void statistics() {
        Map<Integer, Long> statistics = new HashMap<>();
        for (Map.Entry<Integer, LongAdder> entry : groupCounters.entrySet()) {
            long sum = entry.getValue().sum();
            statistics.put(entry.getKey(), sum);

        }

        for (Map.Entry<Integer, Long> entry : statistics.entrySet()) {
            log.info("Group {} : {}  users", entry.getKey(), entry.getValue());
        }

        for (Map.Entry<Integer, LongAdder> entry : groupCounters.entrySet()) {
            entry.getValue().reset();
        }

        log.info("rta shard statistics");

    }

    /**
     * 每天凌晨 清除统计数据
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void clearStatistics() {
        log.info("Starting to clear rta shard statistics");
        try {
            groupCounters.forEach((key, longAdder) -> longAdder.reset());
            log.info("Successfully cleared rta shard statistics");
        } catch (Exception e) {
            log.error("Error clearing rta shard statistics", e);
        }
    }
}
