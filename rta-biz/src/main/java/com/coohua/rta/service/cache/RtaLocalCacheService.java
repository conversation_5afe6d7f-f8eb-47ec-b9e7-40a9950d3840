package com.coohua.rta.service.cache;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheStats;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RtaLocalCacheService {
    private static Cache<String, Integer> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(24L, TimeUnit.HOURS)
            .maximumSize(50000L)
            .build();

    public static Integer getSharding(String cacheKey) {
        return cache.getIfPresent(cacheKey);
    }

    public static void setSharding(String cacheKey, Integer shard) {
        cache.put(cacheKey, shard);
    }

//    @Scheduled(cron = "0 0/5 * * * ?")
    public void statistics() {
        CacheStats stats = cache.stats();
        log.info("cache statistics: {}", stats);
    }
}
