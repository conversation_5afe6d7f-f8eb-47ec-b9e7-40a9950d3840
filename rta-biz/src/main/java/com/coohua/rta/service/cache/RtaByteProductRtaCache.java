package com.coohua.rta.service.cache;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.coohua.rta.entity.RtaByteConfigProduct;
import com.coohua.rta.mapper.RtaConfigProductMapper;
import com.coohua.rta.redis.RtaRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class RtaByteProductRtaCache {

    @Resource
    private RtaRedisService rtaRedisService;
    @Autowired
    @Qualifier("rta-redis-newJedisClusterClient")
    private JedisClusterClient rtaRedis;
    @Resource
    private RtaConfigProductMapper rtaConfigProductMapper;

    // byte rtaId-tag 正反向map
//    public static Map<Long, RtaByteConfigProduct> byteRtaIdTagMap = new HashMap<>();
    public static Map<Long, RtaByteConfigProduct> byteRtaIdTagAndroidMap = new HashMap<>();
    public static Map<Long, RtaByteConfigProduct> byteRtaIdTagIosMap = new HashMap<>();
    public static Map<String, Set<Long>> byteTagRtaAndroidMap = new HashMap<>();
    public static Map<String, Set<Long>> byteTagRtaIosMap = new HashMap<>();
    // 百度 rtaId-tag 正反向map
//    public static Map<Long, RtaByteConfigProduct> baiduRtaIdTagMap = new HashMap<>();
    public static Map<Long, RtaByteConfigProduct> baiduRtaIdTagAndroidMap = new HashMap<>();
    public static Map<Long, RtaByteConfigProduct> baiduRtaIdTagIosMap = new HashMap<>();
    public static Map<String, Set<Long>> baiduTagRtaAndroidMap = new HashMap<>();
    public static Map<String, Set<Long>> baiduTagRtaIosMap = new HashMap<>();

    // ks rtaId-tag 正反向map
//    public static Map<Long, RtaByteConfigProduct> ksRtaIdTagMap = new HashMap<>();
    public static Map<Long, RtaByteConfigProduct> ksRtaIdTagAndroidMap = new HashMap<>();
    public static Map<Long, RtaByteConfigProduct> ksRtaIdTagIosMap = new HashMap<>();
    public static Map<String, Set<Long>> ksTagRtaAndroidMap = new HashMap<>();
    public static Map<String, Set<Long>> ksTagRtaIosMap = new HashMap<>();

    private static final String ANDROID = "android";
    private static final String IOS = "ios";

    public static List<String> rtaCompanyList = Arrays.asList("byte", "baidu", "ks");
    public static String TABLE_PREFIX = "rta_config_product_%s";


    @Scheduled(cron = "0 1/10 * * * ?")
    @PostConstruct
    public void initRtaProductMap() {
        try {
            log.info("initRtaProductMap start");
            for (String thirdPlatform : rtaCompanyList) {
                log.info("start init rta thirdPlatform:{}", thirdPlatform);
                String table = String.format(TABLE_PREFIX, thirdPlatform);
                refreshLocalCacheMap(thirdPlatform, table);
                log.info("finish init rta thirdPlatform:{} success", thirdPlatform);
            }
            log.info("initRtaProductMap success");
        } catch (Exception e) {
            log.error("Failed to initialize RTA product map.", e);
        }
    }

    private void refreshLocalCacheMap(String thirdPlatform, String table) {

        // 查库配置
        List<RtaByteConfigProduct> list = rtaConfigProductMapper.selectAll(table);

        if (CollUtil.isEmpty(list)) {
            return;
        }

        Map<Long, RtaByteConfigProduct> rtaIdTagAndroidTmpMap = new HashMap<>();
        Map<Long, RtaByteConfigProduct> rtaIdTagIosTmpMap = new HashMap<>();
        Map<String, Set<Long>> tagRtaAndroidTmpMap = new HashMap<>();
        Map<String, Set<Long>> tagRtaIosTmpMap = new HashMap<>();

        for (RtaByteConfigProduct rtaByteConfigProduct : list) {

            if (ANDROID.equals(rtaByteConfigProduct.getOs())) {
                rtaIdTagAndroidTmpMap.put(rtaByteConfigProduct.getRtaId(), rtaByteConfigProduct);
                setRtaIdTagMap(rtaByteConfigProduct, tagRtaAndroidTmpMap);
            } else {
                rtaIdTagIosTmpMap.put(rtaByteConfigProduct.getRtaId(), rtaByteConfigProduct);
                setRtaIdTagMap(rtaByteConfigProduct, tagRtaIosTmpMap);
            }
        }

        switch (thirdPlatform) {
            case "byte":
                byteRtaIdTagAndroidMap = rtaIdTagAndroidTmpMap;
                byteRtaIdTagIosMap = rtaIdTagIosTmpMap;
                byteTagRtaAndroidMap = tagRtaAndroidTmpMap;
                byteTagRtaIosMap = tagRtaIosTmpMap;
                break;
            case "baidu":
                baiduRtaIdTagAndroidMap = rtaIdTagAndroidTmpMap;
                baiduRtaIdTagIosMap = rtaIdTagIosTmpMap;
                baiduTagRtaAndroidMap = tagRtaAndroidTmpMap;
                baiduTagRtaIosMap = tagRtaIosTmpMap;
                break;
            case "ks":
                ksRtaIdTagAndroidMap = rtaIdTagAndroidTmpMap;
                ksRtaIdTagIosMap = rtaIdTagIosTmpMap;
                ksTagRtaAndroidMap = tagRtaAndroidTmpMap;
                ksTagRtaIosMap = tagRtaIosTmpMap;
                break;
        }

    }

    /**
     * 刷新各rta配置
     *
     * @param rtaTagKey
     * @param format
     * @param byteRtaIdTagMap
     * @param byteTagRtaAndroidMap
     * @param byteTagRtaIosMap
     */
    private void refreshLocalCacheMap(String rtaTagKey, String table, Map<Long, RtaByteConfigProduct> rtaIdTagMap, Map<String, Set<Long>> tagRtaAndroidMap, Map<String, Set<Long>> tagRtaIosMap) {

        Map<String, String> tagMap = rtaRedisService.getMapFromRedisBatch(rtaTagKey);

        if (CollUtil.isEmpty(tagMap)) {
            // 查库配置
            List<RtaByteConfigProduct> list = rtaConfigProductMapper.selectAll(table);

            if (CollUtil.isEmpty(list)) {
                return;
            }

            for (RtaByteConfigProduct rtaByteConfigProduct : list) {
                rtaIdTagMap.put(rtaByteConfigProduct.getRtaId(), rtaByteConfigProduct);
                if (ANDROID.equals(rtaByteConfigProduct.getOs())) {
                    setRtaIdTagMap(rtaByteConfigProduct, tagRtaAndroidMap);
                } else {
                    setRtaIdTagMap(rtaByteConfigProduct, tagRtaIosMap);
                }
            }

            rtaRedis.hmset(rtaTagKey, convertToStringMap(rtaIdTagMap));
            rtaRedis.expire(rtaTagKey, 900);

        } else {
            Map<Long, RtaByteConfigProduct> longRtaByteConfigProductMap = convertToLongMap(tagMap);
            rtaIdTagMap.putAll(longRtaByteConfigProductMap);
            rtaIdTagMap.forEach((rtaId, rtaByteConfigProduct) -> {
                if (ANDROID.equals(rtaByteConfigProduct.getOs())) {
                    setRtaIdTagMap(rtaByteConfigProduct, tagRtaAndroidMap);
                } else {
                    setRtaIdTagMap(rtaByteConfigProduct, tagRtaIosMap);
                }
            });
        }
    }

    private void setRtaIdTagMap(RtaByteConfigProduct rtaByteConfigProduct, Map<String, Set<Long>> map) {
        String[] tags = rtaByteConfigProduct.getTag().split(",");
        for (String tag : tags) {
            if (map.containsKey(tag)) {
                map.get(tag).add(rtaByteConfigProduct.getRtaId());
            } else {
                map.put(tag, new HashSet<>());
                map.get(tag).add(rtaByteConfigProduct.getRtaId());
            }
        }


    }

    private Map<Long, RtaByteConfigProduct> convertToLongMap(Map<String, String> tagMap) {
        Map<Long, RtaByteConfigProduct> rtaProductMap = new HashMap<>();
        tagMap.forEach((k, v) -> {
            rtaProductMap.put(Long.valueOf(k), JSON.parseObject(v, RtaByteConfigProduct.class));
        });
        return rtaProductMap;
    }

    private Map<String, String> convertToStringMap(Map<Long, RtaByteConfigProduct> map) {
        Map<String, String> rtaProductMap = new HashMap<>();
        map.forEach((k, v) -> {
            rtaProductMap.put(k.toString(), JSON.toJSONString(v));
        });
        return rtaProductMap;
    }
}
