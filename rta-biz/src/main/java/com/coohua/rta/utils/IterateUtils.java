package com.coohua.rta.utils;

import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2021/6/7
 */
public class IterateUtils {

    /**
     * 根据步长来迭代集合
     *
     * @param data           数据集
     * @param resultConsumer 数据集处理方式
     * @param stepSize       步长
     * @param <T>            数据实体定义
     */
    public static <T> void iterateByStepSize(List<T> data, int stepSize, Consumer<List<T>> resultConsumer) {

        if (CollectionUtils.isEmpty(data)) {
            return;
        }

        int dataSize = data.size();

        int stepAmount = (data.size() - 1) / stepSize + 1;

        for (int step = 0; step < stepAmount; step++) {

            int startIndex = step * stepSize;

            int endIndex = Integer.min(dataSize, startIndex + stepSize);

            resultConsumer.accept(data.subList(startIndex, endIndex));

        }
    }

    /**
     * 使用数据库ID和limit(一种方式，不强制使用)进行mysql表的迭代
     *
     * @param startId        起始ID
     * @param resultMapper   查询数据集方式
     * @param idMapper       id提取方法
     * @param resultConsumer 结果处理方法
     * @param <T>            数据库实体类型
     * @param <ID>           主键类型
     */
    public static <T, ID> void iterateMySqlByIdAndLimit(ID startId, Function<ID, List<T>> resultMapper,
                                                        Function<T, ID> idMapper, Consumer<List<T>> resultConsumer) {

        while (true) {

            List<T> result = resultMapper.apply(startId);

            if (CollectionUtils.isEmpty(result)) {
                return;
            }

            resultConsumer.accept(result);

            startId = idMapper.apply(result.get(result.size() - 1));

        }
    }
}
