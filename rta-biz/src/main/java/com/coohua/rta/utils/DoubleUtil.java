package com.coohua.rta.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;

public class DoubleUtil {


	public static double getBoundDouble(double fx, double start, double end,double yzi) {
		double xmin = start;
		double xmax = xmin * yzi;
		// 确保 x 在 [xmin, xmax] 范围内
		if (fx < xmin) {
			return fx;
		} else if (fx > xmax) {
			fx = xmax;
		}

		// 计算映射后的值
		double ratio = (fx - xmin) / (xmax - xmin);
		return DoubleUtil.getDoubleByTwo(start + ratio * (end - start));
	}


	/**
	 * double 加法
	 * 
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double addDouble(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a + b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}
	
	public static Double addDoubleOne(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a + b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Double addDoubleZero(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a + b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(0, BigDecimal.ROUND_FLOOR).doubleValue();
		return f1;
	}

	public static Double getNoFloat(Double a) {
		if (a == null) {
			a = 0.0d;
		}

		Double c = a;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Double getIntegerNoF(Integer a) {
		Double c = 0d;
		if (a != null) {
			c = a*1.0d;
		}
		return c;
	}

	public static Double getNoFloatUp(Double a) {
		if (a == null) {
			a = 0.0d;
		}

		Double c = a;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(0, BigDecimal.ROUND_UP).doubleValue();
		return f1;
	}

	public static Double getDoubleByTwo(Double a) {
		BigDecimal bc = new BigDecimal(a);
		Double f1 = bc.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}
	
	public static Double getDoubleByOne(Double a) {
		BigDecimal bc = new BigDecimal(a);
		Double f1 = bc.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Double getDoubleByZero(Double a) {
		BigDecimal bc = new BigDecimal(a);
		Double f1 = bc.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Double addDouble(Double a, Double b, Double c) {
		Double f1 = addDouble(addDouble(a, b), c);
		return f1;
	}

	public static Double addDouble(Double a, Double... bs) {
		Double ax = a;
		for (Double b : bs) {
			ax = addDouble(ax, b);
		}
		return ax;
	}

	/**
	 * double 减法
	 * 
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double subtractionDouble(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a - b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}
	
	public static Double subtractionDoubleOne(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a - b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Double subtractionDouble(Double a, Double... bs) {
		Double ax = a;
		for (Double b : bs) {
			ax = subtractionDouble(ax, b);
		}
		return ax;
	}
	
	
	public static Double multiplicationXDouble(Double a, Double... bs) {
		Double ax = a;
		for (Double b : bs) {
			ax = multiplicationDouble(ax, b);
		}
		return ax;
	}

	/**
	 * double 乘法
	 * 
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double multiplicationDouble(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a * b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}
	
	public static Double multiplication4Double(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a * b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}
	
	public static Double multiplication1Double(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a * b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Double multiplication0Double(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a * b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Double multiplicationDownDouble(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a * b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(0, BigDecimal.ROUND_DOWN).doubleValue();
		return f1;
	}
	/**
	 * double 除法
	 * 
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double divideDouble(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a / b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	public static Integer divideDouble(Integer a, Integer b) {
		if (a == null) {
			a = 0;
		}

		if (b == null) {
			b = 0;
		}


		Double c = a*1d / b*1d;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1.intValue();
	}

	/**
	 * 四舍五入取整
	 * @param a
	 * @param b
	 * @return
	 */
	public static Double divideDouble0(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 1d;
		}
		Double c = a / b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}

	
	public static Double divideDouble4(Double a, Double b) {
		if (a == null) {
			a = 0.0d;
		}

		if (b == null) {
			b = 0.0d;
		}
		Double c = a / b;
		BigDecimal bc = new BigDecimal(c);
		Double f1 = bc.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
		return f1;
	}
	
	/**
	 * 计算百分比
	 * 
	 * @param a
	 *            分子
	 * @param b
	 *            分母，为0有异常
	 * @param decimal
	 *            设置精确位数
	 * @return
	 */
	public static String percent(Double a, Double b, Integer decimal) {

		if (a == null)
			a = 0.0;
		if (b == null)
			b = 1.0;
		NumberFormat numberFormat = NumberFormat.getInstance();
		numberFormat.setMaximumFractionDigits(decimal);
		return numberFormat.format(a / b * 100) + "%";
	}

	/**
	 * 计算百分比
	 * 
	 * @param a
	 *            分子
	 * @param b
	 *            分母，为0有异常
	 * @param decimal
	 *            设置精确位数
	 * @return
	 */
	public static String percentReverseInteger(Double a, Double b) {
		if (a == null)
			a = 0.0;
		if (b == null)
			b = 1.0;
		if (to45(a / b * 100) < 0)
			return (to45(a / b * 100) + "%").replace("-", "+");
		else if (to45(a / b * 100) > 0)
			return "-" + to45(a / b * 100) + "%";
		else
			return to45(a / b * 100) + "%";
	}

	/**
	 * 四舍五入运算
	 * 
	 * @param number
	 * @return
	 */
	public static Integer to45(Double number) {
		return number == null ? null : new BigDecimal(number).setScale(0, BigDecimal.ROUND_HALF_UP).intValue();
	}

	public static Double getNoFloatFloor(Double number) {
		return Math.floor(number);
	}

	/**
	 * 去掉小数位，向下取整
	 * 
	 * @param number
	 * @return
	 */
	public static String getFloorStr(Double number) {
		if (number == null) {
			return "0";
		}
		Long l = number.longValue();
		return l.toString();
	}
	
	public static String format(Double d) {
		return  NumberFormat.getInstance().format(d);
	}
	

	/**
	 * 向上取整
	 * 
	 * @param number
	 * @return
	 */
	public static Double getUpInteger(Double number1, Double number2) {
		DecimalFormat num = new DecimalFormat("0");
		num.setRoundingMode(RoundingMode.UP);
		return Double.valueOf(num.format(multiplication4Double(number1, number2)));
	}

	public static Double getUpInteger1(Double number1, Double number2) {
		DecimalFormat num = new DecimalFormat("0");
		num.setRoundingMode(RoundingMode.UP);
		return Double.valueOf(num.format(multiplication1Double(number1, number2)));
	}
	
	public static Integer getDownInteger(Double number1) {
		if(number1==null){
			return 0;
		}
		DecimalFormat num = new DecimalFormat("0");
		num.setRoundingMode(RoundingMode.DOWN);
		return Double.valueOf(num.format(number1)).intValue();
	}
	
	public static boolean equals(Double number1, Double number2){
		return subtractionDouble(number1, number2) == 0D;
	}
	
	public static void main(String[] args){
//		String[] dst = new String[]{
//				"f4437c5a-7663-4c38-9bd9-729fdeb68e54",
//				"dea2287d-e604-42fc-8f6d-a3e49dda11a8",
//				"ee29ba2f9e628cc5a3942f1640e71fafb848793d9f3b589687290a7d305cb464",
//				"7547abdb-a3b8-4934-a7df-9aa09dcca8cf",
//				"5BEB39EBBCFB48B4A14DC8CE6546CC14be4dba98303cab9b316c532a996762b9",
//
//				"00D657D252AC49D6ADE6C1B3024B2BF49f781502b3a2e745cd926acc135999a1",
//				"84b678a4414b9efc085615d3b4f9531fcbe1f518727cf2bae3ec43f7f0551034",
//				"52F54D56D7F44C67B507428ED4E2450D7f2a5ed86f42b2dc745e6dfd5d5b83dc",
//				"4a60fbbedaadfb915e1ec99dc585e5fde39631a0651981fde6a9c58c5c880729",
//				"b1ca934f-f613-4d10-8e81-21639579c60e",
//		};
//
//		for(String sc : dst){
//			System.out.println(MD5Utils.getMd5Sum(sc));
//		}
		System.out.println(format(getUpInteger(367.0,7.3733)));
		System.out.println(DoubleUtil.getUpInteger1(1000D, 0.060504));
		Double aa = 61D;
		System.out.println(aa.equals(DoubleUtil.getUpInteger1(1000D, 0.060504)));
		for(int i=15;i<100;i++){
			System.out.println(i+"@"+getBoundDouble(i,23,60,6));
		}


		System.out.println(getBoundDouble(30d,23.74d,60d,6));
	}

}
