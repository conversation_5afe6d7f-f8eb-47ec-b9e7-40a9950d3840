package com.coohua.rta.utils;

import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 */
public class PairUtil {

    public static <T> Pair<<PERSON>olean, T> ofFalse() {
        return Pair.of(false, null);
    }

    public static <T> Pair<<PERSON><PERSON><PERSON>, T> ofTrue(T t) {
        return Pair.of(true, t);
    }

    public static <T> Pair<<PERSON><PERSON><PERSON>, T> of(<PERSON><PERSON><PERSON> left) {
        return Pair.of(left, null);
    }

}
