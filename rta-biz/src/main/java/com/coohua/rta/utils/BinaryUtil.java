package com.coohua.rta.utils;

import cn.hutool.core.util.StrUtil;

import java.util.ArrayList;
import java.util.List;

public class BinaryUtil {
    public static String getZipCode(String binaryCode){
        if (StrUtil.isBlank(binaryCode)){
            return "参数为空";
        }
        StringBuilder res = new StringBuilder();
        List<String> binaryList = splitBinary(binaryCode, 6);
        for (String binary : binaryList){
            res.append(getRealCode(Integer.parseInt(binary, 2)));
        }

        return res.toString();
    }

    /**
     *
     */
    public static String decryptZipCodeToBinary(String code){
        if (StrUtil.isBlank(code)){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (char c : code.toCharArray()){
            StringBuilder binary = new StringBuilder(String.format("%06d", Integer.parseInt(Integer.toBinaryString(decryptRealNumber((int) c)))));
            sb.append(binary.reverse());
        }
        return sb.toString();
    }

    public static int decryptRealNumber(Integer num){

        Integer resNum = new Integer(-1);
        if (num == 35){
            resNum = 0;
        }else if (num == 36){
            resNum = 1;
        }else if (num >= 48 && num <= 57){
            resNum = num - 46;
        }else if (num >= 65  && num <= 90){
            resNum = num - 53;
        }else if(num >= 97  && num <= 122){
            resNum = num - 59;
        }
        return resNum;

    }

    public static String getRealCode(Integer num){

        Integer resNum = new Integer(-1);
        if (num == 0){
            resNum = 35;
        }else if (num == 1){
            resNum = 36;
        }else if (num >= 2 && num <= 11){
            resNum = num + 46;
        }else if (num >= 12 && num <= 37){
            resNum = num + 53;
        }else if(num >= 38 && num <= 63){
            resNum = num + 59;
        }

        return Character.toString((char) resNum.intValue()) ;

    }

    public static List<String> splitBinary(String binary, int groupSize){
        int length = binary.length();
        int groupCount = (length + groupSize - 1) / groupSize;
        List<String> binaryList = new ArrayList<>();

        for (int i = 0; i < groupCount; i++) {
            int start = i * groupSize;
            int end = Math.min(start + groupSize, length);
            binaryList.add(binary.substring(start, end));
        }

        return binaryList;
    }
}
