package com.coohua.rta.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashSet;
import java.util.Set;

@Slf4j
public class MD5Utils {
    public static void main(String[] args) {
        String s = "FD45D1206D25452FFD48AB64583978F3";
        macSet.forEach(df -> {
            System.out.println(getMd5Mac(df));
        });
    }

    public static String getMd5Sum(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.reset();
            byte[] buf = input.getBytes();
            md.update(buf);
            byte[] digest = md.digest();
            String result = new String(Hex.encodeHex(digest));
            md.reset();
            return result;
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
    }


    public static String getMd5AndroidId(String androidId) {
        if (StringUtils.isNotBlank(androidId) && androidId.length() != 32) {
            return MD5Utils.getMd5Sum(androidId);
        }
        return androidId;
    }

    public static String getMd5Ua(String userAgent) {
        if (StringUtils.isNotBlank(userAgent)) {
            return MD5Utils.getMd5Sum(userAgent);
        }
        return userAgent;
    }

    public static Set<String> macSet = new HashSet<>();

    static {
        macSet.add("02:00:00:00:00:02");
        macSet.add("02:00:00:00:00:00");

        macSet.add("00865256196d82d9663ead08c67ce04e");
        macSet.add("1035d532877200d6598f96118c2821e8");
        macSet.add("e3f5536a141811db40efd6400f1d0a4e");
    }

    public static String getMd5Mac(String macSoc) {
        if(macSet.contains(macSoc)){
//            log.info("macset "+macSoc);
            return null;
        }
        if (StringUtils.isNotBlank(macSoc) && macSoc.contains(":")) {
            String rstr = macSoc.replaceAll(":", "").toUpperCase();
            return MD5Utils.getMd5Sum(rstr);
        }
        return macSoc;
    }
}
