package com.coohua.rta.utils;

import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;

import java.util.List;
import java.util.SortedMap;
import java.util.TreeMap;

public class ConsistentHashingShardingUtil {

    // 哈希环，存储虚拟节点到组的映射
    private final SortedMap<Integer, Integer> hashRing = new TreeMap<>();
    // 哈希函数
    private final HashFunction hashFunction = Hashing.murmur3_32();
    // 虚拟节点数，用于控制权重精度
    private final int virtualNodeCount;

    public ConsistentHashingShardingUtil(int virtualNodeCount) {
        this.virtualNodeCount = virtualNodeCount;
    }

    /**
     * 添加组及其权重
     *
     * @param group  组ID
     * @param weight 权重
     */
    public void addGroup(int group, int weight) {
        for (int i = 0; i < virtualNodeCount * weight; i++) {
            // 虚拟节点名称：group_i
            String virtualNode = group + "_" + i;
            // 计算虚拟节点的哈希值
            int hash = hashFunction.hashUnencodedChars(virtualNode).asInt();
            // 将虚拟节点映射到哈希环
            hashRing.put(hash, group);
        }
    }

    /**
     * 移除组
     *
     * @param group 组ID
     */
    public void removeGroup(int group) {
        hashRing.entrySet().removeIf(entry -> entry.getValue() == group);
    }

    /**
     * 根据设备ID进行分流
     *
     * @param deviceId 设备ID
     * @return 组ID
     */
    public int shard(String deviceId) {
        if (hashRing.isEmpty()) {
            throw new IllegalStateException("No groups available");
        }
        // 计算设备ID的哈希值
        int hash = hashFunction.hashUnencodedChars(deviceId).asInt();
        // 找到哈希环上最近的虚拟节点
        SortedMap<Integer, Integer> tailMap = hashRing.tailMap(hash);
        int targetHash = tailMap.isEmpty() ? hashRing.firstKey() : tailMap.firstKey();
        return hashRing.get(targetHash);
    }

    /**
     * 获取当前哈希环的状态（用于调试）
     */
    public void printHashRing() {
        hashRing.forEach((hash, group) -> System.out.println("Hash: " + hash + " -> Group: " + group));
    }


    public static void testSharding(String key) {

    }

    public static void main(String[] args) {
        // 初始化一致性哈希分流器，设置虚拟节点数为100
        ConsistentHashingShardingUtil sharding = new ConsistentHashingShardingUtil(1000);

        // 添加10个组，每组权重为10
        for (int i = 0; i < 10; i++) {
            sharding.addGroup(i, 10);
        }

        // 模拟100万个设备ID进行分流
//        int[] counts = new int[10];
//        for (int i = 0; i < 1000000; i++) {
//            String deviceId = "device_" + i;
//            int group = sharding.shard(deviceId);
//            counts[group]++;
//        }

        List<String> deviceIds = ABShardingUtil.getDeviceList();
        int[] counts = new int[10];
        for (String deviceId : deviceIds) {
            int group = sharding.shard(deviceId + "android");
            counts[group]++;
        }
        // 输出分流结果
        for (int i = 0; i < counts.length; i++) {
            System.out.println("Group " + i + ": " + counts[i] + " users");
        }

        // 动态调整权重：将Group 0的权重从10增加到20
        sharding.addGroup(0, 20); // 增加10个虚拟节点
        System.out.println("\nAfter adjusting weight of Group 0:");

        // 重新分流
        counts = new int[10];
        for (int i = 0; i < 1000000; i++) {
            String deviceId = "device_" + i;
            int group = sharding.shard(deviceId);
            counts[group]++;
        }

        // 输出分流结果
        for (int i = 0; i < counts.length; i++) {
            System.out.println("Group " + i + ": " + counts[i] + " users");
        }
    }
}