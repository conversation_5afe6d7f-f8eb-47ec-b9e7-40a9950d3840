package com.coohua.rta.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class HashToRangeUtil {

    public static int hashToRange(String input, Integer min, Integer max) {
        try {
            if (min == null) min = 1;
            if (max == null) max = 500;
            // 使用 SHA-256 哈希算法
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));

            // 将哈希值转换为一个长整型数
            long hashValue = 0;
            for (byte b : hash) {
                hashValue = (hashValue << 8) | (b & 0xff);
            }

            // 取模运算并加 1
            return (int) ((hashValue % (max - min + 1) + (max - min + 1)) % (max - min + 1)) + min;

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Hashing algorithm not found", e);
        }
    }
}
