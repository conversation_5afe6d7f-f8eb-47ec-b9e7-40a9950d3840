package com.coohua.rta.utils;

public class IntegerUtils {
    public static  int add(Integer a,Integer b){
        if(a==null){
            a = 0;
        }
        if(b==null){
            b=0;
        }
        return a+b;
    }

    public static  int add(Integer a,Integer... bs){
        int x = 0;
        if(bs!=null && bs.length>0){
            for(Integer x1:bs){
                x = add(x,x1);
            }
        }
        return x;
    }

    public static  int sub(Integer a,Integer b){
        if(a==null){
            a = 0;
        }
        if(b==null){
            b=0;
        }
        return a-b;
    }
}
