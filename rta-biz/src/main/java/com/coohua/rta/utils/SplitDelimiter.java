package com.coohua.rta.utils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum SplitDelimiter {
    <PERSON><PERSON><PERSON><PERSON>(",", "，"),

    <PERSON><PERSON><PERSON><PERSON>("\n","\r"),

    <PERSON><PERSON><PERSON>(" "),

    ;

    public static final String CommonCustomDelimiter = "CommonCustomDelimiter";

    public List<String> codes;

    SplitDelimiter(String... codes) {
        this.codes = Arrays.asList(codes);
    }

    /**
     * 判断分隔符在输入字符串中是否存在
     * @param input
     */
    public boolean exist(String input) {
        return codes.stream().filter(code-> input.contains(code)).findFirst().isPresent();
    }

    /**
     * 根据当前分隔符对字符串进行分割
     * @param input
     */
    private List<String> splitByCurrent(String input) {
        Stream<String> stringStream = null;
        for (int i = 0; i < codes.size(); i++) {
            String code = codes.get(i);
            if (i == 0) {
                stringStream = Arrays.stream(StringUtils.split(input, code));
            } else {
                stringStream = stringStream.flatMap(k-> Arrays.stream(StringUtils.split(k, code)));
            }
        }
        return stringStream.collect(Collectors.toList());
    }

    /**
     * 将输入字符串按自定义的分隔符分割成 List<String>
     * @param input 输入字符串
     * @param useDelimiters 使用的分隔符，不填时默认使用所有分隔符
     * @return
     */
    public static List<String> customSplit(String input, SplitDelimiter... useDelimiters) {
        return customSplit(input, "多个值", useDelimiters);
//        try {
//        } catch (Exception e) {
//            System.out.println(e.getMessage() + " " + input);
//            return null;
//        }
    }
    public static List<String> customSplit(String input, String columnName, SplitDelimiter... useDelimiters) {
        return customSplit(input, columnName, false, useDelimiters);
    }
    /**
     * @param nullable 是否可为空
     */
    public static List<String> customSplit(String input, String columnName, boolean nullable, SplitDelimiter... useDelimiters) {
        if (input == null) {
            if (nullable) {
                return new ArrayList<>();
            } else {
                throw new RuntimeException(columnName + " 参数为空");
            }
        }

        // 未输入时默认取全部
        List<SplitDelimiter> useDelimiterList = useDelimiters.length > 0 ? Arrays.asList(useDelimiters) : Arrays.asList(SplitDelimiter.values());

        Map<Boolean, List<SplitDelimiter>> boolMap = useDelimiterList.stream().collect(Collectors.partitioningBy(k -> k.exist(input)));

        // input中没有任何有效分隔符时直接返回
        if (CollectionUtils.isEmpty(boolMap.get(true))) {
            return new ArrayList<String>(){{add(input);}};
        }

        // input中存在的有效分隔符种类 的数量大于1时
        if (boolMap.get(true).size() > 1) {
            throw new RuntimeException(columnName + " 列表只能统一用'逗号/换行符/空格'中的一种分隔");
        }

        return boolMap.get(true).get(0).splitByCurrent(input);
    }

    public static List<String> customSplit(Collection<String> inputs, SplitDelimiter... useDelimiters) {
        List<String> resList = new ArrayList<>();
        for (String input : inputs) {
            resList.addAll(customSplit(input, "多个值", true, useDelimiters));
        }
        return resList;
    }
}
