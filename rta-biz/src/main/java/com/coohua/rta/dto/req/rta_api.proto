syntax = "proto3";
import "google/protobuf/wrappers.proto";

enum PlatformType {
    PLATFORM_UNKNOWN = 0;
    ANDROID = 1;
    IOS = 2;
}

enum DeviceType {
    DEVICE_UNKNOWN = 0;
    PHONE = 1;
    TABLET = 2;
}

enum DeviceOsType {
    DEVICE_OS_UNKNOWN = 0;
    DEVICE_ANDROID = 1;
    DEVICE_IOS = 2;
    DEVICE_WINDOWS = 3;
}

enum GenderType {
    GENDER_UNKNOWN = 0;
    MALE = 1;
    FEMALE = 2;
}

enum DidType {
    IMEI = 0;
    IMEI_MD5 = 1;
    ANDROID_ID = 2;
    ANDROID_ID_MD5 = 3;
    IDFA = 4;
    IDFA_MD5 = 5;
    OAID = 6;
    OAID_MD5 = 7;
    GAID = 8;
    GAID_MD5 = 9;
}

enum EnableStrategy {
    DISABLE = 0;
    ENABLE_DEFAULT = 1;
    ENABLE_Type_2 = 2;
    ENABLE_Type_3 = 3;
    ENABLE_Type_4 = 4;
    ENABLE_Type_5 = 5;
    ENABLE_Type_6 = 6;
    ENABLE_Type_7 = 7;
    ENABLE_Type_8 = 8;
    ENABLE_Type_9 = 9;
    ENABLE_Type_10 = 10;
}

enum ImageMode {
    IMAGE_DEFAULT = 0;
    IMAGE_VIDEO = 1;
    IMAGE_OTHER = 2;
}

enum CusVid {
    CUS_VID_UNKNOWN = 0;
    CUS_VID_1 = 1;
    CUS_VID_2 = 2;
    CUS_VID_3 = 3;
    CUS_VID_4 = 4;
    CUS_VID_5 = 5;
    CUS_VID_6 = 6;
    CUS_VID_7 = 7;
    CUS_VID_8 = 8;
    CUS_VID_9 = 9;
    CUS_VID_10 = 10;
}

message Paid {
    string value  = 1;
    string version = 2; // 1.3/1.4
}

message Device {
    string imei_md5 = 1;
    string android_id_md5 = 2;
    string oaid = 3;
    string idfa = 4;
    string imei_secure_id = 5;
    string caid1 = 6;
    string caid2 = 7;
    string global_did = 8;
    string gaid = 9;
    string imei_md5_secure_id = 10; // 原值md5后加密
    string idfa_secure_id = 11; // 大写原值加密
    string idfa_md5_secure_id = 12; // 大写原值md5后加密
    string oaid_secure_id = 13; // 大写原值加密
    string oaid_md5_secure_id = 14; // 大写md5后加密
    string sk_version = 15; // 加密秘钥版本
    string ip = 17;
    string ua = 18;
    string union_imei = 19;
    string paid = 20;   // 支持拼多多在无 IDFA 流量上参竞与归因
    string idfv = 21;
    string mb_time_md5 = 22; // 拼多多系统更新时间
    string boot_time_md5 = 23; // 拼多多设备启动时间
    string caid1_secure_id = 24;
    string caid2_secure_id = 25;
    int64 app_id = 26; // app点位
    DeviceOsType device_os_type = 27; // 操作系统类型
    string os_version = 28; // 操作系统版本
    DeviceOsType interface_type = 29;
    string content_group = 30;
    string caid1_version = 31;
    string caid2_version = 32;
    repeated Paid paids = 33;
    string caid = 34; // CAID 2024版本
}

message Industry {
    int64 first_industry = 1;
    int64 second_industry = 2;
}

message Geo {
    int64 city = 1;
    double lat = 2;
    double lon = 3;
    string Country = 4;
    int64 district = 5;
}

message ModelMeta {
    string id = 1;
    double score = 2;
}

message PrivateModel {
    repeated ModelMeta model_meta = 1;
}

message AdvPrivateModel {
    map<int64, PrivateModel> adv_private_model = 1;
}

message Req {
    PlatformType platform = 1;
    string did = 2; // 安卓为imei，ios为idfa，站内必传字段
    int64 slot_id = 3; //广告位ID
    int32 age = 4; //  0(<18), 1(18~23), 2(24~30), 3(31~40) 4(41~49), 5(>50)
    GenderType gender = 5;
    string city = 6;
    string model = 7;
    repeated string install_list = 8;//安装包名列表,只包含关注包名
    DidType did_type = 9;  //did_type:imei,imei_md5,idfa,Android id，站内必传字段
    int32 experiment = 10;  //实验分组 1~n
    DeviceType device_type = 11;
    string req_id = 12; // 站内必传字段
    string source = 13; // 站内必传字段
    repeated int64 rta_ids = 14; // 站内必传字段
    EnableStrategy enable_strategy = 15; // 联合实验的参数，站内必传字段
    int64 tt_did = 16; //联运使用
    Device device = 17; //新版设备信息
    Industry industry = 18; // 媒体的行业信息
    Geo geo = 19; // 地理位置信息
    string DeviceModel = 20; // 设备型号
    repeated int32 creative_type = 21; // 可以出的素材类型
    int32 ad_slot_type = 22; // 广告位类型
    string app_bundle_id = 23; // 媒体包名
    repeated int32 vids = 24; // 流量命中的vid列表，非必填
    repeated ModelMeta private_model = 25; // 联合建模分数(数组)
    ImageMode image_mode = 26; // 请求素材类型（粗略划分）
    int64 user_id = 27; // 用户id
    int64 adx_id = 28; // adx_id
    int64 app_id = 29; // app点位
}

message UserInfo {
    enum UserType {
        EMPTY = 0;
        PRICE_COEF = 1;
        DEFAULT = 2;
        PRICE_BID = 3; // 站内直接出价
	    RTA_BID = 7; //穿山甲使用
	    BOOST_COEF = 8; //穿山甲使用
        DEEP_MIN = 9; //穿山甲双出价@22.5.16
    }

    enum PidType {
        PidType_0 = 0;
        PidType_1 = 1;
        PidType_2 = 2;
        PidType_3 = 3;
        PidType_4 = 4;
    }

    // 个性化出价支持分pricing type
    // 保障数据合规 这里和我们的pricing 非对应关系
    // rerank阶段单独做一次映射
    enum PricingType {
        NOLIMIT = 0; // 不限定
        OCPM = 1;
        OCPC = 2;
        CPC = 3;
    }

    enum ActionType {
        ACTION_DEFAULT = 0;  // 待补充，尽量与站内转化行为一致
        IN_APP_ORDER = 20;  // app内下单
        CUSTOMER_EFFECTIVE = 26; //有效获客
        LOAN_CREDIT = 105; // 授信
    }

    message UserQuality {
        UserType user_type = 1; // 对用户的分类，与rta广告的分类相对应
        double quality = 2;
        PricingType pricing_type = 3; // 分pricing type 支持出价扰动
    };

    message ActionQuality {
        ActionType action_type = 1; // 对用户转化行为的分类
        double quality = 2;  // value range [0, 1]
    };

    // rta+dpa维度个性化出价
    message PidScore {
        // pid
        int64 pid = 1;
        // RTA_BID(7) 直接出价  BOOST_COEF(8) 溢价系数出价
        UserType user_type = 2;
        // 出价
        double quality = 3;
    }

    message PidList {
        PidType pid_type = 1;  // 商品列表的重要程度，0代表最重要
        repeated int64 pids = 2; // 商品列表
        int64 platform_id = 3; // 商品库id
        // pid维度个性化出价
        repeated PidScore pid_scores = 4;
    }

    bool is_interested = 1; // 是否对这个用户感兴趣，标识是否投放用的字段，站内必传
    repeated UserQuality user_scores = 2; // 对用户的一些评判
    int64 rta_id = 3;      //rit_id，站内必传
    repeated PidList pid_lists = 4;  // RTA+DPA 相关的数据
    repeated ActionQuality action_scores = 5; // 对用户转化行为的一些评判
    repeated int64 target_advertiser_ids = 6; // 用于广告定向投放的advId列表
    repeated int64 ad_ids = 7; // 用于广告定向投放的ad_id列表，只有is_interested为true的情况下才会生效
    int32 advertising_scene = 8; // ug广告投放场景，仅穿山甲使用
    CusVid cus_vid = 9; // 广告主实验标签，rta_id粒度
}

message Rsp {
    int32 status_code = 1;// 0代表成功，其他表示错误 ，站内必传字段
    repeated int64 exclude_advertiser_id = 2 [packed = true];// 禁止投放的账户ID
    repeated int64 exclude_ad_id = 3 [packed = true];// 禁止投放的广告ID
    int64 rta_bid = 4;
    int32 cache_duration = 5 [deprecated=true];   //缓存时长：小时之后使用cache_time_secs字段该字段废弃
    string req_id = 6;
    repeated UserInfo user_infos = 7; // 站内必传字段
    int64 ts = 8; // 时间戳ms
    google.protobuf.Int32Value bid_type = 9; // 0表示参竞，1表示设备不参竞，2表示slot_id不参竞，结合缓存时长使用
    int32 cache_time_secs = 10; //缓存时间: 秒
    repeated float fl_embedding = 11; // fl 客户在 rta 一次请求返回的 embedding
    string rta_vid = 12; // 反向联合实验 vid
    CusVid cus_vid = 13; // 请求粒度广告主实验标签
}

