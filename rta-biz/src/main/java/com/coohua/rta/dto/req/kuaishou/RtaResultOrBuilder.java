// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

public interface RtaResultOrBuilder extends
    // @@protoc_insertion_point(interface_extends:kuaishou.ad.rta.RtaResult)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 账号id, 必填
   * </pre>
   *
   * <code>uint64 account_id = 1;</code>
   */
  long getAccountId();

  /**
   * <pre>
   * 广告组id, 不填代表所有广告组
   * </pre>
   *
   * <code>uint64 unit_id = 3;</code>
   */
  long getUnitId();

  /**
   * <pre>
   *动态出价系数
   * </pre>
   *
   * <code>double bid_ratio = 4;</code>
   */
  double getBidRatio();

  /**
   * <pre>
   * 策略id，strategy_id 存在时，忽略 account_id 和 unit_id 字段
   * </pre>
   *
   * <code>uint64 strategy_id = 5;</code>
   */
  long getStrategyId();

  /**
   * <pre>
   * 动态出价,单位元
   * </pre>
   *
   * <code>double bid = 6;</code>
   */
  double getBid();

  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  java.util.List<DpaResult>
      getDpaResultList();
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  DpaResult getDpaResult(int index);
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  int getDpaResultCount();
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  java.util.List<? extends DpaResultOrBuilder>
      getDpaResultOrBuilderList();
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  DpaResultOrBuilder getDpaResultOrBuilder(
      int index);
}
