// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

public interface DpaResultOrBuilder extends
    // @@protoc_insertion_point(interface_extends:kuaishou.ad.rta.DpaResult)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 商品库Id
   * </pre>
   *
   * <code>uint32 lib_id = 1;</code>
   */
  int getLibId();

  /**
   * <pre>
   * 取值范围0-4，整数，0为最重要，4为最次要
   * </pre>
   *
   * <code>uint32 pid_score = 2;</code>
   */
  int getPidScore();

  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  java.util.List<String>
      getPidList();
  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  int getPidCount();
  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  String getPid(int index);
  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  com.google.protobuf.ByteString
      getPidBytes(int index);
}
