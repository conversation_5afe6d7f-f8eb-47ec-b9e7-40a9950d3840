// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf type {@code kuaishou.ad.rta.UserEmb}
 */
public  final class UserEmb extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:kuaishou.ad.rta.UserEmb)
    UserEmbOrBuilder {
private static final long serialVersionUID = 0L;
  // Use UserEmb.newBuilder() to construct.
  private UserEmb(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private UserEmb() {
    userEmbA_ = java.util.Collections.emptyList();
    userEmbB_ = java.util.Collections.emptyList();
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private UserEmb(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          default: {
            if (!parseUnknownFieldProto3(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
          case 13: {
            if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
              userEmbA_ = new java.util.ArrayList<Float>();
              mutable_bitField0_ |= 0x00000001;
            }
            userEmbA_.add(input.readFloat());
            break;
          }
          case 10: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
              userEmbA_ = new java.util.ArrayList<Float>();
              mutable_bitField0_ |= 0x00000001;
            }
            while (input.getBytesUntilLimit() > 0) {
              userEmbA_.add(input.readFloat());
            }
            input.popLimit(limit);
            break;
          }
          case 21: {
            if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
              userEmbB_ = new java.util.ArrayList<Float>();
              mutable_bitField0_ |= 0x00000002;
            }
            userEmbB_.add(input.readFloat());
            break;
          }
          case 18: {
            int length = input.readRawVarint32();
            int limit = input.pushLimit(length);
            if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
              userEmbB_ = new java.util.ArrayList<Float>();
              mutable_bitField0_ |= 0x00000002;
            }
            while (input.getBytesUntilLimit() > 0) {
              userEmbB_.add(input.readFloat());
            }
            input.popLimit(limit);
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
        userEmbA_ = java.util.Collections.unmodifiableList(userEmbA_);
      }
      if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
        userEmbB_ = java.util.Collections.unmodifiableList(userEmbB_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return RTAProto.internal_static_kuaishou_ad_rta_UserEmb_descriptor;
  }

  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return RTAProto.internal_static_kuaishou_ad_rta_UserEmb_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            UserEmb.class, UserEmb.Builder.class);
  }

  public static final int USER_EMB_A_FIELD_NUMBER = 1;
  private java.util.List<Float> userEmbA_;
  /**
   * <pre>
   * 向量a，一开始默认使用字段
   * </pre>
   *
   * <code>repeated float user_emb_a = 1;</code>
   */
  public java.util.List<Float>
      getUserEmbAList() {
    return userEmbA_;
  }
  /**
   * <pre>
   * 向量a，一开始默认使用字段
   * </pre>
   *
   * <code>repeated float user_emb_a = 1;</code>
   */
  public int getUserEmbACount() {
    return userEmbA_.size();
  }
  /**
   * <pre>
   * 向量a，一开始默认使用字段
   * </pre>
   *
   * <code>repeated float user_emb_a = 1;</code>
   */
  public float getUserEmbA(int index) {
    return userEmbA_.get(index);
  }
  private int userEmbAMemoizedSerializedSize = -1;

  public static final int USER_EMB_B_FIELD_NUMBER = 2;
  private java.util.List<Float> userEmbB_;
  /**
   * <pre>
   * 向量b，向量升级时的切换字段
   * </pre>
   *
   * <code>repeated float user_emb_b = 2;</code>
   */
  public java.util.List<Float>
      getUserEmbBList() {
    return userEmbB_;
  }
  /**
   * <pre>
   * 向量b，向量升级时的切换字段
   * </pre>
   *
   * <code>repeated float user_emb_b = 2;</code>
   */
  public int getUserEmbBCount() {
    return userEmbB_.size();
  }
  /**
   * <pre>
   * 向量b，向量升级时的切换字段
   * </pre>
   *
   * <code>repeated float user_emb_b = 2;</code>
   */
  public float getUserEmbB(int index) {
    return userEmbB_.get(index);
  }
  private int userEmbBMemoizedSerializedSize = -1;

  private byte memoizedIsInitialized = -1;
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (getUserEmbAList().size() > 0) {
      output.writeUInt32NoTag(10);
      output.writeUInt32NoTag(userEmbAMemoizedSerializedSize);
    }
    for (int i = 0; i < userEmbA_.size(); i++) {
      output.writeFloatNoTag(userEmbA_.get(i));
    }
    if (getUserEmbBList().size() > 0) {
      output.writeUInt32NoTag(18);
      output.writeUInt32NoTag(userEmbBMemoizedSerializedSize);
    }
    for (int i = 0; i < userEmbB_.size(); i++) {
      output.writeFloatNoTag(userEmbB_.get(i));
    }
    unknownFields.writeTo(output);
  }

  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      dataSize = 4 * getUserEmbAList().size();
      size += dataSize;
      if (!getUserEmbAList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      userEmbAMemoizedSerializedSize = dataSize;
    }
    {
      int dataSize = 0;
      dataSize = 4 * getUserEmbBList().size();
      size += dataSize;
      if (!getUserEmbBList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      userEmbBMemoizedSerializedSize = dataSize;
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof UserEmb)) {
      return super.equals(obj);
    }
    UserEmb other = (UserEmb) obj;

    boolean result = true;
    result = result && getUserEmbAList()
        .equals(other.getUserEmbAList());
    result = result && getUserEmbBList()
        .equals(other.getUserEmbBList());
    result = result && unknownFields.equals(other.unknownFields);
    return result;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getUserEmbACount() > 0) {
      hash = (37 * hash) + USER_EMB_A_FIELD_NUMBER;
      hash = (53 * hash) + getUserEmbAList().hashCode();
    }
    if (getUserEmbBCount() > 0) {
      hash = (37 * hash) + USER_EMB_B_FIELD_NUMBER;
      hash = (53 * hash) + getUserEmbBList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static UserEmb parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static UserEmb parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static UserEmb parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static UserEmb parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static UserEmb parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static UserEmb parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static UserEmb parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static UserEmb parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static UserEmb parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static UserEmb parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static UserEmb parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static UserEmb parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(UserEmb prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code kuaishou.ad.rta.UserEmb}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:kuaishou.ad.rta.UserEmb)
          UserEmbOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return RTAProto.internal_static_kuaishou_ad_rta_UserEmb_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return RTAProto.internal_static_kuaishou_ad_rta_UserEmb_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              UserEmb.class, UserEmb.Builder.class);
    }

    // Construct using com.kuaishou.protobuf.ad.rta.UserEmb.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    public Builder clear() {
      super.clear();
      userEmbA_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00000001);
      userEmbB_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00000002);
      return this;
    }

    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return RTAProto.internal_static_kuaishou_ad_rta_UserEmb_descriptor;
    }

    public UserEmb getDefaultInstanceForType() {
      return UserEmb.getDefaultInstance();
    }

    public UserEmb build() {
      UserEmb result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    public UserEmb buildPartial() {
      UserEmb result = new UserEmb(this);
      int from_bitField0_ = bitField0_;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        userEmbA_ = java.util.Collections.unmodifiableList(userEmbA_);
        bitField0_ = (bitField0_ & ~0x00000001);
      }
      result.userEmbA_ = userEmbA_;
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        userEmbB_ = java.util.Collections.unmodifiableList(userEmbB_);
        bitField0_ = (bitField0_ & ~0x00000002);
      }
      result.userEmbB_ = userEmbB_;
      onBuilt();
      return result;
    }

    public Builder clone() {
      return (Builder) super.clone();
    }
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.setField(field, value);
    }
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return (Builder) super.clearField(field);
    }
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return (Builder) super.clearOneof(oneof);
    }
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return (Builder) super.setRepeatedField(field, index, value);
    }
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.addRepeatedField(field, value);
    }
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof UserEmb) {
        return mergeFrom((UserEmb)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(UserEmb other) {
      if (other == UserEmb.getDefaultInstance()) return this;
      if (!other.userEmbA_.isEmpty()) {
        if (userEmbA_.isEmpty()) {
          userEmbA_ = other.userEmbA_;
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          ensureUserEmbAIsMutable();
          userEmbA_.addAll(other.userEmbA_);
        }
        onChanged();
      }
      if (!other.userEmbB_.isEmpty()) {
        if (userEmbB_.isEmpty()) {
          userEmbB_ = other.userEmbB_;
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          ensureUserEmbBIsMutable();
          userEmbB_.addAll(other.userEmbB_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    public final boolean isInitialized() {
      return true;
    }

    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      UserEmb parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (UserEmb) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private java.util.List<Float> userEmbA_ = java.util.Collections.emptyList();
    private void ensureUserEmbAIsMutable() {
      if (!((bitField0_ & 0x00000001) == 0x00000001)) {
        userEmbA_ = new java.util.ArrayList<Float>(userEmbA_);
        bitField0_ |= 0x00000001;
       }
    }
    /**
     * <pre>
     * 向量a，一开始默认使用字段
     * </pre>
     *
     * <code>repeated float user_emb_a = 1;</code>
     */
    public java.util.List<Float>
        getUserEmbAList() {
      return java.util.Collections.unmodifiableList(userEmbA_);
    }
    /**
     * <pre>
     * 向量a，一开始默认使用字段
     * </pre>
     *
     * <code>repeated float user_emb_a = 1;</code>
     */
    public int getUserEmbACount() {
      return userEmbA_.size();
    }
    /**
     * <pre>
     * 向量a，一开始默认使用字段
     * </pre>
     *
     * <code>repeated float user_emb_a = 1;</code>
     */
    public float getUserEmbA(int index) {
      return userEmbA_.get(index);
    }
    /**
     * <pre>
     * 向量a，一开始默认使用字段
     * </pre>
     *
     * <code>repeated float user_emb_a = 1;</code>
     */
    public Builder setUserEmbA(
        int index, float value) {
      ensureUserEmbAIsMutable();
      userEmbA_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 向量a，一开始默认使用字段
     * </pre>
     *
     * <code>repeated float user_emb_a = 1;</code>
     */
    public Builder addUserEmbA(float value) {
      ensureUserEmbAIsMutable();
      userEmbA_.add(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 向量a，一开始默认使用字段
     * </pre>
     *
     * <code>repeated float user_emb_a = 1;</code>
     */
    public Builder addAllUserEmbA(
        Iterable<? extends Float> values) {
      ensureUserEmbAIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, userEmbA_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 向量a，一开始默认使用字段
     * </pre>
     *
     * <code>repeated float user_emb_a = 1;</code>
     */
    public Builder clearUserEmbA() {
      userEmbA_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }

    private java.util.List<Float> userEmbB_ = java.util.Collections.emptyList();
    private void ensureUserEmbBIsMutable() {
      if (!((bitField0_ & 0x00000002) == 0x00000002)) {
        userEmbB_ = new java.util.ArrayList<Float>(userEmbB_);
        bitField0_ |= 0x00000002;
       }
    }
    /**
     * <pre>
     * 向量b，向量升级时的切换字段
     * </pre>
     *
     * <code>repeated float user_emb_b = 2;</code>
     */
    public java.util.List<Float>
        getUserEmbBList() {
      return java.util.Collections.unmodifiableList(userEmbB_);
    }
    /**
     * <pre>
     * 向量b，向量升级时的切换字段
     * </pre>
     *
     * <code>repeated float user_emb_b = 2;</code>
     */
    public int getUserEmbBCount() {
      return userEmbB_.size();
    }
    /**
     * <pre>
     * 向量b，向量升级时的切换字段
     * </pre>
     *
     * <code>repeated float user_emb_b = 2;</code>
     */
    public float getUserEmbB(int index) {
      return userEmbB_.get(index);
    }
    /**
     * <pre>
     * 向量b，向量升级时的切换字段
     * </pre>
     *
     * <code>repeated float user_emb_b = 2;</code>
     */
    public Builder setUserEmbB(
        int index, float value) {
      ensureUserEmbBIsMutable();
      userEmbB_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 向量b，向量升级时的切换字段
     * </pre>
     *
     * <code>repeated float user_emb_b = 2;</code>
     */
    public Builder addUserEmbB(float value) {
      ensureUserEmbBIsMutable();
      userEmbB_.add(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 向量b，向量升级时的切换字段
     * </pre>
     *
     * <code>repeated float user_emb_b = 2;</code>
     */
    public Builder addAllUserEmbB(
        Iterable<? extends Float> values) {
      ensureUserEmbBIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, userEmbB_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 向量b，向量升级时的切换字段
     * </pre>
     *
     * <code>repeated float user_emb_b = 2;</code>
     */
    public Builder clearUserEmbB() {
      userEmbB_ = java.util.Collections.emptyList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFieldsProto3(unknownFields);
    }

    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:kuaishou.ad.rta.UserEmb)
  }

  // @@protoc_insertion_point(class_scope:kuaishou.ad.rta.UserEmb)
  private static final UserEmb DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new UserEmb();
  }

  public static UserEmb getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<UserEmb>
      PARSER = new com.google.protobuf.AbstractParser<UserEmb>() {
    public UserEmb parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new UserEmb(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<UserEmb> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<UserEmb> getParserForType() {
    return PARSER;
  }

  public UserEmb getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

