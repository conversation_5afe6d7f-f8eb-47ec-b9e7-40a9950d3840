// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

public interface UserEmbOrBuilder extends
    // @@protoc_insertion_point(interface_extends:kuaishou.ad.rta.UserEmb)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 向量a，一开始默认使用字段
   * </pre>
   *
   * <code>repeated float user_emb_a = 1;</code>
   */
  java.util.List<Float> getUserEmbAList();
  /**
   * <pre>
   * 向量a，一开始默认使用字段
   * </pre>
   *
   * <code>repeated float user_emb_a = 1;</code>
   */
  int getUserEmbACount();
  /**
   * <pre>
   * 向量a，一开始默认使用字段
   * </pre>
   *
   * <code>repeated float user_emb_a = 1;</code>
   */
  float getUserEmbA(int index);

  /**
   * <pre>
   * 向量b，向量升级时的切换字段
   * </pre>
   *
   * <code>repeated float user_emb_b = 2;</code>
   */
  java.util.List<Float> getUserEmbBList();
  /**
   * <pre>
   * 向量b，向量升级时的切换字段
   * </pre>
   *
   * <code>repeated float user_emb_b = 2;</code>
   */
  int getUserEmbBCount();
  /**
   * <pre>
   * 向量b，向量升级时的切换字段
   * </pre>
   *
   * <code>repeated float user_emb_b = 2;</code>
   */
  float getUserEmbB(int index);
}
