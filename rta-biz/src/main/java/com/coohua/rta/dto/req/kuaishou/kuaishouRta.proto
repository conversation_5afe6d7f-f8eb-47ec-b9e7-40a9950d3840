syntax = "proto3";

package kuaishou.ad.rta;

option java_package = "com.kuaishou.protobuf.ad.rta";

option java_outer_classname = "RTAProto";

option java_multiple_files = true;

enum OsType {
    UNKNOWN_OS = 0; // 未知类型
    ANDROID = 1; // Android
    IOS = 2; // iOS
}

enum ConnectionType {
    UNKNOWN_CONNECTION_TYPE = 0; // 无法探测当前网络状态
    CELL_UNKNOWN = 1; // 蜂窝数据接入，未知网络类型
    CELL_2G = 2; // 蜂窝数据2G网络
    CELL_3G = 3; // 蜂窝数据3G网络
    CELL_4G = 4; // 蜂窝数据4G网络
    CELL_5G = 5; // 蜂窝数据5G网络
    LTE = 6; //LTE网络
    WIFI = 100; // Wi-Fi网络接入
    ETHERNET = 101; // 以太网接入
}

enum OperatorType {
    UNKNOWN_OPERATOR_TYPE = 0; // 未知的运营商
    CHINA_MOBILE = 1; // 中国移动
    CHINA_TELECOM = 2; // 中国电信
    CHINA_UNICOM = 3; // 中国联通
    OTHER_OPERATOR = 99; // 其他运营商
}

message UserEmb {
  repeated float user_emb_a = 1; // 向量a，一开始默认使用字段
  repeated float user_emb_b = 2; // 向量b，向量升级时的切换字段
}

message Req {
    string id = 1; // 请求唯一id
    OsType os_type = 2; // 系统
    string did_md5 = 3; // 安卓为 imei_md5，ios 为 idfa_md5
    string tag_id = 4; // 广告位 ID

    int32 age = 6; // 年龄段, 0(0-11), 1(12-17), 2(18~23), 3(24~30), 4(31~35), 5(36~40), 6(41~45), 7(46~49), 8(50~54), 9(55~59), 10(60+)
    string model = 9; // 设备型号
    string oaid = 10; // 设备oaid
    string user_agent = 11; //请求UA信息
    string oaid_md5 = 12; //安卓设备md5(oaid)
    string ipv6 = 13;
    ConnectionType connection_type = 14; //上网类型
    OperatorType operator_type = 15; // 运营商类型
    uint64 adcode = 16; // 常驻地adcode

    string current_caid = 21; // 当前caid
    string current_caid_version = 22; // 当前caid版本
    string last_caid = 23; // 上一版本caid
    string last_caid_version = 24;// 上一版本caid版本
    string brand = 25; //手机厂商名
    uint64 media_industry_id = 26;  //媒体一级行业id
    string custom_info = 28; // 客户自定义信息，使用前需要线下约定。
    repeated string install_app = 30; // 安装的app列表
    string paid = 38; // 拼多多设备指纹
    string aaid = 39; // 阿里设备指纹
    uint32 site_set = 40;  // 资源位信息: 1: 信息流、2: 激励视频、3：搜索、4：开屏; 现为邀请制功能，如有需求请联系客户运营
    string ipv4 = 41;
    uint32 install_social_insurance = 45; // 是否安装社保app, 1(未安装)、2（已安装）
    uint32 payment = 46; // 是否支付, 1(未安装)、2（已安装）
    uint32 phone_price_id = 50; // 设备价格, 1(1000以下)、2（1000-1500）、3（1500-2000）、4（2000-2500）、5（2500-3000）、6（3000-3500）、7（3500-4000）、8（4000-5000）、9（5000-6000）、10（6000+）
    uint32 consumption_level_id = 51; // 消费水平, 1(高)、2（中）、3（低）
    uint32 education_id = 53; // 教育水平, 1(高中及以下)、2(专科)、3(本科)、4(硕士及以上)
    uint32 gender_id = 54; // 性别, 1(男)、2(女)
    double latitude = 55;  // 纬度
    double longitude = 56;  // 经度
    UserEmb user_embedding = 57; // 用户embedding向量
    string current_paid_1_5 = 60; // 拼多多设备指纹paid1.5, 当前版本
    string last_paid_1_5 = 61; // 拼多多设备指纹paid1.5, 上一个版本
    string previous_model = 62; // 换机设备标识
    string feature = 101; // 实验or策略信息，需线下约定
}

message RtaResult {
    uint64 account_id = 1; // 账号id, 必填
    uint64 unit_id = 3; // 广告组id, 不填代表所有广告组
    double bid_ratio = 4;   //动态出价系数
    uint64 strategy_id = 5; // 策略id，strategy_id 存在时，忽略 account_id 和 unit_id 字段
    double bid = 6; // 动态出价,单位元
    repeated DpaResult dpa_result = 7; // 推荐商品信息
}

message DpaResult {
    uint32 lib_id = 1; // 商品库Id
    uint32 pid_score = 2; // 取值范围0-4，整数，0为最重要，4为最次要
    repeated string pid = 3; // 商品id列表
}

message Resp {
    enum ResultType {
        ALL = 0; // 全部都投
        NONE = 1; // 全部不投
        PART = 2; // 只投一部分
    }
    string id = 1; // 请求唯一id
    ResultType result_type = 2; // 结果类型, 等于PART时, result必填
    repeated RtaResult result = 3; // result_type=PART时允许投放的广告列表, 如有重复取并集
    double bid_ratio = 4;   // result_type=ALL时的动态出价系数
    uint32 refuse_time = 6; //拒绝时间
    uint32 cache_time = 7; //缓存时间
    repeated DpaResult dpa_result = 8;//推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
    double bid = 9; //result_type=ALL时的动态出价,单位元
    uint32 bucket_id = 10; // 客户分桶id
}