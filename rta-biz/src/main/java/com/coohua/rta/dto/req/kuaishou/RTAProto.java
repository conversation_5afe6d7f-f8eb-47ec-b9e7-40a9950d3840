// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

public final class RTAProto {
  private RTAProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_kuaishou_ad_rta_UserEmb_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_kuaishou_ad_rta_UserEmb_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_kuaishou_ad_rta_Req_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_kuaishou_ad_rta_Req_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_kuaishou_ad_rta_RtaResult_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_kuaishou_ad_rta_RtaResult_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_kuaishou_ad_rta_DpaResult_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_kuaishou_ad_rta_DpaResult_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_kuaishou_ad_rta_Resp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_kuaishou_ad_rta_Resp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\021kuaishouRta.proto\022\017kuaishou.ad.rta\"1\n\007" +
      "UserEmb\022\022\n\nuser_emb_a\030\001 \003(\002\022\022\n\nuser_emb_" +
      "b\030\002 \003(\002\"\357\006\n\003Req\022\n\n\002id\030\001 \001(\t\022(\n\007os_type\030\002" +
      " \001(\0162\027.kuaishou.ad.rta.OsType\022\017\n\007did_md5" +
      "\030\003 \001(\t\022\016\n\006tag_id\030\004 \001(\t\022\013\n\003age\030\006 \001(\005\022\r\n\005m" +
      "odel\030\t \001(\t\022\014\n\004oaid\030\n \001(\t\022\022\n\nuser_agent\030\013" +
      " \001(\t\022\020\n\010oaid_md5\030\014 \001(\t\022\014\n\004ipv6\030\r \001(\t\0228\n\017" +
      "connection_type\030\016 \001(\0162\037.kuaishou.ad.rta." +
      "ConnectionType\0224\n\roperator_type\030\017 \001(\0162\035." +
      "kuaishou.ad.rta.OperatorType\022\016\n\006adcode\030\020" +
      " \001(\004\022\024\n\014current_caid\030\025 \001(\t\022\034\n\024current_ca" +
      "id_version\030\026 \001(\t\022\021\n\tlast_caid\030\027 \001(\t\022\031\n\021l" +
      "ast_caid_version\030\030 \001(\t\022\r\n\005brand\030\031 \001(\t\022\031\n" +
      "\021media_industry_id\030\032 \001(\004\022\023\n\013custom_info\030" +
      "\034 \001(\t\022\023\n\013install_app\030\036 \003(\t\022\014\n\004paid\030& \001(\t" +
      "\022\014\n\004aaid\030\' \001(\t\022\020\n\010site_set\030( \001(\r\022\014\n\004ipv4" +
      "\030) \001(\t\022 \n\030install_social_insurance\030- \001(\r" +
      "\022\017\n\007payment\030. \001(\r\022\026\n\016phone_price_id\0302 \001(" +
      "\r\022\034\n\024consumption_level_id\0303 \001(\r\022\024\n\014educa" +
      "tion_id\0305 \001(\r\022\021\n\tgender_id\0306 \001(\r\022\020\n\010lati" +
      "tude\0307 \001(\001\022\021\n\tlongitude\0308 \001(\001\0220\n\016user_em" +
      "bedding\0309 \001(\0132\030.kuaishou.ad.rta.UserEmb\022" +
      "\030\n\020current_paid_1_5\030< \001(\t\022\025\n\rlast_paid_1" +
      "_5\030= \001(\t\022\026\n\016previous_model\030> \001(\t\022\017\n\007feat" +
      "ure\030e \001(\t\"\225\001\n\tRtaResult\022\022\n\naccount_id\030\001 " +
      "\001(\004\022\017\n\007unit_id\030\003 \001(\004\022\021\n\tbid_ratio\030\004 \001(\001\022" +
      "\023\n\013strategy_id\030\005 \001(\004\022\013\n\003bid\030\006 \001(\001\022.\n\ndpa" +
      "_result\030\007 \003(\0132\032.kuaishou.ad.rta.DpaResul" +
      "t\";\n\tDpaResult\022\016\n\006lib_id\030\001 \001(\r\022\021\n\tpid_sc" +
      "ore\030\002 \001(\r\022\013\n\003pid\030\003 \003(\t\"\254\002\n\004Resp\022\n\n\002id\030\001 " +
      "\001(\t\0225\n\013result_type\030\002 \001(\0162 .kuaishou.ad.r" +
      "ta.Resp.ResultType\022*\n\006result\030\003 \003(\0132\032.kua" +
      "ishou.ad.rta.RtaResult\022\021\n\tbid_ratio\030\004 \001(" +
      "\001\022\023\n\013refuse_time\030\006 \001(\r\022\022\n\ncache_time\030\007 \001" +
      "(\r\022.\n\ndpa_result\030\010 \003(\0132\032.kuaishou.ad.rta" +
      ".DpaResult\022\013\n\003bid\030\t \001(\001\022\021\n\tbucket_id\030\n \001" +
      "(\r\")\n\nResultType\022\007\n\003ALL\020\000\022\010\n\004NONE\020\001\022\010\n\004P" +
      "ART\020\002*.\n\006OsType\022\016\n\nUNKNOWN_OS\020\000\022\013\n\007ANDRO" +
      "ID\020\001\022\007\n\003IOS\020\002*\224\001\n\016ConnectionType\022\033\n\027UNKN" +
      "OWN_CONNECTION_TYPE\020\000\022\020\n\014CELL_UNKNOWN\020\001\022" +
      "\013\n\007CELL_2G\020\002\022\013\n\007CELL_3G\020\003\022\013\n\007CELL_4G\020\004\022\013" +
      "\n\007CELL_5G\020\005\022\007\n\003LTE\020\006\022\010\n\004WIFI\020d\022\014\n\010ETHERN" +
      "ET\020e*t\n\014OperatorType\022\031\n\025UNKNOWN_OPERATOR" +
      "_TYPE\020\000\022\020\n\014CHINA_MOBILE\020\001\022\021\n\rCHINA_TELEC" +
      "OM\020\002\022\020\n\014CHINA_UNICOM\020\003\022\022\n\016OTHER_OPERATOR" +
      "\020cB*\n\034com.kuaishou.protobuf.ad.rtaB\010RTAP" +
      "rotoP\001b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_kuaishou_ad_rta_UserEmb_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_kuaishou_ad_rta_UserEmb_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_kuaishou_ad_rta_UserEmb_descriptor,
        new String[] { "UserEmbA", "UserEmbB", });
    internal_static_kuaishou_ad_rta_Req_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_kuaishou_ad_rta_Req_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_kuaishou_ad_rta_Req_descriptor,
        new String[] { "Id", "OsType", "DidMd5", "TagId", "Age", "Model", "Oaid", "UserAgent", "OaidMd5", "Ipv6", "ConnectionType", "OperatorType", "Adcode", "CurrentCaid", "CurrentCaidVersion", "LastCaid", "LastCaidVersion", "Brand", "MediaIndustryId", "CustomInfo", "InstallApp", "Paid", "Aaid", "SiteSet", "Ipv4", "InstallSocialInsurance", "Payment", "PhonePriceId", "ConsumptionLevelId", "EducationId", "GenderId", "Latitude", "Longitude", "UserEmbedding", "CurrentPaid15", "LastPaid15", "PreviousModel", "Feature", });
    internal_static_kuaishou_ad_rta_RtaResult_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_kuaishou_ad_rta_RtaResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_kuaishou_ad_rta_RtaResult_descriptor,
        new String[] { "AccountId", "UnitId", "BidRatio", "StrategyId", "Bid", "DpaResult", });
    internal_static_kuaishou_ad_rta_DpaResult_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_kuaishou_ad_rta_DpaResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_kuaishou_ad_rta_DpaResult_descriptor,
        new String[] { "LibId", "PidScore", "Pid", });
    internal_static_kuaishou_ad_rta_Resp_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_kuaishou_ad_rta_Resp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_kuaishou_ad_rta_Resp_descriptor,
        new String[] { "Id", "ResultType", "Result", "BidRatio", "RefuseTime", "CacheTime", "DpaResult", "Bid", "BucketId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
