// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf type {@code kuaishou.ad.rta.Req}
 */
public  final class Req extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:kuaishou.ad.rta.Req)
    ReqOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Req.newBuilder() to construct.
  private Req(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Req() {
    id_ = "";
    osType_ = 0;
    didMd5_ = "";
    tagId_ = "";
    age_ = 0;
    model_ = "";
    oaid_ = "";
    userAgent_ = "";
    oaidMd5_ = "";
    ipv6_ = "";
    connectionType_ = 0;
    operatorType_ = 0;
    adcode_ = 0L;
    currentCaid_ = "";
    currentCaidVersion_ = "";
    lastCaid_ = "";
    lastCaidVersion_ = "";
    brand_ = "";
    mediaIndustryId_ = 0L;
    customInfo_ = "";
    installApp_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    paid_ = "";
    aaid_ = "";
    siteSet_ = 0;
    ipv4_ = "";
    installSocialInsurance_ = 0;
    payment_ = 0;
    phonePriceId_ = 0;
    consumptionLevelId_ = 0;
    educationId_ = 0;
    genderId_ = 0;
    latitude_ = 0D;
    longitude_ = 0D;
    currentPaid15_ = "";
    lastPaid15_ = "";
    previousModel_ = "";
    feature_ = "";
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Req(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    int mutable_bitField0_ = 0;
    int mutable_bitField1_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          default: {
            if (!parseUnknownFieldProto3(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
          case 10: {
            String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            osType_ = rawValue;
            break;
          }
          case 26: {
            String s = input.readStringRequireUtf8();

            didMd5_ = s;
            break;
          }
          case 34: {
            String s = input.readStringRequireUtf8();

            tagId_ = s;
            break;
          }
          case 48: {

            age_ = input.readInt32();
            break;
          }
          case 74: {
            String s = input.readStringRequireUtf8();

            model_ = s;
            break;
          }
          case 82: {
            String s = input.readStringRequireUtf8();

            oaid_ = s;
            break;
          }
          case 90: {
            String s = input.readStringRequireUtf8();

            userAgent_ = s;
            break;
          }
          case 98: {
            String s = input.readStringRequireUtf8();

            oaidMd5_ = s;
            break;
          }
          case 106: {
            String s = input.readStringRequireUtf8();

            ipv6_ = s;
            break;
          }
          case 112: {
            int rawValue = input.readEnum();

            connectionType_ = rawValue;
            break;
          }
          case 120: {
            int rawValue = input.readEnum();

            operatorType_ = rawValue;
            break;
          }
          case 128: {

            adcode_ = input.readUInt64();
            break;
          }
          case 170: {
            String s = input.readStringRequireUtf8();

            currentCaid_ = s;
            break;
          }
          case 178: {
            String s = input.readStringRequireUtf8();

            currentCaidVersion_ = s;
            break;
          }
          case 186: {
            String s = input.readStringRequireUtf8();

            lastCaid_ = s;
            break;
          }
          case 194: {
            String s = input.readStringRequireUtf8();

            lastCaidVersion_ = s;
            break;
          }
          case 202: {
            String s = input.readStringRequireUtf8();

            brand_ = s;
            break;
          }
          case 208: {

            mediaIndustryId_ = input.readUInt64();
            break;
          }
          case 226: {
            String s = input.readStringRequireUtf8();

            customInfo_ = s;
            break;
          }
          case 242: {
            String s = input.readStringRequireUtf8();
            if (!((mutable_bitField0_ & 0x00100000) == 0x00100000)) {
              installApp_ = new com.google.protobuf.LazyStringArrayList();
              mutable_bitField0_ |= 0x00100000;
            }
            installApp_.add(s);
            break;
          }
          case 306: {
            String s = input.readStringRequireUtf8();

            paid_ = s;
            break;
          }
          case 314: {
            String s = input.readStringRequireUtf8();

            aaid_ = s;
            break;
          }
          case 320: {

            siteSet_ = input.readUInt32();
            break;
          }
          case 330: {
            String s = input.readStringRequireUtf8();

            ipv4_ = s;
            break;
          }
          case 360: {

            installSocialInsurance_ = input.readUInt32();
            break;
          }
          case 368: {

            payment_ = input.readUInt32();
            break;
          }
          case 400: {

            phonePriceId_ = input.readUInt32();
            break;
          }
          case 408: {

            consumptionLevelId_ = input.readUInt32();
            break;
          }
          case 424: {

            educationId_ = input.readUInt32();
            break;
          }
          case 432: {

            genderId_ = input.readUInt32();
            break;
          }
          case 441: {

            latitude_ = input.readDouble();
            break;
          }
          case 449: {

            longitude_ = input.readDouble();
            break;
          }
          case 458: {
            UserEmb.Builder subBuilder = null;
            if (userEmbedding_ != null) {
              subBuilder = userEmbedding_.toBuilder();
            }
            userEmbedding_ = input.readMessage(UserEmb.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(userEmbedding_);
              userEmbedding_ = subBuilder.buildPartial();
            }

            break;
          }
          case 482: {
            String s = input.readStringRequireUtf8();

            currentPaid15_ = s;
            break;
          }
          case 490: {
            String s = input.readStringRequireUtf8();

            lastPaid15_ = s;
            break;
          }
          case 498: {
            String s = input.readStringRequireUtf8();

            previousModel_ = s;
            break;
          }
          case 810: {
            String s = input.readStringRequireUtf8();

            feature_ = s;
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00100000) == 0x00100000)) {
        installApp_ = installApp_.getUnmodifiableView();
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return RTAProto.internal_static_kuaishou_ad_rta_Req_descriptor;
  }

  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return RTAProto.internal_static_kuaishou_ad_rta_Req_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            Req.class, Req.Builder.class);
  }

  private int bitField0_;
  private int bitField1_;
  public static final int ID_FIELD_NUMBER = 1;
  private volatile Object id_;
  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  public String getId() {
    Object ref = id_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  public com.google.protobuf.ByteString
      getIdBytes() {
    Object ref = id_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OS_TYPE_FIELD_NUMBER = 2;
  private int osType_;
  /**
   * <pre>
   * 系统
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
   */
  public int getOsTypeValue() {
    return osType_;
  }
  /**
   * <pre>
   * 系统
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
   */
  public OsType getOsType() {
    OsType result = OsType.valueOf(osType_);
    return result == null ? OsType.UNRECOGNIZED : result;
  }

  public static final int DID_MD5_FIELD_NUMBER = 3;
  private volatile Object didMd5_;
  /**
   * <pre>
   * 安卓为 imei_md5，ios 为 idfa_md5
   * </pre>
   *
   * <code>string did_md5 = 3;</code>
   */
  public String getDidMd5() {
    Object ref = didMd5_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      didMd5_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 安卓为 imei_md5，ios 为 idfa_md5
   * </pre>
   *
   * <code>string did_md5 = 3;</code>
   */
  public com.google.protobuf.ByteString
      getDidMd5Bytes() {
    Object ref = didMd5_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      didMd5_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TAG_ID_FIELD_NUMBER = 4;
  private volatile Object tagId_;
  /**
   * <pre>
   * 广告位 ID
   * </pre>
   *
   * <code>string tag_id = 4;</code>
   */
  public String getTagId() {
    Object ref = tagId_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      tagId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 广告位 ID
   * </pre>
   *
   * <code>string tag_id = 4;</code>
   */
  public com.google.protobuf.ByteString
      getTagIdBytes() {
    Object ref = tagId_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      tagId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AGE_FIELD_NUMBER = 6;
  private int age_;
  /**
   * <pre>
   * 年龄段, 0(0-11), 1(12-17), 2(18~23), 3(24~30), 4(31~35), 5(36~40), 6(41~45), 7(46~49), 8(50~54), 9(55~59), 10(60+)
   * </pre>
   *
   * <code>int32 age = 6;</code>
   */
  public int getAge() {
    return age_;
  }

  public static final int MODEL_FIELD_NUMBER = 9;
  private volatile Object model_;
  /**
   * <pre>
   * 设备型号
   * </pre>
   *
   * <code>string model = 9;</code>
   */
  public String getModel() {
    Object ref = model_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      model_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 设备型号
   * </pre>
   *
   * <code>string model = 9;</code>
   */
  public com.google.protobuf.ByteString
      getModelBytes() {
    Object ref = model_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      model_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OAID_FIELD_NUMBER = 10;
  private volatile Object oaid_;
  /**
   * <pre>
   * 设备oaid
   * </pre>
   *
   * <code>string oaid = 10;</code>
   */
  public String getOaid() {
    Object ref = oaid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      oaid_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 设备oaid
   * </pre>
   *
   * <code>string oaid = 10;</code>
   */
  public com.google.protobuf.ByteString
      getOaidBytes() {
    Object ref = oaid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      oaid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int USER_AGENT_FIELD_NUMBER = 11;
  private volatile Object userAgent_;
  /**
   * <pre>
   *请求UA信息
   * </pre>
   *
   * <code>string user_agent = 11;</code>
   */
  public String getUserAgent() {
    Object ref = userAgent_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      userAgent_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *请求UA信息
   * </pre>
   *
   * <code>string user_agent = 11;</code>
   */
  public com.google.protobuf.ByteString
      getUserAgentBytes() {
    Object ref = userAgent_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      userAgent_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int OAID_MD5_FIELD_NUMBER = 12;
  private volatile Object oaidMd5_;
  /**
   * <pre>
   *安卓设备md5(oaid)
   * </pre>
   *
   * <code>string oaid_md5 = 12;</code>
   */
  public String getOaidMd5() {
    Object ref = oaidMd5_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      oaidMd5_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *安卓设备md5(oaid)
   * </pre>
   *
   * <code>string oaid_md5 = 12;</code>
   */
  public com.google.protobuf.ByteString
      getOaidMd5Bytes() {
    Object ref = oaidMd5_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      oaidMd5_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int IPV6_FIELD_NUMBER = 13;
  private volatile Object ipv6_;
  /**
   * <code>string ipv6 = 13;</code>
   */
  public String getIpv6() {
    Object ref = ipv6_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      ipv6_ = s;
      return s;
    }
  }
  /**
   * <code>string ipv6 = 13;</code>
   */
  public com.google.protobuf.ByteString
      getIpv6Bytes() {
    Object ref = ipv6_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      ipv6_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CONNECTION_TYPE_FIELD_NUMBER = 14;
  private int connectionType_;
  /**
   * <pre>
   *上网类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
   */
  public int getConnectionTypeValue() {
    return connectionType_;
  }
  /**
   * <pre>
   *上网类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
   */
  public ConnectionType getConnectionType() {
    ConnectionType result = ConnectionType.valueOf(connectionType_);
    return result == null ? ConnectionType.UNRECOGNIZED : result;
  }

  public static final int OPERATOR_TYPE_FIELD_NUMBER = 15;
  private int operatorType_;
  /**
   * <pre>
   * 运营商类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
   */
  public int getOperatorTypeValue() {
    return operatorType_;
  }
  /**
   * <pre>
   * 运营商类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
   */
  public OperatorType getOperatorType() {
    OperatorType result = OperatorType.valueOf(operatorType_);
    return result == null ? OperatorType.UNRECOGNIZED : result;
  }

  public static final int ADCODE_FIELD_NUMBER = 16;
  private long adcode_;
  /**
   * <pre>
   * 常驻地adcode
   * </pre>
   *
   * <code>uint64 adcode = 16;</code>
   */
  public long getAdcode() {
    return adcode_;
  }

  public static final int CURRENT_CAID_FIELD_NUMBER = 21;
  private volatile Object currentCaid_;
  /**
   * <pre>
   * 当前caid
   * </pre>
   *
   * <code>string current_caid = 21;</code>
   */
  public String getCurrentCaid() {
    Object ref = currentCaid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      currentCaid_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 当前caid
   * </pre>
   *
   * <code>string current_caid = 21;</code>
   */
  public com.google.protobuf.ByteString
      getCurrentCaidBytes() {
    Object ref = currentCaid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      currentCaid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENT_CAID_VERSION_FIELD_NUMBER = 22;
  private volatile Object currentCaidVersion_;
  /**
   * <pre>
   * 当前caid版本
   * </pre>
   *
   * <code>string current_caid_version = 22;</code>
   */
  public String getCurrentCaidVersion() {
    Object ref = currentCaidVersion_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      currentCaidVersion_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 当前caid版本
   * </pre>
   *
   * <code>string current_caid_version = 22;</code>
   */
  public com.google.protobuf.ByteString
      getCurrentCaidVersionBytes() {
    Object ref = currentCaidVersion_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      currentCaidVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LAST_CAID_FIELD_NUMBER = 23;
  private volatile Object lastCaid_;
  /**
   * <pre>
   * 上一版本caid
   * </pre>
   *
   * <code>string last_caid = 23;</code>
   */
  public String getLastCaid() {
    Object ref = lastCaid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      lastCaid_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 上一版本caid
   * </pre>
   *
   * <code>string last_caid = 23;</code>
   */
  public com.google.protobuf.ByteString
      getLastCaidBytes() {
    Object ref = lastCaid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      lastCaid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LAST_CAID_VERSION_FIELD_NUMBER = 24;
  private volatile Object lastCaidVersion_;
  /**
   * <pre>
   * 上一版本caid版本
   * </pre>
   *
   * <code>string last_caid_version = 24;</code>
   */
  public String getLastCaidVersion() {
    Object ref = lastCaidVersion_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      lastCaidVersion_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 上一版本caid版本
   * </pre>
   *
   * <code>string last_caid_version = 24;</code>
   */
  public com.google.protobuf.ByteString
      getLastCaidVersionBytes() {
    Object ref = lastCaidVersion_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      lastCaidVersion_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int BRAND_FIELD_NUMBER = 25;
  private volatile Object brand_;
  /**
   * <pre>
   *手机厂商名
   * </pre>
   *
   * <code>string brand = 25;</code>
   */
  public String getBrand() {
    Object ref = brand_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      brand_ = s;
      return s;
    }
  }
  /**
   * <pre>
   *手机厂商名
   * </pre>
   *
   * <code>string brand = 25;</code>
   */
  public com.google.protobuf.ByteString
      getBrandBytes() {
    Object ref = brand_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      brand_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int MEDIA_INDUSTRY_ID_FIELD_NUMBER = 26;
  private long mediaIndustryId_;
  /**
   * <pre>
   *媒体一级行业id
   * </pre>
   *
   * <code>uint64 media_industry_id = 26;</code>
   */
  public long getMediaIndustryId() {
    return mediaIndustryId_;
  }

  public static final int CUSTOM_INFO_FIELD_NUMBER = 28;
  private volatile Object customInfo_;
  /**
   * <pre>
   * 客户自定义信息，使用前需要线下约定。
   * </pre>
   *
   * <code>string custom_info = 28;</code>
   */
  public String getCustomInfo() {
    Object ref = customInfo_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      customInfo_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 客户自定义信息，使用前需要线下约定。
   * </pre>
   *
   * <code>string custom_info = 28;</code>
   */
  public com.google.protobuf.ByteString
      getCustomInfoBytes() {
    Object ref = customInfo_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      customInfo_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INSTALL_APP_FIELD_NUMBER = 30;
  private com.google.protobuf.LazyStringList installApp_;
  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  public com.google.protobuf.ProtocolStringList
      getInstallAppList() {
    return installApp_;
  }
  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  public int getInstallAppCount() {
    return installApp_.size();
  }
  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  public String getInstallApp(int index) {
    return installApp_.get(index);
  }
  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  public com.google.protobuf.ByteString
      getInstallAppBytes(int index) {
    return installApp_.getByteString(index);
  }

  public static final int PAID_FIELD_NUMBER = 38;
  private volatile Object paid_;
  /**
   * <pre>
   * 拼多多设备指纹
   * </pre>
   *
   * <code>string paid = 38;</code>
   */
  public String getPaid() {
    Object ref = paid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      paid_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 拼多多设备指纹
   * </pre>
   *
   * <code>string paid = 38;</code>
   */
  public com.google.protobuf.ByteString
      getPaidBytes() {
    Object ref = paid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      paid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AAID_FIELD_NUMBER = 39;
  private volatile Object aaid_;
  /**
   * <pre>
   * 阿里设备指纹
   * </pre>
   *
   * <code>string aaid = 39;</code>
   */
  public String getAaid() {
    Object ref = aaid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      aaid_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 阿里设备指纹
   * </pre>
   *
   * <code>string aaid = 39;</code>
   */
  public com.google.protobuf.ByteString
      getAaidBytes() {
    Object ref = aaid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      aaid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int SITE_SET_FIELD_NUMBER = 40;
  private int siteSet_;
  /**
   * <pre>
   * 资源位信息: 1: 信息流、2: 激励视频、3：搜索、4：开屏; 现为邀请制功能，如有需求请联系客户运营
   * </pre>
   *
   * <code>uint32 site_set = 40;</code>
   */
  public int getSiteSet() {
    return siteSet_;
  }

  public static final int IPV4_FIELD_NUMBER = 41;
  private volatile Object ipv4_;
  /**
   * <code>string ipv4 = 41;</code>
   */
  public String getIpv4() {
    Object ref = ipv4_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      ipv4_ = s;
      return s;
    }
  }
  /**
   * <code>string ipv4 = 41;</code>
   */
  public com.google.protobuf.ByteString
      getIpv4Bytes() {
    Object ref = ipv4_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      ipv4_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int INSTALL_SOCIAL_INSURANCE_FIELD_NUMBER = 45;
  private int installSocialInsurance_;
  /**
   * <pre>
   * 是否安装社保app, 1(未安装)、2（已安装）
   * </pre>
   *
   * <code>uint32 install_social_insurance = 45;</code>
   */
  public int getInstallSocialInsurance() {
    return installSocialInsurance_;
  }

  public static final int PAYMENT_FIELD_NUMBER = 46;
  private int payment_;
  /**
   * <pre>
   * 是否支付, 1(未安装)、2（已安装）
   * </pre>
   *
   * <code>uint32 payment = 46;</code>
   */
  public int getPayment() {
    return payment_;
  }

  public static final int PHONE_PRICE_ID_FIELD_NUMBER = 50;
  private int phonePriceId_;
  /**
   * <pre>
   * 设备价格, 1(1000以下)、2（1000-1500）、3（1500-2000）、4（2000-2500）、5（2500-3000）、6（3000-3500）、7（3500-4000）、8（4000-5000）、9（5000-6000）、10（6000+）
   * </pre>
   *
   * <code>uint32 phone_price_id = 50;</code>
   */
  public int getPhonePriceId() {
    return phonePriceId_;
  }

  public static final int CONSUMPTION_LEVEL_ID_FIELD_NUMBER = 51;
  private int consumptionLevelId_;
  /**
   * <pre>
   * 消费水平, 1(高)、2（中）、3（低）
   * </pre>
   *
   * <code>uint32 consumption_level_id = 51;</code>
   */
  public int getConsumptionLevelId() {
    return consumptionLevelId_;
  }

  public static final int EDUCATION_ID_FIELD_NUMBER = 53;
  private int educationId_;
  /**
   * <pre>
   * 教育水平, 1(高中及以下)、2(专科)、3(本科)、4(硕士及以上)
   * </pre>
   *
   * <code>uint32 education_id = 53;</code>
   */
  public int getEducationId() {
    return educationId_;
  }

  public static final int GENDER_ID_FIELD_NUMBER = 54;
  private int genderId_;
  /**
   * <pre>
   * 性别, 1(男)、2(女)
   * </pre>
   *
   * <code>uint32 gender_id = 54;</code>
   */
  public int getGenderId() {
    return genderId_;
  }

  public static final int LATITUDE_FIELD_NUMBER = 55;
  private double latitude_;
  /**
   * <pre>
   * 纬度
   * </pre>
   *
   * <code>double latitude = 55;</code>
   */
  public double getLatitude() {
    return latitude_;
  }

  public static final int LONGITUDE_FIELD_NUMBER = 56;
  private double longitude_;
  /**
   * <pre>
   * 经度
   * </pre>
   *
   * <code>double longitude = 56;</code>
   */
  public double getLongitude() {
    return longitude_;
  }

  public static final int USER_EMBEDDING_FIELD_NUMBER = 57;
  private UserEmb userEmbedding_;
  /**
   * <pre>
   * 用户embedding向量
   * </pre>
   *
   * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
   */
  public boolean hasUserEmbedding() {
    return userEmbedding_ != null;
  }
  /**
   * <pre>
   * 用户embedding向量
   * </pre>
   *
   * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
   */
  public UserEmb getUserEmbedding() {
    return userEmbedding_ == null ? UserEmb.getDefaultInstance() : userEmbedding_;
  }
  /**
   * <pre>
   * 用户embedding向量
   * </pre>
   *
   * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
   */
  public UserEmbOrBuilder getUserEmbeddingOrBuilder() {
    return getUserEmbedding();
  }

  public static final int CURRENT_PAID_1_5_FIELD_NUMBER = 60;
  private volatile Object currentPaid15_;
  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 当前版本
   * </pre>
   *
   * <code>string current_paid_1_5 = 60;</code>
   */
  public String getCurrentPaid15() {
    Object ref = currentPaid15_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      currentPaid15_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 当前版本
   * </pre>
   *
   * <code>string current_paid_1_5 = 60;</code>
   */
  public com.google.protobuf.ByteString
      getCurrentPaid15Bytes() {
    Object ref = currentPaid15_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      currentPaid15_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int LAST_PAID_1_5_FIELD_NUMBER = 61;
  private volatile Object lastPaid15_;
  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 上一个版本
   * </pre>
   *
   * <code>string last_paid_1_5 = 61;</code>
   */
  public String getLastPaid15() {
    Object ref = lastPaid15_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      lastPaid15_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 上一个版本
   * </pre>
   *
   * <code>string last_paid_1_5 = 61;</code>
   */
  public com.google.protobuf.ByteString
      getLastPaid15Bytes() {
    Object ref = lastPaid15_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      lastPaid15_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PREVIOUS_MODEL_FIELD_NUMBER = 62;
  private volatile Object previousModel_;
  /**
   * <pre>
   * 换机设备标识
   * </pre>
   *
   * <code>string previous_model = 62;</code>
   */
  public String getPreviousModel() {
    Object ref = previousModel_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      previousModel_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 换机设备标识
   * </pre>
   *
   * <code>string previous_model = 62;</code>
   */
  public com.google.protobuf.ByteString
      getPreviousModelBytes() {
    Object ref = previousModel_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      previousModel_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int FEATURE_FIELD_NUMBER = 101;
  private volatile Object feature_;
  /**
   * <pre>
   * 实验or策略信息，需线下约定
   * </pre>
   *
   * <code>string feature = 101;</code>
   */
  public String getFeature() {
    Object ref = feature_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      feature_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 实验or策略信息，需线下约定
   * </pre>
   *
   * <code>string feature = 101;</code>
   */
  public com.google.protobuf.ByteString
      getFeatureBytes() {
    Object ref = feature_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      feature_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (osType_ != OsType.UNKNOWN_OS.getNumber()) {
      output.writeEnum(2, osType_);
    }
    if (!getDidMd5Bytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, didMd5_);
    }
    if (!getTagIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, tagId_);
    }
    if (age_ != 0) {
      output.writeInt32(6, age_);
    }
    if (!getModelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, model_);
    }
    if (!getOaidBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, oaid_);
    }
    if (!getUserAgentBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, userAgent_);
    }
    if (!getOaidMd5Bytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 12, oaidMd5_);
    }
    if (!getIpv6Bytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, ipv6_);
    }
    if (connectionType_ != ConnectionType.UNKNOWN_CONNECTION_TYPE.getNumber()) {
      output.writeEnum(14, connectionType_);
    }
    if (operatorType_ != OperatorType.UNKNOWN_OPERATOR_TYPE.getNumber()) {
      output.writeEnum(15, operatorType_);
    }
    if (adcode_ != 0L) {
      output.writeUInt64(16, adcode_);
    }
    if (!getCurrentCaidBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 21, currentCaid_);
    }
    if (!getCurrentCaidVersionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 22, currentCaidVersion_);
    }
    if (!getLastCaidBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 23, lastCaid_);
    }
    if (!getLastCaidVersionBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 24, lastCaidVersion_);
    }
    if (!getBrandBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 25, brand_);
    }
    if (mediaIndustryId_ != 0L) {
      output.writeUInt64(26, mediaIndustryId_);
    }
    if (!getCustomInfoBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 28, customInfo_);
    }
    for (int i = 0; i < installApp_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 30, installApp_.getRaw(i));
    }
    if (!getPaidBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 38, paid_);
    }
    if (!getAaidBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 39, aaid_);
    }
    if (siteSet_ != 0) {
      output.writeUInt32(40, siteSet_);
    }
    if (!getIpv4Bytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 41, ipv4_);
    }
    if (installSocialInsurance_ != 0) {
      output.writeUInt32(45, installSocialInsurance_);
    }
    if (payment_ != 0) {
      output.writeUInt32(46, payment_);
    }
    if (phonePriceId_ != 0) {
      output.writeUInt32(50, phonePriceId_);
    }
    if (consumptionLevelId_ != 0) {
      output.writeUInt32(51, consumptionLevelId_);
    }
    if (educationId_ != 0) {
      output.writeUInt32(53, educationId_);
    }
    if (genderId_ != 0) {
      output.writeUInt32(54, genderId_);
    }
    if (latitude_ != 0D) {
      output.writeDouble(55, latitude_);
    }
    if (longitude_ != 0D) {
      output.writeDouble(56, longitude_);
    }
    if (userEmbedding_ != null) {
      output.writeMessage(57, getUserEmbedding());
    }
    if (!getCurrentPaid15Bytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 60, currentPaid15_);
    }
    if (!getLastPaid15Bytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 61, lastPaid15_);
    }
    if (!getPreviousModelBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 62, previousModel_);
    }
    if (!getFeatureBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 101, feature_);
    }
    unknownFields.writeTo(output);
  }

  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (osType_ != OsType.UNKNOWN_OS.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, osType_);
    }
    if (!getDidMd5Bytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, didMd5_);
    }
    if (!getTagIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, tagId_);
    }
    if (age_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, age_);
    }
    if (!getModelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, model_);
    }
    if (!getOaidBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, oaid_);
    }
    if (!getUserAgentBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, userAgent_);
    }
    if (!getOaidMd5Bytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, oaidMd5_);
    }
    if (!getIpv6Bytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, ipv6_);
    }
    if (connectionType_ != ConnectionType.UNKNOWN_CONNECTION_TYPE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(14, connectionType_);
    }
    if (operatorType_ != OperatorType.UNKNOWN_OPERATOR_TYPE.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(15, operatorType_);
    }
    if (adcode_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(16, adcode_);
    }
    if (!getCurrentCaidBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, currentCaid_);
    }
    if (!getCurrentCaidVersionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(22, currentCaidVersion_);
    }
    if (!getLastCaidBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(23, lastCaid_);
    }
    if (!getLastCaidVersionBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(24, lastCaidVersion_);
    }
    if (!getBrandBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(25, brand_);
    }
    if (mediaIndustryId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(26, mediaIndustryId_);
    }
    if (!getCustomInfoBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(28, customInfo_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < installApp_.size(); i++) {
        dataSize += computeStringSizeNoTag(installApp_.getRaw(i));
      }
      size += dataSize;
      size += 2 * getInstallAppList().size();
    }
    if (!getPaidBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(38, paid_);
    }
    if (!getAaidBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(39, aaid_);
    }
    if (siteSet_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(40, siteSet_);
    }
    if (!getIpv4Bytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(41, ipv4_);
    }
    if (installSocialInsurance_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(45, installSocialInsurance_);
    }
    if (payment_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(46, payment_);
    }
    if (phonePriceId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(50, phonePriceId_);
    }
    if (consumptionLevelId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(51, consumptionLevelId_);
    }
    if (educationId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(53, educationId_);
    }
    if (genderId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(54, genderId_);
    }
    if (latitude_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(55, latitude_);
    }
    if (longitude_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(56, longitude_);
    }
    if (userEmbedding_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(57, getUserEmbedding());
    }
    if (!getCurrentPaid15Bytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(60, currentPaid15_);
    }
    if (!getLastPaid15Bytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(61, lastPaid15_);
    }
    if (!getPreviousModelBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(62, previousModel_);
    }
    if (!getFeatureBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(101, feature_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof Req)) {
      return super.equals(obj);
    }
    Req other = (Req) obj;

    boolean result = true;
    result = result && getId()
        .equals(other.getId());
    result = result && osType_ == other.osType_;
    result = result && getDidMd5()
        .equals(other.getDidMd5());
    result = result && getTagId()
        .equals(other.getTagId());
    result = result && (getAge()
        == other.getAge());
    result = result && getModel()
        .equals(other.getModel());
    result = result && getOaid()
        .equals(other.getOaid());
    result = result && getUserAgent()
        .equals(other.getUserAgent());
    result = result && getOaidMd5()
        .equals(other.getOaidMd5());
    result = result && getIpv6()
        .equals(other.getIpv6());
    result = result && connectionType_ == other.connectionType_;
    result = result && operatorType_ == other.operatorType_;
    result = result && (getAdcode()
        == other.getAdcode());
    result = result && getCurrentCaid()
        .equals(other.getCurrentCaid());
    result = result && getCurrentCaidVersion()
        .equals(other.getCurrentCaidVersion());
    result = result && getLastCaid()
        .equals(other.getLastCaid());
    result = result && getLastCaidVersion()
        .equals(other.getLastCaidVersion());
    result = result && getBrand()
        .equals(other.getBrand());
    result = result && (getMediaIndustryId()
        == other.getMediaIndustryId());
    result = result && getCustomInfo()
        .equals(other.getCustomInfo());
    result = result && getInstallAppList()
        .equals(other.getInstallAppList());
    result = result && getPaid()
        .equals(other.getPaid());
    result = result && getAaid()
        .equals(other.getAaid());
    result = result && (getSiteSet()
        == other.getSiteSet());
    result = result && getIpv4()
        .equals(other.getIpv4());
    result = result && (getInstallSocialInsurance()
        == other.getInstallSocialInsurance());
    result = result && (getPayment()
        == other.getPayment());
    result = result && (getPhonePriceId()
        == other.getPhonePriceId());
    result = result && (getConsumptionLevelId()
        == other.getConsumptionLevelId());
    result = result && (getEducationId()
        == other.getEducationId());
    result = result && (getGenderId()
        == other.getGenderId());
    result = result && (
        Double.doubleToLongBits(getLatitude())
        == Double.doubleToLongBits(
            other.getLatitude()));
    result = result && (
        Double.doubleToLongBits(getLongitude())
        == Double.doubleToLongBits(
            other.getLongitude()));
    result = result && (hasUserEmbedding() == other.hasUserEmbedding());
    if (hasUserEmbedding()) {
      result = result && getUserEmbedding()
          .equals(other.getUserEmbedding());
    }
    result = result && getCurrentPaid15()
        .equals(other.getCurrentPaid15());
    result = result && getLastPaid15()
        .equals(other.getLastPaid15());
    result = result && getPreviousModel()
        .equals(other.getPreviousModel());
    result = result && getFeature()
        .equals(other.getFeature());
    result = result && unknownFields.equals(other.unknownFields);
    return result;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + OS_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + osType_;
    hash = (37 * hash) + DID_MD5_FIELD_NUMBER;
    hash = (53 * hash) + getDidMd5().hashCode();
    hash = (37 * hash) + TAG_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTagId().hashCode();
    hash = (37 * hash) + AGE_FIELD_NUMBER;
    hash = (53 * hash) + getAge();
    hash = (37 * hash) + MODEL_FIELD_NUMBER;
    hash = (53 * hash) + getModel().hashCode();
    hash = (37 * hash) + OAID_FIELD_NUMBER;
    hash = (53 * hash) + getOaid().hashCode();
    hash = (37 * hash) + USER_AGENT_FIELD_NUMBER;
    hash = (53 * hash) + getUserAgent().hashCode();
    hash = (37 * hash) + OAID_MD5_FIELD_NUMBER;
    hash = (53 * hash) + getOaidMd5().hashCode();
    hash = (37 * hash) + IPV6_FIELD_NUMBER;
    hash = (53 * hash) + getIpv6().hashCode();
    hash = (37 * hash) + CONNECTION_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + connectionType_;
    hash = (37 * hash) + OPERATOR_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + operatorType_;
    hash = (37 * hash) + ADCODE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getAdcode());
    hash = (37 * hash) + CURRENT_CAID_FIELD_NUMBER;
    hash = (53 * hash) + getCurrentCaid().hashCode();
    hash = (37 * hash) + CURRENT_CAID_VERSION_FIELD_NUMBER;
    hash = (53 * hash) + getCurrentCaidVersion().hashCode();
    hash = (37 * hash) + LAST_CAID_FIELD_NUMBER;
    hash = (53 * hash) + getLastCaid().hashCode();
    hash = (37 * hash) + LAST_CAID_VERSION_FIELD_NUMBER;
    hash = (53 * hash) + getLastCaidVersion().hashCode();
    hash = (37 * hash) + BRAND_FIELD_NUMBER;
    hash = (53 * hash) + getBrand().hashCode();
    hash = (37 * hash) + MEDIA_INDUSTRY_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getMediaIndustryId());
    hash = (37 * hash) + CUSTOM_INFO_FIELD_NUMBER;
    hash = (53 * hash) + getCustomInfo().hashCode();
    if (getInstallAppCount() > 0) {
      hash = (37 * hash) + INSTALL_APP_FIELD_NUMBER;
      hash = (53 * hash) + getInstallAppList().hashCode();
    }
    hash = (37 * hash) + PAID_FIELD_NUMBER;
    hash = (53 * hash) + getPaid().hashCode();
    hash = (37 * hash) + AAID_FIELD_NUMBER;
    hash = (53 * hash) + getAaid().hashCode();
    hash = (37 * hash) + SITE_SET_FIELD_NUMBER;
    hash = (53 * hash) + getSiteSet();
    hash = (37 * hash) + IPV4_FIELD_NUMBER;
    hash = (53 * hash) + getIpv4().hashCode();
    hash = (37 * hash) + INSTALL_SOCIAL_INSURANCE_FIELD_NUMBER;
    hash = (53 * hash) + getInstallSocialInsurance();
    hash = (37 * hash) + PAYMENT_FIELD_NUMBER;
    hash = (53 * hash) + getPayment();
    hash = (37 * hash) + PHONE_PRICE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getPhonePriceId();
    hash = (37 * hash) + CONSUMPTION_LEVEL_ID_FIELD_NUMBER;
    hash = (53 * hash) + getConsumptionLevelId();
    hash = (37 * hash) + EDUCATION_ID_FIELD_NUMBER;
    hash = (53 * hash) + getEducationId();
    hash = (37 * hash) + GENDER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getGenderId();
    hash = (37 * hash) + LATITUDE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        Double.doubleToLongBits(getLatitude()));
    hash = (37 * hash) + LONGITUDE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        Double.doubleToLongBits(getLongitude()));
    if (hasUserEmbedding()) {
      hash = (37 * hash) + USER_EMBEDDING_FIELD_NUMBER;
      hash = (53 * hash) + getUserEmbedding().hashCode();
    }
    hash = (37 * hash) + CURRENT_PAID_1_5_FIELD_NUMBER;
    hash = (53 * hash) + getCurrentPaid15().hashCode();
    hash = (37 * hash) + LAST_PAID_1_5_FIELD_NUMBER;
    hash = (53 * hash) + getLastPaid15().hashCode();
    hash = (37 * hash) + PREVIOUS_MODEL_FIELD_NUMBER;
    hash = (53 * hash) + getPreviousModel().hashCode();
    hash = (37 * hash) + FEATURE_FIELD_NUMBER;
    hash = (53 * hash) + getFeature().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static Req parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Req parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Req parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Req parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Req parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Req parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Req parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static Req parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static Req parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static Req parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static Req parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static Req parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(Req prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code kuaishou.ad.rta.Req}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:kuaishou.ad.rta.Req)
          ReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return RTAProto.internal_static_kuaishou_ad_rta_Req_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return RTAProto.internal_static_kuaishou_ad_rta_Req_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Req.class, Req.Builder.class);
    }

    // Construct using com.kuaishou.protobuf.ad.rta.Req.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    public Builder clear() {
      super.clear();
      id_ = "";

      osType_ = 0;

      didMd5_ = "";

      tagId_ = "";

      age_ = 0;

      model_ = "";

      oaid_ = "";

      userAgent_ = "";

      oaidMd5_ = "";

      ipv6_ = "";

      connectionType_ = 0;

      operatorType_ = 0;

      adcode_ = 0L;

      currentCaid_ = "";

      currentCaidVersion_ = "";

      lastCaid_ = "";

      lastCaidVersion_ = "";

      brand_ = "";

      mediaIndustryId_ = 0L;

      customInfo_ = "";

      installApp_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00100000);
      paid_ = "";

      aaid_ = "";

      siteSet_ = 0;

      ipv4_ = "";

      installSocialInsurance_ = 0;

      payment_ = 0;

      phonePriceId_ = 0;

      consumptionLevelId_ = 0;

      educationId_ = 0;

      genderId_ = 0;

      latitude_ = 0D;

      longitude_ = 0D;

      if (userEmbeddingBuilder_ == null) {
        userEmbedding_ = null;
      } else {
        userEmbedding_ = null;
        userEmbeddingBuilder_ = null;
      }
      currentPaid15_ = "";

      lastPaid15_ = "";

      previousModel_ = "";

      feature_ = "";

      return this;
    }

    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return RTAProto.internal_static_kuaishou_ad_rta_Req_descriptor;
    }

    public Req getDefaultInstanceForType() {
      return Req.getDefaultInstance();
    }

    public Req build() {
      Req result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    public Req buildPartial() {
      Req result = new Req(this);
      int from_bitField0_ = bitField0_;
      int from_bitField1_ = bitField1_;
      int to_bitField0_ = 0;
      int to_bitField1_ = 0;
      result.id_ = id_;
      result.osType_ = osType_;
      result.didMd5_ = didMd5_;
      result.tagId_ = tagId_;
      result.age_ = age_;
      result.model_ = model_;
      result.oaid_ = oaid_;
      result.userAgent_ = userAgent_;
      result.oaidMd5_ = oaidMd5_;
      result.ipv6_ = ipv6_;
      result.connectionType_ = connectionType_;
      result.operatorType_ = operatorType_;
      result.adcode_ = adcode_;
      result.currentCaid_ = currentCaid_;
      result.currentCaidVersion_ = currentCaidVersion_;
      result.lastCaid_ = lastCaid_;
      result.lastCaidVersion_ = lastCaidVersion_;
      result.brand_ = brand_;
      result.mediaIndustryId_ = mediaIndustryId_;
      result.customInfo_ = customInfo_;
      if (((bitField0_ & 0x00100000) == 0x00100000)) {
        installApp_ = installApp_.getUnmodifiableView();
        bitField0_ = (bitField0_ & ~0x00100000);
      }
      result.installApp_ = installApp_;
      result.paid_ = paid_;
      result.aaid_ = aaid_;
      result.siteSet_ = siteSet_;
      result.ipv4_ = ipv4_;
      result.installSocialInsurance_ = installSocialInsurance_;
      result.payment_ = payment_;
      result.phonePriceId_ = phonePriceId_;
      result.consumptionLevelId_ = consumptionLevelId_;
      result.educationId_ = educationId_;
      result.genderId_ = genderId_;
      result.latitude_ = latitude_;
      result.longitude_ = longitude_;
      if (userEmbeddingBuilder_ == null) {
        result.userEmbedding_ = userEmbedding_;
      } else {
        result.userEmbedding_ = userEmbeddingBuilder_.build();
      }
      result.currentPaid15_ = currentPaid15_;
      result.lastPaid15_ = lastPaid15_;
      result.previousModel_ = previousModel_;
      result.feature_ = feature_;
      result.bitField0_ = to_bitField0_;
      result.bitField1_ = to_bitField1_;
      onBuilt();
      return result;
    }

    public Builder clone() {
      return (Builder) super.clone();
    }
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.setField(field, value);
    }
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return (Builder) super.clearField(field);
    }
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return (Builder) super.clearOneof(oneof);
    }
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return (Builder) super.setRepeatedField(field, index, value);
    }
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.addRepeatedField(field, value);
    }
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof Req) {
        return mergeFrom((Req)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(Req other) {
      if (other == Req.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (other.osType_ != 0) {
        setOsTypeValue(other.getOsTypeValue());
      }
      if (!other.getDidMd5().isEmpty()) {
        didMd5_ = other.didMd5_;
        onChanged();
      }
      if (!other.getTagId().isEmpty()) {
        tagId_ = other.tagId_;
        onChanged();
      }
      if (other.getAge() != 0) {
        setAge(other.getAge());
      }
      if (!other.getModel().isEmpty()) {
        model_ = other.model_;
        onChanged();
      }
      if (!other.getOaid().isEmpty()) {
        oaid_ = other.oaid_;
        onChanged();
      }
      if (!other.getUserAgent().isEmpty()) {
        userAgent_ = other.userAgent_;
        onChanged();
      }
      if (!other.getOaidMd5().isEmpty()) {
        oaidMd5_ = other.oaidMd5_;
        onChanged();
      }
      if (!other.getIpv6().isEmpty()) {
        ipv6_ = other.ipv6_;
        onChanged();
      }
      if (other.connectionType_ != 0) {
        setConnectionTypeValue(other.getConnectionTypeValue());
      }
      if (other.operatorType_ != 0) {
        setOperatorTypeValue(other.getOperatorTypeValue());
      }
      if (other.getAdcode() != 0L) {
        setAdcode(other.getAdcode());
      }
      if (!other.getCurrentCaid().isEmpty()) {
        currentCaid_ = other.currentCaid_;
        onChanged();
      }
      if (!other.getCurrentCaidVersion().isEmpty()) {
        currentCaidVersion_ = other.currentCaidVersion_;
        onChanged();
      }
      if (!other.getLastCaid().isEmpty()) {
        lastCaid_ = other.lastCaid_;
        onChanged();
      }
      if (!other.getLastCaidVersion().isEmpty()) {
        lastCaidVersion_ = other.lastCaidVersion_;
        onChanged();
      }
      if (!other.getBrand().isEmpty()) {
        brand_ = other.brand_;
        onChanged();
      }
      if (other.getMediaIndustryId() != 0L) {
        setMediaIndustryId(other.getMediaIndustryId());
      }
      if (!other.getCustomInfo().isEmpty()) {
        customInfo_ = other.customInfo_;
        onChanged();
      }
      if (!other.installApp_.isEmpty()) {
        if (installApp_.isEmpty()) {
          installApp_ = other.installApp_;
          bitField0_ = (bitField0_ & ~0x00100000);
        } else {
          ensureInstallAppIsMutable();
          installApp_.addAll(other.installApp_);
        }
        onChanged();
      }
      if (!other.getPaid().isEmpty()) {
        paid_ = other.paid_;
        onChanged();
      }
      if (!other.getAaid().isEmpty()) {
        aaid_ = other.aaid_;
        onChanged();
      }
      if (other.getSiteSet() != 0) {
        setSiteSet(other.getSiteSet());
      }
      if (!other.getIpv4().isEmpty()) {
        ipv4_ = other.ipv4_;
        onChanged();
      }
      if (other.getInstallSocialInsurance() != 0) {
        setInstallSocialInsurance(other.getInstallSocialInsurance());
      }
      if (other.getPayment() != 0) {
        setPayment(other.getPayment());
      }
      if (other.getPhonePriceId() != 0) {
        setPhonePriceId(other.getPhonePriceId());
      }
      if (other.getConsumptionLevelId() != 0) {
        setConsumptionLevelId(other.getConsumptionLevelId());
      }
      if (other.getEducationId() != 0) {
        setEducationId(other.getEducationId());
      }
      if (other.getGenderId() != 0) {
        setGenderId(other.getGenderId());
      }
      if (other.getLatitude() != 0D) {
        setLatitude(other.getLatitude());
      }
      if (other.getLongitude() != 0D) {
        setLongitude(other.getLongitude());
      }
      if (other.hasUserEmbedding()) {
        mergeUserEmbedding(other.getUserEmbedding());
      }
      if (!other.getCurrentPaid15().isEmpty()) {
        currentPaid15_ = other.currentPaid15_;
        onChanged();
      }
      if (!other.getLastPaid15().isEmpty()) {
        lastPaid15_ = other.lastPaid15_;
        onChanged();
      }
      if (!other.getPreviousModel().isEmpty()) {
        previousModel_ = other.previousModel_;
        onChanged();
      }
      if (!other.getFeature().isEmpty()) {
        feature_ = other.feature_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    public final boolean isInitialized() {
      return true;
    }

    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      Req parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (Req) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;
    private int bitField1_;

    private Object id_ = "";
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public String getId() {
      Object ref = id_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public Builder setId(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private int osType_ = 0;
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
     */
    public int getOsTypeValue() {
      return osType_;
    }
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
     */
    public Builder setOsTypeValue(int value) {
      osType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
     */
    public OsType getOsType() {
      OsType result = OsType.valueOf(osType_);
      return result == null ? OsType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
     */
    public Builder setOsType(OsType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      osType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 系统
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
     */
    public Builder clearOsType() {
      
      osType_ = 0;
      onChanged();
      return this;
    }

    private Object didMd5_ = "";
    /**
     * <pre>
     * 安卓为 imei_md5，ios 为 idfa_md5
     * </pre>
     *
     * <code>string did_md5 = 3;</code>
     */
    public String getDidMd5() {
      Object ref = didMd5_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        didMd5_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 安卓为 imei_md5，ios 为 idfa_md5
     * </pre>
     *
     * <code>string did_md5 = 3;</code>
     */
    public com.google.protobuf.ByteString
        getDidMd5Bytes() {
      Object ref = didMd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        didMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 安卓为 imei_md5，ios 为 idfa_md5
     * </pre>
     *
     * <code>string did_md5 = 3;</code>
     */
    public Builder setDidMd5(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      didMd5_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 安卓为 imei_md5，ios 为 idfa_md5
     * </pre>
     *
     * <code>string did_md5 = 3;</code>
     */
    public Builder clearDidMd5() {
      
      didMd5_ = getDefaultInstance().getDidMd5();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 安卓为 imei_md5，ios 为 idfa_md5
     * </pre>
     *
     * <code>string did_md5 = 3;</code>
     */
    public Builder setDidMd5Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      didMd5_ = value;
      onChanged();
      return this;
    }

    private Object tagId_ = "";
    /**
     * <pre>
     * 广告位 ID
     * </pre>
     *
     * <code>string tag_id = 4;</code>
     */
    public String getTagId() {
      Object ref = tagId_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        tagId_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 广告位 ID
     * </pre>
     *
     * <code>string tag_id = 4;</code>
     */
    public com.google.protobuf.ByteString
        getTagIdBytes() {
      Object ref = tagId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        tagId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 广告位 ID
     * </pre>
     *
     * <code>string tag_id = 4;</code>
     */
    public Builder setTagId(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      tagId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告位 ID
     * </pre>
     *
     * <code>string tag_id = 4;</code>
     */
    public Builder clearTagId() {
      
      tagId_ = getDefaultInstance().getTagId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告位 ID
     * </pre>
     *
     * <code>string tag_id = 4;</code>
     */
    public Builder setTagIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      tagId_ = value;
      onChanged();
      return this;
    }

    private int age_ ;
    /**
     * <pre>
     * 年龄段, 0(0-11), 1(12-17), 2(18~23), 3(24~30), 4(31~35), 5(36~40), 6(41~45), 7(46~49), 8(50~54), 9(55~59), 10(60+)
     * </pre>
     *
     * <code>int32 age = 6;</code>
     */
    public int getAge() {
      return age_;
    }
    /**
     * <pre>
     * 年龄段, 0(0-11), 1(12-17), 2(18~23), 3(24~30), 4(31~35), 5(36~40), 6(41~45), 7(46~49), 8(50~54), 9(55~59), 10(60+)
     * </pre>
     *
     * <code>int32 age = 6;</code>
     */
    public Builder setAge(int value) {
      
      age_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 年龄段, 0(0-11), 1(12-17), 2(18~23), 3(24~30), 4(31~35), 5(36~40), 6(41~45), 7(46~49), 8(50~54), 9(55~59), 10(60+)
     * </pre>
     *
     * <code>int32 age = 6;</code>
     */
    public Builder clearAge() {
      
      age_ = 0;
      onChanged();
      return this;
    }

    private Object model_ = "";
    /**
     * <pre>
     * 设备型号
     * </pre>
     *
     * <code>string model = 9;</code>
     */
    public String getModel() {
      Object ref = model_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        model_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 设备型号
     * </pre>
     *
     * <code>string model = 9;</code>
     */
    public com.google.protobuf.ByteString
        getModelBytes() {
      Object ref = model_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        model_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 设备型号
     * </pre>
     *
     * <code>string model = 9;</code>
     */
    public Builder setModel(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      model_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 设备型号
     * </pre>
     *
     * <code>string model = 9;</code>
     */
    public Builder clearModel() {
      
      model_ = getDefaultInstance().getModel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 设备型号
     * </pre>
     *
     * <code>string model = 9;</code>
     */
    public Builder setModelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      model_ = value;
      onChanged();
      return this;
    }

    private Object oaid_ = "";
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 10;</code>
     */
    public String getOaid() {
      Object ref = oaid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        oaid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 10;</code>
     */
    public com.google.protobuf.ByteString
        getOaidBytes() {
      Object ref = oaid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        oaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 10;</code>
     */
    public Builder setOaid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      oaid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 10;</code>
     */
    public Builder clearOaid() {
      
      oaid_ = getDefaultInstance().getOaid();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 设备oaid
     * </pre>
     *
     * <code>string oaid = 10;</code>
     */
    public Builder setOaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      oaid_ = value;
      onChanged();
      return this;
    }

    private Object userAgent_ = "";
    /**
     * <pre>
     *请求UA信息
     * </pre>
     *
     * <code>string user_agent = 11;</code>
     */
    public String getUserAgent() {
      Object ref = userAgent_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        userAgent_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     *请求UA信息
     * </pre>
     *
     * <code>string user_agent = 11;</code>
     */
    public com.google.protobuf.ByteString
        getUserAgentBytes() {
      Object ref = userAgent_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        userAgent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *请求UA信息
     * </pre>
     *
     * <code>string user_agent = 11;</code>
     */
    public Builder setUserAgent(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      userAgent_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *请求UA信息
     * </pre>
     *
     * <code>string user_agent = 11;</code>
     */
    public Builder clearUserAgent() {
      
      userAgent_ = getDefaultInstance().getUserAgent();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *请求UA信息
     * </pre>
     *
     * <code>string user_agent = 11;</code>
     */
    public Builder setUserAgentBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      userAgent_ = value;
      onChanged();
      return this;
    }

    private Object oaidMd5_ = "";
    /**
     * <pre>
     *安卓设备md5(oaid)
     * </pre>
     *
     * <code>string oaid_md5 = 12;</code>
     */
    public String getOaidMd5() {
      Object ref = oaidMd5_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        oaidMd5_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     *安卓设备md5(oaid)
     * </pre>
     *
     * <code>string oaid_md5 = 12;</code>
     */
    public com.google.protobuf.ByteString
        getOaidMd5Bytes() {
      Object ref = oaidMd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        oaidMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *安卓设备md5(oaid)
     * </pre>
     *
     * <code>string oaid_md5 = 12;</code>
     */
    public Builder setOaidMd5(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      oaidMd5_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *安卓设备md5(oaid)
     * </pre>
     *
     * <code>string oaid_md5 = 12;</code>
     */
    public Builder clearOaidMd5() {
      
      oaidMd5_ = getDefaultInstance().getOaidMd5();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *安卓设备md5(oaid)
     * </pre>
     *
     * <code>string oaid_md5 = 12;</code>
     */
    public Builder setOaidMd5Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      oaidMd5_ = value;
      onChanged();
      return this;
    }

    private Object ipv6_ = "";
    /**
     * <code>string ipv6 = 13;</code>
     */
    public String getIpv6() {
      Object ref = ipv6_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        ipv6_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string ipv6 = 13;</code>
     */
    public com.google.protobuf.ByteString
        getIpv6Bytes() {
      Object ref = ipv6_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipv6_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string ipv6 = 13;</code>
     */
    public Builder setIpv6(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ipv6_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string ipv6 = 13;</code>
     */
    public Builder clearIpv6() {
      
      ipv6_ = getDefaultInstance().getIpv6();
      onChanged();
      return this;
    }
    /**
     * <code>string ipv6 = 13;</code>
     */
    public Builder setIpv6Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ipv6_ = value;
      onChanged();
      return this;
    }

    private int connectionType_ = 0;
    /**
     * <pre>
     *上网类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
     */
    public int getConnectionTypeValue() {
      return connectionType_;
    }
    /**
     * <pre>
     *上网类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
     */
    public Builder setConnectionTypeValue(int value) {
      connectionType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *上网类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
     */
    public ConnectionType getConnectionType() {
      ConnectionType result = ConnectionType.valueOf(connectionType_);
      return result == null ? ConnectionType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     *上网类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
     */
    public Builder setConnectionType(ConnectionType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      connectionType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *上网类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
     */
    public Builder clearConnectionType() {
      
      connectionType_ = 0;
      onChanged();
      return this;
    }

    private int operatorType_ = 0;
    /**
     * <pre>
     * 运营商类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
     */
    public int getOperatorTypeValue() {
      return operatorType_;
    }
    /**
     * <pre>
     * 运营商类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
     */
    public Builder setOperatorTypeValue(int value) {
      operatorType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 运营商类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
     */
    public OperatorType getOperatorType() {
      OperatorType result = OperatorType.valueOf(operatorType_);
      return result == null ? OperatorType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 运营商类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
     */
    public Builder setOperatorType(OperatorType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      operatorType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 运营商类型
     * </pre>
     *
     * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
     */
    public Builder clearOperatorType() {
      
      operatorType_ = 0;
      onChanged();
      return this;
    }

    private long adcode_ ;
    /**
     * <pre>
     * 常驻地adcode
     * </pre>
     *
     * <code>uint64 adcode = 16;</code>
     */
    public long getAdcode() {
      return adcode_;
    }
    /**
     * <pre>
     * 常驻地adcode
     * </pre>
     *
     * <code>uint64 adcode = 16;</code>
     */
    public Builder setAdcode(long value) {
      
      adcode_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 常驻地adcode
     * </pre>
     *
     * <code>uint64 adcode = 16;</code>
     */
    public Builder clearAdcode() {
      
      adcode_ = 0L;
      onChanged();
      return this;
    }

    private Object currentCaid_ = "";
    /**
     * <pre>
     * 当前caid
     * </pre>
     *
     * <code>string current_caid = 21;</code>
     */
    public String getCurrentCaid() {
      Object ref = currentCaid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        currentCaid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 当前caid
     * </pre>
     *
     * <code>string current_caid = 21;</code>
     */
    public com.google.protobuf.ByteString
        getCurrentCaidBytes() {
      Object ref = currentCaid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        currentCaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 当前caid
     * </pre>
     *
     * <code>string current_caid = 21;</code>
     */
    public Builder setCurrentCaid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      currentCaid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前caid
     * </pre>
     *
     * <code>string current_caid = 21;</code>
     */
    public Builder clearCurrentCaid() {
      
      currentCaid_ = getDefaultInstance().getCurrentCaid();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前caid
     * </pre>
     *
     * <code>string current_caid = 21;</code>
     */
    public Builder setCurrentCaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      currentCaid_ = value;
      onChanged();
      return this;
    }

    private Object currentCaidVersion_ = "";
    /**
     * <pre>
     * 当前caid版本
     * </pre>
     *
     * <code>string current_caid_version = 22;</code>
     */
    public String getCurrentCaidVersion() {
      Object ref = currentCaidVersion_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        currentCaidVersion_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 当前caid版本
     * </pre>
     *
     * <code>string current_caid_version = 22;</code>
     */
    public com.google.protobuf.ByteString
        getCurrentCaidVersionBytes() {
      Object ref = currentCaidVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        currentCaidVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 当前caid版本
     * </pre>
     *
     * <code>string current_caid_version = 22;</code>
     */
    public Builder setCurrentCaidVersion(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      currentCaidVersion_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前caid版本
     * </pre>
     *
     * <code>string current_caid_version = 22;</code>
     */
    public Builder clearCurrentCaidVersion() {
      
      currentCaidVersion_ = getDefaultInstance().getCurrentCaidVersion();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前caid版本
     * </pre>
     *
     * <code>string current_caid_version = 22;</code>
     */
    public Builder setCurrentCaidVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      currentCaidVersion_ = value;
      onChanged();
      return this;
    }

    private Object lastCaid_ = "";
    /**
     * <pre>
     * 上一版本caid
     * </pre>
     *
     * <code>string last_caid = 23;</code>
     */
    public String getLastCaid() {
      Object ref = lastCaid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        lastCaid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 上一版本caid
     * </pre>
     *
     * <code>string last_caid = 23;</code>
     */
    public com.google.protobuf.ByteString
        getLastCaidBytes() {
      Object ref = lastCaid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        lastCaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 上一版本caid
     * </pre>
     *
     * <code>string last_caid = 23;</code>
     */
    public Builder setLastCaid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lastCaid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 上一版本caid
     * </pre>
     *
     * <code>string last_caid = 23;</code>
     */
    public Builder clearLastCaid() {
      
      lastCaid_ = getDefaultInstance().getLastCaid();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 上一版本caid
     * </pre>
     *
     * <code>string last_caid = 23;</code>
     */
    public Builder setLastCaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lastCaid_ = value;
      onChanged();
      return this;
    }

    private Object lastCaidVersion_ = "";
    /**
     * <pre>
     * 上一版本caid版本
     * </pre>
     *
     * <code>string last_caid_version = 24;</code>
     */
    public String getLastCaidVersion() {
      Object ref = lastCaidVersion_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        lastCaidVersion_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 上一版本caid版本
     * </pre>
     *
     * <code>string last_caid_version = 24;</code>
     */
    public com.google.protobuf.ByteString
        getLastCaidVersionBytes() {
      Object ref = lastCaidVersion_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        lastCaidVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 上一版本caid版本
     * </pre>
     *
     * <code>string last_caid_version = 24;</code>
     */
    public Builder setLastCaidVersion(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lastCaidVersion_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 上一版本caid版本
     * </pre>
     *
     * <code>string last_caid_version = 24;</code>
     */
    public Builder clearLastCaidVersion() {
      
      lastCaidVersion_ = getDefaultInstance().getLastCaidVersion();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 上一版本caid版本
     * </pre>
     *
     * <code>string last_caid_version = 24;</code>
     */
    public Builder setLastCaidVersionBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lastCaidVersion_ = value;
      onChanged();
      return this;
    }

    private Object brand_ = "";
    /**
     * <pre>
     *手机厂商名
     * </pre>
     *
     * <code>string brand = 25;</code>
     */
    public String getBrand() {
      Object ref = brand_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        brand_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     *手机厂商名
     * </pre>
     *
     * <code>string brand = 25;</code>
     */
    public com.google.protobuf.ByteString
        getBrandBytes() {
      Object ref = brand_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        brand_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     *手机厂商名
     * </pre>
     *
     * <code>string brand = 25;</code>
     */
    public Builder setBrand(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      brand_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *手机厂商名
     * </pre>
     *
     * <code>string brand = 25;</code>
     */
    public Builder clearBrand() {
      
      brand_ = getDefaultInstance().getBrand();
      onChanged();
      return this;
    }
    /**
     * <pre>
     *手机厂商名
     * </pre>
     *
     * <code>string brand = 25;</code>
     */
    public Builder setBrandBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      brand_ = value;
      onChanged();
      return this;
    }

    private long mediaIndustryId_ ;
    /**
     * <pre>
     *媒体一级行业id
     * </pre>
     *
     * <code>uint64 media_industry_id = 26;</code>
     */
    public long getMediaIndustryId() {
      return mediaIndustryId_;
    }
    /**
     * <pre>
     *媒体一级行业id
     * </pre>
     *
     * <code>uint64 media_industry_id = 26;</code>
     */
    public Builder setMediaIndustryId(long value) {
      
      mediaIndustryId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *媒体一级行业id
     * </pre>
     *
     * <code>uint64 media_industry_id = 26;</code>
     */
    public Builder clearMediaIndustryId() {
      
      mediaIndustryId_ = 0L;
      onChanged();
      return this;
    }

    private Object customInfo_ = "";
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 28;</code>
     */
    public String getCustomInfo() {
      Object ref = customInfo_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        customInfo_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 28;</code>
     */
    public com.google.protobuf.ByteString
        getCustomInfoBytes() {
      Object ref = customInfo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        customInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 28;</code>
     */
    public Builder setCustomInfo(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      customInfo_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 28;</code>
     */
    public Builder clearCustomInfo() {
      
      customInfo_ = getDefaultInstance().getCustomInfo();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 客户自定义信息，使用前需要线下约定。
     * </pre>
     *
     * <code>string custom_info = 28;</code>
     */
    public Builder setCustomInfoBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      customInfo_ = value;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringList installApp_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    private void ensureInstallAppIsMutable() {
      if (!((bitField0_ & 0x00100000) == 0x00100000)) {
        installApp_ = new com.google.protobuf.LazyStringArrayList(installApp_);
        bitField0_ |= 0x00100000;
       }
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getInstallAppList() {
      return installApp_.getUnmodifiableView();
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public int getInstallAppCount() {
      return installApp_.size();
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public String getInstallApp(int index) {
      return installApp_.get(index);
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public com.google.protobuf.ByteString
        getInstallAppBytes(int index) {
      return installApp_.getByteString(index);
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public Builder setInstallApp(
        int index, String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureInstallAppIsMutable();
      installApp_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public Builder addInstallApp(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensureInstallAppIsMutable();
      installApp_.add(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public Builder addAllInstallApp(
        Iterable<String> values) {
      ensureInstallAppIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, installApp_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public Builder clearInstallApp() {
      installApp_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 安装的app列表
     * </pre>
     *
     * <code>repeated string install_app = 30;</code>
     */
    public Builder addInstallAppBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      ensureInstallAppIsMutable();
      installApp_.add(value);
      onChanged();
      return this;
    }

    private Object paid_ = "";
    /**
     * <pre>
     * 拼多多设备指纹
     * </pre>
     *
     * <code>string paid = 38;</code>
     */
    public String getPaid() {
      Object ref = paid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        paid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 拼多多设备指纹
     * </pre>
     *
     * <code>string paid = 38;</code>
     */
    public com.google.protobuf.ByteString
        getPaidBytes() {
      Object ref = paid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        paid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 拼多多设备指纹
     * </pre>
     *
     * <code>string paid = 38;</code>
     */
    public Builder setPaid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      paid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 拼多多设备指纹
     * </pre>
     *
     * <code>string paid = 38;</code>
     */
    public Builder clearPaid() {
      
      paid_ = getDefaultInstance().getPaid();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 拼多多设备指纹
     * </pre>
     *
     * <code>string paid = 38;</code>
     */
    public Builder setPaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      paid_ = value;
      onChanged();
      return this;
    }

    private Object aaid_ = "";
    /**
     * <pre>
     * 阿里设备指纹
     * </pre>
     *
     * <code>string aaid = 39;</code>
     */
    public String getAaid() {
      Object ref = aaid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        aaid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 阿里设备指纹
     * </pre>
     *
     * <code>string aaid = 39;</code>
     */
    public com.google.protobuf.ByteString
        getAaidBytes() {
      Object ref = aaid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        aaid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 阿里设备指纹
     * </pre>
     *
     * <code>string aaid = 39;</code>
     */
    public Builder setAaid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      aaid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 阿里设备指纹
     * </pre>
     *
     * <code>string aaid = 39;</code>
     */
    public Builder clearAaid() {
      
      aaid_ = getDefaultInstance().getAaid();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 阿里设备指纹
     * </pre>
     *
     * <code>string aaid = 39;</code>
     */
    public Builder setAaidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      aaid_ = value;
      onChanged();
      return this;
    }

    private int siteSet_ ;
    /**
     * <pre>
     * 资源位信息: 1: 信息流、2: 激励视频、3：搜索、4：开屏; 现为邀请制功能，如有需求请联系客户运营
     * </pre>
     *
     * <code>uint32 site_set = 40;</code>
     */
    public int getSiteSet() {
      return siteSet_;
    }
    /**
     * <pre>
     * 资源位信息: 1: 信息流、2: 激励视频、3：搜索、4：开屏; 现为邀请制功能，如有需求请联系客户运营
     * </pre>
     *
     * <code>uint32 site_set = 40;</code>
     */
    public Builder setSiteSet(int value) {
      
      siteSet_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 资源位信息: 1: 信息流、2: 激励视频、3：搜索、4：开屏; 现为邀请制功能，如有需求请联系客户运营
     * </pre>
     *
     * <code>uint32 site_set = 40;</code>
     */
    public Builder clearSiteSet() {
      
      siteSet_ = 0;
      onChanged();
      return this;
    }

    private Object ipv4_ = "";
    /**
     * <code>string ipv4 = 41;</code>
     */
    public String getIpv4() {
      Object ref = ipv4_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        ipv4_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string ipv4 = 41;</code>
     */
    public com.google.protobuf.ByteString
        getIpv4Bytes() {
      Object ref = ipv4_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipv4_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string ipv4 = 41;</code>
     */
    public Builder setIpv4(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      ipv4_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string ipv4 = 41;</code>
     */
    public Builder clearIpv4() {
      
      ipv4_ = getDefaultInstance().getIpv4();
      onChanged();
      return this;
    }
    /**
     * <code>string ipv4 = 41;</code>
     */
    public Builder setIpv4Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      ipv4_ = value;
      onChanged();
      return this;
    }

    private int installSocialInsurance_ ;
    /**
     * <pre>
     * 是否安装社保app, 1(未安装)、2（已安装）
     * </pre>
     *
     * <code>uint32 install_social_insurance = 45;</code>
     */
    public int getInstallSocialInsurance() {
      return installSocialInsurance_;
    }
    /**
     * <pre>
     * 是否安装社保app, 1(未安装)、2（已安装）
     * </pre>
     *
     * <code>uint32 install_social_insurance = 45;</code>
     */
    public Builder setInstallSocialInsurance(int value) {
      
      installSocialInsurance_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否安装社保app, 1(未安装)、2（已安装）
     * </pre>
     *
     * <code>uint32 install_social_insurance = 45;</code>
     */
    public Builder clearInstallSocialInsurance() {
      
      installSocialInsurance_ = 0;
      onChanged();
      return this;
    }

    private int payment_ ;
    /**
     * <pre>
     * 是否支付, 1(未安装)、2（已安装）
     * </pre>
     *
     * <code>uint32 payment = 46;</code>
     */
    public int getPayment() {
      return payment_;
    }
    /**
     * <pre>
     * 是否支付, 1(未安装)、2（已安装）
     * </pre>
     *
     * <code>uint32 payment = 46;</code>
     */
    public Builder setPayment(int value) {
      
      payment_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否支付, 1(未安装)、2（已安装）
     * </pre>
     *
     * <code>uint32 payment = 46;</code>
     */
    public Builder clearPayment() {
      
      payment_ = 0;
      onChanged();
      return this;
    }

    private int phonePriceId_ ;
    /**
     * <pre>
     * 设备价格, 1(1000以下)、2（1000-1500）、3（1500-2000）、4（2000-2500）、5（2500-3000）、6（3000-3500）、7（3500-4000）、8（4000-5000）、9（5000-6000）、10（6000+）
     * </pre>
     *
     * <code>uint32 phone_price_id = 50;</code>
     */
    public int getPhonePriceId() {
      return phonePriceId_;
    }
    /**
     * <pre>
     * 设备价格, 1(1000以下)、2（1000-1500）、3（1500-2000）、4（2000-2500）、5（2500-3000）、6（3000-3500）、7（3500-4000）、8（4000-5000）、9（5000-6000）、10（6000+）
     * </pre>
     *
     * <code>uint32 phone_price_id = 50;</code>
     */
    public Builder setPhonePriceId(int value) {
      
      phonePriceId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 设备价格, 1(1000以下)、2（1000-1500）、3（1500-2000）、4（2000-2500）、5（2500-3000）、6（3000-3500）、7（3500-4000）、8（4000-5000）、9（5000-6000）、10（6000+）
     * </pre>
     *
     * <code>uint32 phone_price_id = 50;</code>
     */
    public Builder clearPhonePriceId() {
      
      phonePriceId_ = 0;
      onChanged();
      return this;
    }

    private int consumptionLevelId_ ;
    /**
     * <pre>
     * 消费水平, 1(高)、2（中）、3（低）
     * </pre>
     *
     * <code>uint32 consumption_level_id = 51;</code>
     */
    public int getConsumptionLevelId() {
      return consumptionLevelId_;
    }
    /**
     * <pre>
     * 消费水平, 1(高)、2（中）、3（低）
     * </pre>
     *
     * <code>uint32 consumption_level_id = 51;</code>
     */
    public Builder setConsumptionLevelId(int value) {
      
      consumptionLevelId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 消费水平, 1(高)、2（中）、3（低）
     * </pre>
     *
     * <code>uint32 consumption_level_id = 51;</code>
     */
    public Builder clearConsumptionLevelId() {
      
      consumptionLevelId_ = 0;
      onChanged();
      return this;
    }

    private int educationId_ ;
    /**
     * <pre>
     * 教育水平, 1(高中及以下)、2(专科)、3(本科)、4(硕士及以上)
     * </pre>
     *
     * <code>uint32 education_id = 53;</code>
     */
    public int getEducationId() {
      return educationId_;
    }
    /**
     * <pre>
     * 教育水平, 1(高中及以下)、2(专科)、3(本科)、4(硕士及以上)
     * </pre>
     *
     * <code>uint32 education_id = 53;</code>
     */
    public Builder setEducationId(int value) {
      
      educationId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 教育水平, 1(高中及以下)、2(专科)、3(本科)、4(硕士及以上)
     * </pre>
     *
     * <code>uint32 education_id = 53;</code>
     */
    public Builder clearEducationId() {
      
      educationId_ = 0;
      onChanged();
      return this;
    }

    private int genderId_ ;
    /**
     * <pre>
     * 性别, 1(男)、2(女)
     * </pre>
     *
     * <code>uint32 gender_id = 54;</code>
     */
    public int getGenderId() {
      return genderId_;
    }
    /**
     * <pre>
     * 性别, 1(男)、2(女)
     * </pre>
     *
     * <code>uint32 gender_id = 54;</code>
     */
    public Builder setGenderId(int value) {
      
      genderId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 性别, 1(男)、2(女)
     * </pre>
     *
     * <code>uint32 gender_id = 54;</code>
     */
    public Builder clearGenderId() {
      
      genderId_ = 0;
      onChanged();
      return this;
    }

    private double latitude_ ;
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>double latitude = 55;</code>
     */
    public double getLatitude() {
      return latitude_;
    }
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>double latitude = 55;</code>
     */
    public Builder setLatitude(double value) {
      
      latitude_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>double latitude = 55;</code>
     */
    public Builder clearLatitude() {
      
      latitude_ = 0D;
      onChanged();
      return this;
    }

    private double longitude_ ;
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>double longitude = 56;</code>
     */
    public double getLongitude() {
      return longitude_;
    }
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>double longitude = 56;</code>
     */
    public Builder setLongitude(double value) {
      
      longitude_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>double longitude = 56;</code>
     */
    public Builder clearLongitude() {
      
      longitude_ = 0D;
      onChanged();
      return this;
    }

    private UserEmb userEmbedding_ = null;
    private com.google.protobuf.SingleFieldBuilderV3<
            UserEmb, UserEmb.Builder, UserEmbOrBuilder> userEmbeddingBuilder_;
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public boolean hasUserEmbedding() {
      return userEmbeddingBuilder_ != null || userEmbedding_ != null;
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public UserEmb getUserEmbedding() {
      if (userEmbeddingBuilder_ == null) {
        return userEmbedding_ == null ? UserEmb.getDefaultInstance() : userEmbedding_;
      } else {
        return userEmbeddingBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public Builder setUserEmbedding(UserEmb value) {
      if (userEmbeddingBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        userEmbedding_ = value;
        onChanged();
      } else {
        userEmbeddingBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public Builder setUserEmbedding(
        UserEmb.Builder builderForValue) {
      if (userEmbeddingBuilder_ == null) {
        userEmbedding_ = builderForValue.build();
        onChanged();
      } else {
        userEmbeddingBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public Builder mergeUserEmbedding(UserEmb value) {
      if (userEmbeddingBuilder_ == null) {
        if (userEmbedding_ != null) {
          userEmbedding_ =
            UserEmb.newBuilder(userEmbedding_).mergeFrom(value).buildPartial();
        } else {
          userEmbedding_ = value;
        }
        onChanged();
      } else {
        userEmbeddingBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public Builder clearUserEmbedding() {
      if (userEmbeddingBuilder_ == null) {
        userEmbedding_ = null;
        onChanged();
      } else {
        userEmbedding_ = null;
        userEmbeddingBuilder_ = null;
      }

      return this;
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public UserEmb.Builder getUserEmbeddingBuilder() {
      
      onChanged();
      return getUserEmbeddingFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    public UserEmbOrBuilder getUserEmbeddingOrBuilder() {
      if (userEmbeddingBuilder_ != null) {
        return userEmbeddingBuilder_.getMessageOrBuilder();
      } else {
        return userEmbedding_ == null ?
            UserEmb.getDefaultInstance() : userEmbedding_;
      }
    }
    /**
     * <pre>
     * 用户embedding向量
     * </pre>
     *
     * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
            UserEmb, UserEmb.Builder, UserEmbOrBuilder>
        getUserEmbeddingFieldBuilder() {
      if (userEmbeddingBuilder_ == null) {
        userEmbeddingBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
                UserEmb, UserEmb.Builder, UserEmbOrBuilder>(
                getUserEmbedding(),
                getParentForChildren(),
                isClean());
        userEmbedding_ = null;
      }
      return userEmbeddingBuilder_;
    }

    private Object currentPaid15_ = "";
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 当前版本
     * </pre>
     *
     * <code>string current_paid_1_5 = 60;</code>
     */
    public String getCurrentPaid15() {
      Object ref = currentPaid15_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        currentPaid15_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 当前版本
     * </pre>
     *
     * <code>string current_paid_1_5 = 60;</code>
     */
    public com.google.protobuf.ByteString
        getCurrentPaid15Bytes() {
      Object ref = currentPaid15_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        currentPaid15_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 当前版本
     * </pre>
     *
     * <code>string current_paid_1_5 = 60;</code>
     */
    public Builder setCurrentPaid15(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      currentPaid15_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 当前版本
     * </pre>
     *
     * <code>string current_paid_1_5 = 60;</code>
     */
    public Builder clearCurrentPaid15() {
      
      currentPaid15_ = getDefaultInstance().getCurrentPaid15();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 当前版本
     * </pre>
     *
     * <code>string current_paid_1_5 = 60;</code>
     */
    public Builder setCurrentPaid15Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      currentPaid15_ = value;
      onChanged();
      return this;
    }

    private Object lastPaid15_ = "";
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 上一个版本
     * </pre>
     *
     * <code>string last_paid_1_5 = 61;</code>
     */
    public String getLastPaid15() {
      Object ref = lastPaid15_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        lastPaid15_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 上一个版本
     * </pre>
     *
     * <code>string last_paid_1_5 = 61;</code>
     */
    public com.google.protobuf.ByteString
        getLastPaid15Bytes() {
      Object ref = lastPaid15_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        lastPaid15_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 上一个版本
     * </pre>
     *
     * <code>string last_paid_1_5 = 61;</code>
     */
    public Builder setLastPaid15(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      lastPaid15_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 上一个版本
     * </pre>
     *
     * <code>string last_paid_1_5 = 61;</code>
     */
    public Builder clearLastPaid15() {
      
      lastPaid15_ = getDefaultInstance().getLastPaid15();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 拼多多设备指纹paid1.5, 上一个版本
     * </pre>
     *
     * <code>string last_paid_1_5 = 61;</code>
     */
    public Builder setLastPaid15Bytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      lastPaid15_ = value;
      onChanged();
      return this;
    }

    private Object previousModel_ = "";
    /**
     * <pre>
     * 换机设备标识
     * </pre>
     *
     * <code>string previous_model = 62;</code>
     */
    public String getPreviousModel() {
      Object ref = previousModel_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        previousModel_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 换机设备标识
     * </pre>
     *
     * <code>string previous_model = 62;</code>
     */
    public com.google.protobuf.ByteString
        getPreviousModelBytes() {
      Object ref = previousModel_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        previousModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 换机设备标识
     * </pre>
     *
     * <code>string previous_model = 62;</code>
     */
    public Builder setPreviousModel(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      previousModel_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 换机设备标识
     * </pre>
     *
     * <code>string previous_model = 62;</code>
     */
    public Builder clearPreviousModel() {
      
      previousModel_ = getDefaultInstance().getPreviousModel();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 换机设备标识
     * </pre>
     *
     * <code>string previous_model = 62;</code>
     */
    public Builder setPreviousModelBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      previousModel_ = value;
      onChanged();
      return this;
    }

    private Object feature_ = "";
    /**
     * <pre>
     * 实验or策略信息，需线下约定
     * </pre>
     *
     * <code>string feature = 101;</code>
     */
    public String getFeature() {
      Object ref = feature_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        feature_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 实验or策略信息，需线下约定
     * </pre>
     *
     * <code>string feature = 101;</code>
     */
    public com.google.protobuf.ByteString
        getFeatureBytes() {
      Object ref = feature_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        feature_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 实验or策略信息，需线下约定
     * </pre>
     *
     * <code>string feature = 101;</code>
     */
    public Builder setFeature(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      feature_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 实验or策略信息，需线下约定
     * </pre>
     *
     * <code>string feature = 101;</code>
     */
    public Builder clearFeature() {
      
      feature_ = getDefaultInstance().getFeature();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 实验or策略信息，需线下约定
     * </pre>
     *
     * <code>string feature = 101;</code>
     */
    public Builder setFeatureBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      feature_ = value;
      onChanged();
      return this;
    }
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFieldsProto3(unknownFields);
    }

    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:kuaishou.ad.rta.Req)
  }

  // @@protoc_insertion_point(class_scope:kuaishou.ad.rta.Req)
  private static final Req DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new Req();
  }

  public static Req getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Req>
      PARSER = new com.google.protobuf.AbstractParser<Req>() {
    public Req parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Req(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Req> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<Req> getParserForType() {
    return PARSER;
  }

  public Req getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

