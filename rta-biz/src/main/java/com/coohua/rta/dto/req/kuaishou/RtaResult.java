// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf type {@code kuaishou.ad.rta.RtaResult}
 */
public  final class RtaR<PERSON>ult extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:kuaishou.ad.rta.RtaResult)
    RtaResultOrBuilder {
private static final long serialVersionUID = 0L;
  // Use RtaResult.newBuilder() to construct.
  private RtaResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private RtaResult() {
    accountId_ = 0L;
    unitId_ = 0L;
    bidRatio_ = 0D;
    strategyId_ = 0L;
    bid_ = 0D;
    dpaResult_ = java.util.Collections.emptyList();
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private RtaResult(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          default: {
            if (!parseUnknownFieldProto3(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
          case 8: {

            accountId_ = input.readUInt64();
            break;
          }
          case 24: {

            unitId_ = input.readUInt64();
            break;
          }
          case 33: {

            bidRatio_ = input.readDouble();
            break;
          }
          case 40: {

            strategyId_ = input.readUInt64();
            break;
          }
          case 49: {

            bid_ = input.readDouble();
            break;
          }
          case 58: {
            if (!((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
              dpaResult_ = new java.util.ArrayList<DpaResult>();
              mutable_bitField0_ |= 0x00000020;
            }
            dpaResult_.add(
                input.readMessage(DpaResult.parser(), extensionRegistry));
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
        dpaResult_ = java.util.Collections.unmodifiableList(dpaResult_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return RTAProto.internal_static_kuaishou_ad_rta_RtaResult_descriptor;
  }

  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return RTAProto.internal_static_kuaishou_ad_rta_RtaResult_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            RtaResult.class, RtaResult.Builder.class);
  }

  private int bitField0_;
  public static final int ACCOUNT_ID_FIELD_NUMBER = 1;
  private long accountId_;
  /**
   * <pre>
   * 账号id, 必填
   * </pre>
   *
   * <code>uint64 account_id = 1;</code>
   */
  public long getAccountId() {
    return accountId_;
  }

  public static final int UNIT_ID_FIELD_NUMBER = 3;
  private long unitId_;
  /**
   * <pre>
   * 广告组id, 不填代表所有广告组
   * </pre>
   *
   * <code>uint64 unit_id = 3;</code>
   */
  public long getUnitId() {
    return unitId_;
  }

  public static final int BID_RATIO_FIELD_NUMBER = 4;
  private double bidRatio_;
  /**
   * <pre>
   *动态出价系数
   * </pre>
   *
   * <code>double bid_ratio = 4;</code>
   */
  public double getBidRatio() {
    return bidRatio_;
  }

  public static final int STRATEGY_ID_FIELD_NUMBER = 5;
  private long strategyId_;
  /**
   * <pre>
   * 策略id，strategy_id 存在时，忽略 account_id 和 unit_id 字段
   * </pre>
   *
   * <code>uint64 strategy_id = 5;</code>
   */
  public long getStrategyId() {
    return strategyId_;
  }

  public static final int BID_FIELD_NUMBER = 6;
  private double bid_;
  /**
   * <pre>
   * 动态出价,单位元
   * </pre>
   *
   * <code>double bid = 6;</code>
   */
  public double getBid() {
    return bid_;
  }

  public static final int DPA_RESULT_FIELD_NUMBER = 7;
  private java.util.List<DpaResult> dpaResult_;
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  public java.util.List<DpaResult> getDpaResultList() {
    return dpaResult_;
  }
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  public java.util.List<? extends DpaResultOrBuilder>
      getDpaResultOrBuilderList() {
    return dpaResult_;
  }
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  public int getDpaResultCount() {
    return dpaResult_.size();
  }
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  public DpaResult getDpaResult(int index) {
    return dpaResult_.get(index);
  }
  /**
   * <pre>
   * 推荐商品信息
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
   */
  public DpaResultOrBuilder getDpaResultOrBuilder(
      int index) {
    return dpaResult_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (accountId_ != 0L) {
      output.writeUInt64(1, accountId_);
    }
    if (unitId_ != 0L) {
      output.writeUInt64(3, unitId_);
    }
    if (bidRatio_ != 0D) {
      output.writeDouble(4, bidRatio_);
    }
    if (strategyId_ != 0L) {
      output.writeUInt64(5, strategyId_);
    }
    if (bid_ != 0D) {
      output.writeDouble(6, bid_);
    }
    for (int i = 0; i < dpaResult_.size(); i++) {
      output.writeMessage(7, dpaResult_.get(i));
    }
    unknownFields.writeTo(output);
  }

  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (accountId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(1, accountId_);
    }
    if (unitId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(3, unitId_);
    }
    if (bidRatio_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, bidRatio_);
    }
    if (strategyId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, strategyId_);
    }
    if (bid_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(6, bid_);
    }
    for (int i = 0; i < dpaResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(7, dpaResult_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof RtaResult)) {
      return super.equals(obj);
    }
    RtaResult other = (RtaResult) obj;

    boolean result = true;
    result = result && (getAccountId()
        == other.getAccountId());
    result = result && (getUnitId()
        == other.getUnitId());
    result = result && (
        Double.doubleToLongBits(getBidRatio())
        == Double.doubleToLongBits(
            other.getBidRatio()));
    result = result && (getStrategyId()
        == other.getStrategyId());
    result = result && (
        Double.doubleToLongBits(getBid())
        == Double.doubleToLongBits(
            other.getBid()));
    result = result && getDpaResultList()
        .equals(other.getDpaResultList());
    result = result && unknownFields.equals(other.unknownFields);
    return result;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ACCOUNT_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getAccountId());
    hash = (37 * hash) + UNIT_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getUnitId());
    hash = (37 * hash) + BID_RATIO_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        Double.doubleToLongBits(getBidRatio()));
    hash = (37 * hash) + STRATEGY_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getStrategyId());
    hash = (37 * hash) + BID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        Double.doubleToLongBits(getBid()));
    if (getDpaResultCount() > 0) {
      hash = (37 * hash) + DPA_RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getDpaResultList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static RtaResult parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static RtaResult parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static RtaResult parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static RtaResult parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static RtaResult parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static RtaResult parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static RtaResult parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static RtaResult parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static RtaResult parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static RtaResult parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static RtaResult parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static RtaResult parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(RtaResult prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code kuaishou.ad.rta.RtaResult}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:kuaishou.ad.rta.RtaResult)
          RtaResultOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return RTAProto.internal_static_kuaishou_ad_rta_RtaResult_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return RTAProto.internal_static_kuaishou_ad_rta_RtaResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RtaResult.class, RtaResult.Builder.class);
    }

    // Construct using com.kuaishou.protobuf.ad.rta.RtaResult.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getDpaResultFieldBuilder();
      }
    }
    public Builder clear() {
      super.clear();
      accountId_ = 0L;

      unitId_ = 0L;

      bidRatio_ = 0D;

      strategyId_ = 0L;

      bid_ = 0D;

      if (dpaResultBuilder_ == null) {
        dpaResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
      } else {
        dpaResultBuilder_.clear();
      }
      return this;
    }

    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return RTAProto.internal_static_kuaishou_ad_rta_RtaResult_descriptor;
    }

    public RtaResult getDefaultInstanceForType() {
      return RtaResult.getDefaultInstance();
    }

    public RtaResult build() {
      RtaResult result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    public RtaResult buildPartial() {
      RtaResult result = new RtaResult(this);
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      result.accountId_ = accountId_;
      result.unitId_ = unitId_;
      result.bidRatio_ = bidRatio_;
      result.strategyId_ = strategyId_;
      result.bid_ = bid_;
      if (dpaResultBuilder_ == null) {
        if (((bitField0_ & 0x00000020) == 0x00000020)) {
          dpaResult_ = java.util.Collections.unmodifiableList(dpaResult_);
          bitField0_ = (bitField0_ & ~0x00000020);
        }
        result.dpaResult_ = dpaResult_;
      } else {
        result.dpaResult_ = dpaResultBuilder_.build();
      }
      result.bitField0_ = to_bitField0_;
      onBuilt();
      return result;
    }

    public Builder clone() {
      return (Builder) super.clone();
    }
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.setField(field, value);
    }
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return (Builder) super.clearField(field);
    }
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return (Builder) super.clearOneof(oneof);
    }
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return (Builder) super.setRepeatedField(field, index, value);
    }
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.addRepeatedField(field, value);
    }
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof RtaResult) {
        return mergeFrom((RtaResult)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(RtaResult other) {
      if (other == RtaResult.getDefaultInstance()) return this;
      if (other.getAccountId() != 0L) {
        setAccountId(other.getAccountId());
      }
      if (other.getUnitId() != 0L) {
        setUnitId(other.getUnitId());
      }
      if (other.getBidRatio() != 0D) {
        setBidRatio(other.getBidRatio());
      }
      if (other.getStrategyId() != 0L) {
        setStrategyId(other.getStrategyId());
      }
      if (other.getBid() != 0D) {
        setBid(other.getBid());
      }
      if (dpaResultBuilder_ == null) {
        if (!other.dpaResult_.isEmpty()) {
          if (dpaResult_.isEmpty()) {
            dpaResult_ = other.dpaResult_;
            bitField0_ = (bitField0_ & ~0x00000020);
          } else {
            ensureDpaResultIsMutable();
            dpaResult_.addAll(other.dpaResult_);
          }
          onChanged();
        }
      } else {
        if (!other.dpaResult_.isEmpty()) {
          if (dpaResultBuilder_.isEmpty()) {
            dpaResultBuilder_.dispose();
            dpaResultBuilder_ = null;
            dpaResult_ = other.dpaResult_;
            bitField0_ = (bitField0_ & ~0x00000020);
            dpaResultBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDpaResultFieldBuilder() : null;
          } else {
            dpaResultBuilder_.addAllMessages(other.dpaResult_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    public final boolean isInitialized() {
      return true;
    }

    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      RtaResult parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (RtaResult) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private long accountId_ ;
    /**
     * <pre>
     * 账号id, 必填
     * </pre>
     *
     * <code>uint64 account_id = 1;</code>
     */
    public long getAccountId() {
      return accountId_;
    }
    /**
     * <pre>
     * 账号id, 必填
     * </pre>
     *
     * <code>uint64 account_id = 1;</code>
     */
    public Builder setAccountId(long value) {
      
      accountId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 账号id, 必填
     * </pre>
     *
     * <code>uint64 account_id = 1;</code>
     */
    public Builder clearAccountId() {
      
      accountId_ = 0L;
      onChanged();
      return this;
    }

    private long unitId_ ;
    /**
     * <pre>
     * 广告组id, 不填代表所有广告组
     * </pre>
     *
     * <code>uint64 unit_id = 3;</code>
     */
    public long getUnitId() {
      return unitId_;
    }
    /**
     * <pre>
     * 广告组id, 不填代表所有广告组
     * </pre>
     *
     * <code>uint64 unit_id = 3;</code>
     */
    public Builder setUnitId(long value) {
      
      unitId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 广告组id, 不填代表所有广告组
     * </pre>
     *
     * <code>uint64 unit_id = 3;</code>
     */
    public Builder clearUnitId() {
      
      unitId_ = 0L;
      onChanged();
      return this;
    }

    private double bidRatio_ ;
    /**
     * <pre>
     *动态出价系数
     * </pre>
     *
     * <code>double bid_ratio = 4;</code>
     */
    public double getBidRatio() {
      return bidRatio_;
    }
    /**
     * <pre>
     *动态出价系数
     * </pre>
     *
     * <code>double bid_ratio = 4;</code>
     */
    public Builder setBidRatio(double value) {
      
      bidRatio_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *动态出价系数
     * </pre>
     *
     * <code>double bid_ratio = 4;</code>
     */
    public Builder clearBidRatio() {
      
      bidRatio_ = 0D;
      onChanged();
      return this;
    }

    private long strategyId_ ;
    /**
     * <pre>
     * 策略id，strategy_id 存在时，忽略 account_id 和 unit_id 字段
     * </pre>
     *
     * <code>uint64 strategy_id = 5;</code>
     */
    public long getStrategyId() {
      return strategyId_;
    }
    /**
     * <pre>
     * 策略id，strategy_id 存在时，忽略 account_id 和 unit_id 字段
     * </pre>
     *
     * <code>uint64 strategy_id = 5;</code>
     */
    public Builder setStrategyId(long value) {
      
      strategyId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 策略id，strategy_id 存在时，忽略 account_id 和 unit_id 字段
     * </pre>
     *
     * <code>uint64 strategy_id = 5;</code>
     */
    public Builder clearStrategyId() {
      
      strategyId_ = 0L;
      onChanged();
      return this;
    }

    private double bid_ ;
    /**
     * <pre>
     * 动态出价,单位元
     * </pre>
     *
     * <code>double bid = 6;</code>
     */
    public double getBid() {
      return bid_;
    }
    /**
     * <pre>
     * 动态出价,单位元
     * </pre>
     *
     * <code>double bid = 6;</code>
     */
    public Builder setBid(double value) {
      
      bid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 动态出价,单位元
     * </pre>
     *
     * <code>double bid = 6;</code>
     */
    public Builder clearBid() {
      
      bid_ = 0D;
      onChanged();
      return this;
    }

    private java.util.List<DpaResult> dpaResult_ =
      java.util.Collections.emptyList();
    private void ensureDpaResultIsMutable() {
      if (!((bitField0_ & 0x00000020) == 0x00000020)) {
        dpaResult_ = new java.util.ArrayList<DpaResult>(dpaResult_);
        bitField0_ |= 0x00000020;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
            DpaResult, DpaResult.Builder, DpaResultOrBuilder> dpaResultBuilder_;

    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public java.util.List<DpaResult> getDpaResultList() {
      if (dpaResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(dpaResult_);
      } else {
        return dpaResultBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public int getDpaResultCount() {
      if (dpaResultBuilder_ == null) {
        return dpaResult_.size();
      } else {
        return dpaResultBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public DpaResult getDpaResult(int index) {
      if (dpaResultBuilder_ == null) {
        return dpaResult_.get(index);
      } else {
        return dpaResultBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder setDpaResult(
        int index, DpaResult value) {
      if (dpaResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDpaResultIsMutable();
        dpaResult_.set(index, value);
        onChanged();
      } else {
        dpaResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder setDpaResult(
        int index, DpaResult.Builder builderForValue) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        dpaResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder addDpaResult(DpaResult value) {
      if (dpaResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDpaResultIsMutable();
        dpaResult_.add(value);
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder addDpaResult(
        int index, DpaResult value) {
      if (dpaResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDpaResultIsMutable();
        dpaResult_.add(index, value);
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder addDpaResult(
        DpaResult.Builder builderForValue) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.add(builderForValue.build());
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder addDpaResult(
        int index, DpaResult.Builder builderForValue) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder addAllDpaResult(
        Iterable<? extends DpaResult> values) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dpaResult_);
        onChanged();
      } else {
        dpaResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder clearDpaResult() {
      if (dpaResultBuilder_ == null) {
        dpaResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
      } else {
        dpaResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public Builder removeDpaResult(int index) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.remove(index);
        onChanged();
      } else {
        dpaResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public DpaResult.Builder getDpaResultBuilder(
        int index) {
      return getDpaResultFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public DpaResultOrBuilder getDpaResultOrBuilder(
        int index) {
      if (dpaResultBuilder_ == null) {
        return dpaResult_.get(index);  } else {
        return dpaResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public java.util.List<? extends DpaResultOrBuilder>
         getDpaResultOrBuilderList() {
      if (dpaResultBuilder_ != null) {
        return dpaResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(dpaResult_);
      }
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public DpaResult.Builder addDpaResultBuilder() {
      return getDpaResultFieldBuilder().addBuilder(
          DpaResult.getDefaultInstance());
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public DpaResult.Builder addDpaResultBuilder(
        int index) {
      return getDpaResultFieldBuilder().addBuilder(
          index, DpaResult.getDefaultInstance());
    }
    /**
     * <pre>
     * 推荐商品信息
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 7;</code>
     */
    public java.util.List<DpaResult.Builder>
         getDpaResultBuilderList() {
      return getDpaResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
            DpaResult, DpaResult.Builder, DpaResultOrBuilder>
        getDpaResultFieldBuilder() {
      if (dpaResultBuilder_ == null) {
        dpaResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                DpaResult, DpaResult.Builder, DpaResultOrBuilder>(
                dpaResult_,
                ((bitField0_ & 0x00000020) == 0x00000020),
                getParentForChildren(),
                isClean());
        dpaResult_ = null;
      }
      return dpaResultBuilder_;
    }
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFieldsProto3(unknownFields);
    }

    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:kuaishou.ad.rta.RtaResult)
  }

  // @@protoc_insertion_point(class_scope:kuaishou.ad.rta.RtaResult)
  private static final RtaResult DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new RtaResult();
  }

  public static RtaResult getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RtaResult>
      PARSER = new com.google.protobuf.AbstractParser<RtaResult>() {
    public RtaResult parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new RtaResult(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<RtaResult> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<RtaResult> getParserForType() {
    return PARSER;
  }

  public RtaResult getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

