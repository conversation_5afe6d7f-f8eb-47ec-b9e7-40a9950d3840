package com.coohua.rta.dto.req;


import com.coohua.rta.enums.OcpcSourceType;
import com.coohua.rta.utils.ConstCls;
import com.coohua.rta.utils.MD5Utils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


@Data
@Slf4j
public class UserEventReq {
    private Integer appId; //应用id
    private String userId;
    private String product;//xiaoxiaole
    private String os;//android || ios
    private String ocpcDeviceId;//md5 imei  ios idfa 没做
    private String mac;//客户端传输原值
    private String androidId;//安卓id原值的md5，32位
    private String originalAndroidId; //安卓id的原值，未加密
    private String oaid;// and头条roid 拿的
    private String oaidMd5;
    private String oaid2;// md5(oaid) 不是回传过来的，是接收到oaid后计算的
    private String idfa2;// md5(idfa) 不是回传过来的，是接收到idfa后计算的
    private String idfa;
    private String idfv;
    private String androidMd5Id;
    private int eventType; //回传事件 定义 0 激活

    private String clickId;
    /**
     * 渠道包标识
     */
    private String pkgChannel;
    private String pkg_channel;
    private OcpcSourceType souceType = OcpcSourceType.DSP;

    private String SourceDeviceId;
    private String SourceOaid;

    private Integer actionType;

    /**
     * 行为计数；采用String是为了兼容整数、小数及未来可能扩展的其他类型
     */
    private String actionNumber;
    private Boolean actionDefault;

    /**
     * 升级版事件类型及相应值回传字段，将满足条件的事件及值全部回传
     */
    private String actionValues;

    /**
     * 数据累积到当前值所花费的时间：单位为分钟
     */
    private Integer accumulateDuration;

    private Boolean isSaveAction = true;

    private Integer payAmount;//微信小游戏 付费 单位分
    private String payOrderNo;
    private String ip;
    private String ua;
    private String model;
    private String openId;
    private String wAppId;
    private String caid;
    private String caidVersion;

    private String guiType;
    private String caid2;
    private String caid1;

    // 命中人群包的caid
    private String matchedCaid;

    private String uarpuRmk;
    private String uarpuGy;

    private boolean timerDelay;
    public String concatIpua() {
        if (StringUtils.isAnyBlank(ip, ua, model)) {
            return null;
        }
        if ("null".equals(ip) || "null".equals(ua) || "null".equals(model)) {
            return null;
        }
        return ip + "_" + ua + "_" + model;
    }

    public String concatIpuaMd() {
        if (StringUtils.isAnyBlank(ip, ua)) {
            return null;
        }
        if ("null".equals(ip) || "null".equals(ua)) {
            return null;
        }
        return ip + "_" + ua;
    }

    public String concatIpMd() {
        if (StringUtils.isAnyBlank(ip,model)) {
            return null;
        }
        if ("null".equals(ip) || "null".equals(model)) {
            return null;
        }
        return ip + "_" + model;
    }

    public String concatIp() {
        if (StringUtils.isAnyBlank(ip)) {
            return null;
        }
        if ("null".equals(ip)) {
            return null;
        }
        return ip;
    }

    public void trySetOaid2AndIdfa2(boolean setCaid2) {
        if (StringUtils.isNotBlank(oaid) && !ConstCls.emptyMd5.equals(oaid) && !ConstCls.zeroMd5.equals(oaid)) {
            oaid2 = MD5Utils.getMd5Sum(oaid);
        }
        if ("ios".equalsIgnoreCase(os) && StringUtils.isNotBlank(SourceDeviceId) && !SourceDeviceId.startsWith("0000-0000")&& !SourceDeviceId.startsWith("000000-0000") && !SourceDeviceId.startsWith("00000000-0000") && SourceDeviceId.length()>3) {
            idfa2 = MD5Utils.getMd5Sum(SourceDeviceId);
        }else if("ios".equalsIgnoreCase(os) && StringUtils.isNotBlank(SourceDeviceId)){
//            log.info("ios过滤idfa2 "+SourceDeviceId+" "+product+" "+userId);
        }

        if("ios".equalsIgnoreCase(os) && StringUtils.isBlank(idfa2) && StringUtils.isNotBlank(idfa)){
            idfa2 = MD5Utils.getMd5Sum(idfa);
//            log.info("ios过滤idfa2 "+idfa+" "+product+" "+userId);
        }
        if(setCaid2 && "ios".equalsIgnoreCase(os) && StringUtils.isBlank(caid2) && StringUtils.isNotBlank(caid)){
            caid2 = MD5Utils.getMd5Sum(caid);
        }
    }

    public static void setMd5Inf(UserEventReq userEventReq, boolean setCaid2){
        userEventReq.trySetOaid2AndIdfa2(setCaid2);
        userEventReq.setMac(MD5Utils.getMd5Mac(userEventReq.getMac()));
        userEventReq.setAndroidId(MD5Utils.getMd5AndroidId(userEventReq.getAndroidId()));
    }

    public static void setMd5Inf(UserEventReq userEventReq) {
        setMd5Inf(userEventReq, false);
    }
}
