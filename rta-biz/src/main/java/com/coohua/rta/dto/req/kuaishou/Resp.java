// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf type {@code kuaishou.ad.rta.Resp}
 */
public  final class Resp extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:kuaishou.ad.rta.Resp)
    RespOrBuilder {
private static final long serialVersionUID = 0L;
  // Use Resp.newBuilder() to construct.
  private Resp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private Resp() {
    id_ = "";
    resultType_ = 0;
    result_ = java.util.Collections.emptyList();
    bidRatio_ = 0D;
    refuseTime_ = 0;
    cacheTime_ = 0;
    dpaResult_ = java.util.Collections.emptyList();
    bid_ = 0D;
    bucketId_ = 0;
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private Resp(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          default: {
            if (!parseUnknownFieldProto3(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
          case 10: {
            String s = input.readStringRequireUtf8();

            id_ = s;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            resultType_ = rawValue;
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
              result_ = new java.util.ArrayList<RtaResult>();
              mutable_bitField0_ |= 0x00000004;
            }
            result_.add(
                input.readMessage(RtaResult.parser(), extensionRegistry));
            break;
          }
          case 33: {

            bidRatio_ = input.readDouble();
            break;
          }
          case 48: {

            refuseTime_ = input.readUInt32();
            break;
          }
          case 56: {

            cacheTime_ = input.readUInt32();
            break;
          }
          case 66: {
            if (!((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
              dpaResult_ = new java.util.ArrayList<DpaResult>();
              mutable_bitField0_ |= 0x00000040;
            }
            dpaResult_.add(
                input.readMessage(DpaResult.parser(), extensionRegistry));
            break;
          }
          case 73: {

            bid_ = input.readDouble();
            break;
          }
          case 80: {

            bucketId_ = input.readUInt32();
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
        result_ = java.util.Collections.unmodifiableList(result_);
      }
      if (((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
        dpaResult_ = java.util.Collections.unmodifiableList(dpaResult_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return RTAProto.internal_static_kuaishou_ad_rta_Resp_descriptor;
  }

  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return RTAProto.internal_static_kuaishou_ad_rta_Resp_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            Resp.class, Resp.Builder.class);
  }

  /**
   * Protobuf enum {@code kuaishou.ad.rta.Resp.ResultType}
   */
  public enum ResultType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 全部都投
     * </pre>
     *
     * <code>ALL = 0;</code>
     */
    ALL(0),
    /**
     * <pre>
     * 全部不投
     * </pre>
     *
     * <code>NONE = 1;</code>
     */
    NONE(1),
    /**
     * <pre>
     * 只投一部分
     * </pre>
     *
     * <code>PART = 2;</code>
     */
    PART(2),
    UNRECOGNIZED(-1),
    ;

    /**
     * <pre>
     * 全部都投
     * </pre>
     *
     * <code>ALL = 0;</code>
     */
    public static final int ALL_VALUE = 0;
    /**
     * <pre>
     * 全部不投
     * </pre>
     *
     * <code>NONE = 1;</code>
     */
    public static final int NONE_VALUE = 1;
    /**
     * <pre>
     * 只投一部分
     * </pre>
     *
     * <code>PART = 2;</code>
     */
    public static final int PART_VALUE = 2;


    public final int getNumber() {
      if (this == UNRECOGNIZED) {
        throw new IllegalArgumentException(
            "Can't get the number of an unknown enum value.");
      }
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static ResultType valueOf(int value) {
      return forNumber(value);
    }

    public static ResultType forNumber(int value) {
      switch (value) {
        case 0: return ALL;
        case 1: return NONE;
        case 2: return PART;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ResultType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ResultType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ResultType>() {
            public ResultType findValueByNumber(int number) {
              return ResultType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return Resp.getDescriptor().getEnumTypes().get(0);
    }

    private static final ResultType[] VALUES = values();

    public static ResultType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      if (desc.getIndex() == -1) {
        return UNRECOGNIZED;
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ResultType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:kuaishou.ad.rta.Resp.ResultType)
  }

  private int bitField0_;
  public static final int ID_FIELD_NUMBER = 1;
  private volatile Object id_;
  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  public String getId() {
    Object ref = id_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      id_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  public com.google.protobuf.ByteString
      getIdBytes() {
    Object ref = id_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      id_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int RESULT_TYPE_FIELD_NUMBER = 2;
  private int resultType_;
  /**
   * <pre>
   * 结果类型, 等于PART时, result必填
   * </pre>
   *
   * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
   */
  public int getResultTypeValue() {
    return resultType_;
  }
  /**
   * <pre>
   * 结果类型, 等于PART时, result必填
   * </pre>
   *
   * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
   */
  public Resp.ResultType getResultType() {
    Resp.ResultType result = Resp.ResultType.valueOf(resultType_);
    return result == null ? Resp.ResultType.UNRECOGNIZED : result;
  }

  public static final int RESULT_FIELD_NUMBER = 3;
  private java.util.List<RtaResult> result_;
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  public java.util.List<RtaResult> getResultList() {
    return result_;
  }
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  public java.util.List<? extends RtaResultOrBuilder>
      getResultOrBuilderList() {
    return result_;
  }
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  public int getResultCount() {
    return result_.size();
  }
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  public RtaResult getResult(int index) {
    return result_.get(index);
  }
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  public RtaResultOrBuilder getResultOrBuilder(
      int index) {
    return result_.get(index);
  }

  public static final int BID_RATIO_FIELD_NUMBER = 4;
  private double bidRatio_;
  /**
   * <pre>
   * result_type=ALL时的动态出价系数
   * </pre>
   *
   * <code>double bid_ratio = 4;</code>
   */
  public double getBidRatio() {
    return bidRatio_;
  }

  public static final int REFUSE_TIME_FIELD_NUMBER = 6;
  private int refuseTime_;
  /**
   * <pre>
   *拒绝时间
   * </pre>
   *
   * <code>uint32 refuse_time = 6;</code>
   */
  public int getRefuseTime() {
    return refuseTime_;
  }

  public static final int CACHE_TIME_FIELD_NUMBER = 7;
  private int cacheTime_;
  /**
   * <pre>
   *缓存时间
   * </pre>
   *
   * <code>uint32 cache_time = 7;</code>
   */
  public int getCacheTime() {
    return cacheTime_;
  }

  public static final int DPA_RESULT_FIELD_NUMBER = 8;
  private java.util.List<DpaResult> dpaResult_;
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  public java.util.List<DpaResult> getDpaResultList() {
    return dpaResult_;
  }
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  public java.util.List<? extends DpaResultOrBuilder>
      getDpaResultOrBuilderList() {
    return dpaResult_;
  }
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  public int getDpaResultCount() {
    return dpaResult_.size();
  }
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  public DpaResult getDpaResult(int index) {
    return dpaResult_.get(index);
  }
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  public DpaResultOrBuilder getDpaResultOrBuilder(
      int index) {
    return dpaResult_.get(index);
  }

  public static final int BID_FIELD_NUMBER = 9;
  private double bid_;
  /**
   * <pre>
   *result_type=ALL时的动态出价,单位元
   * </pre>
   *
   * <code>double bid = 9;</code>
   */
  public double getBid() {
    return bid_;
  }

  public static final int BUCKET_ID_FIELD_NUMBER = 10;
  private int bucketId_;
  /**
   * <pre>
   * 客户分桶id
   * </pre>
   *
   * <code>uint32 bucket_id = 10;</code>
   */
  public int getBucketId() {
    return bucketId_;
  }

  private byte memoizedIsInitialized = -1;
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getIdBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
    }
    if (resultType_ != Resp.ResultType.ALL.getNumber()) {
      output.writeEnum(2, resultType_);
    }
    for (int i = 0; i < result_.size(); i++) {
      output.writeMessage(3, result_.get(i));
    }
    if (bidRatio_ != 0D) {
      output.writeDouble(4, bidRatio_);
    }
    if (refuseTime_ != 0) {
      output.writeUInt32(6, refuseTime_);
    }
    if (cacheTime_ != 0) {
      output.writeUInt32(7, cacheTime_);
    }
    for (int i = 0; i < dpaResult_.size(); i++) {
      output.writeMessage(8, dpaResult_.get(i));
    }
    if (bid_ != 0D) {
      output.writeDouble(9, bid_);
    }
    if (bucketId_ != 0) {
      output.writeUInt32(10, bucketId_);
    }
    unknownFields.writeTo(output);
  }

  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getIdBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
    }
    if (resultType_ != Resp.ResultType.ALL.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, resultType_);
    }
    for (int i = 0; i < result_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, result_.get(i));
    }
    if (bidRatio_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(4, bidRatio_);
    }
    if (refuseTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(6, refuseTime_);
    }
    if (cacheTime_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(7, cacheTime_);
    }
    for (int i = 0; i < dpaResult_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(8, dpaResult_.get(i));
    }
    if (bid_ != 0D) {
      size += com.google.protobuf.CodedOutputStream
        .computeDoubleSize(9, bid_);
    }
    if (bucketId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(10, bucketId_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof Resp)) {
      return super.equals(obj);
    }
    Resp other = (Resp) obj;

    boolean result = true;
    result = result && getId()
        .equals(other.getId());
    result = result && resultType_ == other.resultType_;
    result = result && getResultList()
        .equals(other.getResultList());
    result = result && (
        Double.doubleToLongBits(getBidRatio())
        == Double.doubleToLongBits(
            other.getBidRatio()));
    result = result && (getRefuseTime()
        == other.getRefuseTime());
    result = result && (getCacheTime()
        == other.getCacheTime());
    result = result && getDpaResultList()
        .equals(other.getDpaResultList());
    result = result && (
        Double.doubleToLongBits(getBid())
        == Double.doubleToLongBits(
            other.getBid()));
    result = result && (getBucketId()
        == other.getBucketId());
    result = result && unknownFields.equals(other.unknownFields);
    return result;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + ID_FIELD_NUMBER;
    hash = (53 * hash) + getId().hashCode();
    hash = (37 * hash) + RESULT_TYPE_FIELD_NUMBER;
    hash = (53 * hash) + resultType_;
    if (getResultCount() > 0) {
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResultList().hashCode();
    }
    hash = (37 * hash) + BID_RATIO_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        Double.doubleToLongBits(getBidRatio()));
    hash = (37 * hash) + REFUSE_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getRefuseTime();
    hash = (37 * hash) + CACHE_TIME_FIELD_NUMBER;
    hash = (53 * hash) + getCacheTime();
    if (getDpaResultCount() > 0) {
      hash = (37 * hash) + DPA_RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getDpaResultList().hashCode();
    }
    hash = (37 * hash) + BID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        Double.doubleToLongBits(getBid()));
    hash = (37 * hash) + BUCKET_ID_FIELD_NUMBER;
    hash = (53 * hash) + getBucketId();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static Resp parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Resp parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Resp parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Resp parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Resp parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static Resp parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static Resp parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static Resp parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static Resp parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static Resp parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static Resp parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static Resp parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(Resp prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code kuaishou.ad.rta.Resp}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:kuaishou.ad.rta.Resp)
          RespOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return RTAProto.internal_static_kuaishou_ad_rta_Resp_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return RTAProto.internal_static_kuaishou_ad_rta_Resp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Resp.class, Resp.Builder.class);
    }

    // Construct using com.kuaishou.protobuf.ad.rta.Resp.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getResultFieldBuilder();
        getDpaResultFieldBuilder();
      }
    }
    public Builder clear() {
      super.clear();
      id_ = "";

      resultType_ = 0;

      if (resultBuilder_ == null) {
        result_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
      } else {
        resultBuilder_.clear();
      }
      bidRatio_ = 0D;

      refuseTime_ = 0;

      cacheTime_ = 0;

      if (dpaResultBuilder_ == null) {
        dpaResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
      } else {
        dpaResultBuilder_.clear();
      }
      bid_ = 0D;

      bucketId_ = 0;

      return this;
    }

    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return RTAProto.internal_static_kuaishou_ad_rta_Resp_descriptor;
    }

    public Resp getDefaultInstanceForType() {
      return Resp.getDefaultInstance();
    }

    public Resp build() {
      Resp result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    public Resp buildPartial() {
      Resp result = new Resp(this);
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      result.id_ = id_;
      result.resultType_ = resultType_;
      if (resultBuilder_ == null) {
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          result_ = java.util.Collections.unmodifiableList(result_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.result_ = result_;
      } else {
        result.result_ = resultBuilder_.build();
      }
      result.bidRatio_ = bidRatio_;
      result.refuseTime_ = refuseTime_;
      result.cacheTime_ = cacheTime_;
      if (dpaResultBuilder_ == null) {
        if (((bitField0_ & 0x00000040) == 0x00000040)) {
          dpaResult_ = java.util.Collections.unmodifiableList(dpaResult_);
          bitField0_ = (bitField0_ & ~0x00000040);
        }
        result.dpaResult_ = dpaResult_;
      } else {
        result.dpaResult_ = dpaResultBuilder_.build();
      }
      result.bid_ = bid_;
      result.bucketId_ = bucketId_;
      result.bitField0_ = to_bitField0_;
      onBuilt();
      return result;
    }

    public Builder clone() {
      return (Builder) super.clone();
    }
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.setField(field, value);
    }
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return (Builder) super.clearField(field);
    }
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return (Builder) super.clearOneof(oneof);
    }
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return (Builder) super.setRepeatedField(field, index, value);
    }
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.addRepeatedField(field, value);
    }
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof Resp) {
        return mergeFrom((Resp)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(Resp other) {
      if (other == Resp.getDefaultInstance()) return this;
      if (!other.getId().isEmpty()) {
        id_ = other.id_;
        onChanged();
      }
      if (other.resultType_ != 0) {
        setResultTypeValue(other.getResultTypeValue());
      }
      if (resultBuilder_ == null) {
        if (!other.result_.isEmpty()) {
          if (result_.isEmpty()) {
            result_ = other.result_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureResultIsMutable();
            result_.addAll(other.result_);
          }
          onChanged();
        }
      } else {
        if (!other.result_.isEmpty()) {
          if (resultBuilder_.isEmpty()) {
            resultBuilder_.dispose();
            resultBuilder_ = null;
            result_ = other.result_;
            bitField0_ = (bitField0_ & ~0x00000004);
            resultBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getResultFieldBuilder() : null;
          } else {
            resultBuilder_.addAllMessages(other.result_);
          }
        }
      }
      if (other.getBidRatio() != 0D) {
        setBidRatio(other.getBidRatio());
      }
      if (other.getRefuseTime() != 0) {
        setRefuseTime(other.getRefuseTime());
      }
      if (other.getCacheTime() != 0) {
        setCacheTime(other.getCacheTime());
      }
      if (dpaResultBuilder_ == null) {
        if (!other.dpaResult_.isEmpty()) {
          if (dpaResult_.isEmpty()) {
            dpaResult_ = other.dpaResult_;
            bitField0_ = (bitField0_ & ~0x00000040);
          } else {
            ensureDpaResultIsMutable();
            dpaResult_.addAll(other.dpaResult_);
          }
          onChanged();
        }
      } else {
        if (!other.dpaResult_.isEmpty()) {
          if (dpaResultBuilder_.isEmpty()) {
            dpaResultBuilder_.dispose();
            dpaResultBuilder_ = null;
            dpaResult_ = other.dpaResult_;
            bitField0_ = (bitField0_ & ~0x00000040);
            dpaResultBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getDpaResultFieldBuilder() : null;
          } else {
            dpaResultBuilder_.addAllMessages(other.dpaResult_);
          }
        }
      }
      if (other.getBid() != 0D) {
        setBid(other.getBid());
      }
      if (other.getBucketId() != 0) {
        setBucketId(other.getBucketId());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    public final boolean isInitialized() {
      return true;
    }

    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      Resp parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (Resp) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private Object id_ = "";
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public String getId() {
      Object ref = id_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        id_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public com.google.protobuf.ByteString
        getIdBytes() {
      Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public Builder setId(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      id_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public Builder clearId() {
      
      id_ = getDefaultInstance().getId();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 请求唯一id
     * </pre>
     *
     * <code>string id = 1;</code>
     */
    public Builder setIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      id_ = value;
      onChanged();
      return this;
    }

    private int resultType_ = 0;
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
     */
    public int getResultTypeValue() {
      return resultType_;
    }
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
     */
    public Builder setResultTypeValue(int value) {
      resultType_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
     */
    public Resp.ResultType getResultType() {
      Resp.ResultType result = Resp.ResultType.valueOf(resultType_);
      return result == null ? Resp.ResultType.UNRECOGNIZED : result;
    }
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
     */
    public Builder setResultType(Resp.ResultType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      resultType_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 结果类型, 等于PART时, result必填
     * </pre>
     *
     * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
     */
    public Builder clearResultType() {
      
      resultType_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<RtaResult> result_ =
      java.util.Collections.emptyList();
    private void ensureResultIsMutable() {
      if (!((bitField0_ & 0x00000004) == 0x00000004)) {
        result_ = new java.util.ArrayList<RtaResult>(result_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
            RtaResult, RtaResult.Builder, RtaResultOrBuilder> resultBuilder_;

    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public java.util.List<RtaResult> getResultList() {
      if (resultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(result_);
      } else {
        return resultBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public int getResultCount() {
      if (resultBuilder_ == null) {
        return result_.size();
      } else {
        return resultBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public RtaResult getResult(int index) {
      if (resultBuilder_ == null) {
        return result_.get(index);
      } else {
        return resultBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder setResult(
        int index, RtaResult value) {
      if (resultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureResultIsMutable();
        result_.set(index, value);
        onChanged();
      } else {
        resultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder setResult(
        int index, RtaResult.Builder builderForValue) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.set(index, builderForValue.build());
        onChanged();
      } else {
        resultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder addResult(RtaResult value) {
      if (resultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureResultIsMutable();
        result_.add(value);
        onChanged();
      } else {
        resultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder addResult(
        int index, RtaResult value) {
      if (resultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureResultIsMutable();
        result_.add(index, value);
        onChanged();
      } else {
        resultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder addResult(
        RtaResult.Builder builderForValue) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.add(builderForValue.build());
        onChanged();
      } else {
        resultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder addResult(
        int index, RtaResult.Builder builderForValue) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.add(index, builderForValue.build());
        onChanged();
      } else {
        resultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder addAllResult(
        Iterable<? extends RtaResult> values) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, result_);
        onChanged();
      } else {
        resultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder clearResult() {
      if (resultBuilder_ == null) {
        result_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        resultBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public Builder removeResult(int index) {
      if (resultBuilder_ == null) {
        ensureResultIsMutable();
        result_.remove(index);
        onChanged();
      } else {
        resultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public RtaResult.Builder getResultBuilder(
        int index) {
      return getResultFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public RtaResultOrBuilder getResultOrBuilder(
        int index) {
      if (resultBuilder_ == null) {
        return result_.get(index);  } else {
        return resultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public java.util.List<? extends RtaResultOrBuilder>
         getResultOrBuilderList() {
      if (resultBuilder_ != null) {
        return resultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(result_);
      }
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public RtaResult.Builder addResultBuilder() {
      return getResultFieldBuilder().addBuilder(
          RtaResult.getDefaultInstance());
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public RtaResult.Builder addResultBuilder(
        int index) {
      return getResultFieldBuilder().addBuilder(
          index, RtaResult.getDefaultInstance());
    }
    /**
     * <pre>
     * result_type=PART时允许投放的广告列表, 如有重复取并集
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
     */
    public java.util.List<RtaResult.Builder>
         getResultBuilderList() {
      return getResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
            RtaResult, RtaResult.Builder, RtaResultOrBuilder>
        getResultFieldBuilder() {
      if (resultBuilder_ == null) {
        resultBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                RtaResult, RtaResult.Builder, RtaResultOrBuilder>(
                result_,
                ((bitField0_ & 0x00000004) == 0x00000004),
                getParentForChildren(),
                isClean());
        result_ = null;
      }
      return resultBuilder_;
    }

    private double bidRatio_ ;
    /**
     * <pre>
     * result_type=ALL时的动态出价系数
     * </pre>
     *
     * <code>double bid_ratio = 4;</code>
     */
    public double getBidRatio() {
      return bidRatio_;
    }
    /**
     * <pre>
     * result_type=ALL时的动态出价系数
     * </pre>
     *
     * <code>double bid_ratio = 4;</code>
     */
    public Builder setBidRatio(double value) {
      
      bidRatio_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * result_type=ALL时的动态出价系数
     * </pre>
     *
     * <code>double bid_ratio = 4;</code>
     */
    public Builder clearBidRatio() {
      
      bidRatio_ = 0D;
      onChanged();
      return this;
    }

    private int refuseTime_ ;
    /**
     * <pre>
     *拒绝时间
     * </pre>
     *
     * <code>uint32 refuse_time = 6;</code>
     */
    public int getRefuseTime() {
      return refuseTime_;
    }
    /**
     * <pre>
     *拒绝时间
     * </pre>
     *
     * <code>uint32 refuse_time = 6;</code>
     */
    public Builder setRefuseTime(int value) {
      
      refuseTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *拒绝时间
     * </pre>
     *
     * <code>uint32 refuse_time = 6;</code>
     */
    public Builder clearRefuseTime() {
      
      refuseTime_ = 0;
      onChanged();
      return this;
    }

    private int cacheTime_ ;
    /**
     * <pre>
     *缓存时间
     * </pre>
     *
     * <code>uint32 cache_time = 7;</code>
     */
    public int getCacheTime() {
      return cacheTime_;
    }
    /**
     * <pre>
     *缓存时间
     * </pre>
     *
     * <code>uint32 cache_time = 7;</code>
     */
    public Builder setCacheTime(int value) {
      
      cacheTime_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *缓存时间
     * </pre>
     *
     * <code>uint32 cache_time = 7;</code>
     */
    public Builder clearCacheTime() {
      
      cacheTime_ = 0;
      onChanged();
      return this;
    }

    private java.util.List<DpaResult> dpaResult_ =
      java.util.Collections.emptyList();
    private void ensureDpaResultIsMutable() {
      if (!((bitField0_ & 0x00000040) == 0x00000040)) {
        dpaResult_ = new java.util.ArrayList<DpaResult>(dpaResult_);
        bitField0_ |= 0x00000040;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
            DpaResult, DpaResult.Builder, DpaResultOrBuilder> dpaResultBuilder_;

    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public java.util.List<DpaResult> getDpaResultList() {
      if (dpaResultBuilder_ == null) {
        return java.util.Collections.unmodifiableList(dpaResult_);
      } else {
        return dpaResultBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public int getDpaResultCount() {
      if (dpaResultBuilder_ == null) {
        return dpaResult_.size();
      } else {
        return dpaResultBuilder_.getCount();
      }
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public DpaResult getDpaResult(int index) {
      if (dpaResultBuilder_ == null) {
        return dpaResult_.get(index);
      } else {
        return dpaResultBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder setDpaResult(
        int index, DpaResult value) {
      if (dpaResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDpaResultIsMutable();
        dpaResult_.set(index, value);
        onChanged();
      } else {
        dpaResultBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder setDpaResult(
        int index, DpaResult.Builder builderForValue) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.set(index, builderForValue.build());
        onChanged();
      } else {
        dpaResultBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder addDpaResult(DpaResult value) {
      if (dpaResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDpaResultIsMutable();
        dpaResult_.add(value);
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder addDpaResult(
        int index, DpaResult value) {
      if (dpaResultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDpaResultIsMutable();
        dpaResult_.add(index, value);
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder addDpaResult(
        DpaResult.Builder builderForValue) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.add(builderForValue.build());
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder addDpaResult(
        int index, DpaResult.Builder builderForValue) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.add(index, builderForValue.build());
        onChanged();
      } else {
        dpaResultBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder addAllDpaResult(
        Iterable<? extends DpaResult> values) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dpaResult_);
        onChanged();
      } else {
        dpaResultBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder clearDpaResult() {
      if (dpaResultBuilder_ == null) {
        dpaResult_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
      } else {
        dpaResultBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public Builder removeDpaResult(int index) {
      if (dpaResultBuilder_ == null) {
        ensureDpaResultIsMutable();
        dpaResult_.remove(index);
        onChanged();
      } else {
        dpaResultBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public DpaResult.Builder getDpaResultBuilder(
        int index) {
      return getDpaResultFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public DpaResultOrBuilder getDpaResultOrBuilder(
        int index) {
      if (dpaResultBuilder_ == null) {
        return dpaResult_.get(index);  } else {
        return dpaResultBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public java.util.List<? extends DpaResultOrBuilder>
         getDpaResultOrBuilderList() {
      if (dpaResultBuilder_ != null) {
        return dpaResultBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(dpaResult_);
      }
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public DpaResult.Builder addDpaResultBuilder() {
      return getDpaResultFieldBuilder().addBuilder(
          DpaResult.getDefaultInstance());
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public DpaResult.Builder addDpaResultBuilder(
        int index) {
      return getDpaResultFieldBuilder().addBuilder(
          index, DpaResult.getDefaultInstance());
    }
    /**
     * <pre>
     *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
     * </pre>
     *
     * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
     */
    public java.util.List<DpaResult.Builder>
         getDpaResultBuilderList() {
      return getDpaResultFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
            DpaResult, DpaResult.Builder, DpaResultOrBuilder>
        getDpaResultFieldBuilder() {
      if (dpaResultBuilder_ == null) {
        dpaResultBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                DpaResult, DpaResult.Builder, DpaResultOrBuilder>(
                dpaResult_,
                ((bitField0_ & 0x00000040) == 0x00000040),
                getParentForChildren(),
                isClean());
        dpaResult_ = null;
      }
      return dpaResultBuilder_;
    }

    private double bid_ ;
    /**
     * <pre>
     *result_type=ALL时的动态出价,单位元
     * </pre>
     *
     * <code>double bid = 9;</code>
     */
    public double getBid() {
      return bid_;
    }
    /**
     * <pre>
     *result_type=ALL时的动态出价,单位元
     * </pre>
     *
     * <code>double bid = 9;</code>
     */
    public Builder setBid(double value) {
      
      bid_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     *result_type=ALL时的动态出价,单位元
     * </pre>
     *
     * <code>double bid = 9;</code>
     */
    public Builder clearBid() {
      
      bid_ = 0D;
      onChanged();
      return this;
    }

    private int bucketId_ ;
    /**
     * <pre>
     * 客户分桶id
     * </pre>
     *
     * <code>uint32 bucket_id = 10;</code>
     */
    public int getBucketId() {
      return bucketId_;
    }
    /**
     * <pre>
     * 客户分桶id
     * </pre>
     *
     * <code>uint32 bucket_id = 10;</code>
     */
    public Builder setBucketId(int value) {
      
      bucketId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 客户分桶id
     * </pre>
     *
     * <code>uint32 bucket_id = 10;</code>
     */
    public Builder clearBucketId() {
      
      bucketId_ = 0;
      onChanged();
      return this;
    }
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFieldsProto3(unknownFields);
    }

    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:kuaishou.ad.rta.Resp)
  }

  // @@protoc_insertion_point(class_scope:kuaishou.ad.rta.Resp)
  private static final Resp DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new Resp();
  }

  public static Resp getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<Resp>
      PARSER = new com.google.protobuf.AbstractParser<Resp>() {
    public Resp parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new Resp(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<Resp> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<Resp> getParserForType() {
    return PARSER;
  }

  public Resp getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

