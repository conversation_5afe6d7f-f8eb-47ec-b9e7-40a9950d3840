// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

public interface ReqOrBuilder extends
    // @@protoc_insertion_point(interface_extends:kuaishou.ad.rta.Req)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  String getId();
  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * 系统
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
   */
  int getOsTypeValue();
  /**
   * <pre>
   * 系统
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OsType os_type = 2;</code>
   */
  OsType getOsType();

  /**
   * <pre>
   * 安卓为 imei_md5，ios 为 idfa_md5
   * </pre>
   *
   * <code>string did_md5 = 3;</code>
   */
  String getDidMd5();
  /**
   * <pre>
   * 安卓为 imei_md5，ios 为 idfa_md5
   * </pre>
   *
   * <code>string did_md5 = 3;</code>
   */
  com.google.protobuf.ByteString
      getDidMd5Bytes();

  /**
   * <pre>
   * 广告位 ID
   * </pre>
   *
   * <code>string tag_id = 4;</code>
   */
  String getTagId();
  /**
   * <pre>
   * 广告位 ID
   * </pre>
   *
   * <code>string tag_id = 4;</code>
   */
  com.google.protobuf.ByteString
      getTagIdBytes();

  /**
   * <pre>
   * 年龄段, 0(0-11), 1(12-17), 2(18~23), 3(24~30), 4(31~35), 5(36~40), 6(41~45), 7(46~49), 8(50~54), 9(55~59), 10(60+)
   * </pre>
   *
   * <code>int32 age = 6;</code>
   */
  int getAge();

  /**
   * <pre>
   * 设备型号
   * </pre>
   *
   * <code>string model = 9;</code>
   */
  String getModel();
  /**
   * <pre>
   * 设备型号
   * </pre>
   *
   * <code>string model = 9;</code>
   */
  com.google.protobuf.ByteString
      getModelBytes();

  /**
   * <pre>
   * 设备oaid
   * </pre>
   *
   * <code>string oaid = 10;</code>
   */
  String getOaid();
  /**
   * <pre>
   * 设备oaid
   * </pre>
   *
   * <code>string oaid = 10;</code>
   */
  com.google.protobuf.ByteString
      getOaidBytes();

  /**
   * <pre>
   *请求UA信息
   * </pre>
   *
   * <code>string user_agent = 11;</code>
   */
  String getUserAgent();
  /**
   * <pre>
   *请求UA信息
   * </pre>
   *
   * <code>string user_agent = 11;</code>
   */
  com.google.protobuf.ByteString
      getUserAgentBytes();

  /**
   * <pre>
   *安卓设备md5(oaid)
   * </pre>
   *
   * <code>string oaid_md5 = 12;</code>
   */
  String getOaidMd5();
  /**
   * <pre>
   *安卓设备md5(oaid)
   * </pre>
   *
   * <code>string oaid_md5 = 12;</code>
   */
  com.google.protobuf.ByteString
      getOaidMd5Bytes();

  /**
   * <code>string ipv6 = 13;</code>
   */
  String getIpv6();
  /**
   * <code>string ipv6 = 13;</code>
   */
  com.google.protobuf.ByteString
      getIpv6Bytes();

  /**
   * <pre>
   *上网类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
   */
  int getConnectionTypeValue();
  /**
   * <pre>
   *上网类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.ConnectionType connection_type = 14;</code>
   */
  ConnectionType getConnectionType();

  /**
   * <pre>
   * 运营商类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
   */
  int getOperatorTypeValue();
  /**
   * <pre>
   * 运营商类型
   * </pre>
   *
   * <code>.kuaishou.ad.rta.OperatorType operator_type = 15;</code>
   */
  OperatorType getOperatorType();

  /**
   * <pre>
   * 常驻地adcode
   * </pre>
   *
   * <code>uint64 adcode = 16;</code>
   */
  long getAdcode();

  /**
   * <pre>
   * 当前caid
   * </pre>
   *
   * <code>string current_caid = 21;</code>
   */
  String getCurrentCaid();
  /**
   * <pre>
   * 当前caid
   * </pre>
   *
   * <code>string current_caid = 21;</code>
   */
  com.google.protobuf.ByteString
      getCurrentCaidBytes();

  /**
   * <pre>
   * 当前caid版本
   * </pre>
   *
   * <code>string current_caid_version = 22;</code>
   */
  String getCurrentCaidVersion();
  /**
   * <pre>
   * 当前caid版本
   * </pre>
   *
   * <code>string current_caid_version = 22;</code>
   */
  com.google.protobuf.ByteString
      getCurrentCaidVersionBytes();

  /**
   * <pre>
   * 上一版本caid
   * </pre>
   *
   * <code>string last_caid = 23;</code>
   */
  String getLastCaid();
  /**
   * <pre>
   * 上一版本caid
   * </pre>
   *
   * <code>string last_caid = 23;</code>
   */
  com.google.protobuf.ByteString
      getLastCaidBytes();

  /**
   * <pre>
   * 上一版本caid版本
   * </pre>
   *
   * <code>string last_caid_version = 24;</code>
   */
  String getLastCaidVersion();
  /**
   * <pre>
   * 上一版本caid版本
   * </pre>
   *
   * <code>string last_caid_version = 24;</code>
   */
  com.google.protobuf.ByteString
      getLastCaidVersionBytes();

  /**
   * <pre>
   *手机厂商名
   * </pre>
   *
   * <code>string brand = 25;</code>
   */
  String getBrand();
  /**
   * <pre>
   *手机厂商名
   * </pre>
   *
   * <code>string brand = 25;</code>
   */
  com.google.protobuf.ByteString
      getBrandBytes();

  /**
   * <pre>
   *媒体一级行业id
   * </pre>
   *
   * <code>uint64 media_industry_id = 26;</code>
   */
  long getMediaIndustryId();

  /**
   * <pre>
   * 客户自定义信息，使用前需要线下约定。
   * </pre>
   *
   * <code>string custom_info = 28;</code>
   */
  String getCustomInfo();
  /**
   * <pre>
   * 客户自定义信息，使用前需要线下约定。
   * </pre>
   *
   * <code>string custom_info = 28;</code>
   */
  com.google.protobuf.ByteString
      getCustomInfoBytes();

  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  java.util.List<String>
      getInstallAppList();
  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  int getInstallAppCount();
  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  String getInstallApp(int index);
  /**
   * <pre>
   * 安装的app列表
   * </pre>
   *
   * <code>repeated string install_app = 30;</code>
   */
  com.google.protobuf.ByteString
      getInstallAppBytes(int index);

  /**
   * <pre>
   * 拼多多设备指纹
   * </pre>
   *
   * <code>string paid = 38;</code>
   */
  String getPaid();
  /**
   * <pre>
   * 拼多多设备指纹
   * </pre>
   *
   * <code>string paid = 38;</code>
   */
  com.google.protobuf.ByteString
      getPaidBytes();

  /**
   * <pre>
   * 阿里设备指纹
   * </pre>
   *
   * <code>string aaid = 39;</code>
   */
  String getAaid();
  /**
   * <pre>
   * 阿里设备指纹
   * </pre>
   *
   * <code>string aaid = 39;</code>
   */
  com.google.protobuf.ByteString
      getAaidBytes();

  /**
   * <pre>
   * 资源位信息: 1: 信息流、2: 激励视频、3：搜索、4：开屏; 现为邀请制功能，如有需求请联系客户运营
   * </pre>
   *
   * <code>uint32 site_set = 40;</code>
   */
  int getSiteSet();

  /**
   * <code>string ipv4 = 41;</code>
   */
  String getIpv4();
  /**
   * <code>string ipv4 = 41;</code>
   */
  com.google.protobuf.ByteString
      getIpv4Bytes();

  /**
   * <pre>
   * 是否安装社保app, 1(未安装)、2（已安装）
   * </pre>
   *
   * <code>uint32 install_social_insurance = 45;</code>
   */
  int getInstallSocialInsurance();

  /**
   * <pre>
   * 是否支付, 1(未安装)、2（已安装）
   * </pre>
   *
   * <code>uint32 payment = 46;</code>
   */
  int getPayment();

  /**
   * <pre>
   * 设备价格, 1(1000以下)、2（1000-1500）、3（1500-2000）、4（2000-2500）、5（2500-3000）、6（3000-3500）、7（3500-4000）、8（4000-5000）、9（5000-6000）、10（6000+）
   * </pre>
   *
   * <code>uint32 phone_price_id = 50;</code>
   */
  int getPhonePriceId();

  /**
   * <pre>
   * 消费水平, 1(高)、2（中）、3（低）
   * </pre>
   *
   * <code>uint32 consumption_level_id = 51;</code>
   */
  int getConsumptionLevelId();

  /**
   * <pre>
   * 教育水平, 1(高中及以下)、2(专科)、3(本科)、4(硕士及以上)
   * </pre>
   *
   * <code>uint32 education_id = 53;</code>
   */
  int getEducationId();

  /**
   * <pre>
   * 性别, 1(男)、2(女)
   * </pre>
   *
   * <code>uint32 gender_id = 54;</code>
   */
  int getGenderId();

  /**
   * <pre>
   * 纬度
   * </pre>
   *
   * <code>double latitude = 55;</code>
   */
  double getLatitude();

  /**
   * <pre>
   * 经度
   * </pre>
   *
   * <code>double longitude = 56;</code>
   */
  double getLongitude();

  /**
   * <pre>
   * 用户embedding向量
   * </pre>
   *
   * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
   */
  boolean hasUserEmbedding();
  /**
   * <pre>
   * 用户embedding向量
   * </pre>
   *
   * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
   */
  UserEmb getUserEmbedding();
  /**
   * <pre>
   * 用户embedding向量
   * </pre>
   *
   * <code>.kuaishou.ad.rta.UserEmb user_embedding = 57;</code>
   */
  UserEmbOrBuilder getUserEmbeddingOrBuilder();

  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 当前版本
   * </pre>
   *
   * <code>string current_paid_1_5 = 60;</code>
   */
  String getCurrentPaid15();
  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 当前版本
   * </pre>
   *
   * <code>string current_paid_1_5 = 60;</code>
   */
  com.google.protobuf.ByteString
      getCurrentPaid15Bytes();

  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 上一个版本
   * </pre>
   *
   * <code>string last_paid_1_5 = 61;</code>
   */
  String getLastPaid15();
  /**
   * <pre>
   * 拼多多设备指纹paid1.5, 上一个版本
   * </pre>
   *
   * <code>string last_paid_1_5 = 61;</code>
   */
  com.google.protobuf.ByteString
      getLastPaid15Bytes();

  /**
   * <pre>
   * 换机设备标识
   * </pre>
   *
   * <code>string previous_model = 62;</code>
   */
  String getPreviousModel();
  /**
   * <pre>
   * 换机设备标识
   * </pre>
   *
   * <code>string previous_model = 62;</code>
   */
  com.google.protobuf.ByteString
      getPreviousModelBytes();

  /**
   * <pre>
   * 实验or策略信息，需线下约定
   * </pre>
   *
   * <code>string feature = 101;</code>
   */
  String getFeature();
  /**
   * <pre>
   * 实验or策略信息，需线下约定
   * </pre>
   *
   * <code>string feature = 101;</code>
   */
  com.google.protobuf.ByteString
      getFeatureBytes();
}
