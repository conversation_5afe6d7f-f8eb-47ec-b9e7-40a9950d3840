// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf enum {@code kuaishou.ad.rta.ConnectionType}
 */
public enum ConnectionType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 无法探测当前网络状态
   * </pre>
   *
   * <code>UNKNOWN_CONNECTION_TYPE = 0;</code>
   */
  UNKNOWN_CONNECTION_TYPE(0),
  /**
   * <pre>
   * 蜂窝数据接入，未知网络类型
   * </pre>
   *
   * <code>CELL_UNKNOWN = 1;</code>
   */
  CELL_UNKNOWN(1),
  /**
   * <pre>
   * 蜂窝数据2G网络
   * </pre>
   *
   * <code>CELL_2G = 2;</code>
   */
  CELL_2G(2),
  /**
   * <pre>
   * 蜂窝数据3G网络
   * </pre>
   *
   * <code>CELL_3G = 3;</code>
   */
  CELL_3G(3),
  /**
   * <pre>
   * 蜂窝数据4G网络
   * </pre>
   *
   * <code>CELL_4G = 4;</code>
   */
  CELL_4G(4),
  /**
   * <pre>
   * 蜂窝数据5G网络
   * </pre>
   *
   * <code>CELL_5G = 5;</code>
   */
  CELL_5G(5),
  /**
   * <pre>
   *LTE网络
   * </pre>
   *
   * <code>LTE = 6;</code>
   */
  LTE(6),
  /**
   * <pre>
   * Wi-Fi网络接入
   * </pre>
   *
   * <code>WIFI = 100;</code>
   */
  WIFI(100),
  /**
   * <pre>
   * 以太网接入
   * </pre>
   *
   * <code>ETHERNET = 101;</code>
   */
  ETHERNET(101),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * 无法探测当前网络状态
   * </pre>
   *
   * <code>UNKNOWN_CONNECTION_TYPE = 0;</code>
   */
  public static final int UNKNOWN_CONNECTION_TYPE_VALUE = 0;
  /**
   * <pre>
   * 蜂窝数据接入，未知网络类型
   * </pre>
   *
   * <code>CELL_UNKNOWN = 1;</code>
   */
  public static final int CELL_UNKNOWN_VALUE = 1;
  /**
   * <pre>
   * 蜂窝数据2G网络
   * </pre>
   *
   * <code>CELL_2G = 2;</code>
   */
  public static final int CELL_2G_VALUE = 2;
  /**
   * <pre>
   * 蜂窝数据3G网络
   * </pre>
   *
   * <code>CELL_3G = 3;</code>
   */
  public static final int CELL_3G_VALUE = 3;
  /**
   * <pre>
   * 蜂窝数据4G网络
   * </pre>
   *
   * <code>CELL_4G = 4;</code>
   */
  public static final int CELL_4G_VALUE = 4;
  /**
   * <pre>
   * 蜂窝数据5G网络
   * </pre>
   *
   * <code>CELL_5G = 5;</code>
   */
  public static final int CELL_5G_VALUE = 5;
  /**
   * <pre>
   *LTE网络
   * </pre>
   *
   * <code>LTE = 6;</code>
   */
  public static final int LTE_VALUE = 6;
  /**
   * <pre>
   * Wi-Fi网络接入
   * </pre>
   *
   * <code>WIFI = 100;</code>
   */
  public static final int WIFI_VALUE = 100;
  /**
   * <pre>
   * 以太网接入
   * </pre>
   *
   * <code>ETHERNET = 101;</code>
   */
  public static final int ETHERNET_VALUE = 101;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @Deprecated
  public static ConnectionType valueOf(int value) {
    return forNumber(value);
  }

  public static ConnectionType forNumber(int value) {
    switch (value) {
      case 0: return UNKNOWN_CONNECTION_TYPE;
      case 1: return CELL_UNKNOWN;
      case 2: return CELL_2G;
      case 3: return CELL_3G;
      case 4: return CELL_4G;
      case 5: return CELL_5G;
      case 6: return LTE;
      case 100: return WIFI;
      case 101: return ETHERNET;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ConnectionType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ConnectionType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<ConnectionType>() {
          public ConnectionType findValueByNumber(int number) {
            return ConnectionType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return RTAProto.getDescriptor().getEnumTypes().get(1);
  }

  private static final ConnectionType[] VALUES = values();

  public static ConnectionType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ConnectionType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:kuaishou.ad.rta.ConnectionType)
}

