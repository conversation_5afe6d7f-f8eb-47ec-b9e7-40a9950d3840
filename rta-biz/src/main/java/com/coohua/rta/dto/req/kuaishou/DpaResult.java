// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf type {@code kuaishou.ad.rta.DpaResult}
 */
public  final class <PERSON><PERSON>R<PERSON>ult extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:kuaishou.ad.rta.DpaResult)
    DpaResultOrBuilder {
private static final long serialVersionUID = 0L;
  // Use DpaResult.newBuilder() to construct.
  private DpaResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private DpaResult() {
    libId_ = 0;
    pidScore_ = 0;
    pid_ = com.google.protobuf.LazyStringArrayList.EMPTY;
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private DpaResult(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          default: {
            if (!parseUnknownFieldProto3(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
          case 8: {

            libId_ = input.readUInt32();
            break;
          }
          case 16: {

            pidScore_ = input.readUInt32();
            break;
          }
          case 26: {
            String s = input.readStringRequireUtf8();
            if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
              pid_ = new com.google.protobuf.LazyStringArrayList();
              mutable_bitField0_ |= 0x00000004;
            }
            pid_.add(s);
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
        pid_ = pid_.getUnmodifiableView();
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return RTAProto.internal_static_kuaishou_ad_rta_DpaResult_descriptor;
  }

  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return RTAProto.internal_static_kuaishou_ad_rta_DpaResult_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            DpaResult.class, DpaResult.Builder.class);
  }

  private int bitField0_;
  public static final int LIB_ID_FIELD_NUMBER = 1;
  private int libId_;
  /**
   * <pre>
   * 商品库Id
   * </pre>
   *
   * <code>uint32 lib_id = 1;</code>
   */
  public int getLibId() {
    return libId_;
  }

  public static final int PID_SCORE_FIELD_NUMBER = 2;
  private int pidScore_;
  /**
   * <pre>
   * 取值范围0-4，整数，0为最重要，4为最次要
   * </pre>
   *
   * <code>uint32 pid_score = 2;</code>
   */
  public int getPidScore() {
    return pidScore_;
  }

  public static final int PID_FIELD_NUMBER = 3;
  private com.google.protobuf.LazyStringList pid_;
  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  public com.google.protobuf.ProtocolStringList
      getPidList() {
    return pid_;
  }
  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  public int getPidCount() {
    return pid_.size();
  }
  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  public String getPid(int index) {
    return pid_.get(index);
  }
  /**
   * <pre>
   * 商品id列表
   * </pre>
   *
   * <code>repeated string pid = 3;</code>
   */
  public com.google.protobuf.ByteString
      getPidBytes(int index) {
    return pid_.getByteString(index);
  }

  private byte memoizedIsInitialized = -1;
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (libId_ != 0) {
      output.writeUInt32(1, libId_);
    }
    if (pidScore_ != 0) {
      output.writeUInt32(2, pidScore_);
    }
    for (int i = 0; i < pid_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, pid_.getRaw(i));
    }
    unknownFields.writeTo(output);
  }

  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (libId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(1, libId_);
    }
    if (pidScore_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt32Size(2, pidScore_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < pid_.size(); i++) {
        dataSize += computeStringSizeNoTag(pid_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getPidList().size();
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof DpaResult)) {
      return super.equals(obj);
    }
    DpaResult other = (DpaResult) obj;

    boolean result = true;
    result = result && (getLibId()
        == other.getLibId());
    result = result && (getPidScore()
        == other.getPidScore());
    result = result && getPidList()
        .equals(other.getPidList());
    result = result && unknownFields.equals(other.unknownFields);
    return result;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LIB_ID_FIELD_NUMBER;
    hash = (53 * hash) + getLibId();
    hash = (37 * hash) + PID_SCORE_FIELD_NUMBER;
    hash = (53 * hash) + getPidScore();
    if (getPidCount() > 0) {
      hash = (37 * hash) + PID_FIELD_NUMBER;
      hash = (53 * hash) + getPidList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static DpaResult parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static DpaResult parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static DpaResult parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static DpaResult parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static DpaResult parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static DpaResult parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static DpaResult parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static DpaResult parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static DpaResult parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static DpaResult parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static DpaResult parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static DpaResult parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(DpaResult prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code kuaishou.ad.rta.DpaResult}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:kuaishou.ad.rta.DpaResult)
          DpaResultOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return RTAProto.internal_static_kuaishou_ad_rta_DpaResult_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return RTAProto.internal_static_kuaishou_ad_rta_DpaResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              DpaResult.class, DpaResult.Builder.class);
    }

    // Construct using com.kuaishou.protobuf.ad.rta.DpaResult.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    public Builder clear() {
      super.clear();
      libId_ = 0;

      pidScore_ = 0;

      pid_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000004);
      return this;
    }

    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return RTAProto.internal_static_kuaishou_ad_rta_DpaResult_descriptor;
    }

    public DpaResult getDefaultInstanceForType() {
      return DpaResult.getDefaultInstance();
    }

    public DpaResult build() {
      DpaResult result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    public DpaResult buildPartial() {
      DpaResult result = new DpaResult(this);
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      result.libId_ = libId_;
      result.pidScore_ = pidScore_;
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        pid_ = pid_.getUnmodifiableView();
        bitField0_ = (bitField0_ & ~0x00000004);
      }
      result.pid_ = pid_;
      result.bitField0_ = to_bitField0_;
      onBuilt();
      return result;
    }

    public Builder clone() {
      return (Builder) super.clone();
    }
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.setField(field, value);
    }
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return (Builder) super.clearField(field);
    }
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return (Builder) super.clearOneof(oneof);
    }
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return (Builder) super.setRepeatedField(field, index, value);
    }
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return (Builder) super.addRepeatedField(field, value);
    }
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof DpaResult) {
        return mergeFrom((DpaResult)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(DpaResult other) {
      if (other == DpaResult.getDefaultInstance()) return this;
      if (other.getLibId() != 0) {
        setLibId(other.getLibId());
      }
      if (other.getPidScore() != 0) {
        setPidScore(other.getPidScore());
      }
      if (!other.pid_.isEmpty()) {
        if (pid_.isEmpty()) {
          pid_ = other.pid_;
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          ensurePidIsMutable();
          pid_.addAll(other.pid_);
        }
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    public final boolean isInitialized() {
      return true;
    }

    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      DpaResult parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (DpaResult) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private int libId_ ;
    /**
     * <pre>
     * 商品库Id
     * </pre>
     *
     * <code>uint32 lib_id = 1;</code>
     */
    public int getLibId() {
      return libId_;
    }
    /**
     * <pre>
     * 商品库Id
     * </pre>
     *
     * <code>uint32 lib_id = 1;</code>
     */
    public Builder setLibId(int value) {
      
      libId_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商品库Id
     * </pre>
     *
     * <code>uint32 lib_id = 1;</code>
     */
    public Builder clearLibId() {
      
      libId_ = 0;
      onChanged();
      return this;
    }

    private int pidScore_ ;
    /**
     * <pre>
     * 取值范围0-4，整数，0为最重要，4为最次要
     * </pre>
     *
     * <code>uint32 pid_score = 2;</code>
     */
    public int getPidScore() {
      return pidScore_;
    }
    /**
     * <pre>
     * 取值范围0-4，整数，0为最重要，4为最次要
     * </pre>
     *
     * <code>uint32 pid_score = 2;</code>
     */
    public Builder setPidScore(int value) {
      
      pidScore_ = value;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 取值范围0-4，整数，0为最重要，4为最次要
     * </pre>
     *
     * <code>uint32 pid_score = 2;</code>
     */
    public Builder clearPidScore() {
      
      pidScore_ = 0;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringList pid_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    private void ensurePidIsMutable() {
      if (!((bitField0_ & 0x00000004) == 0x00000004)) {
        pid_ = new com.google.protobuf.LazyStringArrayList(pid_);
        bitField0_ |= 0x00000004;
       }
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getPidList() {
      return pid_.getUnmodifiableView();
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public int getPidCount() {
      return pid_.size();
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public String getPid(int index) {
      return pid_.get(index);
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public com.google.protobuf.ByteString
        getPidBytes(int index) {
      return pid_.getByteString(index);
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public Builder setPid(
        int index, String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensurePidIsMutable();
      pid_.set(index, value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public Builder addPid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  ensurePidIsMutable();
      pid_.add(value);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public Builder addAllPid(
        Iterable<String> values) {
      ensurePidIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, pid_);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public Builder clearPid() {
      pid_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 商品id列表
     * </pre>
     *
     * <code>repeated string pid = 3;</code>
     */
    public Builder addPidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      ensurePidIsMutable();
      pid_.add(value);
      onChanged();
      return this;
    }
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFieldsProto3(unknownFields);
    }

    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:kuaishou.ad.rta.DpaResult)
  }

  // @@protoc_insertion_point(class_scope:kuaishou.ad.rta.DpaResult)
  private static final DpaResult DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new DpaResult();
  }

  public static DpaResult getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<DpaResult>
      PARSER = new com.google.protobuf.AbstractParser<DpaResult>() {
    public DpaResult parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new DpaResult(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<DpaResult> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<DpaResult> getParserForType() {
    return PARSER;
  }

  public DpaResult getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

