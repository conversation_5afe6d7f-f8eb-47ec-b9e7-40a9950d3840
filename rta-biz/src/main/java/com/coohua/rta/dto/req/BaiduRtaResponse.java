package com.coohua.rta.dto.req;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: BaiduRtaResponse.proto

public final class BaiduRtaResponse {
  private BaiduRtaResponse() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code RtaBidType}
   */
  public enum RtaBidType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 不溢价
     * </pre>
     *
     * <code>RTA_NORMAL = 0;</code>
     */
    RTA_NORMAL(0),
    /**
     * <pre>
     * 动态出价
     * </pre>
     *
     * <code>RTA_BID = 1;</code>
     */
    RTA_BID(1),
    /**
     * <pre>
     * rta溢价
     * </pre>
     *
     * <code>RTA_RISE = 2;</code>
     */
    RTA_RISE(2),
    ;

    /**
     * <pre>
     * 不溢价
     * </pre>
     *
     * <code>RTA_NORMAL = 0;</code>
     */
    public static final int RTA_NORMAL_VALUE = 0;
    /**
     * <pre>
     * 动态出价
     * </pre>
     *
     * <code>RTA_BID = 1;</code>
     */
    public static final int RTA_BID_VALUE = 1;
    /**
     * <pre>
     * rta溢价
     * </pre>
     *
     * <code>RTA_RISE = 2;</code>
     */
    public static final int RTA_RISE_VALUE = 2;


    public final int getNumber() {
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static RtaBidType valueOf(int value) {
      return forNumber(value);
    }

    public static RtaBidType forNumber(int value) {
      switch (value) {
        case 0: return RTA_NORMAL;
        case 1: return RTA_BID;
        case 2: return RTA_RISE;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<RtaBidType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        RtaBidType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<RtaBidType>() {
            public RtaBidType findValueByNumber(int number) {
              return RtaBidType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return BaiduRtaResponse.getDescriptor().getEnumTypes().get(0);
    }

    private static final RtaBidType[] VALUES = values();

    public static RtaBidType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private RtaBidType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:RtaBidType)
  }

  /**
   * Protobuf enum {@code ResType}
   */
  public enum ResType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 全部投放
     * </pre>
     *
     * <code>ALL = 0;</code>
     */
    ALL(0),
    /**
     * <pre>
     * 全部不投
     * </pre>
     *
     * <code>NONE = 1;</code>
     */
    NONE(1),
    /**
     * <pre>
     * 只投放指定部分
     * </pre>
     *
     * <code>PART = 2;</code>
     */
    PART(2),
    ;

    /**
     * <pre>
     * 全部投放
     * </pre>
     *
     * <code>ALL = 0;</code>
     */
    public static final int ALL_VALUE = 0;
    /**
     * <pre>
     * 全部不投
     * </pre>
     *
     * <code>NONE = 1;</code>
     */
    public static final int NONE_VALUE = 1;
    /**
     * <pre>
     * 只投放指定部分
     * </pre>
     *
     * <code>PART = 2;</code>
     */
    public static final int PART_VALUE = 2;


    public final int getNumber() {
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static ResType valueOf(int value) {
      return forNumber(value);
    }

    public static ResType forNumber(int value) {
      switch (value) {
        case 0: return ALL;
        case 1: return NONE;
        case 2: return PART;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ResType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        ResType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ResType>() {
            public ResType findValueByNumber(int number) {
              return ResType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return BaiduRtaResponse.getDescriptor().getEnumTypes().get(1);
    }

    private static final ResType[] VALUES = values();

    public static ResType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private ResType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:ResType)
  }

  public interface RtaApiResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RtaApiResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required uint64 qid = 1;</code>
     */
    boolean hasQid();
    /**
     * <code>required uint64 qid = 1;</code>
     */
    long getQid();

    /**
     * <code>required .ResType res = 2;</code>
     */
    boolean hasRes();
    /**
     * <code>required .ResType res = 2;</code>
     */
    ResType getRes();

    /**
     * <pre>
     * 客户打分，可选，DPA暂不可用
     * </pre>
     *
     * <code>optional uint32 user_score = 3;</code>
     */
    boolean hasUserScore();
    /**
     * <pre>
     * 客户打分，可选，DPA暂不可用
     * </pre>
     *
     * <code>optional uint32 user_score = 3;</code>
     */
    int getUserScore();

    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    java.util.List<AdResult>
        getAdResultsList();
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    AdResult getAdResults(int index);
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    int getAdResultsCount();
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    java.util.List<? extends AdResultOrBuilder>
        getAdResultsOrBuilderList();
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    AdResultOrBuilder getAdResultsOrBuilder(
        int index);

    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    java.util.List<RtaStrategyAdResult>
        getStrategyResultsList();
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    RtaStrategyAdResult getStrategyResults(int index);
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    int getStrategyResultsCount();
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    java.util.List<? extends RtaStrategyAdResultOrBuilder>
        getStrategyResultsOrBuilderList();
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    RtaStrategyAdResultOrBuilder getStrategyResultsOrBuilder(
        int index);

    /**
     * <pre>
     * DPA相关数据
     * </pre>
     *
     * <code>optional .DpaResult dpa_results = 6;</code>
     */
    boolean hasDpaResults();
    /**
     * <pre>
     * DPA相关数据
     * </pre>
     *
     * <code>optional .DpaResult dpa_results = 6;</code>
     */
    DpaResult getDpaResults();
    /**
     * <pre>
     * DPA相关数据
     * </pre>
     *
     * <code>optional .DpaResult dpa_results = 6;</code>
     */
    DpaResultOrBuilder getDpaResultsOrBuilder();

    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    java.util.List<BidRise>
        getRtaBidRiseList();
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    BidRise getRtaBidRise(int index);
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    int getRtaBidRiseCount();
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    java.util.List<? extends BidRiseOrBuilder>
        getRtaBidRiseOrBuilderList();
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    BidRiseOrBuilder getRtaBidRiseOrBuilder(
        int index);

    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 9;</code>
     */
    boolean hasPrefetchDate();
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 9;</code>
     */
    String getPrefetchDate();
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 9;</code>
     */
    com.google.protobuf.ByteString
        getPrefetchDateBytes();

    /**
     * <pre>
     * 标志是否使用rta溢价
     * </pre>
     *
     * <code>optional .RtaBidType rta_bid_type = 10;</code>
     */
    boolean hasRtaBidType();
    /**
     * <pre>
     * 标志是否使用rta溢价
     * </pre>
     *
     * <code>optional .RtaBidType rta_bid_type = 10;</code>
     */
    RtaBidType getRtaBidType();

    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    java.util.List<RtaBid>
        getRtaBidList();
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    RtaBid getRtaBid(int index);
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    int getRtaBidCount();
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    java.util.List<? extends RtaBidOrBuilder>
        getRtaBidOrBuilderList();
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    RtaBidOrBuilder getRtaBidOrBuilder(
        int index);

    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    java.util.List<RtaBid>
        getRtaCpcBidList();
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    RtaBid getRtaCpcBid(int index);
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    int getRtaCpcBidCount();
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    java.util.List<? extends RtaBidOrBuilder>
        getRtaCpcBidOrBuilderList();
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    RtaBidOrBuilder getRtaCpcBidOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code RtaApiResponse}
   */
  public  static final class RtaApiResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RtaApiResponse)
      RtaApiResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RtaApiResponse.newBuilder() to construct.
    private RtaApiResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RtaApiResponse() {
      qid_ = 0L;
      res_ = 0;
      userScore_ = 0;
      adResults_ = java.util.Collections.emptyList();
      strategyResults_ = java.util.Collections.emptyList();
      rtaBidRise_ = java.util.Collections.emptyList();
      prefetchDate_ = "";
      rtaBidType_ = 0;
      rtaBid_ = java.util.Collections.emptyList();
      rtaCpcBid_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RtaApiResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              qid_ = input.readUInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
              ResType value = ResType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                res_ = rawValue;
              }
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              userScore_ = input.readUInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                adResults_ = new java.util.ArrayList<AdResult>();
                mutable_bitField0_ |= 0x00000008;
              }
              adResults_.add(
                  input.readMessage(AdResult.PARSER, extensionRegistry));
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                strategyResults_ = new java.util.ArrayList<RtaStrategyAdResult>();
                mutable_bitField0_ |= 0x00000010;
              }
              strategyResults_.add(
                  input.readMessage(RtaStrategyAdResult.PARSER, extensionRegistry));
              break;
            }
            case 50: {
              DpaResult.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) == 0x00000008)) {
                subBuilder = dpaResults_.toBuilder();
              }
              dpaResults_ = input.readMessage(DpaResult.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(dpaResults_);
                dpaResults_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 66: {
              if (!((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
                rtaBidRise_ = new java.util.ArrayList<BidRise>();
                mutable_bitField0_ |= 0x00000040;
              }
              rtaBidRise_.add(
                  input.readMessage(BidRise.PARSER, extensionRegistry));
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              prefetchDate_ = bs;
              break;
            }
            case 80: {
              int rawValue = input.readEnum();
              RtaBidType value = RtaBidType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(10, rawValue);
              } else {
                bitField0_ |= 0x00000020;
                rtaBidType_ = rawValue;
              }
              break;
            }
            case 90: {
              if (!((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
                rtaBid_ = new java.util.ArrayList<RtaBid>();
                mutable_bitField0_ |= 0x00000200;
              }
              rtaBid_.add(
                  input.readMessage(RtaBid.PARSER, extensionRegistry));
              break;
            }
            case 98: {
              if (!((mutable_bitField0_ & 0x00000400) == 0x00000400)) {
                rtaCpcBid_ = new java.util.ArrayList<RtaBid>();
                mutable_bitField0_ |= 0x00000400;
              }
              rtaCpcBid_.add(
                  input.readMessage(RtaBid.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          adResults_ = java.util.Collections.unmodifiableList(adResults_);
        }
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          strategyResults_ = java.util.Collections.unmodifiableList(strategyResults_);
        }
        if (((mutable_bitField0_ & 0x00000040) == 0x00000040)) {
          rtaBidRise_ = java.util.Collections.unmodifiableList(rtaBidRise_);
        }
        if (((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
          rtaBid_ = java.util.Collections.unmodifiableList(rtaBid_);
        }
        if (((mutable_bitField0_ & 0x00000400) == 0x00000400)) {
          rtaCpcBid_ = java.util.Collections.unmodifiableList(rtaCpcBid_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaResponse.internal_static_RtaApiResponse_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaResponse.internal_static_RtaApiResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RtaApiResponse.class, Builder.class);
    }

    private int bitField0_;
    public static final int QID_FIELD_NUMBER = 1;
    private long qid_;
    /**
     * <code>required uint64 qid = 1;</code>
     */
    public boolean hasQid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required uint64 qid = 1;</code>
     */
    public long getQid() {
      return qid_;
    }

    public static final int RES_FIELD_NUMBER = 2;
    private int res_;
    /**
     * <code>required .ResType res = 2;</code>
     */
    public boolean hasRes() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required .ResType res = 2;</code>
     */
    public ResType getRes() {
      ResType result = ResType.valueOf(res_);
      return result == null ? ResType.ALL : result;
    }

    public static final int USER_SCORE_FIELD_NUMBER = 3;
    private int userScore_;
    /**
     * <pre>
     * 客户打分，可选，DPA暂不可用
     * </pre>
     *
     * <code>optional uint32 user_score = 3;</code>
     */
    public boolean hasUserScore() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     * 客户打分，可选，DPA暂不可用
     * </pre>
     *
     * <code>optional uint32 user_score = 3;</code>
     */
    public int getUserScore() {
      return userScore_;
    }

    public static final int AD_RESULTS_FIELD_NUMBER = 4;
    private java.util.List<AdResult> adResults_;
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    public java.util.List<AdResult> getAdResultsList() {
      return adResults_;
    }
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    public java.util.List<? extends AdResultOrBuilder>
        getAdResultsOrBuilderList() {
      return adResults_;
    }
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    public int getAdResultsCount() {
      return adResults_.size();
    }
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    public AdResult getAdResults(int index) {
      return adResults_.get(index);
    }
    /**
     * <pre>
     * 指定要出的广告组，可选
     * </pre>
     *
     * <code>repeated .AdResult ad_results = 4;</code>
     */
    public AdResultOrBuilder getAdResultsOrBuilder(
        int index) {
      return adResults_.get(index);
    }

    public static final int STRATEGY_RESULTS_FIELD_NUMBER = 5;
    private java.util.List<RtaStrategyAdResult> strategyResults_;
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    public java.util.List<RtaStrategyAdResult> getStrategyResultsList() {
      return strategyResults_;
    }
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    public java.util.List<? extends RtaStrategyAdResultOrBuilder>
        getStrategyResultsOrBuilderList() {
      return strategyResults_;
    }
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    public int getStrategyResultsCount() {
      return strategyResults_.size();
    }
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    public RtaStrategyAdResult getStrategyResults(int index) {
      return strategyResults_.get(index);
    }
    /**
     * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
     */
    public RtaStrategyAdResultOrBuilder getStrategyResultsOrBuilder(
        int index) {
      return strategyResults_.get(index);
    }

    public static final int DPA_RESULTS_FIELD_NUMBER = 6;
    private DpaResult dpaResults_;
    /**
     * <pre>
     * DPA相关数据
     * </pre>
     *
     * <code>optional .DpaResult dpa_results = 6;</code>
     */
    public boolean hasDpaResults() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     * DPA相关数据
     * </pre>
     *
     * <code>optional .DpaResult dpa_results = 6;</code>
     */
    public DpaResult getDpaResults() {
      return dpaResults_ == null ? DpaResult.getDefaultInstance() : dpaResults_;
    }
    /**
     * <pre>
     * DPA相关数据
     * </pre>
     *
     * <code>optional .DpaResult dpa_results = 6;</code>
     */
    public DpaResultOrBuilder getDpaResultsOrBuilder() {
      return dpaResults_ == null ? DpaResult.getDefaultInstance() : dpaResults_;
    }

    public static final int RTA_BID_RISE_FIELD_NUMBER = 8;
    private java.util.List<BidRise> rtaBidRise_;
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    public java.util.List<BidRise> getRtaBidRiseList() {
      return rtaBidRise_;
    }
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    public java.util.List<? extends BidRiseOrBuilder>
        getRtaBidRiseOrBuilderList() {
      return rtaBidRise_;
    }
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    public int getRtaBidRiseCount() {
      return rtaBidRise_.size();
    }
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    public BidRise getRtaBidRise(int index) {
      return rtaBidRise_.get(index);
    }
    /**
     * <pre>
     *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
     * </pre>
     *
     * <code>repeated .BidRise rta_bid_rise = 8;</code>
     */
    public BidRiseOrBuilder getRtaBidRiseOrBuilder(
        int index) {
      return rtaBidRise_.get(index);
    }

    public static final int PREFETCH_DATE_FIELD_NUMBER = 9;
    private volatile Object prefetchDate_;
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 9;</code>
     */
    public boolean hasPrefetchDate() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 9;</code>
     */
    public String getPrefetchDate() {
      Object ref = prefetchDate_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          prefetchDate_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 9;</code>
     */
    public com.google.protobuf.ByteString
        getPrefetchDateBytes() {
      Object ref = prefetchDate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        prefetchDate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RTA_BID_TYPE_FIELD_NUMBER = 10;
    private int rtaBidType_;
    /**
     * <pre>
     * 标志是否使用rta溢价
     * </pre>
     *
     * <code>optional .RtaBidType rta_bid_type = 10;</code>
     */
    public boolean hasRtaBidType() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     * 标志是否使用rta溢价
     * </pre>
     *
     * <code>optional .RtaBidType rta_bid_type = 10;</code>
     */
    public RtaBidType getRtaBidType() {
      RtaBidType result = RtaBidType.valueOf(rtaBidType_);
      return result == null ? RtaBidType.RTA_NORMAL : result;
    }

    public static final int RTA_BID_FIELD_NUMBER = 11;
    private java.util.List<RtaBid> rtaBid_;
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    public java.util.List<RtaBid> getRtaBidList() {
      return rtaBid_;
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    public java.util.List<? extends RtaBidOrBuilder>
        getRtaBidOrBuilderList() {
      return rtaBid_;
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    public int getRtaBidCount() {
      return rtaBid_.size();
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    public RtaBid getRtaBid(int index) {
      return rtaBid_.get(index);
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
     * </pre>
     *
     * <code>repeated .RtaBid rta_bid = 11;</code>
     */
    public RtaBidOrBuilder getRtaBidOrBuilder(
        int index) {
      return rtaBid_.get(index);
    }

    public static final int RTA_CPC_BID_FIELD_NUMBER = 12;
    private java.util.List<RtaBid> rtaCpcBid_;
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    public java.util.List<RtaBid> getRtaCpcBidList() {
      return rtaCpcBid_;
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    public java.util.List<? extends RtaBidOrBuilder>
        getRtaCpcBidOrBuilderList() {
      return rtaCpcBid_;
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    public int getRtaCpcBidCount() {
      return rtaCpcBid_.size();
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    public RtaBid getRtaCpcBid(int index) {
      return rtaCpcBid_.get(index);
    }
    /**
     * <pre>
     * rta直接出价,必须配合策略id使用,用于cpc广告
     * </pre>
     *
     * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
     */
    public RtaBidOrBuilder getRtaCpcBidOrBuilder(
        int index) {
      return rtaCpcBid_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasQid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRes()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getAdResultsCount(); i++) {
        if (!getAdResults(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getStrategyResultsCount(); i++) {
        if (!getStrategyResults(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasDpaResults()) {
        if (!getDpaResults().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getRtaBidCount(); i++) {
        if (!getRtaBid(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getRtaCpcBidCount(); i++) {
        if (!getRtaCpcBid(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, qid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeEnum(2, res_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeUInt32(3, userScore_);
      }
      for (int i = 0; i < adResults_.size(); i++) {
        output.writeMessage(4, adResults_.get(i));
      }
      for (int i = 0; i < strategyResults_.size(); i++) {
        output.writeMessage(5, strategyResults_.get(i));
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeMessage(6, getDpaResults());
      }
      for (int i = 0; i < rtaBidRise_.size(); i++) {
        output.writeMessage(8, rtaBidRise_.get(i));
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, prefetchDate_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeEnum(10, rtaBidType_);
      }
      for (int i = 0; i < rtaBid_.size(); i++) {
        output.writeMessage(11, rtaBid_.get(i));
      }
      for (int i = 0; i < rtaCpcBid_.size(); i++) {
        output.writeMessage(12, rtaCpcBid_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, qid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, res_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, userScore_);
      }
      for (int i = 0; i < adResults_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, adResults_.get(i));
      }
      for (int i = 0; i < strategyResults_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, strategyResults_.get(i));
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getDpaResults());
      }
      for (int i = 0; i < rtaBidRise_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, rtaBidRise_.get(i));
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, prefetchDate_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(10, rtaBidType_);
      }
      for (int i = 0; i < rtaBid_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, rtaBid_.get(i));
      }
      for (int i = 0; i < rtaCpcBid_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, rtaCpcBid_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof RtaApiResponse)) {
        return super.equals(obj);
      }
      RtaApiResponse other = (RtaApiResponse) obj;

      boolean result = true;
      result = result && (hasQid() == other.hasQid());
      if (hasQid()) {
        result = result && (getQid()
            == other.getQid());
      }
      result = result && (hasRes() == other.hasRes());
      if (hasRes()) {
        result = result && res_ == other.res_;
      }
      result = result && (hasUserScore() == other.hasUserScore());
      if (hasUserScore()) {
        result = result && (getUserScore()
            == other.getUserScore());
      }
      result = result && getAdResultsList()
          .equals(other.getAdResultsList());
      result = result && getStrategyResultsList()
          .equals(other.getStrategyResultsList());
      result = result && (hasDpaResults() == other.hasDpaResults());
      if (hasDpaResults()) {
        result = result && getDpaResults()
            .equals(other.getDpaResults());
      }
      result = result && getRtaBidRiseList()
          .equals(other.getRtaBidRiseList());
      result = result && (hasPrefetchDate() == other.hasPrefetchDate());
      if (hasPrefetchDate()) {
        result = result && getPrefetchDate()
            .equals(other.getPrefetchDate());
      }
      result = result && (hasRtaBidType() == other.hasRtaBidType());
      if (hasRtaBidType()) {
        result = result && rtaBidType_ == other.rtaBidType_;
      }
      result = result && getRtaBidList()
          .equals(other.getRtaBidList());
      result = result && getRtaCpcBidList()
          .equals(other.getRtaCpcBidList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQid()) {
        hash = (37 * hash) + QID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getQid());
      }
      if (hasRes()) {
        hash = (37 * hash) + RES_FIELD_NUMBER;
        hash = (53 * hash) + res_;
      }
      if (hasUserScore()) {
        hash = (37 * hash) + USER_SCORE_FIELD_NUMBER;
        hash = (53 * hash) + getUserScore();
      }
      if (getAdResultsCount() > 0) {
        hash = (37 * hash) + AD_RESULTS_FIELD_NUMBER;
        hash = (53 * hash) + getAdResultsList().hashCode();
      }
      if (getStrategyResultsCount() > 0) {
        hash = (37 * hash) + STRATEGY_RESULTS_FIELD_NUMBER;
        hash = (53 * hash) + getStrategyResultsList().hashCode();
      }
      if (hasDpaResults()) {
        hash = (37 * hash) + DPA_RESULTS_FIELD_NUMBER;
        hash = (53 * hash) + getDpaResults().hashCode();
      }
      if (getRtaBidRiseCount() > 0) {
        hash = (37 * hash) + RTA_BID_RISE_FIELD_NUMBER;
        hash = (53 * hash) + getRtaBidRiseList().hashCode();
      }
      if (hasPrefetchDate()) {
        hash = (37 * hash) + PREFETCH_DATE_FIELD_NUMBER;
        hash = (53 * hash) + getPrefetchDate().hashCode();
      }
      if (hasRtaBidType()) {
        hash = (37 * hash) + RTA_BID_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + rtaBidType_;
      }
      if (getRtaBidCount() > 0) {
        hash = (37 * hash) + RTA_BID_FIELD_NUMBER;
        hash = (53 * hash) + getRtaBidList().hashCode();
      }
      if (getRtaCpcBidCount() > 0) {
        hash = (37 * hash) + RTA_CPC_BID_FIELD_NUMBER;
        hash = (53 * hash) + getRtaCpcBidList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static RtaApiResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaApiResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaApiResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaApiResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaApiResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaApiResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaApiResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaApiResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaApiResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static RtaApiResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaApiResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaApiResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(RtaApiResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RtaApiResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RtaApiResponse)
        RtaApiResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaResponse.internal_static_RtaApiResponse_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaResponse.internal_static_RtaApiResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RtaApiResponse.class, Builder.class);
      }

      // Construct using BaiduRtaResponse.RtaApiResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAdResultsFieldBuilder();
          getStrategyResultsFieldBuilder();
          getDpaResultsFieldBuilder();
          getRtaBidRiseFieldBuilder();
          getRtaBidFieldBuilder();
          getRtaCpcBidFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        qid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        res_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        userScore_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (adResultsBuilder_ == null) {
          adResults_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          adResultsBuilder_.clear();
        }
        if (strategyResultsBuilder_ == null) {
          strategyResults_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          strategyResultsBuilder_.clear();
        }
        if (dpaResultsBuilder_ == null) {
          dpaResults_ = null;
        } else {
          dpaResultsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (rtaBidRiseBuilder_ == null) {
          rtaBidRise_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
        } else {
          rtaBidRiseBuilder_.clear();
        }
        prefetchDate_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        rtaBidType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        if (rtaBidBuilder_ == null) {
          rtaBid_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);
        } else {
          rtaBidBuilder_.clear();
        }
        if (rtaCpcBidBuilder_ == null) {
          rtaCpcBid_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000400);
        } else {
          rtaCpcBidBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaResponse.internal_static_RtaApiResponse_descriptor;
      }

      public RtaApiResponse getDefaultInstanceForType() {
        return RtaApiResponse.getDefaultInstance();
      }

      public RtaApiResponse build() {
        RtaApiResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RtaApiResponse buildPartial() {
        RtaApiResponse result = new RtaApiResponse(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.qid_ = qid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.res_ = res_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.userScore_ = userScore_;
        if (adResultsBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            adResults_ = java.util.Collections.unmodifiableList(adResults_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.adResults_ = adResults_;
        } else {
          result.adResults_ = adResultsBuilder_.build();
        }
        if (strategyResultsBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            strategyResults_ = java.util.Collections.unmodifiableList(strategyResults_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.strategyResults_ = strategyResults_;
        } else {
          result.strategyResults_ = strategyResultsBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000008;
        }
        if (dpaResultsBuilder_ == null) {
          result.dpaResults_ = dpaResults_;
        } else {
          result.dpaResults_ = dpaResultsBuilder_.build();
        }
        if (rtaBidRiseBuilder_ == null) {
          if (((bitField0_ & 0x00000040) == 0x00000040)) {
            rtaBidRise_ = java.util.Collections.unmodifiableList(rtaBidRise_);
            bitField0_ = (bitField0_ & ~0x00000040);
          }
          result.rtaBidRise_ = rtaBidRise_;
        } else {
          result.rtaBidRise_ = rtaBidRiseBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000010;
        }
        result.prefetchDate_ = prefetchDate_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000020;
        }
        result.rtaBidType_ = rtaBidType_;
        if (rtaBidBuilder_ == null) {
          if (((bitField0_ & 0x00000200) == 0x00000200)) {
            rtaBid_ = java.util.Collections.unmodifiableList(rtaBid_);
            bitField0_ = (bitField0_ & ~0x00000200);
          }
          result.rtaBid_ = rtaBid_;
        } else {
          result.rtaBid_ = rtaBidBuilder_.build();
        }
        if (rtaCpcBidBuilder_ == null) {
          if (((bitField0_ & 0x00000400) == 0x00000400)) {
            rtaCpcBid_ = java.util.Collections.unmodifiableList(rtaCpcBid_);
            bitField0_ = (bitField0_ & ~0x00000400);
          }
          result.rtaCpcBid_ = rtaCpcBid_;
        } else {
          result.rtaCpcBid_ = rtaCpcBidBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RtaApiResponse) {
          return mergeFrom((RtaApiResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RtaApiResponse other) {
        if (other == RtaApiResponse.getDefaultInstance()) return this;
        if (other.hasQid()) {
          setQid(other.getQid());
        }
        if (other.hasRes()) {
          setRes(other.getRes());
        }
        if (other.hasUserScore()) {
          setUserScore(other.getUserScore());
        }
        if (adResultsBuilder_ == null) {
          if (!other.adResults_.isEmpty()) {
            if (adResults_.isEmpty()) {
              adResults_ = other.adResults_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureAdResultsIsMutable();
              adResults_.addAll(other.adResults_);
            }
            onChanged();
          }
        } else {
          if (!other.adResults_.isEmpty()) {
            if (adResultsBuilder_.isEmpty()) {
              adResultsBuilder_.dispose();
              adResultsBuilder_ = null;
              adResults_ = other.adResults_;
              bitField0_ = (bitField0_ & ~0x00000008);
              adResultsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getAdResultsFieldBuilder() : null;
            } else {
              adResultsBuilder_.addAllMessages(other.adResults_);
            }
          }
        }
        if (strategyResultsBuilder_ == null) {
          if (!other.strategyResults_.isEmpty()) {
            if (strategyResults_.isEmpty()) {
              strategyResults_ = other.strategyResults_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureStrategyResultsIsMutable();
              strategyResults_.addAll(other.strategyResults_);
            }
            onChanged();
          }
        } else {
          if (!other.strategyResults_.isEmpty()) {
            if (strategyResultsBuilder_.isEmpty()) {
              strategyResultsBuilder_.dispose();
              strategyResultsBuilder_ = null;
              strategyResults_ = other.strategyResults_;
              bitField0_ = (bitField0_ & ~0x00000010);
              strategyResultsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getStrategyResultsFieldBuilder() : null;
            } else {
              strategyResultsBuilder_.addAllMessages(other.strategyResults_);
            }
          }
        }
        if (other.hasDpaResults()) {
          mergeDpaResults(other.getDpaResults());
        }
        if (rtaBidRiseBuilder_ == null) {
          if (!other.rtaBidRise_.isEmpty()) {
            if (rtaBidRise_.isEmpty()) {
              rtaBidRise_ = other.rtaBidRise_;
              bitField0_ = (bitField0_ & ~0x00000040);
            } else {
              ensureRtaBidRiseIsMutable();
              rtaBidRise_.addAll(other.rtaBidRise_);
            }
            onChanged();
          }
        } else {
          if (!other.rtaBidRise_.isEmpty()) {
            if (rtaBidRiseBuilder_.isEmpty()) {
              rtaBidRiseBuilder_.dispose();
              rtaBidRiseBuilder_ = null;
              rtaBidRise_ = other.rtaBidRise_;
              bitField0_ = (bitField0_ & ~0x00000040);
              rtaBidRiseBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRtaBidRiseFieldBuilder() : null;
            } else {
              rtaBidRiseBuilder_.addAllMessages(other.rtaBidRise_);
            }
          }
        }
        if (other.hasPrefetchDate()) {
          bitField0_ |= 0x00000080;
          prefetchDate_ = other.prefetchDate_;
          onChanged();
        }
        if (other.hasRtaBidType()) {
          setRtaBidType(other.getRtaBidType());
        }
        if (rtaBidBuilder_ == null) {
          if (!other.rtaBid_.isEmpty()) {
            if (rtaBid_.isEmpty()) {
              rtaBid_ = other.rtaBid_;
              bitField0_ = (bitField0_ & ~0x00000200);
            } else {
              ensureRtaBidIsMutable();
              rtaBid_.addAll(other.rtaBid_);
            }
            onChanged();
          }
        } else {
          if (!other.rtaBid_.isEmpty()) {
            if (rtaBidBuilder_.isEmpty()) {
              rtaBidBuilder_.dispose();
              rtaBidBuilder_ = null;
              rtaBid_ = other.rtaBid_;
              bitField0_ = (bitField0_ & ~0x00000200);
              rtaBidBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRtaBidFieldBuilder() : null;
            } else {
              rtaBidBuilder_.addAllMessages(other.rtaBid_);
            }
          }
        }
        if (rtaCpcBidBuilder_ == null) {
          if (!other.rtaCpcBid_.isEmpty()) {
            if (rtaCpcBid_.isEmpty()) {
              rtaCpcBid_ = other.rtaCpcBid_;
              bitField0_ = (bitField0_ & ~0x00000400);
            } else {
              ensureRtaCpcBidIsMutable();
              rtaCpcBid_.addAll(other.rtaCpcBid_);
            }
            onChanged();
          }
        } else {
          if (!other.rtaCpcBid_.isEmpty()) {
            if (rtaCpcBidBuilder_.isEmpty()) {
              rtaCpcBidBuilder_.dispose();
              rtaCpcBidBuilder_ = null;
              rtaCpcBid_ = other.rtaCpcBid_;
              bitField0_ = (bitField0_ & ~0x00000400);
              rtaCpcBidBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRtaCpcBidFieldBuilder() : null;
            } else {
              rtaCpcBidBuilder_.addAllMessages(other.rtaCpcBid_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        if (!hasQid()) {
          return false;
        }
        if (!hasRes()) {
          return false;
        }
        for (int i = 0; i < getAdResultsCount(); i++) {
          if (!getAdResults(i).isInitialized()) {
            return false;
          }
        }
        for (int i = 0; i < getStrategyResultsCount(); i++) {
          if (!getStrategyResults(i).isInitialized()) {
            return false;
          }
        }
        if (hasDpaResults()) {
          if (!getDpaResults().isInitialized()) {
            return false;
          }
        }
        for (int i = 0; i < getRtaBidCount(); i++) {
          if (!getRtaBid(i).isInitialized()) {
            return false;
          }
        }
        for (int i = 0; i < getRtaCpcBidCount(); i++) {
          if (!getRtaCpcBid(i).isInitialized()) {
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RtaApiResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RtaApiResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long qid_ ;
      /**
       * <code>required uint64 qid = 1;</code>
       */
      public boolean hasQid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required uint64 qid = 1;</code>
       */
      public long getQid() {
        return qid_;
      }
      /**
       * <code>required uint64 qid = 1;</code>
       */
      public Builder setQid(long value) {
        bitField0_ |= 0x00000001;
        qid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required uint64 qid = 1;</code>
       */
      public Builder clearQid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        qid_ = 0L;
        onChanged();
        return this;
      }

      private int res_ = 0;
      /**
       * <code>required .ResType res = 2;</code>
       */
      public boolean hasRes() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required .ResType res = 2;</code>
       */
      public ResType getRes() {
        ResType result = ResType.valueOf(res_);
        return result == null ? ResType.ALL : result;
      }
      /**
       * <code>required .ResType res = 2;</code>
       */
      public Builder setRes(ResType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        res_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>required .ResType res = 2;</code>
       */
      public Builder clearRes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        res_ = 0;
        onChanged();
        return this;
      }

      private int userScore_ ;
      /**
       * <pre>
       * 客户打分，可选，DPA暂不可用
       * </pre>
       *
       * <code>optional uint32 user_score = 3;</code>
       */
      public boolean hasUserScore() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       * 客户打分，可选，DPA暂不可用
       * </pre>
       *
       * <code>optional uint32 user_score = 3;</code>
       */
      public int getUserScore() {
        return userScore_;
      }
      /**
       * <pre>
       * 客户打分，可选，DPA暂不可用
       * </pre>
       *
       * <code>optional uint32 user_score = 3;</code>
       */
      public Builder setUserScore(int value) {
        bitField0_ |= 0x00000004;
        userScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户打分，可选，DPA暂不可用
       * </pre>
       *
       * <code>optional uint32 user_score = 3;</code>
       */
      public Builder clearUserScore() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userScore_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<AdResult> adResults_ =
        java.util.Collections.emptyList();
      private void ensureAdResultsIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          adResults_ = new java.util.ArrayList<AdResult>(adResults_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          AdResult, AdResult.Builder, AdResultOrBuilder> adResultsBuilder_;

      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public java.util.List<AdResult> getAdResultsList() {
        if (adResultsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(adResults_);
        } else {
          return adResultsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public int getAdResultsCount() {
        if (adResultsBuilder_ == null) {
          return adResults_.size();
        } else {
          return adResultsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public AdResult getAdResults(int index) {
        if (adResultsBuilder_ == null) {
          return adResults_.get(index);
        } else {
          return adResultsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder setAdResults(
          int index, AdResult value) {
        if (adResultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAdResultsIsMutable();
          adResults_.set(index, value);
          onChanged();
        } else {
          adResultsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder setAdResults(
          int index, AdResult.Builder builderForValue) {
        if (adResultsBuilder_ == null) {
          ensureAdResultsIsMutable();
          adResults_.set(index, builderForValue.build());
          onChanged();
        } else {
          adResultsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder addAdResults(AdResult value) {
        if (adResultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAdResultsIsMutable();
          adResults_.add(value);
          onChanged();
        } else {
          adResultsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder addAdResults(
          int index, AdResult value) {
        if (adResultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAdResultsIsMutable();
          adResults_.add(index, value);
          onChanged();
        } else {
          adResultsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder addAdResults(
          AdResult.Builder builderForValue) {
        if (adResultsBuilder_ == null) {
          ensureAdResultsIsMutable();
          adResults_.add(builderForValue.build());
          onChanged();
        } else {
          adResultsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder addAdResults(
          int index, AdResult.Builder builderForValue) {
        if (adResultsBuilder_ == null) {
          ensureAdResultsIsMutable();
          adResults_.add(index, builderForValue.build());
          onChanged();
        } else {
          adResultsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder addAllAdResults(
          Iterable<? extends AdResult> values) {
        if (adResultsBuilder_ == null) {
          ensureAdResultsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, adResults_);
          onChanged();
        } else {
          adResultsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder clearAdResults() {
        if (adResultsBuilder_ == null) {
          adResults_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          adResultsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public Builder removeAdResults(int index) {
        if (adResultsBuilder_ == null) {
          ensureAdResultsIsMutable();
          adResults_.remove(index);
          onChanged();
        } else {
          adResultsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public AdResult.Builder getAdResultsBuilder(
          int index) {
        return getAdResultsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public AdResultOrBuilder getAdResultsOrBuilder(
          int index) {
        if (adResultsBuilder_ == null) {
          return adResults_.get(index);  } else {
          return adResultsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public java.util.List<? extends AdResultOrBuilder>
           getAdResultsOrBuilderList() {
        if (adResultsBuilder_ != null) {
          return adResultsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(adResults_);
        }
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public AdResult.Builder addAdResultsBuilder() {
        return getAdResultsFieldBuilder().addBuilder(
            AdResult.getDefaultInstance());
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public AdResult.Builder addAdResultsBuilder(
          int index) {
        return getAdResultsFieldBuilder().addBuilder(
            index, AdResult.getDefaultInstance());
      }
      /**
       * <pre>
       * 指定要出的广告组，可选
       * </pre>
       *
       * <code>repeated .AdResult ad_results = 4;</code>
       */
      public java.util.List<AdResult.Builder>
           getAdResultsBuilderList() {
        return getAdResultsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          AdResult, AdResult.Builder, AdResultOrBuilder>
          getAdResultsFieldBuilder() {
        if (adResultsBuilder_ == null) {
          adResultsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              AdResult, AdResult.Builder, AdResultOrBuilder>(
                  adResults_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          adResults_ = null;
        }
        return adResultsBuilder_;
      }

      private java.util.List<RtaStrategyAdResult> strategyResults_ =
        java.util.Collections.emptyList();
      private void ensureStrategyResultsIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          strategyResults_ = new java.util.ArrayList<RtaStrategyAdResult>(strategyResults_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          RtaStrategyAdResult, RtaStrategyAdResult.Builder, RtaStrategyAdResultOrBuilder> strategyResultsBuilder_;

      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public java.util.List<RtaStrategyAdResult> getStrategyResultsList() {
        if (strategyResultsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(strategyResults_);
        } else {
          return strategyResultsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public int getStrategyResultsCount() {
        if (strategyResultsBuilder_ == null) {
          return strategyResults_.size();
        } else {
          return strategyResultsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public RtaStrategyAdResult getStrategyResults(int index) {
        if (strategyResultsBuilder_ == null) {
          return strategyResults_.get(index);
        } else {
          return strategyResultsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder setStrategyResults(
          int index, RtaStrategyAdResult value) {
        if (strategyResultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStrategyResultsIsMutable();
          strategyResults_.set(index, value);
          onChanged();
        } else {
          strategyResultsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder setStrategyResults(
          int index, RtaStrategyAdResult.Builder builderForValue) {
        if (strategyResultsBuilder_ == null) {
          ensureStrategyResultsIsMutable();
          strategyResults_.set(index, builderForValue.build());
          onChanged();
        } else {
          strategyResultsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder addStrategyResults(RtaStrategyAdResult value) {
        if (strategyResultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStrategyResultsIsMutable();
          strategyResults_.add(value);
          onChanged();
        } else {
          strategyResultsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder addStrategyResults(
          int index, RtaStrategyAdResult value) {
        if (strategyResultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureStrategyResultsIsMutable();
          strategyResults_.add(index, value);
          onChanged();
        } else {
          strategyResultsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder addStrategyResults(
          RtaStrategyAdResult.Builder builderForValue) {
        if (strategyResultsBuilder_ == null) {
          ensureStrategyResultsIsMutable();
          strategyResults_.add(builderForValue.build());
          onChanged();
        } else {
          strategyResultsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder addStrategyResults(
          int index, RtaStrategyAdResult.Builder builderForValue) {
        if (strategyResultsBuilder_ == null) {
          ensureStrategyResultsIsMutable();
          strategyResults_.add(index, builderForValue.build());
          onChanged();
        } else {
          strategyResultsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder addAllStrategyResults(
          Iterable<? extends RtaStrategyAdResult> values) {
        if (strategyResultsBuilder_ == null) {
          ensureStrategyResultsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, strategyResults_);
          onChanged();
        } else {
          strategyResultsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder clearStrategyResults() {
        if (strategyResultsBuilder_ == null) {
          strategyResults_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          strategyResultsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public Builder removeStrategyResults(int index) {
        if (strategyResultsBuilder_ == null) {
          ensureStrategyResultsIsMutable();
          strategyResults_.remove(index);
          onChanged();
        } else {
          strategyResultsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public RtaStrategyAdResult.Builder getStrategyResultsBuilder(
          int index) {
        return getStrategyResultsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public RtaStrategyAdResultOrBuilder getStrategyResultsOrBuilder(
          int index) {
        if (strategyResultsBuilder_ == null) {
          return strategyResults_.get(index);  } else {
          return strategyResultsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public java.util.List<? extends RtaStrategyAdResultOrBuilder>
           getStrategyResultsOrBuilderList() {
        if (strategyResultsBuilder_ != null) {
          return strategyResultsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(strategyResults_);
        }
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public RtaStrategyAdResult.Builder addStrategyResultsBuilder() {
        return getStrategyResultsFieldBuilder().addBuilder(
            RtaStrategyAdResult.getDefaultInstance());
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public RtaStrategyAdResult.Builder addStrategyResultsBuilder(
          int index) {
        return getStrategyResultsFieldBuilder().addBuilder(
            index, RtaStrategyAdResult.getDefaultInstance());
      }
      /**
       * <code>repeated .RtaStrategyAdResult strategy_results = 5;</code>
       */
      public java.util.List<RtaStrategyAdResult.Builder>
           getStrategyResultsBuilderList() {
        return getStrategyResultsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          RtaStrategyAdResult, RtaStrategyAdResult.Builder, RtaStrategyAdResultOrBuilder>
          getStrategyResultsFieldBuilder() {
        if (strategyResultsBuilder_ == null) {
          strategyResultsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              RtaStrategyAdResult, RtaStrategyAdResult.Builder, RtaStrategyAdResultOrBuilder>(
                  strategyResults_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          strategyResults_ = null;
        }
        return strategyResultsBuilder_;
      }

      private DpaResult dpaResults_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          DpaResult, DpaResult.Builder, DpaResultOrBuilder> dpaResultsBuilder_;
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public boolean hasDpaResults() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public DpaResult getDpaResults() {
        if (dpaResultsBuilder_ == null) {
          return dpaResults_ == null ? DpaResult.getDefaultInstance() : dpaResults_;
        } else {
          return dpaResultsBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public Builder setDpaResults(DpaResult value) {
        if (dpaResultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dpaResults_ = value;
          onChanged();
        } else {
          dpaResultsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public Builder setDpaResults(
          DpaResult.Builder builderForValue) {
        if (dpaResultsBuilder_ == null) {
          dpaResults_ = builderForValue.build();
          onChanged();
        } else {
          dpaResultsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public Builder mergeDpaResults(DpaResult value) {
        if (dpaResultsBuilder_ == null) {
          if (((bitField0_ & 0x00000020) == 0x00000020) &&
              dpaResults_ != null &&
              dpaResults_ != DpaResult.getDefaultInstance()) {
            dpaResults_ =
              DpaResult.newBuilder(dpaResults_).mergeFrom(value).buildPartial();
          } else {
            dpaResults_ = value;
          }
          onChanged();
        } else {
          dpaResultsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public Builder clearDpaResults() {
        if (dpaResultsBuilder_ == null) {
          dpaResults_ = null;
          onChanged();
        } else {
          dpaResultsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public DpaResult.Builder getDpaResultsBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getDpaResultsFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      public DpaResultOrBuilder getDpaResultsOrBuilder() {
        if (dpaResultsBuilder_ != null) {
          return dpaResultsBuilder_.getMessageOrBuilder();
        } else {
          return dpaResults_ == null ?
              DpaResult.getDefaultInstance() : dpaResults_;
        }
      }
      /**
       * <pre>
       * DPA相关数据
       * </pre>
       *
       * <code>optional .DpaResult dpa_results = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          DpaResult, DpaResult.Builder, DpaResultOrBuilder>
          getDpaResultsFieldBuilder() {
        if (dpaResultsBuilder_ == null) {
          dpaResultsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              DpaResult, DpaResult.Builder, DpaResultOrBuilder>(
                  getDpaResults(),
                  getParentForChildren(),
                  isClean());
          dpaResults_ = null;
        }
        return dpaResultsBuilder_;
      }

      private java.util.List<BidRise> rtaBidRise_ =
        java.util.Collections.emptyList();
      private void ensureRtaBidRiseIsMutable() {
        if (!((bitField0_ & 0x00000040) == 0x00000040)) {
          rtaBidRise_ = new java.util.ArrayList<BidRise>(rtaBidRise_);
          bitField0_ |= 0x00000040;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          BidRise, BidRise.Builder, BidRiseOrBuilder> rtaBidRiseBuilder_;

      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public java.util.List<BidRise> getRtaBidRiseList() {
        if (rtaBidRiseBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rtaBidRise_);
        } else {
          return rtaBidRiseBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public int getRtaBidRiseCount() {
        if (rtaBidRiseBuilder_ == null) {
          return rtaBidRise_.size();
        } else {
          return rtaBidRiseBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public BidRise getRtaBidRise(int index) {
        if (rtaBidRiseBuilder_ == null) {
          return rtaBidRise_.get(index);
        } else {
          return rtaBidRiseBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder setRtaBidRise(
          int index, BidRise value) {
        if (rtaBidRiseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaBidRiseIsMutable();
          rtaBidRise_.set(index, value);
          onChanged();
        } else {
          rtaBidRiseBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder setRtaBidRise(
          int index, BidRise.Builder builderForValue) {
        if (rtaBidRiseBuilder_ == null) {
          ensureRtaBidRiseIsMutable();
          rtaBidRise_.set(index, builderForValue.build());
          onChanged();
        } else {
          rtaBidRiseBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder addRtaBidRise(BidRise value) {
        if (rtaBidRiseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaBidRiseIsMutable();
          rtaBidRise_.add(value);
          onChanged();
        } else {
          rtaBidRiseBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder addRtaBidRise(
          int index, BidRise value) {
        if (rtaBidRiseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaBidRiseIsMutable();
          rtaBidRise_.add(index, value);
          onChanged();
        } else {
          rtaBidRiseBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder addRtaBidRise(
          BidRise.Builder builderForValue) {
        if (rtaBidRiseBuilder_ == null) {
          ensureRtaBidRiseIsMutable();
          rtaBidRise_.add(builderForValue.build());
          onChanged();
        } else {
          rtaBidRiseBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder addRtaBidRise(
          int index, BidRise.Builder builderForValue) {
        if (rtaBidRiseBuilder_ == null) {
          ensureRtaBidRiseIsMutable();
          rtaBidRise_.add(index, builderForValue.build());
          onChanged();
        } else {
          rtaBidRiseBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder addAllRtaBidRise(
          Iterable<? extends BidRise> values) {
        if (rtaBidRiseBuilder_ == null) {
          ensureRtaBidRiseIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rtaBidRise_);
          onChanged();
        } else {
          rtaBidRiseBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder clearRtaBidRise() {
        if (rtaBidRiseBuilder_ == null) {
          rtaBidRise_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
        } else {
          rtaBidRiseBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public Builder removeRtaBidRise(int index) {
        if (rtaBidRiseBuilder_ == null) {
          ensureRtaBidRiseIsMutable();
          rtaBidRise_.remove(index);
          onChanged();
        } else {
          rtaBidRiseBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public BidRise.Builder getRtaBidRiseBuilder(
          int index) {
        return getRtaBidRiseFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public BidRiseOrBuilder getRtaBidRiseOrBuilder(
          int index) {
        if (rtaBidRiseBuilder_ == null) {
          return rtaBidRise_.get(index);  } else {
          return rtaBidRiseBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public java.util.List<? extends BidRiseOrBuilder>
           getRtaBidRiseOrBuilderList() {
        if (rtaBidRiseBuilder_ != null) {
          return rtaBidRiseBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rtaBidRise_);
        }
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public BidRise.Builder addRtaBidRiseBuilder() {
        return getRtaBidRiseFieldBuilder().addBuilder(
            BidRise.getDefaultInstance());
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public BidRise.Builder addRtaBidRiseBuilder(
          int index) {
        return getRtaBidRiseFieldBuilder().addBuilder(
            index, BidRise.getDefaultInstance());
      }
      /**
       * <pre>
       *分rta_id的溢价系数，rta_id不填则为所有召回广告的溢价系数。
       * </pre>
       *
       * <code>repeated .BidRise rta_bid_rise = 8;</code>
       */
      public java.util.List<BidRise.Builder>
           getRtaBidRiseBuilderList() {
        return getRtaBidRiseFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          BidRise, BidRise.Builder, BidRiseOrBuilder>
          getRtaBidRiseFieldBuilder() {
        if (rtaBidRiseBuilder_ == null) {
          rtaBidRiseBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              BidRise, BidRise.Builder, BidRiseOrBuilder>(
                  rtaBidRise_,
                  ((bitField0_ & 0x00000040) == 0x00000040),
                  getParentForChildren(),
                  isClean());
          rtaBidRise_ = null;
        }
        return rtaBidRiseBuilder_;
      }

      private Object prefetchDate_ = "";
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 9;</code>
       */
      public boolean hasPrefetchDate() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 9;</code>
       */
      public String getPrefetchDate() {
        Object ref = prefetchDate_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            prefetchDate_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 9;</code>
       */
      public com.google.protobuf.ByteString
          getPrefetchDateBytes() {
        Object ref = prefetchDate_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          prefetchDate_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 9;</code>
       */
      public Builder setPrefetchDate(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        prefetchDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 9;</code>
       */
      public Builder clearPrefetchDate() {
        bitField0_ = (bitField0_ & ~0x00000080);
        prefetchDate_ = getDefaultInstance().getPrefetchDate();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 9;</code>
       */
      public Builder setPrefetchDateBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        prefetchDate_ = value;
        onChanged();
        return this;
      }

      private int rtaBidType_ = 0;
      /**
       * <pre>
       * 标志是否使用rta溢价
       * </pre>
       *
       * <code>optional .RtaBidType rta_bid_type = 10;</code>
       */
      public boolean hasRtaBidType() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       * 标志是否使用rta溢价
       * </pre>
       *
       * <code>optional .RtaBidType rta_bid_type = 10;</code>
       */
      public RtaBidType getRtaBidType() {
        RtaBidType result = RtaBidType.valueOf(rtaBidType_);
        return result == null ? RtaBidType.RTA_NORMAL : result;
      }
      /**
       * <pre>
       * 标志是否使用rta溢价
       * </pre>
       *
       * <code>optional .RtaBidType rta_bid_type = 10;</code>
       */
      public Builder setRtaBidType(RtaBidType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000100;
        rtaBidType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标志是否使用rta溢价
       * </pre>
       *
       * <code>optional .RtaBidType rta_bid_type = 10;</code>
       */
      public Builder clearRtaBidType() {
        bitField0_ = (bitField0_ & ~0x00000100);
        rtaBidType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<RtaBid> rtaBid_ =
        java.util.Collections.emptyList();
      private void ensureRtaBidIsMutable() {
        if (!((bitField0_ & 0x00000200) == 0x00000200)) {
          rtaBid_ = new java.util.ArrayList<RtaBid>(rtaBid_);
          bitField0_ |= 0x00000200;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          RtaBid, RtaBid.Builder, RtaBidOrBuilder> rtaBidBuilder_;

      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public java.util.List<RtaBid> getRtaBidList() {
        if (rtaBidBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rtaBid_);
        } else {
          return rtaBidBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public int getRtaBidCount() {
        if (rtaBidBuilder_ == null) {
          return rtaBid_.size();
        } else {
          return rtaBidBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public RtaBid getRtaBid(int index) {
        if (rtaBidBuilder_ == null) {
          return rtaBid_.get(index);
        } else {
          return rtaBidBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder setRtaBid(
          int index, RtaBid value) {
        if (rtaBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaBidIsMutable();
          rtaBid_.set(index, value);
          onChanged();
        } else {
          rtaBidBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder setRtaBid(
          int index, RtaBid.Builder builderForValue) {
        if (rtaBidBuilder_ == null) {
          ensureRtaBidIsMutable();
          rtaBid_.set(index, builderForValue.build());
          onChanged();
        } else {
          rtaBidBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder addRtaBid(RtaBid value) {
        if (rtaBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaBidIsMutable();
          rtaBid_.add(value);
          onChanged();
        } else {
          rtaBidBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder addRtaBid(
          int index, RtaBid value) {
        if (rtaBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaBidIsMutable();
          rtaBid_.add(index, value);
          onChanged();
        } else {
          rtaBidBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder addRtaBid(
          RtaBid.Builder builderForValue) {
        if (rtaBidBuilder_ == null) {
          ensureRtaBidIsMutable();
          rtaBid_.add(builderForValue.build());
          onChanged();
        } else {
          rtaBidBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder addRtaBid(
          int index, RtaBid.Builder builderForValue) {
        if (rtaBidBuilder_ == null) {
          ensureRtaBidIsMutable();
          rtaBid_.add(index, builderForValue.build());
          onChanged();
        } else {
          rtaBidBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder addAllRtaBid(
          Iterable<? extends RtaBid> values) {
        if (rtaBidBuilder_ == null) {
          ensureRtaBidIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rtaBid_);
          onChanged();
        } else {
          rtaBidBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder clearRtaBid() {
        if (rtaBidBuilder_ == null) {
          rtaBid_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);
          onChanged();
        } else {
          rtaBidBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public Builder removeRtaBid(int index) {
        if (rtaBidBuilder_ == null) {
          ensureRtaBidIsMutable();
          rtaBid_.remove(index);
          onChanged();
        } else {
          rtaBidBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public RtaBid.Builder getRtaBidBuilder(
          int index) {
        return getRtaBidFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public RtaBidOrBuilder getRtaBidOrBuilder(
          int index) {
        if (rtaBidBuilder_ == null) {
          return rtaBid_.get(index);  } else {
          return rtaBidBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public java.util.List<? extends RtaBidOrBuilder>
           getRtaBidOrBuilderList() {
        if (rtaBidBuilder_ != null) {
          return rtaBidBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rtaBid_);
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public RtaBid.Builder addRtaBidBuilder() {
        return getRtaBidFieldBuilder().addBuilder(
            RtaBid.getDefaultInstance());
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public RtaBid.Builder addRtaBidBuilder(
          int index) {
        return getRtaBidFieldBuilder().addBuilder(
            index, RtaBid.getDefaultInstance());
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于ocpx的浅层转化出价
       * </pre>
       *
       * <code>repeated .RtaBid rta_bid = 11;</code>
       */
      public java.util.List<RtaBid.Builder>
           getRtaBidBuilderList() {
        return getRtaBidFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          RtaBid, RtaBid.Builder, RtaBidOrBuilder>
          getRtaBidFieldBuilder() {
        if (rtaBidBuilder_ == null) {
          rtaBidBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              RtaBid, RtaBid.Builder, RtaBidOrBuilder>(
                  rtaBid_,
                  ((bitField0_ & 0x00000200) == 0x00000200),
                  getParentForChildren(),
                  isClean());
          rtaBid_ = null;
        }
        return rtaBidBuilder_;
      }

      private java.util.List<RtaBid> rtaCpcBid_ =
        java.util.Collections.emptyList();
      private void ensureRtaCpcBidIsMutable() {
        if (!((bitField0_ & 0x00000400) == 0x00000400)) {
          rtaCpcBid_ = new java.util.ArrayList<RtaBid>(rtaCpcBid_);
          bitField0_ |= 0x00000400;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          RtaBid, RtaBid.Builder, RtaBidOrBuilder> rtaCpcBidBuilder_;

      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public java.util.List<RtaBid> getRtaCpcBidList() {
        if (rtaCpcBidBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rtaCpcBid_);
        } else {
          return rtaCpcBidBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public int getRtaCpcBidCount() {
        if (rtaCpcBidBuilder_ == null) {
          return rtaCpcBid_.size();
        } else {
          return rtaCpcBidBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public RtaBid getRtaCpcBid(int index) {
        if (rtaCpcBidBuilder_ == null) {
          return rtaCpcBid_.get(index);
        } else {
          return rtaCpcBidBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder setRtaCpcBid(
          int index, RtaBid value) {
        if (rtaCpcBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaCpcBidIsMutable();
          rtaCpcBid_.set(index, value);
          onChanged();
        } else {
          rtaCpcBidBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder setRtaCpcBid(
          int index, RtaBid.Builder builderForValue) {
        if (rtaCpcBidBuilder_ == null) {
          ensureRtaCpcBidIsMutable();
          rtaCpcBid_.set(index, builderForValue.build());
          onChanged();
        } else {
          rtaCpcBidBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder addRtaCpcBid(RtaBid value) {
        if (rtaCpcBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaCpcBidIsMutable();
          rtaCpcBid_.add(value);
          onChanged();
        } else {
          rtaCpcBidBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder addRtaCpcBid(
          int index, RtaBid value) {
        if (rtaCpcBidBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRtaCpcBidIsMutable();
          rtaCpcBid_.add(index, value);
          onChanged();
        } else {
          rtaCpcBidBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder addRtaCpcBid(
          RtaBid.Builder builderForValue) {
        if (rtaCpcBidBuilder_ == null) {
          ensureRtaCpcBidIsMutable();
          rtaCpcBid_.add(builderForValue.build());
          onChanged();
        } else {
          rtaCpcBidBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder addRtaCpcBid(
          int index, RtaBid.Builder builderForValue) {
        if (rtaCpcBidBuilder_ == null) {
          ensureRtaCpcBidIsMutable();
          rtaCpcBid_.add(index, builderForValue.build());
          onChanged();
        } else {
          rtaCpcBidBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder addAllRtaCpcBid(
          Iterable<? extends RtaBid> values) {
        if (rtaCpcBidBuilder_ == null) {
          ensureRtaCpcBidIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rtaCpcBid_);
          onChanged();
        } else {
          rtaCpcBidBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder clearRtaCpcBid() {
        if (rtaCpcBidBuilder_ == null) {
          rtaCpcBid_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000400);
          onChanged();
        } else {
          rtaCpcBidBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public Builder removeRtaCpcBid(int index) {
        if (rtaCpcBidBuilder_ == null) {
          ensureRtaCpcBidIsMutable();
          rtaCpcBid_.remove(index);
          onChanged();
        } else {
          rtaCpcBidBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public RtaBid.Builder getRtaCpcBidBuilder(
          int index) {
        return getRtaCpcBidFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public RtaBidOrBuilder getRtaCpcBidOrBuilder(
          int index) {
        if (rtaCpcBidBuilder_ == null) {
          return rtaCpcBid_.get(index);  } else {
          return rtaCpcBidBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public java.util.List<? extends RtaBidOrBuilder>
           getRtaCpcBidOrBuilderList() {
        if (rtaCpcBidBuilder_ != null) {
          return rtaCpcBidBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rtaCpcBid_);
        }
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public RtaBid.Builder addRtaCpcBidBuilder() {
        return getRtaCpcBidFieldBuilder().addBuilder(
            RtaBid.getDefaultInstance());
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public RtaBid.Builder addRtaCpcBidBuilder(
          int index) {
        return getRtaCpcBidFieldBuilder().addBuilder(
            index, RtaBid.getDefaultInstance());
      }
      /**
       * <pre>
       * rta直接出价,必须配合策略id使用,用于cpc广告
       * </pre>
       *
       * <code>repeated .RtaBid rta_cpc_bid = 12;</code>
       */
      public java.util.List<RtaBid.Builder>
           getRtaCpcBidBuilderList() {
        return getRtaCpcBidFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          RtaBid, RtaBid.Builder, RtaBidOrBuilder>
          getRtaCpcBidFieldBuilder() {
        if (rtaCpcBidBuilder_ == null) {
          rtaCpcBidBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              RtaBid, RtaBid.Builder, RtaBidOrBuilder>(
                  rtaCpcBid_,
                  ((bitField0_ & 0x00000400) == 0x00000400),
                  getParentForChildren(),
                  isClean());
          rtaCpcBid_ = null;
        }
        return rtaCpcBidBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RtaApiResponse)
    }

    // @@protoc_insertion_point(class_scope:RtaApiResponse)
    private static final RtaApiResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new RtaApiResponse();
    }

    public static RtaApiResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<RtaApiResponse>
        PARSER = new com.google.protobuf.AbstractParser<RtaApiResponse>() {
      public RtaApiResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RtaApiResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RtaApiResponse> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<RtaApiResponse> getParserForType() {
      return PARSER;
    }

    public RtaApiResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface AdResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:AdResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 账户id，百度方分配的账户id
     * </pre>
     *
     * <code>required uint64 account_id = 1;</code>
     */
    boolean hasAccountId();
    /**
     * <pre>
     * 账户id，百度方分配的账户id
     * </pre>
     *
     * <code>required uint64 account_id = 1;</code>
     */
    long getAccountId();

    /**
     * <pre>
     * 广告单元id
     * </pre>
     *
     * <code>repeated uint64 unit_id = 2;</code>
     */
    java.util.List<Long> getUnitIdList();
    /**
     * <pre>
     * 广告单元id
     * </pre>
     *
     * <code>repeated uint64 unit_id = 2;</code>
     */
    int getUnitIdCount();
    /**
     * <pre>
     * 广告单元id
     * </pre>
     *
     * <code>repeated uint64 unit_id = 2;</code>
     */
    long getUnitId(int index);
  }
  /**
   * Protobuf type {@code AdResult}
   */
  public  static final class AdResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:AdResult)
      AdResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use AdResult.newBuilder() to construct.
    private AdResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private AdResult() {
      accountId_ = 0L;
      unitId_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private AdResult(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              accountId_ = input.readUInt64();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                unitId_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              unitId_.add(input.readUInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                unitId_ = new java.util.ArrayList<Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                unitId_.add(input.readUInt64());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          unitId_ = java.util.Collections.unmodifiableList(unitId_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaResponse.internal_static_AdResult_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaResponse.internal_static_AdResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              AdResult.class, Builder.class);
    }

    private int bitField0_;
    public static final int ACCOUNT_ID_FIELD_NUMBER = 1;
    private long accountId_;
    /**
     * <pre>
     * 账户id，百度方分配的账户id
     * </pre>
     *
     * <code>required uint64 account_id = 1;</code>
     */
    public boolean hasAccountId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     * 账户id，百度方分配的账户id
     * </pre>
     *
     * <code>required uint64 account_id = 1;</code>
     */
    public long getAccountId() {
      return accountId_;
    }

    public static final int UNIT_ID_FIELD_NUMBER = 2;
    private java.util.List<Long> unitId_;
    /**
     * <pre>
     * 广告单元id
     * </pre>
     *
     * <code>repeated uint64 unit_id = 2;</code>
     */
    public java.util.List<Long>
        getUnitIdList() {
      return unitId_;
    }
    /**
     * <pre>
     * 广告单元id
     * </pre>
     *
     * <code>repeated uint64 unit_id = 2;</code>
     */
    public int getUnitIdCount() {
      return unitId_.size();
    }
    /**
     * <pre>
     * 广告单元id
     * </pre>
     *
     * <code>repeated uint64 unit_id = 2;</code>
     */
    public long getUnitId(int index) {
      return unitId_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasAccountId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, accountId_);
      }
      for (int i = 0; i < unitId_.size(); i++) {
        output.writeUInt64(2, unitId_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, accountId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < unitId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(unitId_.get(i));
        }
        size += dataSize;
        size += 1 * getUnitIdList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof AdResult)) {
        return super.equals(obj);
      }
      AdResult other = (AdResult) obj;

      boolean result = true;
      result = result && (hasAccountId() == other.hasAccountId());
      if (hasAccountId()) {
        result = result && (getAccountId()
            == other.getAccountId());
      }
      result = result && getUnitIdList()
          .equals(other.getUnitIdList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAccountId()) {
        hash = (37 * hash) + ACCOUNT_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getAccountId());
      }
      if (getUnitIdCount() > 0) {
        hash = (37 * hash) + UNIT_ID_FIELD_NUMBER;
        hash = (53 * hash) + getUnitIdList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static AdResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static AdResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static AdResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static AdResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static AdResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static AdResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static AdResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static AdResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static AdResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static AdResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static AdResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static AdResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(AdResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code AdResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:AdResult)
        AdResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaResponse.internal_static_AdResult_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaResponse.internal_static_AdResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                AdResult.class, Builder.class);
      }

      // Construct using BaiduRtaResponse.AdResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        accountId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        unitId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaResponse.internal_static_AdResult_descriptor;
      }

      public AdResult getDefaultInstanceForType() {
        return AdResult.getDefaultInstance();
      }

      public AdResult build() {
        AdResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public AdResult buildPartial() {
        AdResult result = new AdResult(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.accountId_ = accountId_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          unitId_ = java.util.Collections.unmodifiableList(unitId_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.unitId_ = unitId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof AdResult) {
          return mergeFrom((AdResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(AdResult other) {
        if (other == AdResult.getDefaultInstance()) return this;
        if (other.hasAccountId()) {
          setAccountId(other.getAccountId());
        }
        if (!other.unitId_.isEmpty()) {
          if (unitId_.isEmpty()) {
            unitId_ = other.unitId_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureUnitIdIsMutable();
            unitId_.addAll(other.unitId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAccountId()) {
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        AdResult parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (AdResult) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long accountId_ ;
      /**
       * <pre>
       * 账户id，百度方分配的账户id
       * </pre>
       *
       * <code>required uint64 account_id = 1;</code>
       */
      public boolean hasAccountId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       * 账户id，百度方分配的账户id
       * </pre>
       *
       * <code>required uint64 account_id = 1;</code>
       */
      public long getAccountId() {
        return accountId_;
      }
      /**
       * <pre>
       * 账户id，百度方分配的账户id
       * </pre>
       *
       * <code>required uint64 account_id = 1;</code>
       */
      public Builder setAccountId(long value) {
        bitField0_ |= 0x00000001;
        accountId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 账户id，百度方分配的账户id
       * </pre>
       *
       * <code>required uint64 account_id = 1;</code>
       */
      public Builder clearAccountId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        accountId_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<Long> unitId_ = java.util.Collections.emptyList();
      private void ensureUnitIdIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          unitId_ = new java.util.ArrayList<Long>(unitId_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       * 广告单元id
       * </pre>
       *
       * <code>repeated uint64 unit_id = 2;</code>
       */
      public java.util.List<Long>
          getUnitIdList() {
        return java.util.Collections.unmodifiableList(unitId_);
      }
      /**
       * <pre>
       * 广告单元id
       * </pre>
       *
       * <code>repeated uint64 unit_id = 2;</code>
       */
      public int getUnitIdCount() {
        return unitId_.size();
      }
      /**
       * <pre>
       * 广告单元id
       * </pre>
       *
       * <code>repeated uint64 unit_id = 2;</code>
       */
      public long getUnitId(int index) {
        return unitId_.get(index);
      }
      /**
       * <pre>
       * 广告单元id
       * </pre>
       *
       * <code>repeated uint64 unit_id = 2;</code>
       */
      public Builder setUnitId(
          int index, long value) {
        ensureUnitIdIsMutable();
        unitId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告单元id
       * </pre>
       *
       * <code>repeated uint64 unit_id = 2;</code>
       */
      public Builder addUnitId(long value) {
        ensureUnitIdIsMutable();
        unitId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告单元id
       * </pre>
       *
       * <code>repeated uint64 unit_id = 2;</code>
       */
      public Builder addAllUnitId(
          Iterable<? extends Long> values) {
        ensureUnitIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, unitId_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 广告单元id
       * </pre>
       *
       * <code>repeated uint64 unit_id = 2;</code>
       */
      public Builder clearUnitId() {
        unitId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:AdResult)
    }

    // @@protoc_insertion_point(class_scope:AdResult)
    private static final AdResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new AdResult();
    }

    public static AdResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<AdResult>
        PARSER = new com.google.protobuf.AbstractParser<AdResult>() {
      public AdResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new AdResult(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<AdResult> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<AdResult> getParserForType() {
      return PARSER;
    }

    public AdResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RtaStrategyAdResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RtaStrategyAdResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    boolean hasRtaId();
    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    long getRtaId();
  }
  /**
   * Protobuf type {@code RtaStrategyAdResult}
   */
  public  static final class RtaStrategyAdResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RtaStrategyAdResult)
      RtaStrategyAdResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RtaStrategyAdResult.newBuilder() to construct.
    private RtaStrategyAdResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RtaStrategyAdResult() {
      rtaId_ = 0L;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RtaStrategyAdResult(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              rtaId_ = input.readUInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaResponse.internal_static_RtaStrategyAdResult_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaResponse.internal_static_RtaStrategyAdResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RtaStrategyAdResult.class, Builder.class);
    }

    private int bitField0_;
    public static final int RTA_ID_FIELD_NUMBER = 1;
    private long rtaId_;
    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    public boolean hasRtaId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    public long getRtaId() {
      return rtaId_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasRtaId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, rtaId_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, rtaId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof RtaStrategyAdResult)) {
        return super.equals(obj);
      }
      RtaStrategyAdResult other = (RtaStrategyAdResult) obj;

      boolean result = true;
      result = result && (hasRtaId() == other.hasRtaId());
      if (hasRtaId()) {
        result = result && (getRtaId()
            == other.getRtaId());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRtaId()) {
        hash = (37 * hash) + RTA_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRtaId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static RtaStrategyAdResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaStrategyAdResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaStrategyAdResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaStrategyAdResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaStrategyAdResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaStrategyAdResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaStrategyAdResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaStrategyAdResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaStrategyAdResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static RtaStrategyAdResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaStrategyAdResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaStrategyAdResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(RtaStrategyAdResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RtaStrategyAdResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RtaStrategyAdResult)
        RtaStrategyAdResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaResponse.internal_static_RtaStrategyAdResult_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaResponse.internal_static_RtaStrategyAdResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RtaStrategyAdResult.class, Builder.class);
      }

      // Construct using BaiduRtaResponse.RtaStrategyAdResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        rtaId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaResponse.internal_static_RtaStrategyAdResult_descriptor;
      }

      public RtaStrategyAdResult getDefaultInstanceForType() {
        return RtaStrategyAdResult.getDefaultInstance();
      }

      public RtaStrategyAdResult build() {
        RtaStrategyAdResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RtaStrategyAdResult buildPartial() {
        RtaStrategyAdResult result = new RtaStrategyAdResult(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.rtaId_ = rtaId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RtaStrategyAdResult) {
          return mergeFrom((RtaStrategyAdResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RtaStrategyAdResult other) {
        if (other == RtaStrategyAdResult.getDefaultInstance()) return this;
        if (other.hasRtaId()) {
          setRtaId(other.getRtaId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRtaId()) {
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RtaStrategyAdResult parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RtaStrategyAdResult) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long rtaId_ ;
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public boolean hasRtaId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public long getRtaId() {
        return rtaId_;
      }
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public Builder setRtaId(long value) {
        bitField0_ |= 0x00000001;
        rtaId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public Builder clearRtaId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rtaId_ = 0L;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RtaStrategyAdResult)
    }

    // @@protoc_insertion_point(class_scope:RtaStrategyAdResult)
    private static final RtaStrategyAdResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new RtaStrategyAdResult();
    }

    public static RtaStrategyAdResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<RtaStrategyAdResult>
        PARSER = new com.google.protobuf.AbstractParser<RtaStrategyAdResult>() {
      public RtaStrategyAdResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RtaStrategyAdResult(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RtaStrategyAdResult> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<RtaStrategyAdResult> getParserForType() {
      return PARSER;
    }

    public RtaStrategyAdResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BidRiseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:BidRise)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint64 rta_id = 1;</code>
     */
    boolean hasRtaId();
    /**
     * <code>optional uint64 rta_id = 1;</code>
     */
    long getRtaId();

    /**
     * <pre>
     * 溢价系数
     * </pre>
     *
     * <code>optional float bid_rise = 2;</code>
     */
    boolean hasBidRise();
    /**
     * <pre>
     * 溢价系数
     * </pre>
     *
     * <code>optional float bid_rise = 2;</code>
     */
    float getBidRise();
  }
  /**
   * Protobuf type {@code BidRise}
   */
  public  static final class BidRise extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:BidRise)
      BidRiseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BidRise.newBuilder() to construct.
    private BidRise(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BidRise() {
      rtaId_ = 0L;
      bidRise_ = 0F;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BidRise(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              rtaId_ = input.readUInt64();
              break;
            }
            case 21: {
              bitField0_ |= 0x00000002;
              bidRise_ = input.readFloat();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaResponse.internal_static_BidRise_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaResponse.internal_static_BidRise_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              BidRise.class, Builder.class);
    }

    private int bitField0_;
    public static final int RTA_ID_FIELD_NUMBER = 1;
    private long rtaId_;
    /**
     * <code>optional uint64 rta_id = 1;</code>
     */
    public boolean hasRtaId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional uint64 rta_id = 1;</code>
     */
    public long getRtaId() {
      return rtaId_;
    }

    public static final int BID_RISE_FIELD_NUMBER = 2;
    private float bidRise_;
    /**
     * <pre>
     * 溢价系数
     * </pre>
     *
     * <code>optional float bid_rise = 2;</code>
     */
    public boolean hasBidRise() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     * 溢价系数
     * </pre>
     *
     * <code>optional float bid_rise = 2;</code>
     */
    public float getBidRise() {
      return bidRise_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, rtaId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeFloat(2, bidRise_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, rtaId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(2, bidRise_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof BidRise)) {
        return super.equals(obj);
      }
      BidRise other = (BidRise) obj;

      boolean result = true;
      result = result && (hasRtaId() == other.hasRtaId());
      if (hasRtaId()) {
        result = result && (getRtaId()
            == other.getRtaId());
      }
      result = result && (hasBidRise() == other.hasBidRise());
      if (hasBidRise()) {
        result = result && (
            Float.floatToIntBits(getBidRise())
            == Float.floatToIntBits(
                other.getBidRise()));
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRtaId()) {
        hash = (37 * hash) + RTA_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRtaId());
      }
      if (hasBidRise()) {
        hash = (37 * hash) + BID_RISE_FIELD_NUMBER;
        hash = (53 * hash) + Float.floatToIntBits(
            getBidRise());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static BidRise parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BidRise parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BidRise parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BidRise parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BidRise parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static BidRise parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static BidRise parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BidRise parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static BidRise parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static BidRise parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static BidRise parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static BidRise parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(BidRise prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code BidRise}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:BidRise)
        BidRiseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaResponse.internal_static_BidRise_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaResponse.internal_static_BidRise_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                BidRise.class, Builder.class);
      }

      // Construct using BaiduRtaResponse.BidRise.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        rtaId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        bidRise_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaResponse.internal_static_BidRise_descriptor;
      }

      public BidRise getDefaultInstanceForType() {
        return BidRise.getDefaultInstance();
      }

      public BidRise build() {
        BidRise result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public BidRise buildPartial() {
        BidRise result = new BidRise(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.rtaId_ = rtaId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.bidRise_ = bidRise_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof BidRise) {
          return mergeFrom((BidRise)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(BidRise other) {
        if (other == BidRise.getDefaultInstance()) return this;
        if (other.hasRtaId()) {
          setRtaId(other.getRtaId());
        }
        if (other.hasBidRise()) {
          setBidRise(other.getBidRise());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        BidRise parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (BidRise) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long rtaId_ ;
      /**
       * <code>optional uint64 rta_id = 1;</code>
       */
      public boolean hasRtaId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional uint64 rta_id = 1;</code>
       */
      public long getRtaId() {
        return rtaId_;
      }
      /**
       * <code>optional uint64 rta_id = 1;</code>
       */
      public Builder setRtaId(long value) {
        bitField0_ |= 0x00000001;
        rtaId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 rta_id = 1;</code>
       */
      public Builder clearRtaId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rtaId_ = 0L;
        onChanged();
        return this;
      }

      private float bidRise_ ;
      /**
       * <pre>
       * 溢价系数
       * </pre>
       *
       * <code>optional float bid_rise = 2;</code>
       */
      public boolean hasBidRise() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       * 溢价系数
       * </pre>
       *
       * <code>optional float bid_rise = 2;</code>
       */
      public float getBidRise() {
        return bidRise_;
      }
      /**
       * <pre>
       * 溢价系数
       * </pre>
       *
       * <code>optional float bid_rise = 2;</code>
       */
      public Builder setBidRise(float value) {
        bitField0_ |= 0x00000002;
        bidRise_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 溢价系数
       * </pre>
       *
       * <code>optional float bid_rise = 2;</code>
       */
      public Builder clearBidRise() {
        bitField0_ = (bitField0_ & ~0x00000002);
        bidRise_ = 0F;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:BidRise)
    }

    // @@protoc_insertion_point(class_scope:BidRise)
    private static final BidRise DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new BidRise();
    }

    public static BidRise getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<BidRise>
        PARSER = new com.google.protobuf.AbstractParser<BidRise>() {
      public BidRise parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BidRise(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BidRise> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<BidRise> getParserForType() {
      return PARSER;
    }

    public BidRise getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RtaBidOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RtaBid)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    boolean hasRtaId();
    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    long getRtaId();

    /**
     * <pre>
     * rta 直接出价
     * </pre>
     *
     * <code>required uint32 rta_bid_value = 2;</code>
     */
    boolean hasRtaBidValue();
    /**
     * <pre>
     * rta 直接出价
     * </pre>
     *
     * <code>required uint32 rta_bid_value = 2;</code>
     */
    int getRtaBidValue();
  }
  /**
   * Protobuf type {@code RtaBid}
   */
  public  static final class RtaBid extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RtaBid)
      RtaBidOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RtaBid.newBuilder() to construct.
    private RtaBid(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RtaBid() {
      rtaId_ = 0L;
      rtaBidValue_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RtaBid(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              rtaId_ = input.readUInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              rtaBidValue_ = input.readUInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaResponse.internal_static_RtaBid_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaResponse.internal_static_RtaBid_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RtaBid.class, Builder.class);
    }

    private int bitField0_;
    public static final int RTA_ID_FIELD_NUMBER = 1;
    private long rtaId_;
    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    public boolean hasRtaId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required uint64 rta_id = 1;</code>
     */
    public long getRtaId() {
      return rtaId_;
    }

    public static final int RTA_BID_VALUE_FIELD_NUMBER = 2;
    private int rtaBidValue_;
    /**
     * <pre>
     * rta 直接出价
     * </pre>
     *
     * <code>required uint32 rta_bid_value = 2;</code>
     */
    public boolean hasRtaBidValue() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     * rta 直接出价
     * </pre>
     *
     * <code>required uint32 rta_bid_value = 2;</code>
     */
    public int getRtaBidValue() {
      return rtaBidValue_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasRtaId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRtaBidValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, rtaId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeUInt32(2, rtaBidValue_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, rtaId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, rtaBidValue_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof RtaBid)) {
        return super.equals(obj);
      }
      RtaBid other = (RtaBid) obj;

      boolean result = true;
      result = result && (hasRtaId() == other.hasRtaId());
      if (hasRtaId()) {
        result = result && (getRtaId()
            == other.getRtaId());
      }
      result = result && (hasRtaBidValue() == other.hasRtaBidValue());
      if (hasRtaBidValue()) {
        result = result && (getRtaBidValue()
            == other.getRtaBidValue());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRtaId()) {
        hash = (37 * hash) + RTA_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRtaId());
      }
      if (hasRtaBidValue()) {
        hash = (37 * hash) + RTA_BID_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getRtaBidValue();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static RtaBid parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaBid parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaBid parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaBid parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaBid parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaBid parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaBid parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaBid parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaBid parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static RtaBid parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaBid parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaBid parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(RtaBid prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RtaBid}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RtaBid)
        RtaBidOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaResponse.internal_static_RtaBid_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaResponse.internal_static_RtaBid_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RtaBid.class, Builder.class);
      }

      // Construct using BaiduRtaResponse.RtaBid.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        rtaId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        rtaBidValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaResponse.internal_static_RtaBid_descriptor;
      }

      public RtaBid getDefaultInstanceForType() {
        return RtaBid.getDefaultInstance();
      }

      public RtaBid build() {
        RtaBid result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RtaBid buildPartial() {
        RtaBid result = new RtaBid(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.rtaId_ = rtaId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.rtaBidValue_ = rtaBidValue_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RtaBid) {
          return mergeFrom((RtaBid)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RtaBid other) {
        if (other == RtaBid.getDefaultInstance()) return this;
        if (other.hasRtaId()) {
          setRtaId(other.getRtaId());
        }
        if (other.hasRtaBidValue()) {
          setRtaBidValue(other.getRtaBidValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRtaId()) {
          return false;
        }
        if (!hasRtaBidValue()) {
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RtaBid parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RtaBid) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long rtaId_ ;
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public boolean hasRtaId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public long getRtaId() {
        return rtaId_;
      }
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public Builder setRtaId(long value) {
        bitField0_ |= 0x00000001;
        rtaId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required uint64 rta_id = 1;</code>
       */
      public Builder clearRtaId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rtaId_ = 0L;
        onChanged();
        return this;
      }

      private int rtaBidValue_ ;
      /**
       * <pre>
       * rta 直接出价
       * </pre>
       *
       * <code>required uint32 rta_bid_value = 2;</code>
       */
      public boolean hasRtaBidValue() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       * rta 直接出价
       * </pre>
       *
       * <code>required uint32 rta_bid_value = 2;</code>
       */
      public int getRtaBidValue() {
        return rtaBidValue_;
      }
      /**
       * <pre>
       * rta 直接出价
       * </pre>
       *
       * <code>required uint32 rta_bid_value = 2;</code>
       */
      public Builder setRtaBidValue(int value) {
        bitField0_ |= 0x00000002;
        rtaBidValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * rta 直接出价
       * </pre>
       *
       * <code>required uint32 rta_bid_value = 2;</code>
       */
      public Builder clearRtaBidValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rtaBidValue_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RtaBid)
    }

    // @@protoc_insertion_point(class_scope:RtaBid)
    private static final RtaBid DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new RtaBid();
    }

    public static RtaBid getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<RtaBid>
        PARSER = new com.google.protobuf.AbstractParser<RtaBid>() {
      public RtaBid parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RtaBid(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RtaBid> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<RtaBid> getParserForType() {
      return PARSER;
    }

    public RtaBid getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DpaResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DpaResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    java.util.List<DpaResult.ProductList>
        getPidListsList();
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    DpaResult.ProductList getPidLists(int index);
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    int getPidListsCount();
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    java.util.List<? extends DpaResult.ProductListOrBuilder>
        getPidListsOrBuilderList();
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    DpaResult.ProductListOrBuilder getPidListsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code DpaResult}
   */
  public  static final class DpaResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DpaResult)
      DpaResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DpaResult.newBuilder() to construct.
    private DpaResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DpaResult() {
      pidLists_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DpaResult(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                pidLists_ = new java.util.ArrayList<ProductList>();
                mutable_bitField0_ |= 0x00000001;
              }
              pidLists_.add(
                  input.readMessage(ProductList.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          pidLists_ = java.util.Collections.unmodifiableList(pidLists_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaResponse.internal_static_DpaResult_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaResponse.internal_static_DpaResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              DpaResult.class, Builder.class);
    }

    public interface ProductListOrBuilder extends
        // @@protoc_insertion_point(interface_extends:DpaResult.ProductList)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <pre>
       * 候选商品队列优先级
       * </pre>
       *
       * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
       */
      boolean hasPriority();
      /**
       * <pre>
       * 候选商品队列优先级
       * </pre>
       *
       * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
       */
      ProductList.Priority getPriority();

      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      java.util.List<ProductList.Product>
          getPidListList();
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      ProductList.Product getPidList(int index);
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      int getPidListCount();
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      java.util.List<? extends ProductList.ProductOrBuilder>
          getPidListOrBuilderList();
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      ProductList.ProductOrBuilder getPidListOrBuilder(
          int index);

      /**
       * <pre>
       * 商品目录id（可选，非必须）
       * </pre>
       *
       * <code>optional uint64 catalog_id = 3;</code>
       */
      boolean hasCatalogId();
      /**
       * <pre>
       * 商品目录id（可选，非必须）
       * </pre>
       *
       * <code>optional uint64 catalog_id = 3;</code>
       */
      long getCatalogId();
    }
    /**
     * <pre>
     * 描述一组候选商品
     * </pre>
     *
     * Protobuf type {@code DpaResult.ProductList}
     */
    public  static final class ProductList extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:DpaResult.ProductList)
        ProductListOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use ProductList.newBuilder() to construct.
      private ProductList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private ProductList() {
        priority_ = 0;
        pidList_ = java.util.Collections.emptyList();
        catalogId_ = 0L;
      }

      @Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private ProductList(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        int mutable_bitField0_ = 0;
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
              case 8: {
                int rawValue = input.readEnum();
                Priority value = Priority.valueOf(rawValue);
                if (value == null) {
                  unknownFields.mergeVarintField(1, rawValue);
                } else {
                  bitField0_ |= 0x00000001;
                  priority_ = rawValue;
                }
                break;
              }
              case 18: {
                if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                  pidList_ = new java.util.ArrayList<Product>();
                  mutable_bitField0_ |= 0x00000002;
                }
                pidList_.add(
                    input.readMessage(Product.PARSER, extensionRegistry));
                break;
              }
              case 24: {
                bitField0_ |= 0x00000002;
                catalogId_ = input.readUInt64();
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
            pidList_ = java.util.Collections.unmodifiableList(pidList_);
          }
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaResponse.internal_static_DpaResult_ProductList_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaResponse.internal_static_DpaResult_ProductList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ProductList.class, Builder.class);
      }

      /**
       * <pre>
       * 描述候选商品队列优先级
       * </pre>
       *
       * Protobuf enum {@code DpaResult.ProductList.Priority}
       */
      public enum Priority
          implements com.google.protobuf.ProtocolMessageEnum {
        /**
         * <pre>
         * 高优先级
         * </pre>
         *
         * <code>Level_0 = 0;</code>
         */
        Level_0(0),
        /**
         * <pre>
         * 中优先级
         * </pre>
         *
         * <code>Level_1 = 1;</code>
         */
        Level_1(1),
        /**
         * <pre>
         * 低优先级
         * </pre>
         *
         * <code>Level_2 = 2;</code>
         */
        Level_2(2),
        ;

        /**
         * <pre>
         * 高优先级
         * </pre>
         *
         * <code>Level_0 = 0;</code>
         */
        public static final int Level_0_VALUE = 0;
        /**
         * <pre>
         * 中优先级
         * </pre>
         *
         * <code>Level_1 = 1;</code>
         */
        public static final int Level_1_VALUE = 1;
        /**
         * <pre>
         * 低优先级
         * </pre>
         *
         * <code>Level_2 = 2;</code>
         */
        public static final int Level_2_VALUE = 2;


        public final int getNumber() {
          return value;
        }

        /**
         * @deprecated Use {@link #forNumber(int)} instead.
         */
        @Deprecated
        public static Priority valueOf(int value) {
          return forNumber(value);
        }

        public static Priority forNumber(int value) {
          switch (value) {
            case 0: return Level_0;
            case 1: return Level_1;
            case 2: return Level_2;
            default: return null;
          }
        }

        public static com.google.protobuf.Internal.EnumLiteMap<Priority>
            internalGetValueMap() {
          return internalValueMap;
        }
        private static final com.google.protobuf.Internal.EnumLiteMap<
            Priority> internalValueMap =
              new com.google.protobuf.Internal.EnumLiteMap<Priority>() {
                public Priority findValueByNumber(int number) {
                  return Priority.forNumber(number);
                }
              };

        public final com.google.protobuf.Descriptors.EnumValueDescriptor
            getValueDescriptor() {
          return getDescriptor().getValues().get(ordinal());
        }
        public final com.google.protobuf.Descriptors.EnumDescriptor
            getDescriptorForType() {
          return getDescriptor();
        }
        public static final com.google.protobuf.Descriptors.EnumDescriptor
            getDescriptor() {
          return ProductList.getDescriptor().getEnumTypes().get(0);
        }

        private static final Priority[] VALUES = values();

        public static Priority valueOf(
            com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
          if (desc.getType() != getDescriptor()) {
            throw new IllegalArgumentException(
              "EnumValueDescriptor is not for this type.");
          }
          return VALUES[desc.getIndex()];
        }

        private final int value;

        private Priority(int value) {
          this.value = value;
        }

        // @@protoc_insertion_point(enum_scope:DpaResult.ProductList.Priority)
      }

      public interface ProductOrBuilder extends
          // @@protoc_insertion_point(interface_extends:DpaResult.ProductList.Product)
          com.google.protobuf.MessageOrBuilder {

        /**
         * <pre>
         * id,通常是商品outer_id
         * </pre>
         *
         * <code>required string id = 1;</code>
         */
        boolean hasId();
        /**
         * <pre>
         * id,通常是商品outer_id
         * </pre>
         *
         * <code>required string id = 1;</code>
         */
        String getId();
        /**
         * <pre>
         * id,通常是商品outer_id
         * </pre>
         *
         * <code>required string id = 1;</code>
         */
        com.google.protobuf.ByteString
            getIdBytes();

        /**
         * <pre>
         * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
         * </pre>
         *
         * <code>optional double bid_ratio = 2;</code>
         */
        boolean hasBidRatio();
        /**
         * <pre>
         * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
         * </pre>
         *
         * <code>optional double bid_ratio = 2;</code>
         */
        double getBidRatio();

        /**
         * <pre>
         * 商品打分，分数越大表示优先级越高（可选，非必须）
         * </pre>
         *
         * <code>optional double score = 3;</code>
         */
        boolean hasScore();
        /**
         * <pre>
         * 商品打分，分数越大表示优先级越高（可选，非必须）
         * </pre>
         *
         * <code>optional double score = 3;</code>
         */
        double getScore();
      }
      /**
       * <pre>
       * 描述单个商品
       * </pre>
       *
       * Protobuf type {@code DpaResult.ProductList.Product}
       */
      public  static final class Product extends
          com.google.protobuf.GeneratedMessageV3 implements
          // @@protoc_insertion_point(message_implements:DpaResult.ProductList.Product)
          ProductOrBuilder {
      private static final long serialVersionUID = 0L;
        // Use Product.newBuilder() to construct.
        private Product(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
          super(builder);
        }
        private Product() {
          id_ = "";
          bidRatio_ = 0D;
          score_ = 0D;
        }

        @Override
        public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
          return this.unknownFields;
        }
        private Product(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          this();
          if (extensionRegistry == null) {
            throw new NullPointerException();
          }
          int mutable_bitField0_ = 0;
          com.google.protobuf.UnknownFieldSet.Builder unknownFields =
              com.google.protobuf.UnknownFieldSet.newBuilder();
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                default: {
                  if (!parseUnknownField(
                      input, unknownFields, extensionRegistry, tag)) {
                    done = true;
                  }
                  break;
                }
                case 10: {
                  com.google.protobuf.ByteString bs = input.readBytes();
                  bitField0_ |= 0x00000001;
                  id_ = bs;
                  break;
                }
                case 17: {
                  bitField0_ |= 0x00000002;
                  bidRatio_ = input.readDouble();
                  break;
                }
                case 25: {
                  bitField0_ |= 0x00000004;
                  score_ = input.readDouble();
                  break;
                }
              }
            }
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(this);
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(
                e).setUnfinishedMessage(this);
          } finally {
            this.unknownFields = unknownFields.build();
            makeExtensionsImmutable();
          }
        }
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return BaiduRtaResponse.internal_static_DpaResult_ProductList_Product_descriptor;
        }

        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return BaiduRtaResponse.internal_static_DpaResult_ProductList_Product_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  Product.class, Builder.class);
        }

        private int bitField0_;
        public static final int ID_FIELD_NUMBER = 1;
        private volatile Object id_;
        /**
         * <pre>
         * id,通常是商品outer_id
         * </pre>
         *
         * <code>required string id = 1;</code>
         */
        public boolean hasId() {
          return ((bitField0_ & 0x00000001) == 0x00000001);
        }
        /**
         * <pre>
         * id,通常是商品outer_id
         * </pre>
         *
         * <code>required string id = 1;</code>
         */
        public String getId() {
          Object ref = id_;
          if (ref instanceof String) {
            return (String) ref;
          } else {
            com.google.protobuf.ByteString bs = 
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            if (bs.isValidUtf8()) {
              id_ = s;
            }
            return s;
          }
        }
        /**
         * <pre>
         * id,通常是商品outer_id
         * </pre>
         *
         * <code>required string id = 1;</code>
         */
        public com.google.protobuf.ByteString
            getIdBytes() {
          Object ref = id_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            id_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }

        public static final int BID_RATIO_FIELD_NUMBER = 2;
        private double bidRatio_;
        /**
         * <pre>
         * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
         * </pre>
         *
         * <code>optional double bid_ratio = 2;</code>
         */
        public boolean hasBidRatio() {
          return ((bitField0_ & 0x00000002) == 0x00000002);
        }
        /**
         * <pre>
         * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
         * </pre>
         *
         * <code>optional double bid_ratio = 2;</code>
         */
        public double getBidRatio() {
          return bidRatio_;
        }

        public static final int SCORE_FIELD_NUMBER = 3;
        private double score_;
        /**
         * <pre>
         * 商品打分，分数越大表示优先级越高（可选，非必须）
         * </pre>
         *
         * <code>optional double score = 3;</code>
         */
        public boolean hasScore() {
          return ((bitField0_ & 0x00000004) == 0x00000004);
        }
        /**
         * <pre>
         * 商品打分，分数越大表示优先级越高（可选，非必须）
         * </pre>
         *
         * <code>optional double score = 3;</code>
         */
        public double getScore() {
          return score_;
        }

        private byte memoizedIsInitialized = -1;
        public final boolean isInitialized() {
          byte isInitialized = memoizedIsInitialized;
          if (isInitialized == 1) return true;
          if (isInitialized == 0) return false;

          if (!hasId()) {
            memoizedIsInitialized = 0;
            return false;
          }
          memoizedIsInitialized = 1;
          return true;
        }

        public void writeTo(com.google.protobuf.CodedOutputStream output)
                            throws java.io.IOException {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            com.google.protobuf.GeneratedMessageV3.writeString(output, 1, id_);
          }
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            output.writeDouble(2, bidRatio_);
          }
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            output.writeDouble(3, score_);
          }
          unknownFields.writeTo(output);
        }

        public int getSerializedSize() {
          int size = memoizedSize;
          if (size != -1) return size;

          size = 0;
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, id_);
          }
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            size += com.google.protobuf.CodedOutputStream
              .computeDoubleSize(2, bidRatio_);
          }
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            size += com.google.protobuf.CodedOutputStream
              .computeDoubleSize(3, score_);
          }
          size += unknownFields.getSerializedSize();
          memoizedSize = size;
          return size;
        }

        @Override
        public boolean equals(final Object obj) {
          if (obj == this) {
           return true;
          }
          if (!(obj instanceof Product)) {
            return super.equals(obj);
          }
          Product other = (Product) obj;

          boolean result = true;
          result = result && (hasId() == other.hasId());
          if (hasId()) {
            result = result && getId()
                .equals(other.getId());
          }
          result = result && (hasBidRatio() == other.hasBidRatio());
          if (hasBidRatio()) {
            result = result && (
                Double.doubleToLongBits(getBidRatio())
                == Double.doubleToLongBits(
                    other.getBidRatio()));
          }
          result = result && (hasScore() == other.hasScore());
          if (hasScore()) {
            result = result && (
                Double.doubleToLongBits(getScore())
                == Double.doubleToLongBits(
                    other.getScore()));
          }
          result = result && unknownFields.equals(other.unknownFields);
          return result;
        }

        @Override
        public int hashCode() {
          if (memoizedHashCode != 0) {
            return memoizedHashCode;
          }
          int hash = 41;
          hash = (19 * hash) + getDescriptor().hashCode();
          if (hasId()) {
            hash = (37 * hash) + ID_FIELD_NUMBER;
            hash = (53 * hash) + getId().hashCode();
          }
          if (hasBidRatio()) {
            hash = (37 * hash) + BID_RATIO_FIELD_NUMBER;
            hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                Double.doubleToLongBits(getBidRatio()));
          }
          if (hasScore()) {
            hash = (37 * hash) + SCORE_FIELD_NUMBER;
            hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
                Double.doubleToLongBits(getScore()));
          }
          hash = (29 * hash) + unknownFields.hashCode();
          memoizedHashCode = hash;
          return hash;
        }

        public static Product parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Product parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Product parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Product parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Product parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data);
        }
        public static Product parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return PARSER.parseFrom(data, extensionRegistry);
        }
        public static Product parseFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static Product parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }
        public static Product parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input);
        }
        public static Product parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
        }
        public static Product parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
        }
        public static Product parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
        }

        public Builder newBuilderForType() { return newBuilder(); }
        public static Builder newBuilder() {
          return DEFAULT_INSTANCE.toBuilder();
        }
        public static Builder newBuilder(Product prototype) {
          return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
        }
        public Builder toBuilder() {
          return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
        }

        @Override
        protected Builder newBuilderForType(
            BuilderParent parent) {
          Builder builder = new Builder(parent);
          return builder;
        }
        /**
         * <pre>
         * 描述单个商品
         * </pre>
         *
         * Protobuf type {@code DpaResult.ProductList.Product}
         */
        public static final class Builder extends
            com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:DpaResult.ProductList.Product)
            ProductOrBuilder {
          public static final com.google.protobuf.Descriptors.Descriptor
              getDescriptor() {
            return BaiduRtaResponse.internal_static_DpaResult_ProductList_Product_descriptor;
          }

          protected FieldAccessorTable
              internalGetFieldAccessorTable() {
            return BaiduRtaResponse.internal_static_DpaResult_ProductList_Product_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                    Product.class, Builder.class);
          }

          // Construct using BaiduRtaResponse.DpaResult.ProductList.Product.newBuilder()
          private Builder() {
            maybeForceBuilderInitialization();
          }

          private Builder(
              BuilderParent parent) {
            super(parent);
            maybeForceBuilderInitialization();
          }
          private void maybeForceBuilderInitialization() {
            if (com.google.protobuf.GeneratedMessageV3
                    .alwaysUseFieldBuilders) {
            }
          }
          public Builder clear() {
            super.clear();
            id_ = "";
            bitField0_ = (bitField0_ & ~0x00000001);
            bidRatio_ = 0D;
            bitField0_ = (bitField0_ & ~0x00000002);
            score_ = 0D;
            bitField0_ = (bitField0_ & ~0x00000004);
            return this;
          }

          public com.google.protobuf.Descriptors.Descriptor
              getDescriptorForType() {
            return BaiduRtaResponse.internal_static_DpaResult_ProductList_Product_descriptor;
          }

          public Product getDefaultInstanceForType() {
            return Product.getDefaultInstance();
          }

          public Product build() {
            Product result = buildPartial();
            if (!result.isInitialized()) {
              throw newUninitializedMessageException(result);
            }
            return result;
          }

          public Product buildPartial() {
            Product result = new Product(this);
            int from_bitField0_ = bitField0_;
            int to_bitField0_ = 0;
            if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
              to_bitField0_ |= 0x00000001;
            }
            result.id_ = id_;
            if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
              to_bitField0_ |= 0x00000002;
            }
            result.bidRatio_ = bidRatio_;
            if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
              to_bitField0_ |= 0x00000004;
            }
            result.score_ = score_;
            result.bitField0_ = to_bitField0_;
            onBuilt();
            return result;
          }

          public Builder clone() {
            return (Builder) super.clone();
          }
          public Builder setField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              Object value) {
            return (Builder) super.setField(field, value);
          }
          public Builder clearField(
              com.google.protobuf.Descriptors.FieldDescriptor field) {
            return (Builder) super.clearField(field);
          }
          public Builder clearOneof(
              com.google.protobuf.Descriptors.OneofDescriptor oneof) {
            return (Builder) super.clearOneof(oneof);
          }
          public Builder setRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              int index, Object value) {
            return (Builder) super.setRepeatedField(field, index, value);
          }
          public Builder addRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              Object value) {
            return (Builder) super.addRepeatedField(field, value);
          }
          public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof Product) {
              return mergeFrom((Product)other);
            } else {
              super.mergeFrom(other);
              return this;
            }
          }

          public Builder mergeFrom(Product other) {
            if (other == Product.getDefaultInstance()) return this;
            if (other.hasId()) {
              bitField0_ |= 0x00000001;
              id_ = other.id_;
              onChanged();
            }
            if (other.hasBidRatio()) {
              setBidRatio(other.getBidRatio());
            }
            if (other.hasScore()) {
              setScore(other.getScore());
            }
            this.mergeUnknownFields(other.unknownFields);
            onChanged();
            return this;
          }

          public final boolean isInitialized() {
            if (!hasId()) {
              return false;
            }
            return true;
          }

          public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
            Product parsedMessage = null;
            try {
              parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
            } catch (com.google.protobuf.InvalidProtocolBufferException e) {
              parsedMessage = (Product) e.getUnfinishedMessage();
              throw e.unwrapIOException();
            } finally {
              if (parsedMessage != null) {
                mergeFrom(parsedMessage);
              }
            }
            return this;
          }
          private int bitField0_;

          private Object id_ = "";
          /**
           * <pre>
           * id,通常是商品outer_id
           * </pre>
           *
           * <code>required string id = 1;</code>
           */
          public boolean hasId() {
            return ((bitField0_ & 0x00000001) == 0x00000001);
          }
          /**
           * <pre>
           * id,通常是商品outer_id
           * </pre>
           *
           * <code>required string id = 1;</code>
           */
          public String getId() {
            Object ref = id_;
            if (!(ref instanceof String)) {
              com.google.protobuf.ByteString bs =
                  (com.google.protobuf.ByteString) ref;
              String s = bs.toStringUtf8();
              if (bs.isValidUtf8()) {
                id_ = s;
              }
              return s;
            } else {
              return (String) ref;
            }
          }
          /**
           * <pre>
           * id,通常是商品outer_id
           * </pre>
           *
           * <code>required string id = 1;</code>
           */
          public com.google.protobuf.ByteString
              getIdBytes() {
            Object ref = id_;
            if (ref instanceof String) {
              com.google.protobuf.ByteString b = 
                  com.google.protobuf.ByteString.copyFromUtf8(
                      (String) ref);
              id_ = b;
              return b;
            } else {
              return (com.google.protobuf.ByteString) ref;
            }
          }
          /**
           * <pre>
           * id,通常是商品outer_id
           * </pre>
           *
           * <code>required string id = 1;</code>
           */
          public Builder setId(
              String value) {
            if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
            id_ = value;
            onChanged();
            return this;
          }
          /**
           * <pre>
           * id,通常是商品outer_id
           * </pre>
           *
           * <code>required string id = 1;</code>
           */
          public Builder clearId() {
            bitField0_ = (bitField0_ & ~0x00000001);
            id_ = getDefaultInstance().getId();
            onChanged();
            return this;
          }
          /**
           * <pre>
           * id,通常是商品outer_id
           * </pre>
           *
           * <code>required string id = 1;</code>
           */
          public Builder setIdBytes(
              com.google.protobuf.ByteString value) {
            if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
            id_ = value;
            onChanged();
            return this;
          }

          private double bidRatio_ ;
          /**
           * <pre>
           * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
           * </pre>
           *
           * <code>optional double bid_ratio = 2;</code>
           */
          public boolean hasBidRatio() {
            return ((bitField0_ & 0x00000002) == 0x00000002);
          }
          /**
           * <pre>
           * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
           * </pre>
           *
           * <code>optional double bid_ratio = 2;</code>
           */
          public double getBidRatio() {
            return bidRatio_;
          }
          /**
           * <pre>
           * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
           * </pre>
           *
           * <code>optional double bid_ratio = 2;</code>
           */
          public Builder setBidRatio(double value) {
            bitField0_ |= 0x00000002;
            bidRatio_ = value;
            onChanged();
            return this;
          }
          /**
           * <pre>
           * 商品溢价系数：1.0表示不溢价(待废弃，目前统一使用rta_bid_rise)
           * </pre>
           *
           * <code>optional double bid_ratio = 2;</code>
           */
          public Builder clearBidRatio() {
            bitField0_ = (bitField0_ & ~0x00000002);
            bidRatio_ = 0D;
            onChanged();
            return this;
          }

          private double score_ ;
          /**
           * <pre>
           * 商品打分，分数越大表示优先级越高（可选，非必须）
           * </pre>
           *
           * <code>optional double score = 3;</code>
           */
          public boolean hasScore() {
            return ((bitField0_ & 0x00000004) == 0x00000004);
          }
          /**
           * <pre>
           * 商品打分，分数越大表示优先级越高（可选，非必须）
           * </pre>
           *
           * <code>optional double score = 3;</code>
           */
          public double getScore() {
            return score_;
          }
          /**
           * <pre>
           * 商品打分，分数越大表示优先级越高（可选，非必须）
           * </pre>
           *
           * <code>optional double score = 3;</code>
           */
          public Builder setScore(double value) {
            bitField0_ |= 0x00000004;
            score_ = value;
            onChanged();
            return this;
          }
          /**
           * <pre>
           * 商品打分，分数越大表示优先级越高（可选，非必须）
           * </pre>
           *
           * <code>optional double score = 3;</code>
           */
          public Builder clearScore() {
            bitField0_ = (bitField0_ & ~0x00000004);
            score_ = 0D;
            onChanged();
            return this;
          }
          public final Builder setUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.setUnknownFields(unknownFields);
          }

          public final Builder mergeUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
            return super.mergeUnknownFields(unknownFields);
          }


          // @@protoc_insertion_point(builder_scope:DpaResult.ProductList.Product)
        }

        // @@protoc_insertion_point(class_scope:DpaResult.ProductList.Product)
        private static final Product DEFAULT_INSTANCE;
        static {
          DEFAULT_INSTANCE = new Product();
        }

        public static Product getDefaultInstance() {
          return DEFAULT_INSTANCE;
        }

        @Deprecated public static final com.google.protobuf.Parser<Product>
            PARSER = new com.google.protobuf.AbstractParser<Product>() {
          public Product parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
            return new Product(input, extensionRegistry);
          }
        };

        public static com.google.protobuf.Parser<Product> parser() {
          return PARSER;
        }

        @Override
        public com.google.protobuf.Parser<Product> getParserForType() {
          return PARSER;
        }

        public Product getDefaultInstanceForType() {
          return DEFAULT_INSTANCE;
        }

      }

      private int bitField0_;
      public static final int PRIORITY_FIELD_NUMBER = 1;
      private int priority_;
      /**
       * <pre>
       * 候选商品队列优先级
       * </pre>
       *
       * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
       */
      public boolean hasPriority() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       * 候选商品队列优先级
       * </pre>
       *
       * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
       */
      public Priority getPriority() {
        Priority result = Priority.valueOf(priority_);
        return result == null ? Priority.Level_0 : result;
      }

      public static final int PID_LIST_FIELD_NUMBER = 2;
      private java.util.List<Product> pidList_;
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      public java.util.List<Product> getPidListList() {
        return pidList_;
      }
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      public java.util.List<? extends ProductOrBuilder>
          getPidListOrBuilderList() {
        return pidList_;
      }
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      public int getPidListCount() {
        return pidList_.size();
      }
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      public Product getPidList(int index) {
        return pidList_.get(index);
      }
      /**
       * <pre>
       * 候选商品队列
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
       */
      public ProductOrBuilder getPidListOrBuilder(
          int index) {
        return pidList_.get(index);
      }

      public static final int CATALOG_ID_FIELD_NUMBER = 3;
      private long catalogId_;
      /**
       * <pre>
       * 商品目录id（可选，非必须）
       * </pre>
       *
       * <code>optional uint64 catalog_id = 3;</code>
       */
      public boolean hasCatalogId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       * 商品目录id（可选，非必须）
       * </pre>
       *
       * <code>optional uint64 catalog_id = 3;</code>
       */
      public long getCatalogId() {
        return catalogId_;
      }

      private byte memoizedIsInitialized = -1;
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        if (!hasPriority()) {
          memoizedIsInitialized = 0;
          return false;
        }
        for (int i = 0; i < getPidListCount(); i++) {
          if (!getPidList(i).isInitialized()) {
            memoizedIsInitialized = 0;
            return false;
          }
        }
        memoizedIsInitialized = 1;
        return true;
      }

      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          output.writeEnum(1, priority_);
        }
        for (int i = 0; i < pidList_.size(); i++) {
          output.writeMessage(2, pidList_.get(i));
        }
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          output.writeUInt64(3, catalogId_);
        }
        unknownFields.writeTo(output);
      }

      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          size += com.google.protobuf.CodedOutputStream
            .computeEnumSize(1, priority_);
        }
        for (int i = 0; i < pidList_.size(); i++) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, pidList_.get(i));
        }
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt64Size(3, catalogId_);
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @Override
      public boolean equals(final Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof ProductList)) {
          return super.equals(obj);
        }
        ProductList other = (ProductList) obj;

        boolean result = true;
        result = result && (hasPriority() == other.hasPriority());
        if (hasPriority()) {
          result = result && priority_ == other.priority_;
        }
        result = result && getPidListList()
            .equals(other.getPidListList());
        result = result && (hasCatalogId() == other.hasCatalogId());
        if (hasCatalogId()) {
          result = result && (getCatalogId()
              == other.getCatalogId());
        }
        result = result && unknownFields.equals(other.unknownFields);
        return result;
      }

      @Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        if (hasPriority()) {
          hash = (37 * hash) + PRIORITY_FIELD_NUMBER;
          hash = (53 * hash) + priority_;
        }
        if (getPidListCount() > 0) {
          hash = (37 * hash) + PID_LIST_FIELD_NUMBER;
          hash = (53 * hash) + getPidListList().hashCode();
        }
        if (hasCatalogId()) {
          hash = (37 * hash) + CATALOG_ID_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getCatalogId());
        }
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static ProductList parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static ProductList parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static ProductList parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static ProductList parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static ProductList parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static ProductList parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static ProductList parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static ProductList parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static ProductList parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static ProductList parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static ProductList parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static ProductList parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(ProductList prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @Override
      protected Builder newBuilderForType(
          BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * <pre>
       * 描述一组候选商品
       * </pre>
       *
       * Protobuf type {@code DpaResult.ProductList}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:DpaResult.ProductList)
          ProductListOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return BaiduRtaResponse.internal_static_DpaResult_ProductList_descriptor;
        }

        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return BaiduRtaResponse.internal_static_DpaResult_ProductList_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  ProductList.class, Builder.class);
        }

        // Construct using BaiduRtaResponse.DpaResult.ProductList.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
            getPidListFieldBuilder();
          }
        }
        public Builder clear() {
          super.clear();
          priority_ = 0;
          bitField0_ = (bitField0_ & ~0x00000001);
          if (pidListBuilder_ == null) {
            pidList_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            pidListBuilder_.clear();
          }
          catalogId_ = 0L;
          bitField0_ = (bitField0_ & ~0x00000004);
          return this;
        }

        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return BaiduRtaResponse.internal_static_DpaResult_ProductList_descriptor;
        }

        public ProductList getDefaultInstanceForType() {
          return ProductList.getDefaultInstance();
        }

        public ProductList build() {
          ProductList result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        public ProductList buildPartial() {
          ProductList result = new ProductList(this);
          int from_bitField0_ = bitField0_;
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
            to_bitField0_ |= 0x00000001;
          }
          result.priority_ = priority_;
          if (pidListBuilder_ == null) {
            if (((bitField0_ & 0x00000002) == 0x00000002)) {
              pidList_ = java.util.Collections.unmodifiableList(pidList_);
              bitField0_ = (bitField0_ & ~0x00000002);
            }
            result.pidList_ = pidList_;
          } else {
            result.pidList_ = pidListBuilder_.build();
          }
          if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
            to_bitField0_ |= 0x00000002;
          }
          result.catalogId_ = catalogId_;
          result.bitField0_ = to_bitField0_;
          onBuilt();
          return result;
        }

        public Builder clone() {
          return (Builder) super.clone();
        }
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            Object value) {
          return (Builder) super.setField(field, value);
        }
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return (Builder) super.clearField(field);
        }
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return (Builder) super.clearOneof(oneof);
        }
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, Object value) {
          return (Builder) super.setRepeatedField(field, index, value);
        }
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            Object value) {
          return (Builder) super.addRepeatedField(field, value);
        }
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof ProductList) {
            return mergeFrom((ProductList)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(ProductList other) {
          if (other == ProductList.getDefaultInstance()) return this;
          if (other.hasPriority()) {
            setPriority(other.getPriority());
          }
          if (pidListBuilder_ == null) {
            if (!other.pidList_.isEmpty()) {
              if (pidList_.isEmpty()) {
                pidList_ = other.pidList_;
                bitField0_ = (bitField0_ & ~0x00000002);
              } else {
                ensurePidListIsMutable();
                pidList_.addAll(other.pidList_);
              }
              onChanged();
            }
          } else {
            if (!other.pidList_.isEmpty()) {
              if (pidListBuilder_.isEmpty()) {
                pidListBuilder_.dispose();
                pidListBuilder_ = null;
                pidList_ = other.pidList_;
                bitField0_ = (bitField0_ & ~0x00000002);
                pidListBuilder_ = 
                  com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                     getPidListFieldBuilder() : null;
              } else {
                pidListBuilder_.addAllMessages(other.pidList_);
              }
            }
          }
          if (other.hasCatalogId()) {
            setCatalogId(other.getCatalogId());
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        public final boolean isInitialized() {
          if (!hasPriority()) {
            return false;
          }
          for (int i = 0; i < getPidListCount(); i++) {
            if (!getPidList(i).isInitialized()) {
              return false;
            }
          }
          return true;
        }

        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          ProductList parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (ProductList) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }
        private int bitField0_;

        private int priority_ = 0;
        /**
         * <pre>
         * 候选商品队列优先级
         * </pre>
         *
         * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
         */
        public boolean hasPriority() {
          return ((bitField0_ & 0x00000001) == 0x00000001);
        }
        /**
         * <pre>
         * 候选商品队列优先级
         * </pre>
         *
         * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
         */
        public Priority getPriority() {
          Priority result = Priority.valueOf(priority_);
          return result == null ? Priority.Level_0 : result;
        }
        /**
         * <pre>
         * 候选商品队列优先级
         * </pre>
         *
         * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
         */
        public Builder setPriority(Priority value) {
          if (value == null) {
            throw new NullPointerException();
          }
          bitField0_ |= 0x00000001;
          priority_ = value.getNumber();
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 候选商品队列优先级
         * </pre>
         *
         * <code>required .DpaResult.ProductList.Priority priority = 1;</code>
         */
        public Builder clearPriority() {
          bitField0_ = (bitField0_ & ~0x00000001);
          priority_ = 0;
          onChanged();
          return this;
        }

        private java.util.List<Product> pidList_ =
          java.util.Collections.emptyList();
        private void ensurePidListIsMutable() {
          if (!((bitField0_ & 0x00000002) == 0x00000002)) {
            pidList_ = new java.util.ArrayList<Product>(pidList_);
            bitField0_ |= 0x00000002;
           }
        }

        private com.google.protobuf.RepeatedFieldBuilderV3<
            Product, Product.Builder, ProductOrBuilder> pidListBuilder_;

        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public java.util.List<Product> getPidListList() {
          if (pidListBuilder_ == null) {
            return java.util.Collections.unmodifiableList(pidList_);
          } else {
            return pidListBuilder_.getMessageList();
          }
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public int getPidListCount() {
          if (pidListBuilder_ == null) {
            return pidList_.size();
          } else {
            return pidListBuilder_.getCount();
          }
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Product getPidList(int index) {
          if (pidListBuilder_ == null) {
            return pidList_.get(index);
          } else {
            return pidListBuilder_.getMessage(index);
          }
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder setPidList(
            int index, Product value) {
          if (pidListBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensurePidListIsMutable();
            pidList_.set(index, value);
            onChanged();
          } else {
            pidListBuilder_.setMessage(index, value);
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder setPidList(
            int index, Product.Builder builderForValue) {
          if (pidListBuilder_ == null) {
            ensurePidListIsMutable();
            pidList_.set(index, builderForValue.build());
            onChanged();
          } else {
            pidListBuilder_.setMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder addPidList(Product value) {
          if (pidListBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensurePidListIsMutable();
            pidList_.add(value);
            onChanged();
          } else {
            pidListBuilder_.addMessage(value);
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder addPidList(
            int index, Product value) {
          if (pidListBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            ensurePidListIsMutable();
            pidList_.add(index, value);
            onChanged();
          } else {
            pidListBuilder_.addMessage(index, value);
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder addPidList(
            Product.Builder builderForValue) {
          if (pidListBuilder_ == null) {
            ensurePidListIsMutable();
            pidList_.add(builderForValue.build());
            onChanged();
          } else {
            pidListBuilder_.addMessage(builderForValue.build());
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder addPidList(
            int index, Product.Builder builderForValue) {
          if (pidListBuilder_ == null) {
            ensurePidListIsMutable();
            pidList_.add(index, builderForValue.build());
            onChanged();
          } else {
            pidListBuilder_.addMessage(index, builderForValue.build());
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder addAllPidList(
            Iterable<? extends Product> values) {
          if (pidListBuilder_ == null) {
            ensurePidListIsMutable();
            com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, pidList_);
            onChanged();
          } else {
            pidListBuilder_.addAllMessages(values);
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder clearPidList() {
          if (pidListBuilder_ == null) {
            pidList_ = java.util.Collections.emptyList();
            bitField0_ = (bitField0_ & ~0x00000002);
            onChanged();
          } else {
            pidListBuilder_.clear();
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Builder removePidList(int index) {
          if (pidListBuilder_ == null) {
            ensurePidListIsMutable();
            pidList_.remove(index);
            onChanged();
          } else {
            pidListBuilder_.remove(index);
          }
          return this;
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Product.Builder getPidListBuilder(
            int index) {
          return getPidListFieldBuilder().getBuilder(index);
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public ProductOrBuilder getPidListOrBuilder(
            int index) {
          if (pidListBuilder_ == null) {
            return pidList_.get(index);  } else {
            return pidListBuilder_.getMessageOrBuilder(index);
          }
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public java.util.List<? extends ProductOrBuilder>
             getPidListOrBuilderList() {
          if (pidListBuilder_ != null) {
            return pidListBuilder_.getMessageOrBuilderList();
          } else {
            return java.util.Collections.unmodifiableList(pidList_);
          }
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Product.Builder addPidListBuilder() {
          return getPidListFieldBuilder().addBuilder(
              Product.getDefaultInstance());
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public Product.Builder addPidListBuilder(
            int index) {
          return getPidListFieldBuilder().addBuilder(
              index, Product.getDefaultInstance());
        }
        /**
         * <pre>
         * 候选商品队列
         * </pre>
         *
         * <code>repeated .DpaResult.ProductList.Product pid_list = 2;</code>
         */
        public java.util.List<Product.Builder>
             getPidListBuilderList() {
          return getPidListFieldBuilder().getBuilderList();
        }
        private com.google.protobuf.RepeatedFieldBuilderV3<
            Product, Product.Builder, ProductOrBuilder>
            getPidListFieldBuilder() {
          if (pidListBuilder_ == null) {
            pidListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
                Product, Product.Builder, ProductOrBuilder>(
                    pidList_,
                    ((bitField0_ & 0x00000002) == 0x00000002),
                    getParentForChildren(),
                    isClean());
            pidList_ = null;
          }
          return pidListBuilder_;
        }

        private long catalogId_ ;
        /**
         * <pre>
         * 商品目录id（可选，非必须）
         * </pre>
         *
         * <code>optional uint64 catalog_id = 3;</code>
         */
        public boolean hasCatalogId() {
          return ((bitField0_ & 0x00000004) == 0x00000004);
        }
        /**
         * <pre>
         * 商品目录id（可选，非必须）
         * </pre>
         *
         * <code>optional uint64 catalog_id = 3;</code>
         */
        public long getCatalogId() {
          return catalogId_;
        }
        /**
         * <pre>
         * 商品目录id（可选，非必须）
         * </pre>
         *
         * <code>optional uint64 catalog_id = 3;</code>
         */
        public Builder setCatalogId(long value) {
          bitField0_ |= 0x00000004;
          catalogId_ = value;
          onChanged();
          return this;
        }
        /**
         * <pre>
         * 商品目录id（可选，非必须）
         * </pre>
         *
         * <code>optional uint64 catalog_id = 3;</code>
         */
        public Builder clearCatalogId() {
          bitField0_ = (bitField0_ & ~0x00000004);
          catalogId_ = 0L;
          onChanged();
          return this;
        }
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:DpaResult.ProductList)
      }

      // @@protoc_insertion_point(class_scope:DpaResult.ProductList)
      private static final ProductList DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new ProductList();
      }

      public static ProductList getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      @Deprecated public static final com.google.protobuf.Parser<ProductList>
          PARSER = new com.google.protobuf.AbstractParser<ProductList>() {
        public ProductList parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          return new ProductList(input, extensionRegistry);
        }
      };

      public static com.google.protobuf.Parser<ProductList> parser() {
        return PARSER;
      }

      @Override
      public com.google.protobuf.Parser<ProductList> getParserForType() {
        return PARSER;
      }

      public ProductList getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int PID_LISTS_FIELD_NUMBER = 1;
    private java.util.List<ProductList> pidLists_;
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    public java.util.List<ProductList> getPidListsList() {
      return pidLists_;
    }
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    public java.util.List<? extends ProductListOrBuilder>
        getPidListsOrBuilderList() {
      return pidLists_;
    }
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    public int getPidListsCount() {
      return pidLists_.size();
    }
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    public ProductList getPidLists(int index) {
      return pidLists_.get(index);
    }
    /**
     * <pre>
     * 多组候选商品队列参竞
     * </pre>
     *
     * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
     */
    public ProductListOrBuilder getPidListsOrBuilder(
        int index) {
      return pidLists_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      for (int i = 0; i < getPidListsCount(); i++) {
        if (!getPidLists(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < pidLists_.size(); i++) {
        output.writeMessage(1, pidLists_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < pidLists_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, pidLists_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof DpaResult)) {
        return super.equals(obj);
      }
      DpaResult other = (DpaResult) obj;

      boolean result = true;
      result = result && getPidListsList()
          .equals(other.getPidListsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPidListsCount() > 0) {
        hash = (37 * hash) + PID_LISTS_FIELD_NUMBER;
        hash = (53 * hash) + getPidListsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static DpaResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DpaResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DpaResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DpaResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DpaResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DpaResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DpaResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static DpaResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static DpaResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static DpaResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static DpaResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static DpaResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(DpaResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DpaResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DpaResult)
        DpaResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaResponse.internal_static_DpaResult_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaResponse.internal_static_DpaResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                DpaResult.class, Builder.class);
      }

      // Construct using BaiduRtaResponse.DpaResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPidListsFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        if (pidListsBuilder_ == null) {
          pidLists_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          pidListsBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaResponse.internal_static_DpaResult_descriptor;
      }

      public DpaResult getDefaultInstanceForType() {
        return DpaResult.getDefaultInstance();
      }

      public DpaResult build() {
        DpaResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public DpaResult buildPartial() {
        DpaResult result = new DpaResult(this);
        int from_bitField0_ = bitField0_;
        if (pidListsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            pidLists_ = java.util.Collections.unmodifiableList(pidLists_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.pidLists_ = pidLists_;
        } else {
          result.pidLists_ = pidListsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof DpaResult) {
          return mergeFrom((DpaResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(DpaResult other) {
        if (other == DpaResult.getDefaultInstance()) return this;
        if (pidListsBuilder_ == null) {
          if (!other.pidLists_.isEmpty()) {
            if (pidLists_.isEmpty()) {
              pidLists_ = other.pidLists_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePidListsIsMutable();
              pidLists_.addAll(other.pidLists_);
            }
            onChanged();
          }
        } else {
          if (!other.pidLists_.isEmpty()) {
            if (pidListsBuilder_.isEmpty()) {
              pidListsBuilder_.dispose();
              pidListsBuilder_ = null;
              pidLists_ = other.pidLists_;
              bitField0_ = (bitField0_ & ~0x00000001);
              pidListsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPidListsFieldBuilder() : null;
            } else {
              pidListsBuilder_.addAllMessages(other.pidLists_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getPidListsCount(); i++) {
          if (!getPidLists(i).isInitialized()) {
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        DpaResult parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (DpaResult) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<ProductList> pidLists_ =
        java.util.Collections.emptyList();
      private void ensurePidListsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          pidLists_ = new java.util.ArrayList<ProductList>(pidLists_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          ProductList, ProductList.Builder, ProductListOrBuilder> pidListsBuilder_;

      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public java.util.List<ProductList> getPidListsList() {
        if (pidListsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(pidLists_);
        } else {
          return pidListsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public int getPidListsCount() {
        if (pidListsBuilder_ == null) {
          return pidLists_.size();
        } else {
          return pidListsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public ProductList getPidLists(int index) {
        if (pidListsBuilder_ == null) {
          return pidLists_.get(index);
        } else {
          return pidListsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder setPidLists(
          int index, ProductList value) {
        if (pidListsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePidListsIsMutable();
          pidLists_.set(index, value);
          onChanged();
        } else {
          pidListsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder setPidLists(
          int index, ProductList.Builder builderForValue) {
        if (pidListsBuilder_ == null) {
          ensurePidListsIsMutable();
          pidLists_.set(index, builderForValue.build());
          onChanged();
        } else {
          pidListsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder addPidLists(ProductList value) {
        if (pidListsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePidListsIsMutable();
          pidLists_.add(value);
          onChanged();
        } else {
          pidListsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder addPidLists(
          int index, ProductList value) {
        if (pidListsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePidListsIsMutable();
          pidLists_.add(index, value);
          onChanged();
        } else {
          pidListsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder addPidLists(
          ProductList.Builder builderForValue) {
        if (pidListsBuilder_ == null) {
          ensurePidListsIsMutable();
          pidLists_.add(builderForValue.build());
          onChanged();
        } else {
          pidListsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder addPidLists(
          int index, ProductList.Builder builderForValue) {
        if (pidListsBuilder_ == null) {
          ensurePidListsIsMutable();
          pidLists_.add(index, builderForValue.build());
          onChanged();
        } else {
          pidListsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder addAllPidLists(
          Iterable<? extends ProductList> values) {
        if (pidListsBuilder_ == null) {
          ensurePidListsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, pidLists_);
          onChanged();
        } else {
          pidListsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder clearPidLists() {
        if (pidListsBuilder_ == null) {
          pidLists_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          pidListsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public Builder removePidLists(int index) {
        if (pidListsBuilder_ == null) {
          ensurePidListsIsMutable();
          pidLists_.remove(index);
          onChanged();
        } else {
          pidListsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public ProductList.Builder getPidListsBuilder(
          int index) {
        return getPidListsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public ProductListOrBuilder getPidListsOrBuilder(
          int index) {
        if (pidListsBuilder_ == null) {
          return pidLists_.get(index);  } else {
          return pidListsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public java.util.List<? extends ProductListOrBuilder>
           getPidListsOrBuilderList() {
        if (pidListsBuilder_ != null) {
          return pidListsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(pidLists_);
        }
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public ProductList.Builder addPidListsBuilder() {
        return getPidListsFieldBuilder().addBuilder(
            ProductList.getDefaultInstance());
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public ProductList.Builder addPidListsBuilder(
          int index) {
        return getPidListsFieldBuilder().addBuilder(
            index, ProductList.getDefaultInstance());
      }
      /**
       * <pre>
       * 多组候选商品队列参竞
       * </pre>
       *
       * <code>repeated .DpaResult.ProductList pid_lists = 1;</code>
       */
      public java.util.List<ProductList.Builder>
           getPidListsBuilderList() {
        return getPidListsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          ProductList, ProductList.Builder, ProductListOrBuilder>
          getPidListsFieldBuilder() {
        if (pidListsBuilder_ == null) {
          pidListsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              ProductList, ProductList.Builder, ProductListOrBuilder>(
                  pidLists_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          pidLists_ = null;
        }
        return pidListsBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DpaResult)
    }

    // @@protoc_insertion_point(class_scope:DpaResult)
    private static final DpaResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new DpaResult();
    }

    public static DpaResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<DpaResult>
        PARSER = new com.google.protobuf.AbstractParser<DpaResult>() {
      public DpaResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DpaResult(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DpaResult> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<DpaResult> getParserForType() {
      return PARSER;
    }

    public DpaResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RtaApiResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RtaApiResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_AdResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_AdResult_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RtaStrategyAdResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RtaStrategyAdResult_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_BidRise_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_BidRise_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RtaBid_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RtaBid_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DpaResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DpaResult_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DpaResult_ProductList_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DpaResult_ProductList_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DpaResult_ProductList_Product_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DpaResult_ProductList_Product_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\026BaiduRtaResponse.proto\"\312\002\n\016RtaApiRespo" +
      "nse\022\013\n\003qid\030\001 \002(\004\022\025\n\003res\030\002 \002(\0162\010.ResType\022" +
      "\022\n\nuser_score\030\003 \001(\r\022\035\n\nad_results\030\004 \003(\0132" +
      "\t.AdResult\022.\n\020strategy_results\030\005 \003(\0132\024.R" +
      "taStrategyAdResult\022\037\n\013dpa_results\030\006 \001(\0132" +
      "\n.DpaResult\022\036\n\014rta_bid_rise\030\010 \003(\0132\010.BidR" +
      "ise\022\025\n\rprefetch_date\030\t \001(\t\022!\n\014rta_bid_ty" +
      "pe\030\n \001(\0162\013.RtaBidType\022\030\n\007rta_bid\030\013 \003(\0132\007" +
      ".RtaBid\022\034\n\013rta_cpc_bid\030\014 \003(\0132\007.RtaBid\"/\n" +
      "\010AdResult\022\022\n\naccount_id\030\001 \002(\004\022\017\n\007unit_id" +
      "\030\002 \003(\004\"%\n\023RtaStrategyAdResult\022\016\n\006rta_id\030" +
      "\001 \002(\004\"+\n\007BidRise\022\016\n\006rta_id\030\001 \001(\004\022\020\n\010bid_" +
      "rise\030\002 \001(\002\"/\n\006RtaBid\022\016\n\006rta_id\030\001 \002(\004\022\025\n\r" +
      "rta_bid_value\030\002 \002(\r\"\253\002\n\tDpaResult\022)\n\tpid" +
      "_lists\030\001 \003(\0132\026.DpaResult.ProductList\032\362\001\n" +
      "\013ProductList\0221\n\010priority\030\001 \002(\0162\037.DpaResu" +
      "lt.ProductList.Priority\0220\n\010pid_list\030\002 \003(" +
      "\0132\036.DpaResult.ProductList.Product\022\022\n\ncat" +
      "alog_id\030\003 \001(\004\0327\n\007Product\022\n\n\002id\030\001 \002(\t\022\021\n\t" +
      "bid_ratio\030\002 \001(\001\022\r\n\005score\030\003 \001(\001\"1\n\010Priori" +
      "ty\022\013\n\007Level_0\020\000\022\013\n\007Level_1\020\001\022\013\n\007Level_2\020" +
      "\002*7\n\nRtaBidType\022\016\n\nRTA_NORMAL\020\000\022\013\n\007RTA_B" +
      "ID\020\001\022\014\n\010RTA_RISE\020\002*&\n\007ResType\022\007\n\003ALL\020\000\022\010" +
      "\n\004NONE\020\001\022\010\n\004PART\020\002"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_RtaApiResponse_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_RtaApiResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RtaApiResponse_descriptor,
        new String[] { "Qid", "Res", "UserScore", "AdResults", "StrategyResults", "DpaResults", "RtaBidRise", "PrefetchDate", "RtaBidType", "RtaBid", "RtaCpcBid", });
    internal_static_AdResult_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_AdResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_AdResult_descriptor,
        new String[] { "AccountId", "UnitId", });
    internal_static_RtaStrategyAdResult_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_RtaStrategyAdResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RtaStrategyAdResult_descriptor,
        new String[] { "RtaId", });
    internal_static_BidRise_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_BidRise_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_BidRise_descriptor,
        new String[] { "RtaId", "BidRise", });
    internal_static_RtaBid_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_RtaBid_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RtaBid_descriptor,
        new String[] { "RtaId", "RtaBidValue", });
    internal_static_DpaResult_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_DpaResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DpaResult_descriptor,
        new String[] { "PidLists", });
    internal_static_DpaResult_ProductList_descriptor =
      internal_static_DpaResult_descriptor.getNestedTypes().get(0);
    internal_static_DpaResult_ProductList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DpaResult_ProductList_descriptor,
        new String[] { "Priority", "PidList", "CatalogId", });
    internal_static_DpaResult_ProductList_Product_descriptor =
      internal_static_DpaResult_ProductList_descriptor.getNestedTypes().get(0);
    internal_static_DpaResult_ProductList_Product_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DpaResult_ProductList_Product_descriptor,
        new String[] { "Id", "BidRatio", "Score", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
