// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf enum {@code kuaishou.ad.rta.OsType}
 */
public enum OsType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 未知类型
   * </pre>
   *
   * <code>UNKNOWN_OS = 0;</code>
   */
  UNKNOWN_OS(0),
  /**
   * <pre>
   * Android
   * </pre>
   *
   * <code>ANDROID = 1;</code>
   */
  ANDROID(1),
  /**
   * <pre>
   * iOS
   * </pre>
   *
   * <code>IOS = 2;</code>
   */
  IOS(2),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * 未知类型
   * </pre>
   *
   * <code>UNKNOWN_OS = 0;</code>
   */
  public static final int UNKNOWN_OS_VALUE = 0;
  /**
   * <pre>
   * Android
   * </pre>
   *
   * <code>ANDROID = 1;</code>
   */
  public static final int ANDROID_VALUE = 1;
  /**
   * <pre>
   * iOS
   * </pre>
   *
   * <code>IOS = 2;</code>
   */
  public static final int IOS_VALUE = 2;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @Deprecated
  public static OsType valueOf(int value) {
    return forNumber(value);
  }

  public static OsType forNumber(int value) {
    switch (value) {
      case 0: return UNKNOWN_OS;
      case 1: return ANDROID;
      case 2: return IOS;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<OsType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      OsType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<OsType>() {
          public OsType findValueByNumber(int number) {
            return OsType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return RTAProto.getDescriptor().getEnumTypes().get(0);
  }

  private static final OsType[] VALUES = values();

  public static OsType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private OsType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:kuaishou.ad.rta.OsType)
}

