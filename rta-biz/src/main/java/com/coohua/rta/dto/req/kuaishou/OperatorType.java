// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

/**
 * Protobuf enum {@code kuaishou.ad.rta.OperatorType}
 */
public enum OperatorType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 未知的运营商
   * </pre>
   *
   * <code>UNKNOWN_OPERATOR_TYPE = 0;</code>
   */
  UNKNOWN_OPERATOR_TYPE(0),
  /**
   * <pre>
   * 中国移动
   * </pre>
   *
   * <code>CHINA_MOBILE = 1;</code>
   */
  CHINA_MOBILE(1),
  /**
   * <pre>
   * 中国电信
   * </pre>
   *
   * <code>CHINA_TELECOM = 2;</code>
   */
  CHINA_TELECOM(2),
  /**
   * <pre>
   * 中国联通
   * </pre>
   *
   * <code>CHINA_UNICOM = 3;</code>
   */
  CHINA_UNICOM(3),
  /**
   * <pre>
   * 其他运营商
   * </pre>
   *
   * <code>OTHER_OPERATOR = 99;</code>
   */
  OTHER_OPERATOR(99),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   * 未知的运营商
   * </pre>
   *
   * <code>UNKNOWN_OPERATOR_TYPE = 0;</code>
   */
  public static final int UNKNOWN_OPERATOR_TYPE_VALUE = 0;
  /**
   * <pre>
   * 中国移动
   * </pre>
   *
   * <code>CHINA_MOBILE = 1;</code>
   */
  public static final int CHINA_MOBILE_VALUE = 1;
  /**
   * <pre>
   * 中国电信
   * </pre>
   *
   * <code>CHINA_TELECOM = 2;</code>
   */
  public static final int CHINA_TELECOM_VALUE = 2;
  /**
   * <pre>
   * 中国联通
   * </pre>
   *
   * <code>CHINA_UNICOM = 3;</code>
   */
  public static final int CHINA_UNICOM_VALUE = 3;
  /**
   * <pre>
   * 其他运营商
   * </pre>
   *
   * <code>OTHER_OPERATOR = 99;</code>
   */
  public static final int OTHER_OPERATOR_VALUE = 99;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @Deprecated
  public static OperatorType valueOf(int value) {
    return forNumber(value);
  }

  public static OperatorType forNumber(int value) {
    switch (value) {
      case 0: return UNKNOWN_OPERATOR_TYPE;
      case 1: return CHINA_MOBILE;
      case 2: return CHINA_TELECOM;
      case 3: return CHINA_UNICOM;
      case 99: return OTHER_OPERATOR;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<OperatorType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      OperatorType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<OperatorType>() {
          public OperatorType findValueByNumber(int number) {
            return OperatorType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return RTAProto.getDescriptor().getEnumTypes().get(2);
  }

  private static final OperatorType[] VALUES = values();

  public static OperatorType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private OperatorType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:kuaishou.ad.rta.OperatorType)
}

