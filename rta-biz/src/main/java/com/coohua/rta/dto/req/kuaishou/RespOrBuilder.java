// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: kuaishouRta.proto

package com.coohua.rta.dto.req.kuaishou;

public interface RespOrBuilder extends
    // @@protoc_insertion_point(interface_extends:kuaishou.ad.rta.Resp)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  String getId();
  /**
   * <pre>
   * 请求唯一id
   * </pre>
   *
   * <code>string id = 1;</code>
   */
  com.google.protobuf.ByteString
      getIdBytes();

  /**
   * <pre>
   * 结果类型, 等于PART时, result必填
   * </pre>
   *
   * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
   */
  int getResultTypeValue();
  /**
   * <pre>
   * 结果类型, 等于PART时, result必填
   * </pre>
   *
   * <code>.kuaishou.ad.rta.Resp.ResultType result_type = 2;</code>
   */
  Resp.ResultType getResultType();

  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  java.util.List<RtaResult>
      getResultList();
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  RtaResult getResult(int index);
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  int getResultCount();
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  java.util.List<? extends RtaResultOrBuilder>
      getResultOrBuilderList();
  /**
   * <pre>
   * result_type=PART时允许投放的广告列表, 如有重复取并集
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.RtaResult result = 3;</code>
   */
  RtaResultOrBuilder getResultOrBuilder(
      int index);

  /**
   * <pre>
   * result_type=ALL时的动态出价系数
   * </pre>
   *
   * <code>double bid_ratio = 4;</code>
   */
  double getBidRatio();

  /**
   * <pre>
   *拒绝时间
   * </pre>
   *
   * <code>uint32 refuse_time = 6;</code>
   */
  int getRefuseTime();

  /**
   * <pre>
   *缓存时间
   * </pre>
   *
   * <code>uint32 cache_time = 7;</code>
   */
  int getCacheTime();

  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  java.util.List<DpaResult>
      getDpaResultList();
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  DpaResult getDpaResult(int index);
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  int getDpaResultCount();
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  java.util.List<? extends DpaResultOrBuilder>
      getDpaResultOrBuilderList();
  /**
   * <pre>
   *推荐商品信息，非必填，如果有不同的score，请设置多组 dpa_result
   * </pre>
   *
   * <code>repeated .kuaishou.ad.rta.DpaResult dpa_result = 8;</code>
   */
  DpaResultOrBuilder getDpaResultOrBuilder(
      int index);

  /**
   * <pre>
   *result_type=ALL时的动态出价,单位元
   * </pre>
   *
   * <code>double bid = 9;</code>
   */
  double getBid();

  /**
   * <pre>
   * 客户分桶id
   * </pre>
   *
   * <code>uint32 bucket_id = 10;</code>
   */
  int getBucketId();
}
