// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rta_api.proto

package rta_api

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	wrappers "github.com/golang/protobuf/ptypes/wrappers"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PlatformType int32

const (
	PlatformType_PLATFORM_UNKNOWN PlatformType = 0
	PlatformType_ANDROID          PlatformType = 1
	PlatformType_IOS              PlatformType = 2
)

var PlatformType_name = map[int32]string{
	0: "PLATFORM_UNKNOWN",
	1: "ANDROID",
	2: "IOS",
}

var PlatformType_value = map[string]int32{
	"PLATFORM_UNKNOWN": 0,
	"ANDROID":          1,
	"IOS":              2,
}

func (x PlatformType) String() string {
	return proto.EnumName(PlatformType_name, int32(x))
}

func (PlatformType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{0}
}

type DeviceType int32

const (
	DeviceType_DEVICE_UNKNOWN DeviceType = 0
	DeviceType_PHONE          DeviceType = 1
	DeviceType_TABLET         DeviceType = 2
)

var DeviceType_name = map[int32]string{
	0: "DEVICE_UNKNOWN",
	1: "PHONE",
	2: "TABLET",
}

var DeviceType_value = map[string]int32{
	"DEVICE_UNKNOWN": 0,
	"PHONE":          1,
	"TABLET":         2,
}

func (x DeviceType) String() string {
	return proto.EnumName(DeviceType_name, int32(x))
}

func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{1}
}

type DeviceOsType int32

const (
	DeviceOsType_DEVICE_OS_UNKNOWN DeviceOsType = 0
	DeviceOsType_DEVICE_ANDROID    DeviceOsType = 1
	DeviceOsType_DEVICE_IOS        DeviceOsType = 2
	DeviceOsType_DEVICE_WINDOWS    DeviceOsType = 3
)

var DeviceOsType_name = map[int32]string{
	0: "DEVICE_OS_UNKNOWN",
	1: "DEVICE_ANDROID",
	2: "DEVICE_IOS",
	3: "DEVICE_WINDOWS",
}

var DeviceOsType_value = map[string]int32{
	"DEVICE_OS_UNKNOWN": 0,
	"DEVICE_ANDROID":    1,
	"DEVICE_IOS":        2,
	"DEVICE_WINDOWS":    3,
}

func (x DeviceOsType) String() string {
	return proto.EnumName(DeviceOsType_name, int32(x))
}

func (DeviceOsType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{2}
}

type GenderType int32

const (
	GenderType_GENDER_UNKNOWN GenderType = 0
	GenderType_MALE           GenderType = 1
	GenderType_FEMALE         GenderType = 2
)

var GenderType_name = map[int32]string{
	0: "GENDER_UNKNOWN",
	1: "MALE",
	2: "FEMALE",
}

var GenderType_value = map[string]int32{
	"GENDER_UNKNOWN": 0,
	"MALE":           1,
	"FEMALE":         2,
}

func (x GenderType) String() string {
	return proto.EnumName(GenderType_name, int32(x))
}

func (GenderType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{3}
}

type DidType int32

const (
	DidType_IMEI           DidType = 0
	DidType_IMEI_MD5       DidType = 1
	DidType_ANDROID_ID     DidType = 2
	DidType_ANDROID_ID_MD5 DidType = 3
	DidType_IDFA           DidType = 4
	DidType_IDFA_MD5       DidType = 5
	DidType_OAID           DidType = 6
	DidType_OAID_MD5       DidType = 7
	DidType_GAID           DidType = 8
	DidType_GAID_MD5       DidType = 9
)

var DidType_name = map[int32]string{
	0: "IMEI",
	1: "IMEI_MD5",
	2: "ANDROID_ID",
	3: "ANDROID_ID_MD5",
	4: "IDFA",
	5: "IDFA_MD5",
	6: "OAID",
	7: "OAID_MD5",
	8: "GAID",
	9: "GAID_MD5",
}

var DidType_value = map[string]int32{
	"IMEI":           0,
	"IMEI_MD5":       1,
	"ANDROID_ID":     2,
	"ANDROID_ID_MD5": 3,
	"IDFA":           4,
	"IDFA_MD5":       5,
	"OAID":           6,
	"OAID_MD5":       7,
	"GAID":           8,
	"GAID_MD5":       9,
}

func (x DidType) String() string {
	return proto.EnumName(DidType_name, int32(x))
}

func (DidType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{4}
}

type EnableStrategy int32

const (
	EnableStrategy_DISABLE        EnableStrategy = 0
	EnableStrategy_ENABLE_DEFAULT EnableStrategy = 1
	EnableStrategy_ENABLE_Type_2  EnableStrategy = 2
	EnableStrategy_ENABLE_Type_3  EnableStrategy = 3
	EnableStrategy_ENABLE_Type_4  EnableStrategy = 4
	EnableStrategy_ENABLE_Type_5  EnableStrategy = 5
	EnableStrategy_ENABLE_Type_6  EnableStrategy = 6
	EnableStrategy_ENABLE_Type_7  EnableStrategy = 7
	EnableStrategy_ENABLE_Type_8  EnableStrategy = 8
	EnableStrategy_ENABLE_Type_9  EnableStrategy = 9
	EnableStrategy_ENABLE_Type_10 EnableStrategy = 10
)

var EnableStrategy_name = map[int32]string{
	0:  "DISABLE",
	1:  "ENABLE_DEFAULT",
	2:  "ENABLE_Type_2",
	3:  "ENABLE_Type_3",
	4:  "ENABLE_Type_4",
	5:  "ENABLE_Type_5",
	6:  "ENABLE_Type_6",
	7:  "ENABLE_Type_7",
	8:  "ENABLE_Type_8",
	9:  "ENABLE_Type_9",
	10: "ENABLE_Type_10",
}

var EnableStrategy_value = map[string]int32{
	"DISABLE":        0,
	"ENABLE_DEFAULT": 1,
	"ENABLE_Type_2":  2,
	"ENABLE_Type_3":  3,
	"ENABLE_Type_4":  4,
	"ENABLE_Type_5":  5,
	"ENABLE_Type_6":  6,
	"ENABLE_Type_7":  7,
	"ENABLE_Type_8":  8,
	"ENABLE_Type_9":  9,
	"ENABLE_Type_10": 10,
}

func (x EnableStrategy) String() string {
	return proto.EnumName(EnableStrategy_name, int32(x))
}

func (EnableStrategy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{5}
}

type ImageMode int32

const (
	ImageMode_IMAGE_DEFAULT ImageMode = 0
	ImageMode_IMAGE_VIDEO   ImageMode = 1
	ImageMode_IMAGE_OTHER   ImageMode = 2
)

var ImageMode_name = map[int32]string{
	0: "IMAGE_DEFAULT",
	1: "IMAGE_VIDEO",
	2: "IMAGE_OTHER",
}

var ImageMode_value = map[string]int32{
	"IMAGE_DEFAULT": 0,
	"IMAGE_VIDEO":   1,
	"IMAGE_OTHER":   2,
}

func (x ImageMode) String() string {
	return proto.EnumName(ImageMode_name, int32(x))
}

func (ImageMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{6}
}

type CusVid int32

const (
	CusVid_CUS_VID_UNKNOWN CusVid = 0
	CusVid_CUS_VID_1       CusVid = 1
	CusVid_CUS_VID_2       CusVid = 2
	CusVid_CUS_VID_3       CusVid = 3
	CusVid_CUS_VID_4       CusVid = 4
	CusVid_CUS_VID_5       CusVid = 5
	CusVid_CUS_VID_6       CusVid = 6
	CusVid_CUS_VID_7       CusVid = 7
	CusVid_CUS_VID_8       CusVid = 8
	CusVid_CUS_VID_9       CusVid = 9
	CusVid_CUS_VID_10      CusVid = 10
)

var CusVid_name = map[int32]string{
	0:  "CUS_VID_UNKNOWN",
	1:  "CUS_VID_1",
	2:  "CUS_VID_2",
	3:  "CUS_VID_3",
	4:  "CUS_VID_4",
	5:  "CUS_VID_5",
	6:  "CUS_VID_6",
	7:  "CUS_VID_7",
	8:  "CUS_VID_8",
	9:  "CUS_VID_9",
	10: "CUS_VID_10",
}

var CusVid_value = map[string]int32{
	"CUS_VID_UNKNOWN": 0,
	"CUS_VID_1":       1,
	"CUS_VID_2":       2,
	"CUS_VID_3":       3,
	"CUS_VID_4":       4,
	"CUS_VID_5":       5,
	"CUS_VID_6":       6,
	"CUS_VID_7":       7,
	"CUS_VID_8":       8,
	"CUS_VID_9":       9,
	"CUS_VID_10":      10,
}

func (x CusVid) String() string {
	return proto.EnumName(CusVid_name, int32(x))
}

func (CusVid) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{7}
}

type UserInfo_UserType int32

const (
	UserInfo_EMPTY      UserInfo_UserType = 0
	UserInfo_PRICE_COEF UserInfo_UserType = 1
	UserInfo_DEFAULT    UserInfo_UserType = 2
	UserInfo_PRICE_BID  UserInfo_UserType = 3
	UserInfo_RTA_BID    UserInfo_UserType = 7
	UserInfo_BOOST_COEF UserInfo_UserType = 8
	UserInfo_DEEP_MIN   UserInfo_UserType = 9
)

var UserInfo_UserType_name = map[int32]string{
	0: "EMPTY",
	1: "PRICE_COEF",
	2: "DEFAULT",
	3: "PRICE_BID",
	7: "RTA_BID",
	8: "BOOST_COEF",
	9: "DEEP_MIN",
}

var UserInfo_UserType_value = map[string]int32{
	"EMPTY":      0,
	"PRICE_COEF": 1,
	"DEFAULT":    2,
	"PRICE_BID":  3,
	"RTA_BID":    7,
	"BOOST_COEF": 8,
	"DEEP_MIN":   9,
}

func (x UserInfo_UserType) String() string {
	return proto.EnumName(UserInfo_UserType_name, int32(x))
}

func (UserInfo_UserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 0}
}

type UserInfo_PidType int32

const (
	UserInfo_PidType_0 UserInfo_PidType = 0
	UserInfo_PidType_1 UserInfo_PidType = 1
	UserInfo_PidType_2 UserInfo_PidType = 2
	UserInfo_PidType_3 UserInfo_PidType = 3
	UserInfo_PidType_4 UserInfo_PidType = 4
)

var UserInfo_PidType_name = map[int32]string{
	0: "PidType_0",
	1: "PidType_1",
	2: "PidType_2",
	3: "PidType_3",
	4: "PidType_4",
}

var UserInfo_PidType_value = map[string]int32{
	"PidType_0": 0,
	"PidType_1": 1,
	"PidType_2": 2,
	"PidType_3": 3,
	"PidType_4": 4,
}

func (x UserInfo_PidType) String() string {
	return proto.EnumName(UserInfo_PidType_name, int32(x))
}

func (UserInfo_PidType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 1}
}

// 个性化出价支持分pricing type
// 保障数据合规 这里和我们的pricing 非对应关系
// rerank阶段单独做一次映射
type UserInfo_PricingType int32

const (
	UserInfo_NOLIMIT UserInfo_PricingType = 0
	UserInfo_OCPM    UserInfo_PricingType = 1
	UserInfo_OCPC    UserInfo_PricingType = 2
	UserInfo_CPC     UserInfo_PricingType = 3
)

var UserInfo_PricingType_name = map[int32]string{
	0: "NOLIMIT",
	1: "OCPM",
	2: "OCPC",
	3: "CPC",
}

var UserInfo_PricingType_value = map[string]int32{
	"NOLIMIT": 0,
	"OCPM":    1,
	"OCPC":    2,
	"CPC":     3,
}

func (x UserInfo_PricingType) String() string {
	return proto.EnumName(UserInfo_PricingType_name, int32(x))
}

func (UserInfo_PricingType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 2}
}

type UserInfo_ActionType int32

const (
	UserInfo_ACTION_DEFAULT     UserInfo_ActionType = 0
	UserInfo_IN_APP_ORDER       UserInfo_ActionType = 20
	UserInfo_CUSTOMER_EFFECTIVE UserInfo_ActionType = 26
	UserInfo_LOAN_CREDIT        UserInfo_ActionType = 105
)

var UserInfo_ActionType_name = map[int32]string{
	0:   "ACTION_DEFAULT",
	20:  "IN_APP_ORDER",
	26:  "CUSTOMER_EFFECTIVE",
	105: "LOAN_CREDIT",
}

var UserInfo_ActionType_value = map[string]int32{
	"ACTION_DEFAULT":     0,
	"IN_APP_ORDER":       20,
	"CUSTOMER_EFFECTIVE": 26,
	"LOAN_CREDIT":        105,
}

func (x UserInfo_ActionType) String() string {
	return proto.EnumName(UserInfo_ActionType_name, int32(x))
}

func (UserInfo_ActionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 3}
}

type Paid struct {
	Value                string   `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
	Version              string   `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Paid) Reset()         { *m = Paid{} }
func (m *Paid) String() string { return proto.CompactTextString(m) }
func (*Paid) ProtoMessage()    {}
func (*Paid) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{0}
}

func (m *Paid) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Paid.Unmarshal(m, b)
}
func (m *Paid) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Paid.Marshal(b, m, deterministic)
}
func (m *Paid) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Paid.Merge(m, src)
}
func (m *Paid) XXX_Size() int {
	return xxx_messageInfo_Paid.Size(m)
}
func (m *Paid) XXX_DiscardUnknown() {
	xxx_messageInfo_Paid.DiscardUnknown(m)
}

var xxx_messageInfo_Paid proto.InternalMessageInfo

func (m *Paid) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *Paid) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

type Device struct {
	ImeiMd5              string       `protobuf:"bytes,1,opt,name=imei_md5,json=imeiMd5,proto3" json:"imei_md5,omitempty"`
	AndroidIdMd5         string       `protobuf:"bytes,2,opt,name=android_id_md5,json=androidIdMd5,proto3" json:"android_id_md5,omitempty"`
	Oaid                 string       `protobuf:"bytes,3,opt,name=oaid,proto3" json:"oaid,omitempty"`
	Idfa                 string       `protobuf:"bytes,4,opt,name=idfa,proto3" json:"idfa,omitempty"`
	ImeiSecureId         string       `protobuf:"bytes,5,opt,name=imei_secure_id,json=imeiSecureId,proto3" json:"imei_secure_id,omitempty"`
	Caid1                string       `protobuf:"bytes,6,opt,name=caid1,proto3" json:"caid1,omitempty"`
	Caid2                string       `protobuf:"bytes,7,opt,name=caid2,proto3" json:"caid2,omitempty"`
	GlobalDid            string       `protobuf:"bytes,8,opt,name=global_did,json=globalDid,proto3" json:"global_did,omitempty"`
	Gaid                 string       `protobuf:"bytes,9,opt,name=gaid,proto3" json:"gaid,omitempty"`
	ImeiMd5SecureId      string       `protobuf:"bytes,10,opt,name=imei_md5_secure_id,json=imeiMd5SecureId,proto3" json:"imei_md5_secure_id,omitempty"`
	IdfaSecureId         string       `protobuf:"bytes,11,opt,name=idfa_secure_id,json=idfaSecureId,proto3" json:"idfa_secure_id,omitempty"`
	IdfaMd5SecureId      string       `protobuf:"bytes,12,opt,name=idfa_md5_secure_id,json=idfaMd5SecureId,proto3" json:"idfa_md5_secure_id,omitempty"`
	OaidSecureId         string       `protobuf:"bytes,13,opt,name=oaid_secure_id,json=oaidSecureId,proto3" json:"oaid_secure_id,omitempty"`
	OaidMd5SecureId      string       `protobuf:"bytes,14,opt,name=oaid_md5_secure_id,json=oaidMd5SecureId,proto3" json:"oaid_md5_secure_id,omitempty"`
	SkVersion            string       `protobuf:"bytes,15,opt,name=sk_version,json=skVersion,proto3" json:"sk_version,omitempty"`
	Ip                   string       `protobuf:"bytes,17,opt,name=ip,proto3" json:"ip,omitempty"`
	Ua                   string       `protobuf:"bytes,18,opt,name=ua,proto3" json:"ua,omitempty"`
	UnionImei            string       `protobuf:"bytes,19,opt,name=union_imei,json=unionImei,proto3" json:"union_imei,omitempty"`
	Paid                 string       `protobuf:"bytes,20,opt,name=paid,proto3" json:"paid,omitempty"`
	Idfv                 string       `protobuf:"bytes,21,opt,name=idfv,proto3" json:"idfv,omitempty"`
	MbTimeMd5            string       `protobuf:"bytes,22,opt,name=mb_time_md5,json=mbTimeMd5,proto3" json:"mb_time_md5,omitempty"`
	BootTimeMd5          string       `protobuf:"bytes,23,opt,name=boot_time_md5,json=bootTimeMd5,proto3" json:"boot_time_md5,omitempty"`
	Caid1SecureId        string       `protobuf:"bytes,24,opt,name=caid1_secure_id,json=caid1SecureId,proto3" json:"caid1_secure_id,omitempty"`
	Caid2SecureId        string       `protobuf:"bytes,25,opt,name=caid2_secure_id,json=caid2SecureId,proto3" json:"caid2_secure_id,omitempty"`
	AppId                int64        `protobuf:"varint,26,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	DeviceOsType         DeviceOsType `protobuf:"varint,27,opt,name=device_os_type,json=deviceOsType,proto3,enum=DeviceOsType" json:"device_os_type,omitempty"`
	OsVersion            string       `protobuf:"bytes,28,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`
	InterfaceType        DeviceOsType `protobuf:"varint,29,opt,name=interface_type,json=interfaceType,proto3,enum=DeviceOsType" json:"interface_type,omitempty"`
	ContentGroup         string       `protobuf:"bytes,30,opt,name=content_group,json=contentGroup,proto3" json:"content_group,omitempty"`
	Caid1Version         string       `protobuf:"bytes,31,opt,name=caid1_version,json=caid1Version,proto3" json:"caid1_version,omitempty"`
	Caid2Version         string       `protobuf:"bytes,32,opt,name=caid2_version,json=caid2Version,proto3" json:"caid2_version,omitempty"`
	Paids                []*Paid      `protobuf:"bytes,33,rep,name=paids,proto3" json:"paids,omitempty"`
	Caid                 string       `protobuf:"bytes,34,opt,name=caid,proto3" json:"caid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *Device) Reset()         { *m = Device{} }
func (m *Device) String() string { return proto.CompactTextString(m) }
func (*Device) ProtoMessage()    {}
func (*Device) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{1}
}

func (m *Device) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Device.Unmarshal(m, b)
}
func (m *Device) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Device.Marshal(b, m, deterministic)
}
func (m *Device) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Device.Merge(m, src)
}
func (m *Device) XXX_Size() int {
	return xxx_messageInfo_Device.Size(m)
}
func (m *Device) XXX_DiscardUnknown() {
	xxx_messageInfo_Device.DiscardUnknown(m)
}

var xxx_messageInfo_Device proto.InternalMessageInfo

func (m *Device) GetImeiMd5() string {
	if m != nil {
		return m.ImeiMd5
	}
	return ""
}

func (m *Device) GetAndroidIdMd5() string {
	if m != nil {
		return m.AndroidIdMd5
	}
	return ""
}

func (m *Device) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *Device) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *Device) GetImeiSecureId() string {
	if m != nil {
		return m.ImeiSecureId
	}
	return ""
}

func (m *Device) GetCaid1() string {
	if m != nil {
		return m.Caid1
	}
	return ""
}

func (m *Device) GetCaid2() string {
	if m != nil {
		return m.Caid2
	}
	return ""
}

func (m *Device) GetGlobalDid() string {
	if m != nil {
		return m.GlobalDid
	}
	return ""
}

func (m *Device) GetGaid() string {
	if m != nil {
		return m.Gaid
	}
	return ""
}

func (m *Device) GetImeiMd5SecureId() string {
	if m != nil {
		return m.ImeiMd5SecureId
	}
	return ""
}

func (m *Device) GetIdfaSecureId() string {
	if m != nil {
		return m.IdfaSecureId
	}
	return ""
}

func (m *Device) GetIdfaMd5SecureId() string {
	if m != nil {
		return m.IdfaMd5SecureId
	}
	return ""
}

func (m *Device) GetOaidSecureId() string {
	if m != nil {
		return m.OaidSecureId
	}
	return ""
}

func (m *Device) GetOaidMd5SecureId() string {
	if m != nil {
		return m.OaidMd5SecureId
	}
	return ""
}

func (m *Device) GetSkVersion() string {
	if m != nil {
		return m.SkVersion
	}
	return ""
}

func (m *Device) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *Device) GetUa() string {
	if m != nil {
		return m.Ua
	}
	return ""
}

func (m *Device) GetUnionImei() string {
	if m != nil {
		return m.UnionImei
	}
	return ""
}

func (m *Device) GetPaid() string {
	if m != nil {
		return m.Paid
	}
	return ""
}

func (m *Device) GetIdfv() string {
	if m != nil {
		return m.Idfv
	}
	return ""
}

func (m *Device) GetMbTimeMd5() string {
	if m != nil {
		return m.MbTimeMd5
	}
	return ""
}

func (m *Device) GetBootTimeMd5() string {
	if m != nil {
		return m.BootTimeMd5
	}
	return ""
}

func (m *Device) GetCaid1SecureId() string {
	if m != nil {
		return m.Caid1SecureId
	}
	return ""
}

func (m *Device) GetCaid2SecureId() string {
	if m != nil {
		return m.Caid2SecureId
	}
	return ""
}

func (m *Device) GetAppId() int64 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *Device) GetDeviceOsType() DeviceOsType {
	if m != nil {
		return m.DeviceOsType
	}
	return DeviceOsType_DEVICE_OS_UNKNOWN
}

func (m *Device) GetOsVersion() string {
	if m != nil {
		return m.OsVersion
	}
	return ""
}

func (m *Device) GetInterfaceType() DeviceOsType {
	if m != nil {
		return m.InterfaceType
	}
	return DeviceOsType_DEVICE_OS_UNKNOWN
}

func (m *Device) GetContentGroup() string {
	if m != nil {
		return m.ContentGroup
	}
	return ""
}

func (m *Device) GetCaid1Version() string {
	if m != nil {
		return m.Caid1Version
	}
	return ""
}

func (m *Device) GetCaid2Version() string {
	if m != nil {
		return m.Caid2Version
	}
	return ""
}

func (m *Device) GetPaids() []*Paid {
	if m != nil {
		return m.Paids
	}
	return nil
}

func (m *Device) GetCaid() string {
	if m != nil {
		return m.Caid
	}
	return ""
}

type Industry struct {
	FirstIndustry        int64    `protobuf:"varint,1,opt,name=first_industry,json=firstIndustry,proto3" json:"first_industry,omitempty"`
	SecondIndustry       int64    `protobuf:"varint,2,opt,name=second_industry,json=secondIndustry,proto3" json:"second_industry,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Industry) Reset()         { *m = Industry{} }
func (m *Industry) String() string { return proto.CompactTextString(m) }
func (*Industry) ProtoMessage()    {}
func (*Industry) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{2}
}

func (m *Industry) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Industry.Unmarshal(m, b)
}
func (m *Industry) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Industry.Marshal(b, m, deterministic)
}
func (m *Industry) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Industry.Merge(m, src)
}
func (m *Industry) XXX_Size() int {
	return xxx_messageInfo_Industry.Size(m)
}
func (m *Industry) XXX_DiscardUnknown() {
	xxx_messageInfo_Industry.DiscardUnknown(m)
}

var xxx_messageInfo_Industry proto.InternalMessageInfo

func (m *Industry) GetFirstIndustry() int64 {
	if m != nil {
		return m.FirstIndustry
	}
	return 0
}

func (m *Industry) GetSecondIndustry() int64 {
	if m != nil {
		return m.SecondIndustry
	}
	return 0
}

type Geo struct {
	City                 int64    `protobuf:"varint,1,opt,name=city,proto3" json:"city,omitempty"`
	Lat                  float64  `protobuf:"fixed64,2,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon                  float64  `protobuf:"fixed64,3,opt,name=lon,proto3" json:"lon,omitempty"`
	Country              string   `protobuf:"bytes,4,opt,name=Country,proto3" json:"Country,omitempty"`
	District             int64    `protobuf:"varint,5,opt,name=district,proto3" json:"district,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Geo) Reset()         { *m = Geo{} }
func (m *Geo) String() string { return proto.CompactTextString(m) }
func (*Geo) ProtoMessage()    {}
func (*Geo) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{3}
}

func (m *Geo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Geo.Unmarshal(m, b)
}
func (m *Geo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Geo.Marshal(b, m, deterministic)
}
func (m *Geo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Geo.Merge(m, src)
}
func (m *Geo) XXX_Size() int {
	return xxx_messageInfo_Geo.Size(m)
}
func (m *Geo) XXX_DiscardUnknown() {
	xxx_messageInfo_Geo.DiscardUnknown(m)
}

var xxx_messageInfo_Geo proto.InternalMessageInfo

func (m *Geo) GetCity() int64 {
	if m != nil {
		return m.City
	}
	return 0
}

func (m *Geo) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *Geo) GetLon() float64 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *Geo) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *Geo) GetDistrict() int64 {
	if m != nil {
		return m.District
	}
	return 0
}

type ModelMeta struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Score                float64  `protobuf:"fixed64,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModelMeta) Reset()         { *m = ModelMeta{} }
func (m *ModelMeta) String() string { return proto.CompactTextString(m) }
func (*ModelMeta) ProtoMessage()    {}
func (*ModelMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{4}
}

func (m *ModelMeta) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModelMeta.Unmarshal(m, b)
}
func (m *ModelMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModelMeta.Marshal(b, m, deterministic)
}
func (m *ModelMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModelMeta.Merge(m, src)
}
func (m *ModelMeta) XXX_Size() int {
	return xxx_messageInfo_ModelMeta.Size(m)
}
func (m *ModelMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_ModelMeta.DiscardUnknown(m)
}

var xxx_messageInfo_ModelMeta proto.InternalMessageInfo

func (m *ModelMeta) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ModelMeta) GetScore() float64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type PrivateModel struct {
	ModelMeta            []*ModelMeta `protobuf:"bytes,1,rep,name=model_meta,json=modelMeta,proto3" json:"model_meta,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PrivateModel) Reset()         { *m = PrivateModel{} }
func (m *PrivateModel) String() string { return proto.CompactTextString(m) }
func (*PrivateModel) ProtoMessage()    {}
func (*PrivateModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{5}
}

func (m *PrivateModel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrivateModel.Unmarshal(m, b)
}
func (m *PrivateModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrivateModel.Marshal(b, m, deterministic)
}
func (m *PrivateModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrivateModel.Merge(m, src)
}
func (m *PrivateModel) XXX_Size() int {
	return xxx_messageInfo_PrivateModel.Size(m)
}
func (m *PrivateModel) XXX_DiscardUnknown() {
	xxx_messageInfo_PrivateModel.DiscardUnknown(m)
}

var xxx_messageInfo_PrivateModel proto.InternalMessageInfo

func (m *PrivateModel) GetModelMeta() []*ModelMeta {
	if m != nil {
		return m.ModelMeta
	}
	return nil
}

type AdvPrivateModel struct {
	AdvPrivateModel      map[int64]*PrivateModel `protobuf:"bytes,1,rep,name=adv_private_model,json=advPrivateModel,proto3" json:"adv_private_model,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AdvPrivateModel) Reset()         { *m = AdvPrivateModel{} }
func (m *AdvPrivateModel) String() string { return proto.CompactTextString(m) }
func (*AdvPrivateModel) ProtoMessage()    {}
func (*AdvPrivateModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{6}
}

func (m *AdvPrivateModel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdvPrivateModel.Unmarshal(m, b)
}
func (m *AdvPrivateModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdvPrivateModel.Marshal(b, m, deterministic)
}
func (m *AdvPrivateModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdvPrivateModel.Merge(m, src)
}
func (m *AdvPrivateModel) XXX_Size() int {
	return xxx_messageInfo_AdvPrivateModel.Size(m)
}
func (m *AdvPrivateModel) XXX_DiscardUnknown() {
	xxx_messageInfo_AdvPrivateModel.DiscardUnknown(m)
}

var xxx_messageInfo_AdvPrivateModel proto.InternalMessageInfo

func (m *AdvPrivateModel) GetAdvPrivateModel() map[int64]*PrivateModel {
	if m != nil {
		return m.AdvPrivateModel
	}
	return nil
}

type Req struct {
	Platform             PlatformType   `protobuf:"varint,1,opt,name=platform,proto3,enum=PlatformType" json:"platform,omitempty"`
	Did                  string         `protobuf:"bytes,2,opt,name=did,proto3" json:"did,omitempty"`
	SlotId               int64          `protobuf:"varint,3,opt,name=slot_id,json=slotId,proto3" json:"slot_id,omitempty"`
	Age                  int32          `protobuf:"varint,4,opt,name=age,proto3" json:"age,omitempty"`
	Gender               GenderType     `protobuf:"varint,5,opt,name=gender,proto3,enum=GenderType" json:"gender,omitempty"`
	City                 string         `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	Model                string         `protobuf:"bytes,7,opt,name=model,proto3" json:"model,omitempty"`
	InstallList          []string       `protobuf:"bytes,8,rep,name=install_list,json=installList,proto3" json:"install_list,omitempty"`
	DidType              DidType        `protobuf:"varint,9,opt,name=did_type,json=didType,proto3,enum=DidType" json:"did_type,omitempty"`
	Experiment           int32          `protobuf:"varint,10,opt,name=experiment,proto3" json:"experiment,omitempty"`
	DeviceType           DeviceType     `protobuf:"varint,11,opt,name=device_type,json=deviceType,proto3,enum=DeviceType" json:"device_type,omitempty"`
	ReqId                string         `protobuf:"bytes,12,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	Source               string         `protobuf:"bytes,13,opt,name=source,proto3" json:"source,omitempty"`
	RtaIds               []int64        `protobuf:"varint,14,rep,packed,name=rta_ids,json=rtaIds,proto3" json:"rta_ids,omitempty"`
	EnableStrategy       EnableStrategy `protobuf:"varint,15,opt,name=enable_strategy,json=enableStrategy,proto3,enum=EnableStrategy" json:"enable_strategy,omitempty"`
	TtDid                int64          `protobuf:"varint,16,opt,name=tt_did,json=ttDid,proto3" json:"tt_did,omitempty"`
	Device               *Device        `protobuf:"bytes,17,opt,name=device,proto3" json:"device,omitempty"`
	Industry             *Industry      `protobuf:"bytes,18,opt,name=industry,proto3" json:"industry,omitempty"`
	Geo                  *Geo           `protobuf:"bytes,19,opt,name=geo,proto3" json:"geo,omitempty"`
	DeviceModel          string         `protobuf:"bytes,20,opt,name=DeviceModel,proto3" json:"DeviceModel,omitempty"`
	CreativeType         []int32        `protobuf:"varint,21,rep,packed,name=creative_type,json=creativeType,proto3" json:"creative_type,omitempty"`
	AdSlotType           int32          `protobuf:"varint,22,opt,name=ad_slot_type,json=adSlotType,proto3" json:"ad_slot_type,omitempty"`
	AppBundleId          string         `protobuf:"bytes,23,opt,name=app_bundle_id,json=appBundleId,proto3" json:"app_bundle_id,omitempty"`
	Vids                 []int32        `protobuf:"varint,24,rep,packed,name=vids,proto3" json:"vids,omitempty"`
	PrivateModel         []*ModelMeta   `protobuf:"bytes,25,rep,name=private_model,json=privateModel,proto3" json:"private_model,omitempty"`
	ImageMode            ImageMode      `protobuf:"varint,26,opt,name=image_mode,json=imageMode,proto3,enum=ImageMode" json:"image_mode,omitempty"`
	UserId               int64          `protobuf:"varint,27,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	AdxId                int64          `protobuf:"varint,28,opt,name=adx_id,json=adxId,proto3" json:"adx_id,omitempty"`
	AppId                int64          `protobuf:"varint,29,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *Req) Reset()         { *m = Req{} }
func (m *Req) String() string { return proto.CompactTextString(m) }
func (*Req) ProtoMessage()    {}
func (*Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{7}
}

func (m *Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Req.Unmarshal(m, b)
}
func (m *Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Req.Marshal(b, m, deterministic)
}
func (m *Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Req.Merge(m, src)
}
func (m *Req) XXX_Size() int {
	return xxx_messageInfo_Req.Size(m)
}
func (m *Req) XXX_DiscardUnknown() {
	xxx_messageInfo_Req.DiscardUnknown(m)
}

var xxx_messageInfo_Req proto.InternalMessageInfo

func (m *Req) GetPlatform() PlatformType {
	if m != nil {
		return m.Platform
	}
	return PlatformType_PLATFORM_UNKNOWN
}

func (m *Req) GetDid() string {
	if m != nil {
		return m.Did
	}
	return ""
}

func (m *Req) GetSlotId() int64 {
	if m != nil {
		return m.SlotId
	}
	return 0
}

func (m *Req) GetAge() int32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *Req) GetGender() GenderType {
	if m != nil {
		return m.Gender
	}
	return GenderType_GENDER_UNKNOWN
}

func (m *Req) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *Req) GetModel() string {
	if m != nil {
		return m.Model
	}
	return ""
}

func (m *Req) GetInstallList() []string {
	if m != nil {
		return m.InstallList
	}
	return nil
}

func (m *Req) GetDidType() DidType {
	if m != nil {
		return m.DidType
	}
	return DidType_IMEI
}

func (m *Req) GetExperiment() int32 {
	if m != nil {
		return m.Experiment
	}
	return 0
}

func (m *Req) GetDeviceType() DeviceType {
	if m != nil {
		return m.DeviceType
	}
	return DeviceType_DEVICE_UNKNOWN
}

func (m *Req) GetReqId() string {
	if m != nil {
		return m.ReqId
	}
	return ""
}

func (m *Req) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *Req) GetRtaIds() []int64 {
	if m != nil {
		return m.RtaIds
	}
	return nil
}

func (m *Req) GetEnableStrategy() EnableStrategy {
	if m != nil {
		return m.EnableStrategy
	}
	return EnableStrategy_DISABLE
}

func (m *Req) GetTtDid() int64 {
	if m != nil {
		return m.TtDid
	}
	return 0
}

func (m *Req) GetDevice() *Device {
	if m != nil {
		return m.Device
	}
	return nil
}

func (m *Req) GetIndustry() *Industry {
	if m != nil {
		return m.Industry
	}
	return nil
}

func (m *Req) GetGeo() *Geo {
	if m != nil {
		return m.Geo
	}
	return nil
}

func (m *Req) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *Req) GetCreativeType() []int32 {
	if m != nil {
		return m.CreativeType
	}
	return nil
}

func (m *Req) GetAdSlotType() int32 {
	if m != nil {
		return m.AdSlotType
	}
	return 0
}

func (m *Req) GetAppBundleId() string {
	if m != nil {
		return m.AppBundleId
	}
	return ""
}

func (m *Req) GetVids() []int32 {
	if m != nil {
		return m.Vids
	}
	return nil
}

func (m *Req) GetPrivateModel() []*ModelMeta {
	if m != nil {
		return m.PrivateModel
	}
	return nil
}

func (m *Req) GetImageMode() ImageMode {
	if m != nil {
		return m.ImageMode
	}
	return ImageMode_IMAGE_DEFAULT
}

func (m *Req) GetUserId() int64 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *Req) GetAdxId() int64 {
	if m != nil {
		return m.AdxId
	}
	return 0
}

func (m *Req) GetAppId() int64 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type UserInfo struct {
	IsInterested         bool                      `protobuf:"varint,1,opt,name=is_interested,json=isInterested,proto3" json:"is_interested,omitempty"`
	UserScores           []*UserInfo_UserQuality   `protobuf:"bytes,2,rep,name=user_scores,json=userScores,proto3" json:"user_scores,omitempty"`
	RtaId                int64                     `protobuf:"varint,3,opt,name=rta_id,json=rtaId,proto3" json:"rta_id,omitempty"`
	PidLists             []*UserInfo_PidList       `protobuf:"bytes,4,rep,name=pid_lists,json=pidLists,proto3" json:"pid_lists,omitempty"`
	ActionScores         []*UserInfo_ActionQuality `protobuf:"bytes,5,rep,name=action_scores,json=actionScores,proto3" json:"action_scores,omitempty"`
	TargetAdvertiserIds  []int64                   `protobuf:"varint,6,rep,packed,name=target_advertiser_ids,json=targetAdvertiserIds,proto3" json:"target_advertiser_ids,omitempty"`
	AdIds                []int64                   `protobuf:"varint,7,rep,packed,name=ad_ids,json=adIds,proto3" json:"ad_ids,omitempty"`
	AdvertisingScene     int32                     `protobuf:"varint,8,opt,name=advertising_scene,json=advertisingScene,proto3" json:"advertising_scene,omitempty"`
	CusVid               CusVid                    `protobuf:"varint,9,opt,name=cus_vid,json=cusVid,proto3,enum=CusVid" json:"cus_vid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8}
}

func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (m *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(m, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetIsInterested() bool {
	if m != nil {
		return m.IsInterested
	}
	return false
}

func (m *UserInfo) GetUserScores() []*UserInfo_UserQuality {
	if m != nil {
		return m.UserScores
	}
	return nil
}

func (m *UserInfo) GetRtaId() int64 {
	if m != nil {
		return m.RtaId
	}
	return 0
}

func (m *UserInfo) GetPidLists() []*UserInfo_PidList {
	if m != nil {
		return m.PidLists
	}
	return nil
}

func (m *UserInfo) GetActionScores() []*UserInfo_ActionQuality {
	if m != nil {
		return m.ActionScores
	}
	return nil
}

func (m *UserInfo) GetTargetAdvertiserIds() []int64 {
	if m != nil {
		return m.TargetAdvertiserIds
	}
	return nil
}

func (m *UserInfo) GetAdIds() []int64 {
	if m != nil {
		return m.AdIds
	}
	return nil
}

func (m *UserInfo) GetAdvertisingScene() int32 {
	if m != nil {
		return m.AdvertisingScene
	}
	return 0
}

func (m *UserInfo) GetCusVid() CusVid {
	if m != nil {
		return m.CusVid
	}
	return CusVid_CUS_VID_UNKNOWN
}

type UserInfo_UserQuality struct {
	UserType             UserInfo_UserType    `protobuf:"varint,1,opt,name=user_type,json=userType,proto3,enum=UserInfo_UserType" json:"user_type,omitempty"`
	Quality              float64              `protobuf:"fixed64,2,opt,name=quality,proto3" json:"quality,omitempty"`
	PricingType          UserInfo_PricingType `protobuf:"varint,3,opt,name=pricing_type,json=pricingType,proto3,enum=UserInfo_PricingType" json:"pricing_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UserInfo_UserQuality) Reset()         { *m = UserInfo_UserQuality{} }
func (m *UserInfo_UserQuality) String() string { return proto.CompactTextString(m) }
func (*UserInfo_UserQuality) ProtoMessage()    {}
func (*UserInfo_UserQuality) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 0}
}

func (m *UserInfo_UserQuality) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo_UserQuality.Unmarshal(m, b)
}
func (m *UserInfo_UserQuality) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo_UserQuality.Marshal(b, m, deterministic)
}
func (m *UserInfo_UserQuality) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo_UserQuality.Merge(m, src)
}
func (m *UserInfo_UserQuality) XXX_Size() int {
	return xxx_messageInfo_UserInfo_UserQuality.Size(m)
}
func (m *UserInfo_UserQuality) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo_UserQuality.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo_UserQuality proto.InternalMessageInfo

func (m *UserInfo_UserQuality) GetUserType() UserInfo_UserType {
	if m != nil {
		return m.UserType
	}
	return UserInfo_EMPTY
}

func (m *UserInfo_UserQuality) GetQuality() float64 {
	if m != nil {
		return m.Quality
	}
	return 0
}

func (m *UserInfo_UserQuality) GetPricingType() UserInfo_PricingType {
	if m != nil {
		return m.PricingType
	}
	return UserInfo_NOLIMIT
}

type UserInfo_ActionQuality struct {
	ActionType           UserInfo_ActionType `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=UserInfo_ActionType" json:"action_type,omitempty"`
	Quality              float64             `protobuf:"fixed64,2,opt,name=quality,proto3" json:"quality,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UserInfo_ActionQuality) Reset()         { *m = UserInfo_ActionQuality{} }
func (m *UserInfo_ActionQuality) String() string { return proto.CompactTextString(m) }
func (*UserInfo_ActionQuality) ProtoMessage()    {}
func (*UserInfo_ActionQuality) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 1}
}

func (m *UserInfo_ActionQuality) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo_ActionQuality.Unmarshal(m, b)
}
func (m *UserInfo_ActionQuality) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo_ActionQuality.Marshal(b, m, deterministic)
}
func (m *UserInfo_ActionQuality) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo_ActionQuality.Merge(m, src)
}
func (m *UserInfo_ActionQuality) XXX_Size() int {
	return xxx_messageInfo_UserInfo_ActionQuality.Size(m)
}
func (m *UserInfo_ActionQuality) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo_ActionQuality.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo_ActionQuality proto.InternalMessageInfo

func (m *UserInfo_ActionQuality) GetActionType() UserInfo_ActionType {
	if m != nil {
		return m.ActionType
	}
	return UserInfo_ACTION_DEFAULT
}

func (m *UserInfo_ActionQuality) GetQuality() float64 {
	if m != nil {
		return m.Quality
	}
	return 0
}

// rta+dpa维度个性化出价
type UserInfo_PidScore struct {
	// pid
	Pid int64 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`
	// RTA_BID(7) 直接出价  BOOST_COEF(8) 溢价系数出价
	UserType UserInfo_UserType `protobuf:"varint,2,opt,name=user_type,json=userType,proto3,enum=UserInfo_UserType" json:"user_type,omitempty"`
	// 出价
	Quality              float64  `protobuf:"fixed64,3,opt,name=quality,proto3" json:"quality,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfo_PidScore) Reset()         { *m = UserInfo_PidScore{} }
func (m *UserInfo_PidScore) String() string { return proto.CompactTextString(m) }
func (*UserInfo_PidScore) ProtoMessage()    {}
func (*UserInfo_PidScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 2}
}

func (m *UserInfo_PidScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo_PidScore.Unmarshal(m, b)
}
func (m *UserInfo_PidScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo_PidScore.Marshal(b, m, deterministic)
}
func (m *UserInfo_PidScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo_PidScore.Merge(m, src)
}
func (m *UserInfo_PidScore) XXX_Size() int {
	return xxx_messageInfo_UserInfo_PidScore.Size(m)
}
func (m *UserInfo_PidScore) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo_PidScore.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo_PidScore proto.InternalMessageInfo

func (m *UserInfo_PidScore) GetPid() int64 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *UserInfo_PidScore) GetUserType() UserInfo_UserType {
	if m != nil {
		return m.UserType
	}
	return UserInfo_EMPTY
}

func (m *UserInfo_PidScore) GetQuality() float64 {
	if m != nil {
		return m.Quality
	}
	return 0
}

type UserInfo_PidList struct {
	PidType    UserInfo_PidType `protobuf:"varint,1,opt,name=pid_type,json=pidType,proto3,enum=UserInfo_PidType" json:"pid_type,omitempty"`
	Pids       []int64          `protobuf:"varint,2,rep,packed,name=pids,proto3" json:"pids,omitempty"`
	PlatformId int64            `protobuf:"varint,3,opt,name=platform_id,json=platformId,proto3" json:"platform_id,omitempty"`
	// pid维度个性化出价
	PidScores            []*UserInfo_PidScore `protobuf:"bytes,4,rep,name=pid_scores,json=pidScores,proto3" json:"pid_scores,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UserInfo_PidList) Reset()         { *m = UserInfo_PidList{} }
func (m *UserInfo_PidList) String() string { return proto.CompactTextString(m) }
func (*UserInfo_PidList) ProtoMessage()    {}
func (*UserInfo_PidList) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{8, 3}
}

func (m *UserInfo_PidList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo_PidList.Unmarshal(m, b)
}
func (m *UserInfo_PidList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo_PidList.Marshal(b, m, deterministic)
}
func (m *UserInfo_PidList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo_PidList.Merge(m, src)
}
func (m *UserInfo_PidList) XXX_Size() int {
	return xxx_messageInfo_UserInfo_PidList.Size(m)
}
func (m *UserInfo_PidList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo_PidList.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo_PidList proto.InternalMessageInfo

func (m *UserInfo_PidList) GetPidType() UserInfo_PidType {
	if m != nil {
		return m.PidType
	}
	return UserInfo_PidType_0
}

func (m *UserInfo_PidList) GetPids() []int64 {
	if m != nil {
		return m.Pids
	}
	return nil
}

func (m *UserInfo_PidList) GetPlatformId() int64 {
	if m != nil {
		return m.PlatformId
	}
	return 0
}

func (m *UserInfo_PidList) GetPidScores() []*UserInfo_PidScore {
	if m != nil {
		return m.PidScores
	}
	return nil
}

type Rsp struct {
	StatusCode           int32                `protobuf:"varint,1,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	ExcludeAdvertiserId  []int64              `protobuf:"varint,2,rep,packed,name=exclude_advertiser_id,json=excludeAdvertiserId,proto3" json:"exclude_advertiser_id,omitempty"`
	ExcludeAdId          []int64              `protobuf:"varint,3,rep,packed,name=exclude_ad_id,json=excludeAdId,proto3" json:"exclude_ad_id,omitempty"`
	RtaBid               int64                `protobuf:"varint,4,opt,name=rta_bid,json=rtaBid,proto3" json:"rta_bid,omitempty"`
	CacheDuration        int32                `protobuf:"varint,5,opt,name=cache_duration,json=cacheDuration,proto3" json:"cache_duration,omitempty"` // Deprecated: Do not use.
	ReqId                string               `protobuf:"bytes,6,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	UserInfos            []*UserInfo          `protobuf:"bytes,7,rep,name=user_infos,json=userInfos,proto3" json:"user_infos,omitempty"`
	Ts                   int64                `protobuf:"varint,8,opt,name=ts,proto3" json:"ts,omitempty"`
	BidType              *wrappers.Int32Value `protobuf:"bytes,9,opt,name=bid_type,json=bidType,proto3" json:"bid_type,omitempty"`
	CacheTimeSecs        int32                `protobuf:"varint,10,opt,name=cache_time_secs,json=cacheTimeSecs,proto3" json:"cache_time_secs,omitempty"`
	FlEmbedding          []float32            `protobuf:"fixed32,11,rep,packed,name=fl_embedding,json=flEmbedding,proto3" json:"fl_embedding,omitempty"`
	RtaVid               string               `protobuf:"bytes,12,opt,name=rta_vid,json=rtaVid,proto3" json:"rta_vid,omitempty"`
	CusVid               CusVid               `protobuf:"varint,13,opt,name=cus_vid,json=cusVid,proto3,enum=CusVid" json:"cus_vid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *Rsp) Reset()         { *m = Rsp{} }
func (m *Rsp) String() string { return proto.CompactTextString(m) }
func (*Rsp) ProtoMessage()    {}
func (*Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_406a580be25c7a3a, []int{9}
}

func (m *Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Rsp.Unmarshal(m, b)
}
func (m *Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Rsp.Marshal(b, m, deterministic)
}
func (m *Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rsp.Merge(m, src)
}
func (m *Rsp) XXX_Size() int {
	return xxx_messageInfo_Rsp.Size(m)
}
func (m *Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_Rsp proto.InternalMessageInfo

func (m *Rsp) GetStatusCode() int32 {
	if m != nil {
		return m.StatusCode
	}
	return 0
}

func (m *Rsp) GetExcludeAdvertiserId() []int64 {
	if m != nil {
		return m.ExcludeAdvertiserId
	}
	return nil
}

func (m *Rsp) GetExcludeAdId() []int64 {
	if m != nil {
		return m.ExcludeAdId
	}
	return nil
}

func (m *Rsp) GetRtaBid() int64 {
	if m != nil {
		return m.RtaBid
	}
	return 0
}

// Deprecated: Do not use.
func (m *Rsp) GetCacheDuration() int32 {
	if m != nil {
		return m.CacheDuration
	}
	return 0
}

func (m *Rsp) GetReqId() string {
	if m != nil {
		return m.ReqId
	}
	return ""
}

func (m *Rsp) GetUserInfos() []*UserInfo {
	if m != nil {
		return m.UserInfos
	}
	return nil
}

func (m *Rsp) GetTs() int64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *Rsp) GetBidType() *wrappers.Int32Value {
	if m != nil {
		return m.BidType
	}
	return nil
}

func (m *Rsp) GetCacheTimeSecs() int32 {
	if m != nil {
		return m.CacheTimeSecs
	}
	return 0
}

func (m *Rsp) GetFlEmbedding() []float32 {
	if m != nil {
		return m.FlEmbedding
	}
	return nil
}

func (m *Rsp) GetRtaVid() string {
	if m != nil {
		return m.RtaVid
	}
	return ""
}

func (m *Rsp) GetCusVid() CusVid {
	if m != nil {
		return m.CusVid
	}
	return CusVid_CUS_VID_UNKNOWN
}

func init() {
	proto.RegisterEnum("PlatformType", PlatformType_name, PlatformType_value)
	proto.RegisterEnum("DeviceType", DeviceType_name, DeviceType_value)
	proto.RegisterEnum("DeviceOsType", DeviceOsType_name, DeviceOsType_value)
	proto.RegisterEnum("GenderType", GenderType_name, GenderType_value)
	proto.RegisterEnum("DidType", DidType_name, DidType_value)
	proto.RegisterEnum("EnableStrategy", EnableStrategy_name, EnableStrategy_value)
	proto.RegisterEnum("ImageMode", ImageMode_name, ImageMode_value)
	proto.RegisterEnum("CusVid", CusVid_name, CusVid_value)
	proto.RegisterEnum("UserInfo_UserType", UserInfo_UserType_name, UserInfo_UserType_value)
	proto.RegisterEnum("UserInfo_PidType", UserInfo_PidType_name, UserInfo_PidType_value)
	proto.RegisterEnum("UserInfo_PricingType", UserInfo_PricingType_name, UserInfo_PricingType_value)
	proto.RegisterEnum("UserInfo_ActionType", UserInfo_ActionType_name, UserInfo_ActionType_value)
	proto.RegisterType((*Paid)(nil), "Paid")
	proto.RegisterType((*Device)(nil), "Device")
	proto.RegisterType((*Industry)(nil), "Industry")
	proto.RegisterType((*Geo)(nil), "Geo")
	proto.RegisterType((*ModelMeta)(nil), "ModelMeta")
	proto.RegisterType((*PrivateModel)(nil), "PrivateModel")
	proto.RegisterType((*AdvPrivateModel)(nil), "AdvPrivateModel")
	proto.RegisterMapType((map[int64]*PrivateModel)(nil), "AdvPrivateModel.AdvPrivateModelEntry")
	proto.RegisterType((*Req)(nil), "Req")
	proto.RegisterType((*UserInfo)(nil), "UserInfo")
	proto.RegisterType((*UserInfo_UserQuality)(nil), "UserInfo.UserQuality")
	proto.RegisterType((*UserInfo_ActionQuality)(nil), "UserInfo.ActionQuality")
	proto.RegisterType((*UserInfo_PidScore)(nil), "UserInfo.PidScore")
	proto.RegisterType((*UserInfo_PidList)(nil), "UserInfo.PidList")
	proto.RegisterType((*Rsp)(nil), "Rsp")
}

func init() { proto.RegisterFile("rta_api.proto", fileDescriptor_406a580be25c7a3a) }

var fileDescriptor_406a580be25c7a3a = []byte{
	// 2395 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x58, 0x6d, 0x73, 0xdb, 0xc6,
	0xf1, 0x37, 0x09, 0xf1, 0x69, 0x41, 0x52, 0xf0, 0x59, 0x72, 0x10, 0x39, 0x76, 0x14, 0xfa, 0x9f,
	0xfc, 0x15, 0x25, 0x43, 0xc7, 0x74, 0xac, 0x38, 0x99, 0xbe, 0x28, 0x25, 0x42, 0x0a, 0xa6, 0xe2,
	0x43, 0x40, 0x5a, 0x99, 0xb6, 0xd3, 0x41, 0x21, 0xde, 0x89, 0xc1, 0x84, 0x24, 0x60, 0x00, 0x64,
	0xed, 0x0f, 0xd1, 0xe9, 0x4c, 0xbf, 0x40, 0xdf, 0xf4, 0x23, 0x74, 0xa6, 0x9f, 0xa5, 0x2f, 0xfa,
	0xa6, 0x9f, 0xa4, 0xb3, 0x7b, 0x07, 0x10, 0x64, 0xdd, 0x17, 0x7d, 0xc5, 0xdd, 0xdf, 0xfd, 0x6e,
	0x6f, 0x6f, 0x6f, 0x6f, 0xf7, 0x40, 0x68, 0x44, 0x89, 0xe7, 0x7a, 0xa1, 0xdf, 0x0e, 0xa3, 0x20,
	0x09, 0x8e, 0x9e, 0xcc, 0x82, 0x60, 0x36, 0x17, 0xcf, 0x48, 0xbb, 0x5d, 0xdd, 0x3d, 0xfb, 0x43,
	0xe4, 0x85, 0xa1, 0x88, 0x62, 0x39, 0xde, 0x3a, 0x83, 0xbd, 0x91, 0xe7, 0x73, 0x76, 0x00, 0xa5,
	0xb5, 0x37, 0x5f, 0x09, 0xb3, 0x70, 0x5c, 0x38, 0xa9, 0x39, 0x52, 0x61, 0x26, 0x54, 0xd6, 0x22,
	0x8a, 0xfd, 0x60, 0x69, 0x16, 0x09, 0x4f, 0xd5, 0xd6, 0x5f, 0xab, 0x50, 0xee, 0x89, 0xb5, 0x3f,
	0x15, 0xec, 0x43, 0xa8, 0xfa, 0x0b, 0xe1, 0xbb, 0x0b, 0xfe, 0x52, 0xcd, 0xae, 0xa0, 0xde, 0xe7,
	0x2f, 0xd9, 0xff, 0x41, 0xd3, 0x5b, 0xf2, 0x28, 0xf0, 0xb9, 0xeb, 0x73, 0x22, 0x48, 0x33, 0x75,
	0x85, 0xda, 0x1c, 0x59, 0x0c, 0xf6, 0x02, 0xcf, 0xe7, 0xa6, 0x46, 0x63, 0x24, 0x23, 0xe6, 0xf3,
	0x3b, 0xcf, 0xdc, 0x93, 0x18, 0xca, 0x68, 0x8d, 0x16, 0x8a, 0xc5, 0x74, 0x15, 0x09, 0xd7, 0xe7,
	0x66, 0x49, 0x5a, 0x43, 0x74, 0x4c, 0xa0, 0x4d, 0x3b, 0x99, 0x7a, 0x3e, 0x7f, 0x6e, 0x96, 0xe5,
	0x4e, 0x48, 0x49, 0xd1, 0x8e, 0x59, 0xd9, 0xa0, 0x1d, 0xf6, 0x18, 0x60, 0x36, 0x0f, 0x6e, 0xbd,
	0xb9, 0xcb, 0x7d, 0x6e, 0x56, 0x69, 0xa8, 0x26, 0x91, 0x9e, 0x74, 0x62, 0x86, 0x8e, 0xd5, 0xa4,
	0x13, 0x28, 0xb3, 0x2f, 0x80, 0xa5, 0xbb, 0xcd, 0x39, 0x02, 0xc4, 0xd8, 0x57, 0xfb, 0xce, 0x7c,
	0x41, 0x8f, 0xf9, 0x9d, 0x97, 0x23, 0xea, 0xca, 0x63, 0x7e, 0xe7, 0x65, 0x2c, 0x34, 0x89, 0xac,
	0x6d, 0x93, 0x75, 0x65, 0x92, 0xdf, 0x79, 0x3b, 0x26, 0x31, 0x40, 0x39, 0x62, 0x43, 0x9a, 0x44,
	0x34, 0x6f, 0x92, 0x58, 0xdb, 0x26, 0x9b, 0xd2, 0x24, 0x8e, 0xe4, 0x4d, 0x3e, 0x06, 0x88, 0x7f,
	0x76, 0xd3, 0x83, 0xde, 0x97, 0x51, 0x88, 0x7f, 0xbe, 0x91, 0x00, 0x6b, 0x42, 0xd1, 0x0f, 0xcd,
	0xfb, 0x04, 0x17, 0xfd, 0x10, 0xf5, 0x95, 0x67, 0x32, 0xa9, 0xaf, 0x3c, 0x9c, 0xbe, 0x5a, 0xfa,
	0xc1, 0xd2, 0xc5, 0xdd, 0x9b, 0x0f, 0xe4, 0x74, 0x42, 0xec, 0x85, 0xf0, 0x31, 0x88, 0x21, 0x06,
	0xf1, 0x40, 0x06, 0x31, 0xdc, 0x9c, 0xee, 0xda, 0x3c, 0xcc, 0x4e, 0x77, 0xcd, 0x9e, 0x80, 0xbe,
	0xb8, 0x75, 0x13, 0x7f, 0x21, 0x28, 0x51, 0x1e, 0x4a, 0x3b, 0x8b, 0xdb, 0x89, 0xbf, 0x10, 0x98,
	0x25, 0x2d, 0x68, 0xdc, 0x06, 0x41, 0xb2, 0x61, 0x7c, 0x40, 0x0c, 0x1d, 0xc1, 0x94, 0xf3, 0x19,
	0xec, 0xd3, 0x71, 0xe7, 0xf6, 0x6c, 0x12, 0xab, 0x41, 0x70, 0xb6, 0x63, 0xc5, 0xeb, 0xe4, 0x78,
	0x1f, 0x6e, 0x78, 0x9d, 0x8c, 0x77, 0x08, 0x65, 0x2f, 0x0c, 0x71, 0xf8, 0xe8, 0xb8, 0x70, 0xa2,
	0x39, 0x25, 0x2f, 0x0c, 0x6d, 0xce, 0x5e, 0x40, 0x93, 0x53, 0xee, 0xbb, 0x41, 0xec, 0x26, 0xef,
	0x42, 0x61, 0x3e, 0x3a, 0x2e, 0x9c, 0x34, 0x3b, 0x8d, 0xb6, 0xbc, 0x12, 0xc3, 0x78, 0xf2, 0x2e,
	0x14, 0x4e, 0x9d, 0xe7, 0x34, 0x0c, 0x53, 0x10, 0x67, 0x51, 0xfe, 0x48, 0x6e, 0x2f, 0x88, 0xd3,
	0x28, 0x7f, 0x0d, 0x4d, 0x7f, 0x99, 0x88, 0xe8, 0xce, 0x9b, 0x0a, 0x69, 0xf3, 0xf1, 0xfb, 0x6c,
	0x36, 0x32, 0x12, 0x19, 0x7d, 0x0a, 0x8d, 0x69, 0xb0, 0x4c, 0xc4, 0x32, 0x71, 0x67, 0x51, 0xb0,
	0x0a, 0xcd, 0x27, 0x32, 0x19, 0x14, 0x78, 0x85, 0x18, 0x91, 0x28, 0x2a, 0xe9, 0xe2, 0x1f, 0x2b,
	0x12, 0x82, 0xe9, 0xfa, 0x8a, 0xd4, 0xc9, 0x48, 0xc7, 0x1b, 0x52, 0x27, 0x25, 0x3d, 0x82, 0x12,
	0x9e, 0x5f, 0x6c, 0x7e, 0x72, 0xac, 0x9d, 0xe8, 0x9d, 0x52, 0x1b, 0x6b, 0x87, 0x23, 0x31, 0x3c,
	0x54, 0x24, 0x9b, 0x2d, 0x79, 0xa8, 0x28, 0xb7, 0x7e, 0x03, 0x55, 0x7b, 0xc9, 0x57, 0x71, 0x12,
	0xbd, 0x63, 0x9f, 0x42, 0xf3, 0xce, 0x8f, 0xe2, 0xc4, 0xf5, 0x15, 0x42, 0xd5, 0x42, 0x73, 0x1a,
	0x84, 0x66, 0xb4, 0xff, 0x87, 0xfd, 0x58, 0x4c, 0x83, 0x25, 0xdf, 0xf0, 0x8a, 0xc4, 0x6b, 0x4a,
	0x38, 0x25, 0xb6, 0x62, 0xd0, 0xae, 0x44, 0x40, 0xcb, 0xfa, 0x49, 0x6a, 0x8c, 0x64, 0x66, 0x80,
	0x36, 0xf7, 0x12, 0x9a, 0x57, 0x70, 0x50, 0x24, 0x24, 0x58, 0x52, 0x89, 0x41, 0x24, 0x58, 0x62,
	0x6d, 0xbb, 0x08, 0x56, 0x4b, 0xb4, 0x2f, 0x8b, 0x4c, 0xaa, 0xb2, 0x23, 0xa8, 0x72, 0x3f, 0x4e,
	0x22, 0x7f, 0x9a, 0x50, 0x85, 0xd1, 0x9c, 0x4c, 0x6f, 0x3d, 0x87, 0x5a, 0x3f, 0xe0, 0x62, 0xde,
	0x17, 0x89, 0x47, 0x37, 0x83, 0xab, 0x9a, 0x57, 0x94, 0x45, 0x34, 0x9e, 0x06, 0x91, 0x50, 0x0b,
	0x4b, 0xa5, 0xf5, 0x2d, 0xd4, 0x47, 0x91, 0xbf, 0xf6, 0x12, 0x41, 0x33, 0xd9, 0xe7, 0x00, 0x0b,
	0x14, 0xdc, 0x85, 0x48, 0x3c, 0xb3, 0x40, 0x91, 0x84, 0x76, 0x66, 0xd5, 0xa9, 0x2d, 0x52, 0xb1,
	0xf5, 0xf7, 0x02, 0xec, 0x77, 0xf9, 0x7a, 0x6b, 0xfa, 0x0f, 0x70, 0xdf, 0xe3, 0x6b, 0x37, 0x94,
	0x98, 0x4b, 0x64, 0x65, 0xe5, 0xd3, 0xf6, 0x0e, 0x79, 0x57, 0xb7, 0x70, 0x7f, 0xce, 0xbe, 0xb7,
	0x8d, 0x1e, 0xfd, 0x00, 0x07, 0xef, 0x23, 0x62, 0xd0, 0x7e, 0x16, 0x69, 0x64, 0x51, 0x64, 0x4f,
	0xd3, 0x36, 0x81, 0x3b, 0xd4, 0x3b, 0x8d, 0x76, 0x7e, 0x92, 0xea, 0x1a, 0xdf, 0x15, 0x5f, 0x15,
	0x5a, 0x7f, 0xaa, 0x80, 0xe6, 0x88, 0x37, 0xec, 0x73, 0xa8, 0x86, 0x73, 0x2f, 0xb9, 0x0b, 0xa2,
	0x05, 0xd9, 0xc1, 0x84, 0x1e, 0x29, 0x80, 0x12, 0x3a, 0x1b, 0xc6, 0xd5, 0xb0, 0x0a, 0xcb, 0x0e,
	0x81, 0x22, 0xfb, 0x00, 0x2a, 0xf1, 0x3c, 0x48, 0x5c, 0xd5, 0x1b, 0x34, 0xa7, 0x8c, 0xaa, 0xcd,
	0x91, 0xea, 0xcd, 0x04, 0x9d, 0x5b, 0xc9, 0x41, 0x91, 0x3d, 0x85, 0xf2, 0x4c, 0x2c, 0xb9, 0x88,
	0xe8, 0xc4, 0x9a, 0x1d, 0xbd, 0x7d, 0x45, 0x2a, 0xad, 0xa1, 0x86, 0xb2, 0x54, 0x29, 0xab, 0x0c,
	0xc5, 0x54, 0x39, 0x80, 0x92, 0x0c, 0xa1, 0x6a, 0x0c, 0xa4, 0xb0, 0x4f, 0xa0, 0xee, 0x2f, 0xe3,
	0xc4, 0x9b, 0xcf, 0xdd, 0xb9, 0x1f, 0x27, 0x66, 0xf5, 0x58, 0xc3, 0x5a, 0xa3, 0xb0, 0x6b, 0x3f,
	0x4e, 0xd8, 0x53, 0xcc, 0x12, 0x2e, 0xaf, 0x6a, 0x8d, 0xd6, 0xac, 0xb6, 0x7b, 0x3e, 0xa7, 0x05,
	0x2b, 0x5c, 0x0a, 0xec, 0x09, 0x80, 0x78, 0x1b, 0x8a, 0xc8, 0x5f, 0x88, 0x65, 0x42, 0x5d, 0xa2,
	0xe4, 0xe4, 0x10, 0xf6, 0x25, 0xe8, 0xaa, 0x92, 0x90, 0x1d, 0x5d, 0xf9, 0x2e, 0xaf, 0x3c, 0x99,
	0x02, 0x9e, 0xc9, 0x58, 0x8e, 0x22, 0xf1, 0x66, 0xd3, 0x1c, 0x4a, 0x91, 0x78, 0x63, 0x73, 0xf6,
	0x10, 0xca, 0x71, 0xb0, 0x8a, 0xa6, 0x42, 0xb5, 0x02, 0xa5, 0x61, 0xf8, 0xf0, 0x31, 0x80, 0xf7,
	0xb5, 0x79, 0xac, 0x61, 0xf8, 0xa2, 0xc4, 0xb3, 0x79, 0xcc, 0x5e, 0xc1, 0xbe, 0x58, 0x7a, 0xb7,
	0x73, 0xe1, 0xc6, 0x49, 0xe4, 0x25, 0x62, 0xf6, 0x8e, 0xaa, 0x7e, 0xb3, 0xb3, 0xdf, 0xb6, 0x08,
	0x1f, 0x2b, 0xd8, 0x69, 0x8a, 0x2d, 0x1d, 0x3d, 0x48, 0x12, 0x6a, 0x96, 0x86, 0x2c, 0x88, 0x49,
	0x82, 0x8d, 0xf2, 0x63, 0x28, 0x4b, 0x37, 0xa9, 0x4d, 0xe8, 0x9d, 0x8a, 0xda, 0x81, 0xa3, 0x60,
	0xf6, 0x29, 0x54, 0xb3, 0xdb, 0xcc, 0x88, 0x52, 0x6b, 0xa7, 0x17, 0xd9, 0xc9, 0x86, 0xd8, 0x43,
	0xd0, 0x66, 0x22, 0xa0, 0x1e, 0xa2, 0x77, 0xf6, 0xda, 0x57, 0x22, 0x70, 0x10, 0x60, 0xc7, 0xa0,
	0x4b, 0x83, 0x94, 0x67, 0xaa, 0x95, 0xe4, 0x21, 0x2a, 0x5f, 0x91, 0xf0, 0x12, 0x7f, 0xad, 0x42,
	0x79, 0x78, 0xac, 0x9d, 0x94, 0x9c, 0x7a, 0x0a, 0x52, 0xfc, 0x8e, 0xa1, 0xee, 0x71, 0x97, 0x52,
	0x8a, 0x38, 0x0f, 0xe5, 0x79, 0x78, 0x7c, 0x3c, 0x0f, 0x12, 0x62, 0xb4, 0xa0, 0x81, 0x05, 0xff,
	0x76, 0xb5, 0xe4, 0x73, 0x6a, 0x0b, 0xaa, 0xc9, 0x78, 0x61, 0x78, 0x4e, 0x98, 0x4d, 0xcd, 0x6b,
	0x8d, 0x31, 0x35, 0x69, 0x05, 0x92, 0xd9, 0x33, 0x68, 0x6c, 0x5f, 0xc8, 0x0f, 0xff, 0xe3, 0x5a,
	0xd7, 0xc3, 0x9d, 0x22, 0xe0, 0x2f, 0xbc, 0x99, 0xa4, 0x53, 0x77, 0x69, 0x76, 0xa0, 0x6d, 0x23,
	0x84, 0x04, 0xa7, 0xe6, 0xa7, 0x22, 0x1e, 0xe3, 0x2a, 0x16, 0x11, 0x7a, 0xf3, 0x48, 0xde, 0x02,
	0x54, 0x55, 0x77, 0xe2, 0x6f, 0x11, 0xff, 0x48, 0x75, 0x27, 0xfe, 0x76, 0xab, 0x69, 0x3d, 0xce,
	0x35, 0xad, 0xd6, 0xbf, 0x6a, 0x50, 0x7d, 0x8d, 0x13, 0x97, 0x77, 0x01, 0x86, 0xcb, 0x8f, 0x5d,
	0xea, 0x25, 0x22, 0x4e, 0x84, 0x2c, 0x62, 0x55, 0xa7, 0xee, 0xc7, 0x76, 0x86, 0xb1, 0x33, 0xd0,
	0x69, 0x61, 0x2a, 0x63, 0xb1, 0x59, 0xa4, 0x2d, 0x1d, 0xb6, 0x53, 0x23, 0x24, 0xfc, 0xb0, 0xf2,
	0xe6, 0x7e, 0xf2, 0xce, 0x01, 0x64, 0x8e, 0x89, 0x48, 0x69, 0x4a, 0x79, 0xa7, 0x6e, 0x6d, 0x89,
	0xd2, 0x8e, 0xb5, 0xa1, 0x16, 0xfa, 0x9c, 0xee, 0x53, 0x6c, 0xee, 0x91, 0xb1, 0xfb, 0x1b, 0x63,
	0x23, 0x9f, 0xe3, 0xb5, 0x72, 0xaa, 0xa1, 0x14, 0x62, 0xf6, 0x0b, 0x68, 0x78, 0xd3, 0x04, 0x1f,
	0x16, 0xca, 0x81, 0x12, 0xcd, 0xf9, 0x60, 0x33, 0xa7, 0x4b, 0xc3, 0xa9, 0x0b, 0x75, 0xc9, 0x56,
	0x4e, 0x74, 0xe0, 0x30, 0xf1, 0xa2, 0x99, 0x48, 0x5c, 0x8f, 0xaf, 0x45, 0x94, 0xf8, 0x32, 0x84,
	0xb1, 0x59, 0xa6, 0xab, 0xf0, 0x40, 0x0e, 0x76, 0xb3, 0x31, 0xbc, 0x17, 0x14, 0x50, 0x22, 0x55,
	0x88, 0x54, 0xf2, 0x38, 0xc2, 0x5f, 0x50, 0xc5, 0x25, 0x9e, 0xbf, 0x9c, 0xb9, 0xf1, 0x54, 0x2c,
	0x05, 0x3d, 0x16, 0x4b, 0x8e, 0x91, 0x1b, 0x18, 0x23, 0xce, 0x8e, 0xa1, 0x32, 0x5d, 0xc5, 0xee,
	0x5a, 0x3d, 0x1b, 0x9b, 0x9d, 0x4a, 0xfb, 0x62, 0x15, 0xdf, 0xf8, 0xdc, 0x29, 0x4f, 0xe9, 0xf7,
	0xe8, 0xcf, 0x05, 0xd0, 0x73, 0xa1, 0x63, 0xcf, 0xa0, 0x46, 0x61, 0xa6, 0x94, 0x94, 0x35, 0x92,
	0x6d, 0x07, 0x59, 0x16, 0xca, 0x95, 0x92, 0xb0, 0x73, 0xbd, 0x91, 0x73, 0x55, 0xa3, 0x49, 0x55,
	0xf6, 0x0a, 0x30, 0xcb, 0xa6, 0xe8, 0x25, 0x59, 0xd3, 0xc8, 0x5a, 0xee, 0xc8, 0x46, 0x72, 0x94,
	0x0c, 0xea, 0xe1, 0x46, 0x39, 0xfa, 0x3d, 0x34, 0xb6, 0xa2, 0xc9, 0x5e, 0x82, 0xae, 0xa2, 0x9f,
	0xf3, 0xeb, 0x60, 0x37, 0xf6, 0xb2, 0x44, 0x79, 0x99, 0xfc, 0xdf, 0x7d, 0x3b, 0x9a, 0x41, 0x75,
	0xe4, 0x73, 0x3a, 0x1d, 0xac, 0xdf, 0xa1, 0xea, 0x9c, 0x9a, 0x83, 0xe2, 0x76, 0x10, 0x8a, 0xff,
	0x5b, 0x10, 0xb4, 0xed, 0x85, 0xfe, 0x52, 0x80, 0x8a, 0xca, 0x26, 0xf6, 0x25, 0x60, 0x3e, 0xe5,
	0xb7, 0xb0, 0x9d, 0x72, 0xb2, 0x5a, 0x87, 0xaa, 0x5a, 0xe3, 0x53, 0x15, 0x4f, 0xbf, 0x48, 0xa7,
	0x4f, 0x32, 0xfb, 0x18, 0xf4, 0xb4, 0x43, 0x6d, 0x32, 0x1a, 0x52, 0xc8, 0xe6, 0xec, 0x39, 0x00,
	0x2e, 0xa1, 0x72, 0x54, 0xe6, 0x35, 0xdb, 0x5a, 0x84, 0xf6, 0xec, 0x60, 0xf2, 0xcb, 0xdc, 0x6c,
	0xcd, 0xe5, 0x4d, 0xa4, 0x35, 0x6b, 0x50, 0xb2, 0xfa, 0xa3, 0xc9, 0xaf, 0x8d, 0x7b, 0xac, 0x09,
	0x30, 0x72, 0xec, 0x0b, 0xcb, 0xbd, 0x18, 0x5a, 0x97, 0x46, 0x81, 0xe9, 0x50, 0xe9, 0x59, 0x97,
	0xdd, 0xd7, 0xd7, 0x13, 0xa3, 0xc8, 0x1a, 0x50, 0x93, 0x83, 0xe7, 0x76, 0xcf, 0xd0, 0x70, 0xcc,
	0x99, 0x74, 0x49, 0xa9, 0xe0, 0xc4, 0xf3, 0xe1, 0x70, 0x3c, 0x91, 0x13, 0xab, 0xac, 0x0e, 0xd5,
	0x9e, 0x65, 0x8d, 0xdc, 0xbe, 0x3d, 0x30, 0x6a, 0xad, 0x09, 0x85, 0x83, 0x16, 0x43, 0x23, 0x52,
	0x74, 0xbf, 0x32, 0xee, 0xe5, 0xd5, 0xe7, 0x46, 0x21, 0xaf, 0x76, 0xd4, 0x8a, 0x4a, 0x7d, 0x61,
	0x68, 0x79, 0xf5, 0x6b, 0x63, 0xaf, 0xf5, 0x0d, 0xe8, 0xb9, 0x64, 0x42, 0x7f, 0x06, 0xc3, 0x6b,
	0xbb, 0x6f, 0x4f, 0x8c, 0x7b, 0xac, 0x0a, 0x7b, 0xc3, 0x8b, 0x51, 0xdf, 0x28, 0x28, 0xe9, 0xc2,
	0x28, 0xb2, 0x0a, 0x68, 0x28, 0x68, 0xad, 0xdf, 0x02, 0x6c, 0x72, 0x87, 0x31, 0x68, 0x76, 0x2f,
	0x26, 0xf6, 0x70, 0xe0, 0xa6, 0x5b, 0xbd, 0xc7, 0x0c, 0xa8, 0xdb, 0x03, 0xb7, 0x3b, 0x1a, 0xb9,
	0x43, 0xa7, 0x67, 0x39, 0xc6, 0x01, 0x7b, 0x08, 0xec, 0xe2, 0xf5, 0x78, 0x32, 0xec, 0x5b, 0x8e,
	0x6b, 0x5d, 0x5e, 0x5a, 0x17, 0x13, 0xfb, 0xc6, 0x32, 0x8e, 0xd8, 0x3e, 0xe8, 0xd7, 0xc3, 0xee,
	0xc0, 0xbd, 0x70, 0xac, 0x9e, 0x3d, 0x31, 0xfc, 0xd6, 0x3f, 0x34, 0xd0, 0x9c, 0x38, 0xc4, 0x53,
	0x8b, 0x13, 0x2f, 0x59, 0xc5, 0xee, 0x14, 0xeb, 0x6b, 0x41, 0x16, 0x7a, 0x09, 0x5d, 0x60, 0x51,
	0x3d, 0x83, 0x43, 0xf1, 0x76, 0x3a, 0x5f, 0x71, 0xb1, 0x5d, 0x1f, 0xe4, 0xd9, 0x9f, 0x17, 0x8d,
	0x82, 0xf3, 0x40, 0x11, 0xf2, 0x35, 0x82, 0x7d, 0x06, 0x8d, 0xcd, 0x3c, 0x99, 0x10, 0x29, 0x5f,
	0xcf, 0xf8, 0x36, 0x4f, 0x7b, 0xef, 0xad, 0xcf, 0xe9, 0x95, 0x22, 0x7b, 0xef, 0xb9, 0xcf, 0xd9,
	0xe7, 0xd0, 0x9c, 0x7a, 0xd3, 0x9f, 0x84, 0xcb, 0x57, 0x91, 0x87, 0x61, 0xa0, 0x07, 0x4b, 0xe9,
	0xbc, 0x68, 0x16, 0xf0, 0xeb, 0x63, 0xfa, 0x93, 0xe8, 0xa9, 0x81, 0x5c, 0xbb, 0x2f, 0xe7, 0xdb,
	0xfd, 0x09, 0x80, 0xec, 0x07, 0xcb, 0xbb, 0x40, 0x56, 0x2a, 0xec, 0xa6, 0x69, 0xc2, 0x39, 0x74,
	0x8f, 0x50, 0x8a, 0xf1, 0x7d, 0x9a, 0xc4, 0x54, 0xa9, 0x34, 0xa7, 0x98, 0xc4, 0xec, 0x0c, 0xaa,
	0xb7, 0xf9, 0x27, 0x8b, 0xde, 0x79, 0xd4, 0x96, 0xff, 0x0f, 0xb4, 0xd3, 0xff, 0x07, 0xda, 0xf6,
	0x32, 0x79, 0xd1, 0xb9, 0xc1, 0x77, 0x9c, 0x53, 0xb9, 0x55, 0x69, 0x43, 0x9f, 0x4b, 0xe8, 0x33,
	0x7d, 0x7b, 0xc5, 0x62, 0x1a, 0xab, 0xa7, 0x8c, 0x74, 0x18, 0xbf, 0xbe, 0xc6, 0x62, 0x1a, 0xe3,
	0xab, 0xe9, 0x6e, 0xee, 0x8a, 0xc5, 0xad, 0xe0, 0xdc, 0x5f, 0xce, 0x4c, 0xfd, 0x58, 0x3b, 0x29,
	0x3a, 0xfa, 0xdd, 0xdc, 0x4a, 0xa1, 0x34, 0x2e, 0xeb, 0xec, 0x0d, 0x83, 0x71, 0xb9, 0xf1, 0x79,
	0xbe, 0x6e, 0x36, 0xde, 0x5b, 0x37, 0x4f, 0xbf, 0x83, 0x7a, 0xfe, 0xe5, 0xc8, 0x0e, 0xc0, 0x18,
	0x5d, 0x77, 0x27, 0x97, 0x43, 0xa7, 0xef, 0xbe, 0x1e, 0xfc, 0x6a, 0x30, 0xfc, 0x71, 0x60, 0xdc,
	0xc3, 0x44, 0xec, 0x0e, 0x7a, 0xce, 0xd0, 0xee, 0x19, 0x05, 0x4c, 0x3a, 0x7b, 0x38, 0x36, 0x8a,
	0xa7, 0xdf, 0x00, 0x6c, 0xde, 0x54, 0x98, 0x74, 0x3d, 0xeb, 0x06, 0x2f, 0xd3, 0x66, 0x5e, 0x0d,
	0x4a, 0xa3, 0xef, 0x87, 0x03, 0xcb, 0x28, 0x30, 0x80, 0xf2, 0xa4, 0x7b, 0x7e, 0x6d, 0x4d, 0x8c,
	0xe2, 0xe9, 0xef, 0xa0, 0x9e, 0xff, 0xfe, 0x62, 0x87, 0x70, 0x5f, 0x4d, 0x1d, 0x8e, 0x73, 0xb3,
	0x37, 0x16, 0x37, 0x8b, 0x37, 0x01, 0x14, 0x46, 0x3e, 0xe4, 0x38, 0x3f, 0xda, 0x83, 0xde, 0xf0,
	0xc7, 0xb1, 0xa1, 0x9d, 0x9e, 0x01, 0x6c, 0xde, 0xa9, 0xc8, 0xb8, 0xb2, 0x06, 0x3d, 0xcb, 0xc9,
	0x59, 0xae, 0xc2, 0x5e, 0xbf, 0x7b, 0xad, 0xdc, 0xba, 0xb4, 0x48, 0x2e, 0x9e, 0xfe, 0xb1, 0x00,
	0x15, 0xf5, 0xd8, 0x44, 0x86, 0xdd, 0xb7, 0x6c, 0xe3, 0x1e, 0xde, 0x7b, 0x94, 0xdc, 0x7e, 0xef,
	0xa5, 0x5c, 0x5f, 0x39, 0xe3, 0xda, 0x3d, 0xb9, 0xfe, 0x46, 0x27, 0x8e, 0x46, 0x73, 0x7b, 0x97,
	0x5d, 0x63, 0x8f, 0xe6, 0xf6, 0x2e, 0xbb, 0x84, 0x97, 0xe8, 0xde, 0x76, 0xed, 0x9e, 0x51, 0x46,
	0x1c, 0x25, 0xc2, 0x2b, 0x88, 0x5f, 0x21, 0x4e, 0x35, 0xe6, 0x2a, 0xc5, 0x6b, 0xa7, 0xff, 0x2c,
	0x40, 0x73, 0xfb, 0xe9, 0x48, 0xd5, 0xcb, 0x1e, 0x63, 0x1c, 0x65, 0x7c, 0xac, 0x01, 0xca, 0xd9,
	0x35, 0x2f, 0xb0, 0xfb, 0xd0, 0x50, 0x58, 0x56, 0x72, 0x76, 0x20, 0x2c, 0x3b, 0x3b, 0xd0, 0xd7,
	0xc6, 0xde, 0x2e, 0x84, 0xfe, 0xee, 0x40, 0x67, 0x46, 0x79, 0x17, 0xfa, 0xc6, 0xa8, 0xec, 0x42,
	0xaf, 0x8c, 0xea, 0x2e, 0xf4, 0xad, 0x51, 0xcb, 0xf9, 0x2a, 0x2b, 0xe3, 0x57, 0x06, 0x9c, 0xfe,
	0x12, 0x6a, 0xd9, 0xdb, 0x0c, 0xe7, 0xd8, 0xfd, 0xee, 0x95, 0x95, 0x2b, 0x59, 0xfb, 0xa0, 0x4b,
	0xe8, 0xc6, 0xee, 0x59, 0x43, 0xa3, 0xb0, 0x01, 0x86, 0x93, 0xef, 0x2d, 0xc7, 0x28, 0x9e, 0xfe,
	0xad, 0x00, 0x65, 0x99, 0xd0, 0xec, 0x01, 0xec, 0x5f, 0xbc, 0x1e, 0x23, 0x35, 0x77, 0xce, 0x0d,
	0xa8, 0xa5, 0xa0, 0xaa, 0xc5, 0xa9, 0xaa, 0x6a, 0x71, 0xaa, 0xaa, 0x5a, 0x9c, 0xaa, 0x18, 0x90,
	0x9c, 0x8a, 0xc1, 0xc8, 0xa9, 0x18, 0x88, 0x9c, 0x8a, 0x41, 0xc8, 0xa9, 0x18, 0x80, 0x9c, 0x8a,
	0x9b, 0x6f, 0x02, 0x64, 0x6e, 0x7c, 0x65, 0xc0, 0x6d, 0x99, 0x0a, 0xc3, 0x8b, 0x7f, 0x07, 0x00,
	0x00, 0xff, 0xff, 0xd4, 0x52, 0xa8, 0xb4, 0x57, 0x14, 0x00, 0x00,
}
