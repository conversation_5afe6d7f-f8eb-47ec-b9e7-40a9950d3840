package com.coohua.rta.dto.req;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: BaiduRtaRequest.proto

public final class BaiduRtaRequest {
  private BaiduRtaRequest() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  /**
   * Protobuf enum {@code FlowType}
   */
  public enum FlowType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 站内流量
     * </pre>
     *
     * <code>SHOUBAI = 1;</code>
     */
    SHOUBAI(1),
    /**
     * <pre>
     * 百青藤流量
     * </pre>
     *
     * <code>UNION = 2;</code>
     */
    UNION(2),
    /**
     * <pre>
     * 开屏（仅指品牌广告百度系开屏流量）
     * </pre>
     *
     * <code>KAIPING = 4;</code>
     */
    KAIPING(4),
    ;

    /**
     * <pre>
     * 站内流量
     * </pre>
     *
     * <code>SHOUBAI = 1;</code>
     */
    public static final int SHOUBAI_VALUE = 1;
    /**
     * <pre>
     * 百青藤流量
     * </pre>
     *
     * <code>UNION = 2;</code>
     */
    public static final int UNION_VALUE = 2;
    /**
     * <pre>
     * 开屏（仅指品牌广告百度系开屏流量）
     * </pre>
     *
     * <code>KAIPING = 4;</code>
     */
    public static final int KAIPING_VALUE = 4;


    public final int getNumber() {
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static FlowType valueOf(int value) {
      return forNumber(value);
    }

    public static FlowType forNumber(int value) {
      switch (value) {
        case 1: return SHOUBAI;
        case 2: return UNION;
        case 4: return KAIPING;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<FlowType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        FlowType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<FlowType>() {
            public FlowType findValueByNumber(int number) {
              return FlowType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return BaiduRtaRequest.getDescriptor().getEnumTypes().get(0);
    }

    private static final FlowType[] VALUES = values();

    public static FlowType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private FlowType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:FlowType)
  }

  /**
   * Protobuf enum {@code OsType}
   */
  public enum OsType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <pre>
     * 类型未知
     * </pre>
     *
     * <code>UNKNOWN = 0;</code>
     */
    UNKNOWN(0),
    /**
     * <pre>
     * android
     * </pre>
     *
     * <code>ANDROID = 1;</code>
     */
    ANDROID(1),
    /**
     * <pre>
     * ios
     * </pre>
     *
     * <code>IOS = 2;</code>
     */
    IOS(2),
    ;

    /**
     * <pre>
     * 类型未知
     * </pre>
     *
     * <code>UNKNOWN = 0;</code>
     */
    public static final int UNKNOWN_VALUE = 0;
    /**
     * <pre>
     * android
     * </pre>
     *
     * <code>ANDROID = 1;</code>
     */
    public static final int ANDROID_VALUE = 1;
    /**
     * <pre>
     * ios
     * </pre>
     *
     * <code>IOS = 2;</code>
     */
    public static final int IOS_VALUE = 2;


    public final int getNumber() {
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static OsType valueOf(int value) {
      return forNumber(value);
    }

    public static OsType forNumber(int value) {
      switch (value) {
        case 0: return UNKNOWN;
        case 1: return ANDROID;
        case 2: return IOS;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<OsType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        OsType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<OsType>() {
            public OsType findValueByNumber(int number) {
              return OsType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return BaiduRtaRequest.getDescriptor().getEnumTypes().get(1);
    }

    private static final OsType[] VALUES = values();

    public static OsType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private OsType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:OsType)
  }

  /**
   * Protobuf enum {@code AndroidDeviceIdType}
   */
  public enum AndroidDeviceIdType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>IMEI = 1;</code>
     */
    IMEI(1),
    /**
     * <code>OAID = 2;</code>
     */
    OAID(2),
    ;

    /**
     * <code>IMEI = 1;</code>
     */
    public static final int IMEI_VALUE = 1;
    /**
     * <code>OAID = 2;</code>
     */
    public static final int OAID_VALUE = 2;


    public final int getNumber() {
      return value;
    }

    /**
     * @deprecated Use {@link #forNumber(int)} instead.
     */
    @Deprecated
    public static AndroidDeviceIdType valueOf(int value) {
      return forNumber(value);
    }

    public static AndroidDeviceIdType forNumber(int value) {
      switch (value) {
        case 1: return IMEI;
        case 2: return OAID;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<AndroidDeviceIdType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static final com.google.protobuf.Internal.EnumLiteMap<
        AndroidDeviceIdType> internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<AndroidDeviceIdType>() {
            public AndroidDeviceIdType findValueByNumber(int number) {
              return AndroidDeviceIdType.forNumber(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(ordinal());
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return BaiduRtaRequest.getDescriptor().getEnumTypes().get(2);
    }

    private static final AndroidDeviceIdType[] VALUES = values();

    public static AndroidDeviceIdType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int value;

    private AndroidDeviceIdType(int value) {
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:AndroidDeviceIdType)
  }

  public interface RtaApiRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:RtaApiRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 请求唯一标识
     * </pre>
     *
     * <code>required uint64 qid = 1;</code>
     */
    boolean hasQid();
    /**
     * <pre>
     * 请求唯一标识
     * </pre>
     *
     * <code>required uint64 qid = 1;</code>
     */
    long getQid();

    /**
     * <pre>
     * 操作系统类型:
     * </pre>
     *
     * <code>required .OsType os_type = 2;</code>
     */
    boolean hasOsType();
    /**
     * <pre>
     * 操作系统类型:
     * </pre>
     *
     * <code>required .OsType os_type = 2;</code>
     */
    OsType getOsType();

    /**
     * <pre>
     * 该字段已停止下发，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>required string device_id_md5 = 3;</code>
     */
    boolean hasDeviceIdMd5();
    /**
     * <pre>
     * 该字段已停止下发，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>required string device_id_md5 = 3;</code>
     */
    String getDeviceIdMd5();
    /**
     * <pre>
     * 该字段已停止下发，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>required string device_id_md5 = 3;</code>
     */
    com.google.protobuf.ByteString
        getDeviceIdMd5Bytes();

    /**
     * <pre>
     * 调用的时间戳, 1970-01-01后的毫秒数
     * </pre>
     *
     * <code>required uint64 sign_time = 4;</code>
     */
    boolean hasSignTime();
    /**
     * <pre>
     * 调用的时间戳, 1970-01-01后的毫秒数
     * </pre>
     *
     * <code>required uint64 sign_time = 4;</code>
     */
    long getSignTime();

    /**
     * <pre>
     * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
     * </pre>
     *
     * <code>required string token = 5;</code>
     */
    boolean hasToken();
    /**
     * <pre>
     * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
     * </pre>
     *
     * <code>required string token = 5;</code>
     */
    String getToken();
    /**
     * <pre>
     * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
     * </pre>
     *
     * <code>required string token = 5;</code>
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    /**
     * <pre>
     * 该字段已停止使用，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
     */
    boolean hasAndroidDeviceType();
    /**
     * <pre>
     * 该字段已停止使用，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
     */
    AndroidDeviceIdType getAndroidDeviceType();

    /**
     * <pre>
     * 用户设备信息
     * </pre>
     *
     * <code>optional .DeviceInfo device_info = 8;</code>
     */
    boolean hasDeviceInfo();
    /**
     * <pre>
     * 用户设备信息
     * </pre>
     *
     * <code>optional .DeviceInfo device_info = 8;</code>
     */
    DeviceInfo getDeviceInfo();
    /**
     * <pre>
     * 用户设备信息
     * </pre>
     *
     * <code>optional .DeviceInfo device_info = 8;</code>
     */
    DeviceInfoOrBuilder getDeviceInfoOrBuilder();

    /**
     * <pre>
     * 流量类型
     * </pre>
     *
     * <code>optional .FlowType flow_type = 9;</code>
     */
    boolean hasFlowType();
    /**
     * <pre>
     * 流量类型
     * </pre>
     *
     * <code>optional .FlowType flow_type = 9;</code>
     */
    FlowType getFlowType();

    /**
     * <pre>
     *媒体id
     * </pre>
     *
     * <code>optional uint64 media_id = 11;</code>
     */
    boolean hasMediaId();
    /**
     * <pre>
     *媒体id
     * </pre>
     *
     * <code>optional uint64 media_id = 11;</code>
     */
    long getMediaId();

    /**
     * <pre>
     *系统主版本号，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint32 os_major_version = 14;</code>
     */
    boolean hasOsMajorVersion();
    /**
     * <pre>
     *系统主版本号，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint32 os_major_version = 14;</code>
     */
    int getOsMajorVersion();

    /**
     * <pre>
     *标记请求是否为DPA请求(需要商品)
     * </pre>
     *
     * <code>optional bool is_dpa_request = 15;</code>
     */
    boolean hasIsDpaRequest();
    /**
     * <pre>
     *标记请求是否为DPA请求(需要商品)
     * </pre>
     *
     * <code>optional bool is_dpa_request = 15;</code>
     */
    boolean getIsDpaRequest();

    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 16;</code>
     */
    boolean hasPrefetchDate();
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 16;</code>
     */
    String getPrefetchDate();
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 16;</code>
     */
    com.google.protobuf.ByteString
        getPrefetchDateBytes();

    /**
     * <pre>
     *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint64 timestamp = 17;</code>
     */
    boolean hasTimestamp();
    /**
     * <pre>
     *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint64 timestamp = 17;</code>
     */
    long getTimestamp();

    /**
     * <pre>
     * 媒体行业
     * </pre>
     *
     * <code>optional uint32 bes_media_group = 18;</code>
     */
    boolean hasBesMediaGroup();
    /**
     * <pre>
     * 媒体行业
     * </pre>
     *
     * <code>optional uint32 bes_media_group = 18;</code>
     */
    int getBesMediaGroup();

    /**
     * <pre>
     * 客户实验号，该字段需申请流量分桶能力后再下发
     * </pre>
     *
     * <code>optional uint32 exp_id = 20;</code>
     */
    boolean hasExpId();
    /**
     * <pre>
     * 客户实验号，该字段需申请流量分桶能力后再下发
     * </pre>
     *
     * <code>optional uint32 exp_id = 20;</code>
     */
    int getExpId();
  }
  /**
   * Protobuf type {@code RtaApiRequest}
   */
  public  static final class RtaApiRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:RtaApiRequest)
      RtaApiRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RtaApiRequest.newBuilder() to construct.
    private RtaApiRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RtaApiRequest() {
      qid_ = 0L;
      osType_ = 0;
      deviceIdMd5_ = "";
      signTime_ = 0L;
      token_ = "";
      androidDeviceType_ = 1;
      flowType_ = 1;
      mediaId_ = 0L;
      osMajorVersion_ = 0;
      isDpaRequest_ = false;
      prefetchDate_ = "";
      timestamp_ = 0L;
      besMediaGroup_ = 0;
      expId_ = 0;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RtaApiRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              qid_ = input.readUInt64();
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
              OsType value = OsType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                osType_ = rawValue;
              }
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              deviceIdMd5_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              signTime_ = input.readUInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              token_ = bs;
              break;
            }
            case 48: {
              int rawValue = input.readEnum();
              AndroidDeviceIdType value = AndroidDeviceIdType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(6, rawValue);
              } else {
                bitField0_ |= 0x00000020;
                androidDeviceType_ = rawValue;
              }
              break;
            }
            case 66: {
              DeviceInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) == 0x00000040)) {
                subBuilder = deviceInfo_.toBuilder();
              }
              deviceInfo_ = input.readMessage(DeviceInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(deviceInfo_);
                deviceInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 72: {
              int rawValue = input.readEnum();
              FlowType value = FlowType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(9, rawValue);
              } else {
                bitField0_ |= 0x00000080;
                flowType_ = rawValue;
              }
              break;
            }
            case 88: {
              bitField0_ |= 0x00000100;
              mediaId_ = input.readUInt64();
              break;
            }
            case 112: {
              bitField0_ |= 0x00000200;
              osMajorVersion_ = input.readUInt32();
              break;
            }
            case 120: {
              bitField0_ |= 0x00000400;
              isDpaRequest_ = input.readBool();
              break;
            }
            case 130: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000800;
              prefetchDate_ = bs;
              break;
            }
            case 136: {
              bitField0_ |= 0x00001000;
              timestamp_ = input.readUInt64();
              break;
            }
            case 144: {
              bitField0_ |= 0x00002000;
              besMediaGroup_ = input.readUInt32();
              break;
            }
            case 160: {
              bitField0_ |= 0x00004000;
              expId_ = input.readUInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaRequest.internal_static_RtaApiRequest_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaRequest.internal_static_RtaApiRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RtaApiRequest.class, Builder.class);
    }

    private int bitField0_;
    public static final int QID_FIELD_NUMBER = 1;
    private long qid_;
    /**
     * <pre>
     * 请求唯一标识
     * </pre>
     *
     * <code>required uint64 qid = 1;</code>
     */
    public boolean hasQid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <pre>
     * 请求唯一标识
     * </pre>
     *
     * <code>required uint64 qid = 1;</code>
     */
    public long getQid() {
      return qid_;
    }

    public static final int OS_TYPE_FIELD_NUMBER = 2;
    private int osType_;
    /**
     * <pre>
     * 操作系统类型:
     * </pre>
     *
     * <code>required .OsType os_type = 2;</code>
     */
    public boolean hasOsType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     * 操作系统类型:
     * </pre>
     *
     * <code>required .OsType os_type = 2;</code>
     */
    public OsType getOsType() {
      OsType result = OsType.valueOf(osType_);
      return result == null ? OsType.UNKNOWN : result;
    }

    public static final int DEVICE_ID_MD5_FIELD_NUMBER = 3;
    private volatile Object deviceIdMd5_;
    /**
     * <pre>
     * 该字段已停止下发，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>required string device_id_md5 = 3;</code>
     */
    public boolean hasDeviceIdMd5() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <pre>
     * 该字段已停止下发，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>required string device_id_md5 = 3;</code>
     */
    public String getDeviceIdMd5() {
      Object ref = deviceIdMd5_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          deviceIdMd5_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 该字段已停止下发，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>required string device_id_md5 = 3;</code>
     */
    public com.google.protobuf.ByteString
        getDeviceIdMd5Bytes() {
      Object ref = deviceIdMd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        deviceIdMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SIGN_TIME_FIELD_NUMBER = 4;
    private long signTime_;
    /**
     * <pre>
     * 调用的时间戳, 1970-01-01后的毫秒数
     * </pre>
     *
     * <code>required uint64 sign_time = 4;</code>
     */
    public boolean hasSignTime() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <pre>
     * 调用的时间戳, 1970-01-01后的毫秒数
     * </pre>
     *
     * <code>required uint64 sign_time = 4;</code>
     */
    public long getSignTime() {
      return signTime_;
    }

    public static final int TOKEN_FIELD_NUMBER = 5;
    private volatile Object token_;
    /**
     * <pre>
     * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
     * </pre>
     *
     * <code>required string token = 5;</code>
     */
    public boolean hasToken() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <pre>
     * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
     * </pre>
     *
     * <code>required string token = 5;</code>
     */
    public String getToken() {
      Object ref = token_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
     * </pre>
     *
     * <code>required string token = 5;</code>
     */
    public com.google.protobuf.ByteString
        getTokenBytes() {
      Object ref = token_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ANDROID_DEVICE_TYPE_FIELD_NUMBER = 6;
    private int androidDeviceType_;
    /**
     * <pre>
     * 该字段已停止使用，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
     */
    public boolean hasAndroidDeviceType() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <pre>
     * 该字段已停止使用，后续直接对接device_info字段。 
     * </pre>
     *
     * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
     */
    public AndroidDeviceIdType getAndroidDeviceType() {
      AndroidDeviceIdType result = AndroidDeviceIdType.valueOf(androidDeviceType_);
      return result == null ? AndroidDeviceIdType.IMEI : result;
    }

    public static final int DEVICE_INFO_FIELD_NUMBER = 8;
    private DeviceInfo deviceInfo_;
    /**
     * <pre>
     * 用户设备信息
     * </pre>
     *
     * <code>optional .DeviceInfo device_info = 8;</code>
     */
    public boolean hasDeviceInfo() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <pre>
     * 用户设备信息
     * </pre>
     *
     * <code>optional .DeviceInfo device_info = 8;</code>
     */
    public DeviceInfo getDeviceInfo() {
      return deviceInfo_ == null ? DeviceInfo.getDefaultInstance() : deviceInfo_;
    }
    /**
     * <pre>
     * 用户设备信息
     * </pre>
     *
     * <code>optional .DeviceInfo device_info = 8;</code>
     */
    public DeviceInfoOrBuilder getDeviceInfoOrBuilder() {
      return deviceInfo_ == null ? DeviceInfo.getDefaultInstance() : deviceInfo_;
    }

    public static final int FLOW_TYPE_FIELD_NUMBER = 9;
    private int flowType_;
    /**
     * <pre>
     * 流量类型
     * </pre>
     *
     * <code>optional .FlowType flow_type = 9;</code>
     */
    public boolean hasFlowType() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <pre>
     * 流量类型
     * </pre>
     *
     * <code>optional .FlowType flow_type = 9;</code>
     */
    public FlowType getFlowType() {
      FlowType result = FlowType.valueOf(flowType_);
      return result == null ? FlowType.SHOUBAI : result;
    }

    public static final int MEDIA_ID_FIELD_NUMBER = 11;
    private long mediaId_;
    /**
     * <pre>
     *媒体id
     * </pre>
     *
     * <code>optional uint64 media_id = 11;</code>
     */
    public boolean hasMediaId() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <pre>
     *媒体id
     * </pre>
     *
     * <code>optional uint64 media_id = 11;</code>
     */
    public long getMediaId() {
      return mediaId_;
    }

    public static final int OS_MAJOR_VERSION_FIELD_NUMBER = 14;
    private int osMajorVersion_;
    /**
     * <pre>
     *系统主版本号，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint32 os_major_version = 14;</code>
     */
    public boolean hasOsMajorVersion() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <pre>
     *系统主版本号，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint32 os_major_version = 14;</code>
     */
    public int getOsMajorVersion() {
      return osMajorVersion_;
    }

    public static final int IS_DPA_REQUEST_FIELD_NUMBER = 15;
    private boolean isDpaRequest_;
    /**
     * <pre>
     *标记请求是否为DPA请求(需要商品)
     * </pre>
     *
     * <code>optional bool is_dpa_request = 15;</code>
     */
    public boolean hasIsDpaRequest() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <pre>
     *标记请求是否为DPA请求(需要商品)
     * </pre>
     *
     * <code>optional bool is_dpa_request = 15;</code>
     */
    public boolean getIsDpaRequest() {
      return isDpaRequest_;
    }

    public static final int PREFETCH_DATE_FIELD_NUMBER = 16;
    private volatile Object prefetchDate_;
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 16;</code>
     */
    public boolean hasPrefetchDate() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 16;</code>
     */
    public String getPrefetchDate() {
      Object ref = prefetchDate_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          prefetchDate_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional string prefetch_date = 16;</code>
     */
    public com.google.protobuf.ByteString
        getPrefetchDateBytes() {
      Object ref = prefetchDate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        prefetchDate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 17;
    private long timestamp_;
    /**
     * <pre>
     *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint64 timestamp = 17;</code>
     */
    public boolean hasTimestamp() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <pre>
     *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
     * </pre>
     *
     * <code>optional uint64 timestamp = 17;</code>
     */
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int BES_MEDIA_GROUP_FIELD_NUMBER = 18;
    private int besMediaGroup_;
    /**
     * <pre>
     * 媒体行业
     * </pre>
     *
     * <code>optional uint32 bes_media_group = 18;</code>
     */
    public boolean hasBesMediaGroup() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <pre>
     * 媒体行业
     * </pre>
     *
     * <code>optional uint32 bes_media_group = 18;</code>
     */
    public int getBesMediaGroup() {
      return besMediaGroup_;
    }

    public static final int EXP_ID_FIELD_NUMBER = 20;
    private int expId_;
    /**
     * <pre>
     * 客户实验号，该字段需申请流量分桶能力后再下发
     * </pre>
     *
     * <code>optional uint32 exp_id = 20;</code>
     */
    public boolean hasExpId() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <pre>
     * 客户实验号，该字段需申请流量分桶能力后再下发
     * </pre>
     *
     * <code>optional uint32 exp_id = 20;</code>
     */
    public int getExpId() {
      return expId_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasQid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasOsType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDeviceIdMd5()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSignTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasToken()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeUInt64(1, qid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeEnum(2, osType_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, deviceIdMd5_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeUInt64(4, signTime_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, token_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeEnum(6, androidDeviceType_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeMessage(8, getDeviceInfo());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeEnum(9, flowType_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeUInt64(11, mediaId_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeUInt32(14, osMajorVersion_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeBool(15, isDpaRequest_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 16, prefetchDate_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeUInt64(17, timestamp_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeUInt32(18, besMediaGroup_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeUInt32(20, expId_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, qid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, osType_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, deviceIdMd5_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, signTime_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, token_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(6, androidDeviceType_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getDeviceInfo());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(9, flowType_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, mediaId_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, osMajorVersion_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(15, isDpaRequest_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, prefetchDate_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(17, timestamp_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, besMediaGroup_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, expId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof RtaApiRequest)) {
        return super.equals(obj);
      }
      RtaApiRequest other = (RtaApiRequest) obj;

      boolean result = true;
      result = result && (hasQid() == other.hasQid());
      if (hasQid()) {
        result = result && (getQid()
            == other.getQid());
      }
      result = result && (hasOsType() == other.hasOsType());
      if (hasOsType()) {
        result = result && osType_ == other.osType_;
      }
      result = result && (hasDeviceIdMd5() == other.hasDeviceIdMd5());
      if (hasDeviceIdMd5()) {
        result = result && getDeviceIdMd5()
            .equals(other.getDeviceIdMd5());
      }
      result = result && (hasSignTime() == other.hasSignTime());
      if (hasSignTime()) {
        result = result && (getSignTime()
            == other.getSignTime());
      }
      result = result && (hasToken() == other.hasToken());
      if (hasToken()) {
        result = result && getToken()
            .equals(other.getToken());
      }
      result = result && (hasAndroidDeviceType() == other.hasAndroidDeviceType());
      if (hasAndroidDeviceType()) {
        result = result && androidDeviceType_ == other.androidDeviceType_;
      }
      result = result && (hasDeviceInfo() == other.hasDeviceInfo());
      if (hasDeviceInfo()) {
        result = result && getDeviceInfo()
            .equals(other.getDeviceInfo());
      }
      result = result && (hasFlowType() == other.hasFlowType());
      if (hasFlowType()) {
        result = result && flowType_ == other.flowType_;
      }
      result = result && (hasMediaId() == other.hasMediaId());
      if (hasMediaId()) {
        result = result && (getMediaId()
            == other.getMediaId());
      }
      result = result && (hasOsMajorVersion() == other.hasOsMajorVersion());
      if (hasOsMajorVersion()) {
        result = result && (getOsMajorVersion()
            == other.getOsMajorVersion());
      }
      result = result && (hasIsDpaRequest() == other.hasIsDpaRequest());
      if (hasIsDpaRequest()) {
        result = result && (getIsDpaRequest()
            == other.getIsDpaRequest());
      }
      result = result && (hasPrefetchDate() == other.hasPrefetchDate());
      if (hasPrefetchDate()) {
        result = result && getPrefetchDate()
            .equals(other.getPrefetchDate());
      }
      result = result && (hasTimestamp() == other.hasTimestamp());
      if (hasTimestamp()) {
        result = result && (getTimestamp()
            == other.getTimestamp());
      }
      result = result && (hasBesMediaGroup() == other.hasBesMediaGroup());
      if (hasBesMediaGroup()) {
        result = result && (getBesMediaGroup()
            == other.getBesMediaGroup());
      }
      result = result && (hasExpId() == other.hasExpId());
      if (hasExpId()) {
        result = result && (getExpId()
            == other.getExpId());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQid()) {
        hash = (37 * hash) + QID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getQid());
      }
      if (hasOsType()) {
        hash = (37 * hash) + OS_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + osType_;
      }
      if (hasDeviceIdMd5()) {
        hash = (37 * hash) + DEVICE_ID_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getDeviceIdMd5().hashCode();
      }
      if (hasSignTime()) {
        hash = (37 * hash) + SIGN_TIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSignTime());
      }
      if (hasToken()) {
        hash = (37 * hash) + TOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getToken().hashCode();
      }
      if (hasAndroidDeviceType()) {
        hash = (37 * hash) + ANDROID_DEVICE_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + androidDeviceType_;
      }
      if (hasDeviceInfo()) {
        hash = (37 * hash) + DEVICE_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getDeviceInfo().hashCode();
      }
      if (hasFlowType()) {
        hash = (37 * hash) + FLOW_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + flowType_;
      }
      if (hasMediaId()) {
        hash = (37 * hash) + MEDIA_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMediaId());
      }
      if (hasOsMajorVersion()) {
        hash = (37 * hash) + OS_MAJOR_VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getOsMajorVersion();
      }
      if (hasIsDpaRequest()) {
        hash = (37 * hash) + IS_DPA_REQUEST_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsDpaRequest());
      }
      if (hasPrefetchDate()) {
        hash = (37 * hash) + PREFETCH_DATE_FIELD_NUMBER;
        hash = (53 * hash) + getPrefetchDate().hashCode();
      }
      if (hasTimestamp()) {
        hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTimestamp());
      }
      if (hasBesMediaGroup()) {
        hash = (37 * hash) + BES_MEDIA_GROUP_FIELD_NUMBER;
        hash = (53 * hash) + getBesMediaGroup();
      }
      if (hasExpId()) {
        hash = (37 * hash) + EXP_ID_FIELD_NUMBER;
        hash = (53 * hash) + getExpId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static RtaApiRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaApiRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaApiRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaApiRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaApiRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RtaApiRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RtaApiRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaApiRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaApiRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static RtaApiRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static RtaApiRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static RtaApiRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(RtaApiRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code RtaApiRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:RtaApiRequest)
        RtaApiRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaRequest.internal_static_RtaApiRequest_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaRequest.internal_static_RtaApiRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RtaApiRequest.class, Builder.class);
      }

      // Construct using BaiduRtaRequest.RtaApiRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDeviceInfoFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        qid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        osType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        deviceIdMd5_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        signTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        androidDeviceType_ = 1;
        bitField0_ = (bitField0_ & ~0x00000020);
        if (deviceInfoBuilder_ == null) {
          deviceInfo_ = null;
        } else {
          deviceInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        flowType_ = 1;
        bitField0_ = (bitField0_ & ~0x00000080);
        mediaId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        osMajorVersion_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        isDpaRequest_ = false;
        bitField0_ = (bitField0_ & ~0x00000400);
        prefetchDate_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        timestamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00001000);
        besMediaGroup_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        expId_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaRequest.internal_static_RtaApiRequest_descriptor;
      }

      public RtaApiRequest getDefaultInstanceForType() {
        return RtaApiRequest.getDefaultInstance();
      }

      public RtaApiRequest build() {
        RtaApiRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RtaApiRequest buildPartial() {
        RtaApiRequest result = new RtaApiRequest(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.qid_ = qid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.osType_ = osType_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.deviceIdMd5_ = deviceIdMd5_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.signTime_ = signTime_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.androidDeviceType_ = androidDeviceType_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        if (deviceInfoBuilder_ == null) {
          result.deviceInfo_ = deviceInfo_;
        } else {
          result.deviceInfo_ = deviceInfoBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.flowType_ = flowType_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.mediaId_ = mediaId_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.osMajorVersion_ = osMajorVersion_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.isDpaRequest_ = isDpaRequest_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.prefetchDate_ = prefetchDate_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.timestamp_ = timestamp_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.besMediaGroup_ = besMediaGroup_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.expId_ = expId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RtaApiRequest) {
          return mergeFrom((RtaApiRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RtaApiRequest other) {
        if (other == RtaApiRequest.getDefaultInstance()) return this;
        if (other.hasQid()) {
          setQid(other.getQid());
        }
        if (other.hasOsType()) {
          setOsType(other.getOsType());
        }
        if (other.hasDeviceIdMd5()) {
          bitField0_ |= 0x00000004;
          deviceIdMd5_ = other.deviceIdMd5_;
          onChanged();
        }
        if (other.hasSignTime()) {
          setSignTime(other.getSignTime());
        }
        if (other.hasToken()) {
          bitField0_ |= 0x00000010;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasAndroidDeviceType()) {
          setAndroidDeviceType(other.getAndroidDeviceType());
        }
        if (other.hasDeviceInfo()) {
          mergeDeviceInfo(other.getDeviceInfo());
        }
        if (other.hasFlowType()) {
          setFlowType(other.getFlowType());
        }
        if (other.hasMediaId()) {
          setMediaId(other.getMediaId());
        }
        if (other.hasOsMajorVersion()) {
          setOsMajorVersion(other.getOsMajorVersion());
        }
        if (other.hasIsDpaRequest()) {
          setIsDpaRequest(other.getIsDpaRequest());
        }
        if (other.hasPrefetchDate()) {
          bitField0_ |= 0x00000800;
          prefetchDate_ = other.prefetchDate_;
          onChanged();
        }
        if (other.hasTimestamp()) {
          setTimestamp(other.getTimestamp());
        }
        if (other.hasBesMediaGroup()) {
          setBesMediaGroup(other.getBesMediaGroup());
        }
        if (other.hasExpId()) {
          setExpId(other.getExpId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        if (!hasQid()) {
          return false;
        }
        if (!hasOsType()) {
          return false;
        }
        if (!hasDeviceIdMd5()) {
          return false;
        }
        if (!hasSignTime()) {
          return false;
        }
        if (!hasToken()) {
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RtaApiRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RtaApiRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long qid_ ;
      /**
       * <pre>
       * 请求唯一标识
       * </pre>
       *
       * <code>required uint64 qid = 1;</code>
       */
      public boolean hasQid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <pre>
       * 请求唯一标识
       * </pre>
       *
       * <code>required uint64 qid = 1;</code>
       */
      public long getQid() {
        return qid_;
      }
      /**
       * <pre>
       * 请求唯一标识
       * </pre>
       *
       * <code>required uint64 qid = 1;</code>
       */
      public Builder setQid(long value) {
        bitField0_ |= 0x00000001;
        qid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求唯一标识
       * </pre>
       *
       * <code>required uint64 qid = 1;</code>
       */
      public Builder clearQid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        qid_ = 0L;
        onChanged();
        return this;
      }

      private int osType_ = 0;
      /**
       * <pre>
       * 操作系统类型:
       * </pre>
       *
       * <code>required .OsType os_type = 2;</code>
       */
      public boolean hasOsType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       * 操作系统类型:
       * </pre>
       *
       * <code>required .OsType os_type = 2;</code>
       */
      public OsType getOsType() {
        OsType result = OsType.valueOf(osType_);
        return result == null ? OsType.UNKNOWN : result;
      }
      /**
       * <pre>
       * 操作系统类型:
       * </pre>
       *
       * <code>required .OsType os_type = 2;</code>
       */
      public Builder setOsType(OsType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        osType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作系统类型:
       * </pre>
       *
       * <code>required .OsType os_type = 2;</code>
       */
      public Builder clearOsType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        osType_ = 0;
        onChanged();
        return this;
      }

      private Object deviceIdMd5_ = "";
      /**
       * <pre>
       * 该字段已停止下发，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>required string device_id_md5 = 3;</code>
       */
      public boolean hasDeviceIdMd5() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <pre>
       * 该字段已停止下发，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>required string device_id_md5 = 3;</code>
       */
      public String getDeviceIdMd5() {
        Object ref = deviceIdMd5_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            deviceIdMd5_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 该字段已停止下发，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>required string device_id_md5 = 3;</code>
       */
      public com.google.protobuf.ByteString
          getDeviceIdMd5Bytes() {
        Object ref = deviceIdMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          deviceIdMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 该字段已停止下发，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>required string device_id_md5 = 3;</code>
       */
      public Builder setDeviceIdMd5(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        deviceIdMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 该字段已停止下发，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>required string device_id_md5 = 3;</code>
       */
      public Builder clearDeviceIdMd5() {
        bitField0_ = (bitField0_ & ~0x00000004);
        deviceIdMd5_ = getDefaultInstance().getDeviceIdMd5();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 该字段已停止下发，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>required string device_id_md5 = 3;</code>
       */
      public Builder setDeviceIdMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        deviceIdMd5_ = value;
        onChanged();
        return this;
      }

      private long signTime_ ;
      /**
       * <pre>
       * 调用的时间戳, 1970-01-01后的毫秒数
       * </pre>
       *
       * <code>required uint64 sign_time = 4;</code>
       */
      public boolean hasSignTime() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <pre>
       * 调用的时间戳, 1970-01-01后的毫秒数
       * </pre>
       *
       * <code>required uint64 sign_time = 4;</code>
       */
      public long getSignTime() {
        return signTime_;
      }
      /**
       * <pre>
       * 调用的时间戳, 1970-01-01后的毫秒数
       * </pre>
       *
       * <code>required uint64 sign_time = 4;</code>
       */
      public Builder setSignTime(long value) {
        bitField0_ |= 0x00000008;
        signTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 调用的时间戳, 1970-01-01后的毫秒数
       * </pre>
       *
       * <code>required uint64 sign_time = 4;</code>
       */
      public Builder clearSignTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        signTime_ = 0L;
        onChanged();
        return this;
      }

      private Object token_ = "";
      /**
       * <pre>
       * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
       * </pre>
       *
       * <code>required string token = 5;</code>
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <pre>
       * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
       * </pre>
       *
       * <code>required string token = 5;</code>
       */
      public String getToken() {
        Object ref = token_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            token_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
       * </pre>
       *
       * <code>required string token = 5;</code>
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
       * </pre>
       *
       * <code>required string token = 5;</code>
       */
      public Builder setToken(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
       * </pre>
       *
       * <code>required string token = 5;</code>
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000010);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 验证令牌，其值为md5(qid+sign_time+客户独有标识),开屏为客户url的md5
       * </pre>
       *
       * <code>required string token = 5;</code>
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        token_ = value;
        onChanged();
        return this;
      }

      private int androidDeviceType_ = 1;
      /**
       * <pre>
       * 该字段已停止使用，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
       */
      public boolean hasAndroidDeviceType() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <pre>
       * 该字段已停止使用，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
       */
      public AndroidDeviceIdType getAndroidDeviceType() {
        AndroidDeviceIdType result = AndroidDeviceIdType.valueOf(androidDeviceType_);
        return result == null ? AndroidDeviceIdType.IMEI : result;
      }
      /**
       * <pre>
       * 该字段已停止使用，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
       */
      public Builder setAndroidDeviceType(AndroidDeviceIdType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000020;
        androidDeviceType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 该字段已停止使用，后续直接对接device_info字段。 
       * </pre>
       *
       * <code>optional .AndroidDeviceIdType android_device_type = 6;</code>
       */
      public Builder clearAndroidDeviceType() {
        bitField0_ = (bitField0_ & ~0x00000020);
        androidDeviceType_ = 1;
        onChanged();
        return this;
      }

      private DeviceInfo deviceInfo_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          DeviceInfo, DeviceInfo.Builder, DeviceInfoOrBuilder> deviceInfoBuilder_;
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public boolean hasDeviceInfo() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public DeviceInfo getDeviceInfo() {
        if (deviceInfoBuilder_ == null) {
          return deviceInfo_ == null ? DeviceInfo.getDefaultInstance() : deviceInfo_;
        } else {
          return deviceInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public Builder setDeviceInfo(DeviceInfo value) {
        if (deviceInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          deviceInfo_ = value;
          onChanged();
        } else {
          deviceInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public Builder setDeviceInfo(
          DeviceInfo.Builder builderForValue) {
        if (deviceInfoBuilder_ == null) {
          deviceInfo_ = builderForValue.build();
          onChanged();
        } else {
          deviceInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public Builder mergeDeviceInfo(DeviceInfo value) {
        if (deviceInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000040) == 0x00000040) &&
              deviceInfo_ != null &&
              deviceInfo_ != DeviceInfo.getDefaultInstance()) {
            deviceInfo_ =
              DeviceInfo.newBuilder(deviceInfo_).mergeFrom(value).buildPartial();
          } else {
            deviceInfo_ = value;
          }
          onChanged();
        } else {
          deviceInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public Builder clearDeviceInfo() {
        if (deviceInfoBuilder_ == null) {
          deviceInfo_ = null;
          onChanged();
        } else {
          deviceInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public DeviceInfo.Builder getDeviceInfoBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getDeviceInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      public DeviceInfoOrBuilder getDeviceInfoOrBuilder() {
        if (deviceInfoBuilder_ != null) {
          return deviceInfoBuilder_.getMessageOrBuilder();
        } else {
          return deviceInfo_ == null ?
              DeviceInfo.getDefaultInstance() : deviceInfo_;
        }
      }
      /**
       * <pre>
       * 用户设备信息
       * </pre>
       *
       * <code>optional .DeviceInfo device_info = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          DeviceInfo, DeviceInfo.Builder, DeviceInfoOrBuilder>
          getDeviceInfoFieldBuilder() {
        if (deviceInfoBuilder_ == null) {
          deviceInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              DeviceInfo, DeviceInfo.Builder, DeviceInfoOrBuilder>(
                  getDeviceInfo(),
                  getParentForChildren(),
                  isClean());
          deviceInfo_ = null;
        }
        return deviceInfoBuilder_;
      }

      private int flowType_ = 1;
      /**
       * <pre>
       * 流量类型
       * </pre>
       *
       * <code>optional .FlowType flow_type = 9;</code>
       */
      public boolean hasFlowType() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <pre>
       * 流量类型
       * </pre>
       *
       * <code>optional .FlowType flow_type = 9;</code>
       */
      public FlowType getFlowType() {
        FlowType result = FlowType.valueOf(flowType_);
        return result == null ? FlowType.SHOUBAI : result;
      }
      /**
       * <pre>
       * 流量类型
       * </pre>
       *
       * <code>optional .FlowType flow_type = 9;</code>
       */
      public Builder setFlowType(FlowType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000080;
        flowType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 流量类型
       * </pre>
       *
       * <code>optional .FlowType flow_type = 9;</code>
       */
      public Builder clearFlowType() {
        bitField0_ = (bitField0_ & ~0x00000080);
        flowType_ = 1;
        onChanged();
        return this;
      }

      private long mediaId_ ;
      /**
       * <pre>
       *媒体id
       * </pre>
       *
       * <code>optional uint64 media_id = 11;</code>
       */
      public boolean hasMediaId() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <pre>
       *媒体id
       * </pre>
       *
       * <code>optional uint64 media_id = 11;</code>
       */
      public long getMediaId() {
        return mediaId_;
      }
      /**
       * <pre>
       *媒体id
       * </pre>
       *
       * <code>optional uint64 media_id = 11;</code>
       */
      public Builder setMediaId(long value) {
        bitField0_ |= 0x00000100;
        mediaId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *媒体id
       * </pre>
       *
       * <code>optional uint64 media_id = 11;</code>
       */
      public Builder clearMediaId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        mediaId_ = 0L;
        onChanged();
        return this;
      }

      private int osMajorVersion_ ;
      /**
       * <pre>
       *系统主版本号，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint32 os_major_version = 14;</code>
       */
      public boolean hasOsMajorVersion() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <pre>
       *系统主版本号，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint32 os_major_version = 14;</code>
       */
      public int getOsMajorVersion() {
        return osMajorVersion_;
      }
      /**
       * <pre>
       *系统主版本号，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint32 os_major_version = 14;</code>
       */
      public Builder setOsMajorVersion(int value) {
        bitField0_ |= 0x00000200;
        osMajorVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *系统主版本号，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint32 os_major_version = 14;</code>
       */
      public Builder clearOsMajorVersion() {
        bitField0_ = (bitField0_ & ~0x00000200);
        osMajorVersion_ = 0;
        onChanged();
        return this;
      }

      private boolean isDpaRequest_ ;
      /**
       * <pre>
       *标记请求是否为DPA请求(需要商品)
       * </pre>
       *
       * <code>optional bool is_dpa_request = 15;</code>
       */
      public boolean hasIsDpaRequest() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <pre>
       *标记请求是否为DPA请求(需要商品)
       * </pre>
       *
       * <code>optional bool is_dpa_request = 15;</code>
       */
      public boolean getIsDpaRequest() {
        return isDpaRequest_;
      }
      /**
       * <pre>
       *标记请求是否为DPA请求(需要商品)
       * </pre>
       *
       * <code>optional bool is_dpa_request = 15;</code>
       */
      public Builder setIsDpaRequest(boolean value) {
        bitField0_ |= 0x00000400;
        isDpaRequest_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *标记请求是否为DPA请求(需要商品)
       * </pre>
       *
       * <code>optional bool is_dpa_request = 15;</code>
       */
      public Builder clearIsDpaRequest() {
        bitField0_ = (bitField0_ & ~0x00000400);
        isDpaRequest_ = false;
        onChanged();
        return this;
      }

      private Object prefetchDate_ = "";
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 16;</code>
       */
      public boolean hasPrefetchDate() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 16;</code>
       */
      public String getPrefetchDate() {
        Object ref = prefetchDate_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            prefetchDate_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 16;</code>
       */
      public com.google.protobuf.ByteString
          getPrefetchDateBytes() {
        Object ref = prefetchDate_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          prefetchDate_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 16;</code>
       */
      public Builder setPrefetchDate(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        prefetchDate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 16;</code>
       */
      public Builder clearPrefetchDate() {
        bitField0_ = (bitField0_ & ~0x00000800);
        prefetchDate_ = getDefaultInstance().getPrefetchDate();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *表示在未来发生投放的某个时间点，用于预请求；如果没有，则指当天，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional string prefetch_date = 16;</code>
       */
      public Builder setPrefetchDateBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        prefetchDate_ = value;
        onChanged();
        return this;
      }

      private long timestamp_ ;
      /**
       * <pre>
       *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint64 timestamp = 17;</code>
       */
      public boolean hasTimestamp() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <pre>
       *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint64 timestamp = 17;</code>
       */
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <pre>
       *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint64 timestamp = 17;</code>
       */
      public Builder setTimestamp(long value) {
        bitField0_ |= 0x00001000;
        timestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *请求的时间戳，单位s，仅用于品牌广告百度系开屏流量使用
       * </pre>
       *
       * <code>optional uint64 timestamp = 17;</code>
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00001000);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private int besMediaGroup_ ;
      /**
       * <pre>
       * 媒体行业
       * </pre>
       *
       * <code>optional uint32 bes_media_group = 18;</code>
       */
      public boolean hasBesMediaGroup() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <pre>
       * 媒体行业
       * </pre>
       *
       * <code>optional uint32 bes_media_group = 18;</code>
       */
      public int getBesMediaGroup() {
        return besMediaGroup_;
      }
      /**
       * <pre>
       * 媒体行业
       * </pre>
       *
       * <code>optional uint32 bes_media_group = 18;</code>
       */
      public Builder setBesMediaGroup(int value) {
        bitField0_ |= 0x00002000;
        besMediaGroup_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 媒体行业
       * </pre>
       *
       * <code>optional uint32 bes_media_group = 18;</code>
       */
      public Builder clearBesMediaGroup() {
        bitField0_ = (bitField0_ & ~0x00002000);
        besMediaGroup_ = 0;
        onChanged();
        return this;
      }

      private int expId_ ;
      /**
       * <pre>
       * 客户实验号，该字段需申请流量分桶能力后再下发
       * </pre>
       *
       * <code>optional uint32 exp_id = 20;</code>
       */
      public boolean hasExpId() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <pre>
       * 客户实验号，该字段需申请流量分桶能力后再下发
       * </pre>
       *
       * <code>optional uint32 exp_id = 20;</code>
       */
      public int getExpId() {
        return expId_;
      }
      /**
       * <pre>
       * 客户实验号，该字段需申请流量分桶能力后再下发
       * </pre>
       *
       * <code>optional uint32 exp_id = 20;</code>
       */
      public Builder setExpId(int value) {
        bitField0_ |= 0x00004000;
        expId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户实验号，该字段需申请流量分桶能力后再下发
       * </pre>
       *
       * <code>optional uint32 exp_id = 20;</code>
       */
      public Builder clearExpId() {
        bitField0_ = (bitField0_ & ~0x00004000);
        expId_ = 0;
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:RtaApiRequest)
    }

    // @@protoc_insertion_point(class_scope:RtaApiRequest)
    private static final RtaApiRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new RtaApiRequest();
    }

    public static RtaApiRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<RtaApiRequest>
        PARSER = new com.google.protobuf.AbstractParser<RtaApiRequest>() {
      public RtaApiRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RtaApiRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RtaApiRequest> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<RtaApiRequest> getParserForType() {
      return PARSER;
    }

    public RtaApiRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CaidInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CaidInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes caid = 1;</code>
     */
    boolean hasCaid();
    /**
     * <code>optional bytes caid = 1;</code>
     */
    com.google.protobuf.ByteString getCaid();

    /**
     * <pre>
     *caid版本
     * </pre>
     *
     * <code>optional bytes caid_version = 2;</code>
     */
    boolean hasCaidVersion();
    /**
     * <pre>
     *caid版本
     * </pre>
     *
     * <code>optional bytes caid_version = 2;</code>
     */
    com.google.protobuf.ByteString getCaidVersion();
  }
  /**
   * Protobuf type {@code CaidInfo}
   */
  public  static final class CaidInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:CaidInfo)
      CaidInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CaidInfo.newBuilder() to construct.
    private CaidInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CaidInfo() {
      caid_ = com.google.protobuf.ByteString.EMPTY;
      caidVersion_ = com.google.protobuf.ByteString.EMPTY;
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CaidInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              caid_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              caidVersion_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaRequest.internal_static_CaidInfo_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaRequest.internal_static_CaidInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              CaidInfo.class, Builder.class);
    }

    private int bitField0_;
    public static final int CAID_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString caid_;
    /**
     * <code>optional bytes caid = 1;</code>
     */
    public boolean hasCaid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes caid = 1;</code>
     */
    public com.google.protobuf.ByteString getCaid() {
      return caid_;
    }

    public static final int CAID_VERSION_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString caidVersion_;
    /**
     * <pre>
     *caid版本
     * </pre>
     *
     * <code>optional bytes caid_version = 2;</code>
     */
    public boolean hasCaidVersion() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <pre>
     *caid版本
     * </pre>
     *
     * <code>optional bytes caid_version = 2;</code>
     */
    public com.google.protobuf.ByteString getCaidVersion() {
      return caidVersion_;
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, caid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, caidVersion_);
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, caid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, caidVersion_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof CaidInfo)) {
        return super.equals(obj);
      }
      CaidInfo other = (CaidInfo) obj;

      boolean result = true;
      result = result && (hasCaid() == other.hasCaid());
      if (hasCaid()) {
        result = result && getCaid()
            .equals(other.getCaid());
      }
      result = result && (hasCaidVersion() == other.hasCaidVersion());
      if (hasCaidVersion()) {
        result = result && getCaidVersion()
            .equals(other.getCaidVersion());
      }
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCaid()) {
        hash = (37 * hash) + CAID_FIELD_NUMBER;
        hash = (53 * hash) + getCaid().hashCode();
      }
      if (hasCaidVersion()) {
        hash = (37 * hash) + CAID_VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getCaidVersion().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static CaidInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CaidInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CaidInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CaidInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CaidInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CaidInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CaidInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CaidInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static CaidInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static CaidInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static CaidInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static CaidInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(CaidInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code CaidInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CaidInfo)
        CaidInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaRequest.internal_static_CaidInfo_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaRequest.internal_static_CaidInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                CaidInfo.class, Builder.class);
      }

      // Construct using BaiduRtaRequest.CaidInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      public Builder clear() {
        super.clear();
        caid_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        caidVersion_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaRequest.internal_static_CaidInfo_descriptor;
      }

      public CaidInfo getDefaultInstanceForType() {
        return CaidInfo.getDefaultInstance();
      }

      public CaidInfo build() {
        CaidInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public CaidInfo buildPartial() {
        CaidInfo result = new CaidInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.caid_ = caid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.caidVersion_ = caidVersion_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof CaidInfo) {
          return mergeFrom((CaidInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(CaidInfo other) {
        if (other == CaidInfo.getDefaultInstance()) return this;
        if (other.hasCaid()) {
          setCaid(other.getCaid());
        }
        if (other.hasCaidVersion()) {
          setCaidVersion(other.getCaidVersion());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        CaidInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (CaidInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString caid_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes caid = 1;</code>
       */
      public boolean hasCaid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes caid = 1;</code>
       */
      public com.google.protobuf.ByteString getCaid() {
        return caid_;
      }
      /**
       * <code>optional bytes caid = 1;</code>
       */
      public Builder setCaid(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        caid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes caid = 1;</code>
       */
      public Builder clearCaid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        caid_ = getDefaultInstance().getCaid();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString caidVersion_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       *caid版本
       * </pre>
       *
       * <code>optional bytes caid_version = 2;</code>
       */
      public boolean hasCaidVersion() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <pre>
       *caid版本
       * </pre>
       *
       * <code>optional bytes caid_version = 2;</code>
       */
      public com.google.protobuf.ByteString getCaidVersion() {
        return caidVersion_;
      }
      /**
       * <pre>
       *caid版本
       * </pre>
       *
       * <code>optional bytes caid_version = 2;</code>
       */
      public Builder setCaidVersion(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        caidVersion_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *caid版本
       * </pre>
       *
       * <code>optional bytes caid_version = 2;</code>
       */
      public Builder clearCaidVersion() {
        bitField0_ = (bitField0_ & ~0x00000002);
        caidVersion_ = getDefaultInstance().getCaidVersion();
        onChanged();
        return this;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:CaidInfo)
    }

    // @@protoc_insertion_point(class_scope:CaidInfo)
    private static final CaidInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new CaidInfo();
    }

    public static CaidInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<CaidInfo>
        PARSER = new com.google.protobuf.AbstractParser<CaidInfo>() {
      public CaidInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CaidInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CaidInfo> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<CaidInfo> getParserForType() {
      return PARSER;
    }

    public CaidInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DeviceInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DeviceInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes idfa_md5 = 1;</code>
     */
    boolean hasIdfaMd5();
    /**
     * <code>optional bytes idfa_md5 = 1;</code>
     */
    com.google.protobuf.ByteString getIdfaMd5();

    /**
     * <code>optional bytes imei_md5 = 2;</code>
     */
    boolean hasImeiMd5();
    /**
     * <code>optional bytes imei_md5 = 2;</code>
     */
    com.google.protobuf.ByteString getImeiMd5();

    /**
     * <code>optional bytes android_id_md5 = 3;</code>
     */
    boolean hasAndroidIdMd5();
    /**
     * <code>optional bytes android_id_md5 = 3;</code>
     */
    com.google.protobuf.ByteString getAndroidIdMd5();

    /**
     * <code>optional bytes oaid_md5 = 4;</code>
     */
    boolean hasOaidMd5();
    /**
     * <code>optional bytes oaid_md5 = 4;</code>
     */
    com.google.protobuf.ByteString getOaidMd5();

    /**
     * <code>optional bytes oaid = 7;</code>
     */
    boolean hasOaid();
    /**
     * <code>optional bytes oaid = 7;</code>
     */
    com.google.protobuf.ByteString getOaid();

    /**
     * <code>optional bytes idfa = 8;</code>
     */
    boolean hasIdfa();
    /**
     * <code>optional bytes idfa = 8;</code>
     */
    com.google.protobuf.ByteString getIdfa();

    /**
     * <code>optional bytes imei2_md5 = 12;</code>
     */
    boolean hasImei2Md5();
    /**
     * <code>optional bytes imei2_md5 = 12;</code>
     */
    com.google.protobuf.ByteString getImei2Md5();

    /**
     * <code>optional bytes bt = 16;</code>
     */
    boolean hasBt();
    /**
     * <code>optional bytes bt = 16;</code>
     */
    com.google.protobuf.ByteString getBt();

    /**
     * <code>optional bytes ut = 17;</code>
     */
    boolean hasUt();
    /**
     * <code>optional bytes ut = 17;</code>
     */
    com.google.protobuf.ByteString getUt();

    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    java.util.List<CaidInfo>
        getCaidInfoList();
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    CaidInfo getCaidInfo(int index);
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    int getCaidInfoCount();
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    java.util.List<? extends CaidInfoOrBuilder>
        getCaidInfoOrBuilderList();
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    CaidInfoOrBuilder getCaidInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code DeviceInfo}
   */
  public  static final class DeviceInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:DeviceInfo)
      DeviceInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DeviceInfo.newBuilder() to construct.
    private DeviceInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DeviceInfo() {
      idfaMd5_ = com.google.protobuf.ByteString.EMPTY;
      imeiMd5_ = com.google.protobuf.ByteString.EMPTY;
      androidIdMd5_ = com.google.protobuf.ByteString.EMPTY;
      oaidMd5_ = com.google.protobuf.ByteString.EMPTY;
      oaid_ = com.google.protobuf.ByteString.EMPTY;
      idfa_ = com.google.protobuf.ByteString.EMPTY;
      imei2Md5_ = com.google.protobuf.ByteString.EMPTY;
      bt_ = com.google.protobuf.ByteString.EMPTY;
      ut_ = com.google.protobuf.ByteString.EMPTY;
      caidInfo_ = java.util.Collections.emptyList();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DeviceInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              idfaMd5_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              imeiMd5_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              androidIdMd5_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              oaidMd5_ = input.readBytes();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000010;
              oaid_ = input.readBytes();
              break;
            }
            case 66: {
              bitField0_ |= 0x00000020;
              idfa_ = input.readBytes();
              break;
            }
            case 98: {
              bitField0_ |= 0x00000040;
              imei2Md5_ = input.readBytes();
              break;
            }
            case 130: {
              bitField0_ |= 0x00000080;
              bt_ = input.readBytes();
              break;
            }
            case 138: {
              bitField0_ |= 0x00000100;
              ut_ = input.readBytes();
              break;
            }
            case 146: {
              if (!((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
                caidInfo_ = new java.util.ArrayList<CaidInfo>();
                mutable_bitField0_ |= 0x00000200;
              }
              caidInfo_.add(
                  input.readMessage(CaidInfo.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
          caidInfo_ = java.util.Collections.unmodifiableList(caidInfo_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return BaiduRtaRequest.internal_static_DeviceInfo_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return BaiduRtaRequest.internal_static_DeviceInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              DeviceInfo.class, Builder.class);
    }

    private int bitField0_;
    public static final int IDFA_MD5_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString idfaMd5_;
    /**
     * <code>optional bytes idfa_md5 = 1;</code>
     */
    public boolean hasIdfaMd5() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bytes idfa_md5 = 1;</code>
     */
    public com.google.protobuf.ByteString getIdfaMd5() {
      return idfaMd5_;
    }

    public static final int IMEI_MD5_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString imeiMd5_;
    /**
     * <code>optional bytes imei_md5 = 2;</code>
     */
    public boolean hasImeiMd5() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bytes imei_md5 = 2;</code>
     */
    public com.google.protobuf.ByteString getImeiMd5() {
      return imeiMd5_;
    }

    public static final int ANDROID_ID_MD5_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString androidIdMd5_;
    /**
     * <code>optional bytes android_id_md5 = 3;</code>
     */
    public boolean hasAndroidIdMd5() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional bytes android_id_md5 = 3;</code>
     */
    public com.google.protobuf.ByteString getAndroidIdMd5() {
      return androidIdMd5_;
    }

    public static final int OAID_MD5_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString oaidMd5_;
    /**
     * <code>optional bytes oaid_md5 = 4;</code>
     */
    public boolean hasOaidMd5() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional bytes oaid_md5 = 4;</code>
     */
    public com.google.protobuf.ByteString getOaidMd5() {
      return oaidMd5_;
    }

    public static final int OAID_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString oaid_;
    /**
     * <code>optional bytes oaid = 7;</code>
     */
    public boolean hasOaid() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional bytes oaid = 7;</code>
     */
    public com.google.protobuf.ByteString getOaid() {
      return oaid_;
    }

    public static final int IDFA_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString idfa_;
    /**
     * <code>optional bytes idfa = 8;</code>
     */
    public boolean hasIdfa() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional bytes idfa = 8;</code>
     */
    public com.google.protobuf.ByteString getIdfa() {
      return idfa_;
    }

    public static final int IMEI2_MD5_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString imei2Md5_;
    /**
     * <code>optional bytes imei2_md5 = 12;</code>
     */
    public boolean hasImei2Md5() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional bytes imei2_md5 = 12;</code>
     */
    public com.google.protobuf.ByteString getImei2Md5() {
      return imei2Md5_;
    }

    public static final int BT_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString bt_;
    /**
     * <code>optional bytes bt = 16;</code>
     */
    public boolean hasBt() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional bytes bt = 16;</code>
     */
    public com.google.protobuf.ByteString getBt() {
      return bt_;
    }

    public static final int UT_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString ut_;
    /**
     * <code>optional bytes ut = 17;</code>
     */
    public boolean hasUt() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional bytes ut = 17;</code>
     */
    public com.google.protobuf.ByteString getUt() {
      return ut_;
    }

    public static final int CAID_INFO_FIELD_NUMBER = 18;
    private java.util.List<CaidInfo> caidInfo_;
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    public java.util.List<CaidInfo> getCaidInfoList() {
      return caidInfo_;
    }
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    public java.util.List<? extends CaidInfoOrBuilder>
        getCaidInfoOrBuilderList() {
      return caidInfo_;
    }
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    public int getCaidInfoCount() {
      return caidInfo_.size();
    }
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    public CaidInfo getCaidInfo(int index) {
      return caidInfo_.get(index);
    }
    /**
     * <pre>
     * 多版本的caid
     * </pre>
     *
     * <code>repeated .CaidInfo caid_info = 18;</code>
     */
    public CaidInfoOrBuilder getCaidInfoOrBuilder(
        int index) {
      return caidInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, idfaMd5_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, imeiMd5_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, androidIdMd5_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, oaidMd5_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(7, oaid_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(8, idfa_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(12, imei2Md5_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(16, bt_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(17, ut_);
      }
      for (int i = 0; i < caidInfo_.size(); i++) {
        output.writeMessage(18, caidInfo_.get(i));
      }
      unknownFields.writeTo(output);
    }

    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, idfaMd5_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, imeiMd5_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, androidIdMd5_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, oaidMd5_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, oaid_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, idfa_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, imei2Md5_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, bt_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, ut_);
      }
      for (int i = 0; i < caidInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(18, caidInfo_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof DeviceInfo)) {
        return super.equals(obj);
      }
      DeviceInfo other = (DeviceInfo) obj;

      boolean result = true;
      result = result && (hasIdfaMd5() == other.hasIdfaMd5());
      if (hasIdfaMd5()) {
        result = result && getIdfaMd5()
            .equals(other.getIdfaMd5());
      }
      result = result && (hasImeiMd5() == other.hasImeiMd5());
      if (hasImeiMd5()) {
        result = result && getImeiMd5()
            .equals(other.getImeiMd5());
      }
      result = result && (hasAndroidIdMd5() == other.hasAndroidIdMd5());
      if (hasAndroidIdMd5()) {
        result = result && getAndroidIdMd5()
            .equals(other.getAndroidIdMd5());
      }
      result = result && (hasOaidMd5() == other.hasOaidMd5());
      if (hasOaidMd5()) {
        result = result && getOaidMd5()
            .equals(other.getOaidMd5());
      }
      result = result && (hasOaid() == other.hasOaid());
      if (hasOaid()) {
        result = result && getOaid()
            .equals(other.getOaid());
      }
      result = result && (hasIdfa() == other.hasIdfa());
      if (hasIdfa()) {
        result = result && getIdfa()
            .equals(other.getIdfa());
      }
      result = result && (hasImei2Md5() == other.hasImei2Md5());
      if (hasImei2Md5()) {
        result = result && getImei2Md5()
            .equals(other.getImei2Md5());
      }
      result = result && (hasBt() == other.hasBt());
      if (hasBt()) {
        result = result && getBt()
            .equals(other.getBt());
      }
      result = result && (hasUt() == other.hasUt());
      if (hasUt()) {
        result = result && getUt()
            .equals(other.getUt());
      }
      result = result && getCaidInfoList()
          .equals(other.getCaidInfoList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIdfaMd5()) {
        hash = (37 * hash) + IDFA_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getIdfaMd5().hashCode();
      }
      if (hasImeiMd5()) {
        hash = (37 * hash) + IMEI_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getImeiMd5().hashCode();
      }
      if (hasAndroidIdMd5()) {
        hash = (37 * hash) + ANDROID_ID_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getAndroidIdMd5().hashCode();
      }
      if (hasOaidMd5()) {
        hash = (37 * hash) + OAID_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getOaidMd5().hashCode();
      }
      if (hasOaid()) {
        hash = (37 * hash) + OAID_FIELD_NUMBER;
        hash = (53 * hash) + getOaid().hashCode();
      }
      if (hasIdfa()) {
        hash = (37 * hash) + IDFA_FIELD_NUMBER;
        hash = (53 * hash) + getIdfa().hashCode();
      }
      if (hasImei2Md5()) {
        hash = (37 * hash) + IMEI2_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getImei2Md5().hashCode();
      }
      if (hasBt()) {
        hash = (37 * hash) + BT_FIELD_NUMBER;
        hash = (53 * hash) + getBt().hashCode();
      }
      if (hasUt()) {
        hash = (37 * hash) + UT_FIELD_NUMBER;
        hash = (53 * hash) + getUt().hashCode();
      }
      if (getCaidInfoCount() > 0) {
        hash = (37 * hash) + CAID_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getCaidInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static DeviceInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DeviceInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DeviceInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DeviceInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DeviceInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DeviceInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DeviceInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static DeviceInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static DeviceInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static DeviceInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static DeviceInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static DeviceInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(DeviceInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DeviceInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DeviceInfo)
        DeviceInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return BaiduRtaRequest.internal_static_DeviceInfo_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return BaiduRtaRequest.internal_static_DeviceInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                DeviceInfo.class, Builder.class);
      }

      // Construct using BaiduRtaRequest.DeviceInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCaidInfoFieldBuilder();
        }
      }
      public Builder clear() {
        super.clear();
        idfaMd5_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        imeiMd5_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        androidIdMd5_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000004);
        oaidMd5_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        oaid_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000010);
        idfa_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000020);
        imei2Md5_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000040);
        bt_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000080);
        ut_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        if (caidInfoBuilder_ == null) {
          caidInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);
        } else {
          caidInfoBuilder_.clear();
        }
        return this;
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return BaiduRtaRequest.internal_static_DeviceInfo_descriptor;
      }

      public DeviceInfo getDefaultInstanceForType() {
        return DeviceInfo.getDefaultInstance();
      }

      public DeviceInfo build() {
        DeviceInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public DeviceInfo buildPartial() {
        DeviceInfo result = new DeviceInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.idfaMd5_ = idfaMd5_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.imeiMd5_ = imeiMd5_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.androidIdMd5_ = androidIdMd5_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.oaidMd5_ = oaidMd5_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.oaid_ = oaid_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.idfa_ = idfa_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.imei2Md5_ = imei2Md5_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.bt_ = bt_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.ut_ = ut_;
        if (caidInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000200) == 0x00000200)) {
            caidInfo_ = java.util.Collections.unmodifiableList(caidInfo_);
            bitField0_ = (bitField0_ & ~0x00000200);
          }
          result.caidInfo_ = caidInfo_;
        } else {
          result.caidInfo_ = caidInfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder clone() {
        return (Builder) super.clone();
      }
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.setField(field, value);
      }
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof DeviceInfo) {
          return mergeFrom((DeviceInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(DeviceInfo other) {
        if (other == DeviceInfo.getDefaultInstance()) return this;
        if (other.hasIdfaMd5()) {
          setIdfaMd5(other.getIdfaMd5());
        }
        if (other.hasImeiMd5()) {
          setImeiMd5(other.getImeiMd5());
        }
        if (other.hasAndroidIdMd5()) {
          setAndroidIdMd5(other.getAndroidIdMd5());
        }
        if (other.hasOaidMd5()) {
          setOaidMd5(other.getOaidMd5());
        }
        if (other.hasOaid()) {
          setOaid(other.getOaid());
        }
        if (other.hasIdfa()) {
          setIdfa(other.getIdfa());
        }
        if (other.hasImei2Md5()) {
          setImei2Md5(other.getImei2Md5());
        }
        if (other.hasBt()) {
          setBt(other.getBt());
        }
        if (other.hasUt()) {
          setUt(other.getUt());
        }
        if (caidInfoBuilder_ == null) {
          if (!other.caidInfo_.isEmpty()) {
            if (caidInfo_.isEmpty()) {
              caidInfo_ = other.caidInfo_;
              bitField0_ = (bitField0_ & ~0x00000200);
            } else {
              ensureCaidInfoIsMutable();
              caidInfo_.addAll(other.caidInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.caidInfo_.isEmpty()) {
            if (caidInfoBuilder_.isEmpty()) {
              caidInfoBuilder_.dispose();
              caidInfoBuilder_ = null;
              caidInfo_ = other.caidInfo_;
              bitField0_ = (bitField0_ & ~0x00000200);
              caidInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCaidInfoFieldBuilder() : null;
            } else {
              caidInfoBuilder_.addAllMessages(other.caidInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        DeviceInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (DeviceInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.ByteString idfaMd5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes idfa_md5 = 1;</code>
       */
      public boolean hasIdfaMd5() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bytes idfa_md5 = 1;</code>
       */
      public com.google.protobuf.ByteString getIdfaMd5() {
        return idfaMd5_;
      }
      /**
       * <code>optional bytes idfa_md5 = 1;</code>
       */
      public Builder setIdfaMd5(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        idfaMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes idfa_md5 = 1;</code>
       */
      public Builder clearIdfaMd5() {
        bitField0_ = (bitField0_ & ~0x00000001);
        idfaMd5_ = getDefaultInstance().getIdfaMd5();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString imeiMd5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes imei_md5 = 2;</code>
       */
      public boolean hasImeiMd5() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bytes imei_md5 = 2;</code>
       */
      public com.google.protobuf.ByteString getImeiMd5() {
        return imeiMd5_;
      }
      /**
       * <code>optional bytes imei_md5 = 2;</code>
       */
      public Builder setImeiMd5(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        imeiMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes imei_md5 = 2;</code>
       */
      public Builder clearImeiMd5() {
        bitField0_ = (bitField0_ & ~0x00000002);
        imeiMd5_ = getDefaultInstance().getImeiMd5();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString androidIdMd5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes android_id_md5 = 3;</code>
       */
      public boolean hasAndroidIdMd5() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional bytes android_id_md5 = 3;</code>
       */
      public com.google.protobuf.ByteString getAndroidIdMd5() {
        return androidIdMd5_;
      }
      /**
       * <code>optional bytes android_id_md5 = 3;</code>
       */
      public Builder setAndroidIdMd5(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        androidIdMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes android_id_md5 = 3;</code>
       */
      public Builder clearAndroidIdMd5() {
        bitField0_ = (bitField0_ & ~0x00000004);
        androidIdMd5_ = getDefaultInstance().getAndroidIdMd5();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString oaidMd5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes oaid_md5 = 4;</code>
       */
      public boolean hasOaidMd5() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional bytes oaid_md5 = 4;</code>
       */
      public com.google.protobuf.ByteString getOaidMd5() {
        return oaidMd5_;
      }
      /**
       * <code>optional bytes oaid_md5 = 4;</code>
       */
      public Builder setOaidMd5(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        oaidMd5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes oaid_md5 = 4;</code>
       */
      public Builder clearOaidMd5() {
        bitField0_ = (bitField0_ & ~0x00000008);
        oaidMd5_ = getDefaultInstance().getOaidMd5();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString oaid_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes oaid = 7;</code>
       */
      public boolean hasOaid() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional bytes oaid = 7;</code>
       */
      public com.google.protobuf.ByteString getOaid() {
        return oaid_;
      }
      /**
       * <code>optional bytes oaid = 7;</code>
       */
      public Builder setOaid(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        oaid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes oaid = 7;</code>
       */
      public Builder clearOaid() {
        bitField0_ = (bitField0_ & ~0x00000010);
        oaid_ = getDefaultInstance().getOaid();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString idfa_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes idfa = 8;</code>
       */
      public boolean hasIdfa() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional bytes idfa = 8;</code>
       */
      public com.google.protobuf.ByteString getIdfa() {
        return idfa_;
      }
      /**
       * <code>optional bytes idfa = 8;</code>
       */
      public Builder setIdfa(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        idfa_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes idfa = 8;</code>
       */
      public Builder clearIdfa() {
        bitField0_ = (bitField0_ & ~0x00000020);
        idfa_ = getDefaultInstance().getIdfa();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString imei2Md5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes imei2_md5 = 12;</code>
       */
      public boolean hasImei2Md5() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional bytes imei2_md5 = 12;</code>
       */
      public com.google.protobuf.ByteString getImei2Md5() {
        return imei2Md5_;
      }
      /**
       * <code>optional bytes imei2_md5 = 12;</code>
       */
      public Builder setImei2Md5(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        imei2Md5_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes imei2_md5 = 12;</code>
       */
      public Builder clearImei2Md5() {
        bitField0_ = (bitField0_ & ~0x00000040);
        imei2Md5_ = getDefaultInstance().getImei2Md5();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString bt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes bt = 16;</code>
       */
      public boolean hasBt() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional bytes bt = 16;</code>
       */
      public com.google.protobuf.ByteString getBt() {
        return bt_;
      }
      /**
       * <code>optional bytes bt = 16;</code>
       */
      public Builder setBt(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        bt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes bt = 16;</code>
       */
      public Builder clearBt() {
        bitField0_ = (bitField0_ & ~0x00000080);
        bt_ = getDefaultInstance().getBt();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ut_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ut = 17;</code>
       */
      public boolean hasUt() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional bytes ut = 17;</code>
       */
      public com.google.protobuf.ByteString getUt() {
        return ut_;
      }
      /**
       * <code>optional bytes ut = 17;</code>
       */
      public Builder setUt(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        ut_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ut = 17;</code>
       */
      public Builder clearUt() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ut_ = getDefaultInstance().getUt();
        onChanged();
        return this;
      }

      private java.util.List<CaidInfo> caidInfo_ =
        java.util.Collections.emptyList();
      private void ensureCaidInfoIsMutable() {
        if (!((bitField0_ & 0x00000200) == 0x00000200)) {
          caidInfo_ = new java.util.ArrayList<CaidInfo>(caidInfo_);
          bitField0_ |= 0x00000200;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          CaidInfo, CaidInfo.Builder, CaidInfoOrBuilder> caidInfoBuilder_;

      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public java.util.List<CaidInfo> getCaidInfoList() {
        if (caidInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(caidInfo_);
        } else {
          return caidInfoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public int getCaidInfoCount() {
        if (caidInfoBuilder_ == null) {
          return caidInfo_.size();
        } else {
          return caidInfoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public CaidInfo getCaidInfo(int index) {
        if (caidInfoBuilder_ == null) {
          return caidInfo_.get(index);
        } else {
          return caidInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder setCaidInfo(
          int index, CaidInfo value) {
        if (caidInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCaidInfoIsMutable();
          caidInfo_.set(index, value);
          onChanged();
        } else {
          caidInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder setCaidInfo(
          int index, CaidInfo.Builder builderForValue) {
        if (caidInfoBuilder_ == null) {
          ensureCaidInfoIsMutable();
          caidInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          caidInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder addCaidInfo(CaidInfo value) {
        if (caidInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCaidInfoIsMutable();
          caidInfo_.add(value);
          onChanged();
        } else {
          caidInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder addCaidInfo(
          int index, CaidInfo value) {
        if (caidInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCaidInfoIsMutable();
          caidInfo_.add(index, value);
          onChanged();
        } else {
          caidInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder addCaidInfo(
          CaidInfo.Builder builderForValue) {
        if (caidInfoBuilder_ == null) {
          ensureCaidInfoIsMutable();
          caidInfo_.add(builderForValue.build());
          onChanged();
        } else {
          caidInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder addCaidInfo(
          int index, CaidInfo.Builder builderForValue) {
        if (caidInfoBuilder_ == null) {
          ensureCaidInfoIsMutable();
          caidInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          caidInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder addAllCaidInfo(
          Iterable<? extends CaidInfo> values) {
        if (caidInfoBuilder_ == null) {
          ensureCaidInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, caidInfo_);
          onChanged();
        } else {
          caidInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder clearCaidInfo() {
        if (caidInfoBuilder_ == null) {
          caidInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);
          onChanged();
        } else {
          caidInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public Builder removeCaidInfo(int index) {
        if (caidInfoBuilder_ == null) {
          ensureCaidInfoIsMutable();
          caidInfo_.remove(index);
          onChanged();
        } else {
          caidInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public CaidInfo.Builder getCaidInfoBuilder(
          int index) {
        return getCaidInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public CaidInfoOrBuilder getCaidInfoOrBuilder(
          int index) {
        if (caidInfoBuilder_ == null) {
          return caidInfo_.get(index);  } else {
          return caidInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public java.util.List<? extends CaidInfoOrBuilder>
           getCaidInfoOrBuilderList() {
        if (caidInfoBuilder_ != null) {
          return caidInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(caidInfo_);
        }
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public CaidInfo.Builder addCaidInfoBuilder() {
        return getCaidInfoFieldBuilder().addBuilder(
            CaidInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public CaidInfo.Builder addCaidInfoBuilder(
          int index) {
        return getCaidInfoFieldBuilder().addBuilder(
            index, CaidInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 多版本的caid
       * </pre>
       *
       * <code>repeated .CaidInfo caid_info = 18;</code>
       */
      public java.util.List<CaidInfo.Builder>
           getCaidInfoBuilderList() {
        return getCaidInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          CaidInfo, CaidInfo.Builder, CaidInfoOrBuilder>
          getCaidInfoFieldBuilder() {
        if (caidInfoBuilder_ == null) {
          caidInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              CaidInfo, CaidInfo.Builder, CaidInfoOrBuilder>(
                  caidInfo_,
                  ((bitField0_ & 0x00000200) == 0x00000200),
                  getParentForChildren(),
                  isClean());
          caidInfo_ = null;
        }
        return caidInfoBuilder_;
      }
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:DeviceInfo)
    }

    // @@protoc_insertion_point(class_scope:DeviceInfo)
    private static final DeviceInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new DeviceInfo();
    }

    public static DeviceInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<DeviceInfo>
        PARSER = new com.google.protobuf.AbstractParser<DeviceInfo>() {
      public DeviceInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DeviceInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DeviceInfo> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<DeviceInfo> getParserForType() {
      return PARSER;
    }

    public DeviceInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_RtaApiRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_RtaApiRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CaidInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_CaidInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DeviceInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_DeviceInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\025BaiduRtaRequest.proto\"\371\002\n\rRtaApiReques" +
      "t\022\013\n\003qid\030\001 \002(\004\022\030\n\007os_type\030\002 \002(\0162\007.OsType" +
      "\022\025\n\rdevice_id_md5\030\003 \002(\t\022\021\n\tsign_time\030\004 \002" +
      "(\004\022\r\n\005token\030\005 \002(\t\0221\n\023android_device_type" +
      "\030\006 \001(\0162\024.AndroidDeviceIdType\022 \n\013device_i" +
      "nfo\030\010 \001(\0132\013.DeviceInfo\022\034\n\tflow_type\030\t \001(" +
      "\0162\t.FlowType\022\020\n\010media_id\030\013 \001(\004\022\030\n\020os_maj" +
      "or_version\030\016 \001(\r\022\026\n\016is_dpa_request\030\017 \001(\010" +
      "\022\025\n\rprefetch_date\030\020 \001(\t\022\021\n\ttimestamp\030\021 \001" +
      "(\004\022\027\n\017bes_media_group\030\022 \001(\r\022\016\n\006exp_id\030\024 " +
      "\001(\r\".\n\010CaidInfo\022\014\n\004caid\030\001 \001(\014\022\024\n\014caid_ve" +
      "rsion\030\002 \001(\014\"\277\001\n\nDeviceInfo\022\020\n\010idfa_md5\030\001" +
      " \001(\014\022\020\n\010imei_md5\030\002 \001(\014\022\026\n\016android_id_md5" +
      "\030\003 \001(\014\022\020\n\010oaid_md5\030\004 \001(\014\022\014\n\004oaid\030\007 \001(\014\022\014" +
      "\n\004idfa\030\010 \001(\014\022\021\n\timei2_md5\030\014 \001(\014\022\n\n\002bt\030\020 " +
      "\001(\014\022\n\n\002ut\030\021 \001(\014\022\034\n\tcaid_info\030\022 \003(\0132\t.Cai" +
      "dInfo*/\n\010FlowType\022\013\n\007SHOUBAI\020\001\022\t\n\005UNION\020" +
      "\002\022\013\n\007KAIPING\020\004*+\n\006OsType\022\013\n\007UNKNOWN\020\000\022\013\n" +
      "\007ANDROID\020\001\022\007\n\003IOS\020\002*)\n\023AndroidDeviceIdTy" +
      "pe\022\010\n\004IMEI\020\001\022\010\n\004OAID\020\002"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_RtaApiRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_RtaApiRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_RtaApiRequest_descriptor,
        new String[] { "Qid", "OsType", "DeviceIdMd5", "SignTime", "Token", "AndroidDeviceType", "DeviceInfo", "FlowType", "MediaId", "OsMajorVersion", "IsDpaRequest", "PrefetchDate", "Timestamp", "BesMediaGroup", "ExpId", });
    internal_static_CaidInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_CaidInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_CaidInfo_descriptor,
        new String[] { "Caid", "CaidVersion", });
    internal_static_DeviceInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_DeviceInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_DeviceInfo_descriptor,
        new String[] { "IdfaMd5", "ImeiMd5", "AndroidIdMd5", "OaidMd5", "Oaid", "Idfa", "Imei2Md5", "Bt", "Ut", "CaidInfo", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
