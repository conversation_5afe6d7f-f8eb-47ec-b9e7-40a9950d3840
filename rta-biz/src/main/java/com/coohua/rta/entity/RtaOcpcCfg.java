package com.coohua.rta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="RtaOcpcCfg对象", description="")
public class RtaOcpcCfg implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String product;

    private String dsp;

    private String os;

    private String accountIds;

    @ApiModelProperty(value = "首日回收数据")
    private Double firstDayRate;

    private Double secondDayRate;

    @ApiModelProperty(value = "pv最低要求值")
    private Integer pvLow;

    private Date createTime;

    private Date updateTime;


}
