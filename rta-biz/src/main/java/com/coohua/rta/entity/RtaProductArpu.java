package com.coohua.rta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="RtaProductArpu对象", description="")
public class RtaProductArpu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String product;

    private String toufangQudao;

    private String os;

    private Double chujia;

    @ApiModelProperty(value = "cvr")
    private Double priceCvr;

    @ApiModelProperty(value = "lt")
    private Double priceLt;

    @ApiModelProperty(value = "出价系数")
    private Double priceUpfloat;

    private Double priceMax;

    private Double priceOtherRate;

    @ApiModelProperty(value = "1开启低价 2不开启")
    private Integer lowerPriceFlg;

    private Date createTime;

    private Date updateTime;


}
