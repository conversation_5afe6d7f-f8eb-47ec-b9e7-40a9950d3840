package com.coohua.rta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="RtaRsp对象", description="")
public class RtaRsp implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String reqId;

    private String oaid;

    private String imei;

    private String caid;

    private String os;

    private Double upFloat;

    @ApiModelProperty(value = "0竞价 1不竞价")
    private Integer bidType;

    private Integer bizUserType;

    private String product;

    private Double ppv;

    private Double parpu;

    private Double pwithdraw;

    private Double pcpa;

    private Double cfgPv;

    private Double cfgArpu;

    private Double cfgCpa;

    private Double cfgDraw;

    private Integer strgyId;

    private String extend1;

    private String extend2;

    private String extend3;

    private String extend4;

    private Date createTime;

    private Date updateTime;


}
