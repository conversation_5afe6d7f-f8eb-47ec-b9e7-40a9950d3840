package com.coohua.rta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * rtabyte配置的产品-rtaId
 * @TableName rta_byte_config_product
 */
@TableName(value ="rta_byte_config_product")
@Data
public class RtaByteConfigProduct implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 产品打点名
     */
    private String product;

    /**
     * rtaId
     */
    private Long rtaId;
    private String tag;
    private String tagType;
    private String tagStrategy;

    private String os;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}