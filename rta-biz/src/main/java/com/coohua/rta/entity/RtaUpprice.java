package com.coohua.rta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="RtaUpprice对象", description="")
public class RtaUpprice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String product;

    private String os;

    private String dspType;

    private Double uppriceFloat;

    private Double chujiaPrice;

    private String rtaId;

    private String caid;

    private String idfa;

    private String oaid;

    private String imei;

    private Double hcpa;

    private Double harpu;

    private Double hpv;

    private Double hwithdraw;

    private String guiyType;

    private String remark;

    private Date createTime;

    private Date updateTime;


}
