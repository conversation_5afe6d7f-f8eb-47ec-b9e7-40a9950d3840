package com.coohua.rta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="RtaDevice对象", description="")
public class RtaDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String userId;

    private String product;

    private Integer productType;

    private String caid;

    private String imei;

    private String os;

    private String oaid;

    private Date logday;

    private Double arpu;

    private Integer pv;

    private Double cpa;

    @ApiModelProperty(value = "提现金额")
    private Integer withdrawAmount;

    private Date userCreateTime;



}
