package com.coohua.rta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="RtaUppriceSub对象", description="")
public class RtaUppriceSub implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer uppriceId;

    private String product;

    private String os;

    private String rtaId;

    private Date logDay;

    private Double uppriceFloat;

    private String caid;

    private String idfa;

    private String oaid;

    private String imei;

    private Double cpa;

    private Double arpu;

    private Double pv;

    private Double withdraw;

    private String guiyType;

    private Date createTime;

    private Date updateTime;


}
