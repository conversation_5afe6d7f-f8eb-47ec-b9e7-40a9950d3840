package com.coohua.rta.strategy;


import java.util.Set;

public class TagBidContext {
    private TagBidStrategy tagBidStrategy;

    public TagBidContext() {
    }

    public TagBidContext(TagBidStrategy tagBidStrategy) {
        this.tagBidStrategy = tagBidStrategy;
    }

    public void setTagBidStrategy(TagBidStrategy tagBidStrategy) {
        this.tagBidStrategy = tagBidStrategy;
    }

    public boolean isNotBidRta(String mappingTagStr, Set<String> unionTagSet) {
        return tagBidStrategy.isNotBidRta(mappingTagStr, unionTagSet);
    }
}
