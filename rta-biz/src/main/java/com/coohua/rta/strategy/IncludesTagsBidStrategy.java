package com.coohua.rta.strategy;

import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Set;

@Service("includesTagsBidStrategy")
public class IncludesTagsBidStrategy implements TagBidStrategy{
    @Override
    public boolean isNotBidRta(String mappingTagStr, Set<String> unionTagSet) {
        if (mappingTagStr == null || mappingTagStr.trim().isEmpty()) {
            return false;
        }
        String[] tags = mappingTagStr.split("\\s*,\\s*");

        return Arrays.stream(tags)
                .anyMatch(unionTagSet::contains);
    }
}
