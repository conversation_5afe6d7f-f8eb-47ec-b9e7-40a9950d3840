package com.coohua.rta.redis;

import com.alibaba.fastjson.JSON;
import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.entity.RtaUpprice;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RtaDeviceRedisService {
    @Autowired
    @Qualifier("rta-redis-newJedisClusterClient")
    private JedisClusterClient rtaRedis;

    public void saveUpPrice(RtaUpprice rtaUpprice){
        String upPricePrefix = "upPrice";
        if(StringUtils.equalsIgnoreCase("ios",rtaUpprice.getOs())){
            if(StringUtils.isNotBlank(rtaUpprice.getCaid())){
                saddDeviceInfo(upPricePrefix+rtaUpprice.getCaid(),rtaUpprice);
            }
        }else{
            if(StringUtils.isNotBlank(rtaUpprice.getOaid())){
                saddDeviceInfo(upPricePrefix+rtaUpprice.getOaid(),rtaUpprice);
            }
            if( StringUtils.isNotBlank(rtaUpprice.getImei())){
                saddDeviceInfo(upPricePrefix+rtaUpprice.getImei(),rtaUpprice);
            }
        }
    }

    private long saddDeviceInfo(String key, RtaUpprice rtaUpprice){
        long dnum = 0;
        if(StringUtils.isNotBlank(key)){
            long ctime = System.currentTimeMillis();
            Set<String> dset = rtaRedis.smembers(key);
            if(dset.size()>0 && dset.size()<30){
                List<RtaUpprice> deviceList = converDeviceSet(dset);

                boolean isCt = isContain(rtaUpprice,deviceList);
                if(!isCt){
                    dnum = rtaRedis.sadd(key, JSON.toJSONString(rtaUpprice));
                }
                removeThanDay(key,deviceList);
            }else if(dset.size()>30){
                rtaRedis.del(key);
                rtaRedis.sadd(key, JSON.toJSONString(rtaUpprice));
                rtaRedis.expire(key,DateTimeConstants.SECONDS_PER_DAY*(rtaSwitcher.redisSaveDay+1));
                log.info("删除key 添加 "+key+" "+dset.size());
            }else{
                dnum = rtaRedis.sadd(key, JSON.toJSONString(rtaUpprice));
                rtaRedis.expire(key,DateTimeConstants.SECONDS_PER_DAY*(rtaSwitcher.redisSaveDay+1));
            }
        }
        return dnum;
    }

    @Autowired
    RtaSwitcher rtaSwitcher;
    public void removeThanDay(String key, List<RtaUpprice> uppriceList){
        for(RtaUpprice rtaUpprice : uppriceList){
            long jiangeTm = System.currentTimeMillis() - rtaUpprice.getCreateTime().getTime();
            if((jiangeTm/ (DateTimeConstants.MILLIS_PER_DAY))>rtaSwitcher.redisSaveDay){
                rtaRedis.srem(key, JSON.toJSONString(rtaUpprice));
                log.info("移除 "+rtaUpprice.getCreateTime()+""+rtaUpprice.getProduct()+" 成功 "+key);
            }
        }
    }
    public List<RtaUpprice> converDeviceSet(Set<String>  dset){
        if(dset.size()>0){
            List<RtaUpprice> rlist = dset.stream().map(a-> JSON.parseObject(a,RtaUpprice.class)).collect(Collectors.toList());
            return rlist;
        }
        return new ArrayList<>();
    }

    public  boolean isContain(RtaUpprice rtaUpprice,List<RtaUpprice> deviceList){
        boolean isCt = false;
        for(RtaUpprice rtaUppriced : deviceList){
            if(StringUtils.equalsIgnoreCase(rtaUpprice.getProduct(),rtaUppriced.getProduct()) &&
                    StringUtils.equalsIgnoreCase(rtaUpprice.getDspType(),rtaUppriced.getDspType())){
                isCt = true;
                break;
            }
        }
        return isCt;
    }
}
