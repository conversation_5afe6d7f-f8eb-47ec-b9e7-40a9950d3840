package com.coohua.rta.redis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.constant.RedisKeyConstants;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.enums.PlatformEnum;
import com.coohua.rta.service.AliOssService;
import com.coohua.rta.service.RtaDeviceService;
import com.coohua.rta.service.RtaTableService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.io.BufferedReader;
import java.io.File;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.coohua.rta.utils.HashToRangeUtil.hashToRange;

@Component
@Slf4j
public class RtaRedisService {
    @Autowired
    @Qualifier("rta-redis-newJedisClusterClient")
    private JedisClusterClient rtaRedis;
    @Autowired
    RtaSetSigleService rtaSetSigleService;
    @Autowired
    RtaSwitcher rtaSwitcher;

    public static Executor deviceToRedisPool = new ThreadPoolExecutor(50,
            50, DateTimeConstants.SECONDS_PER_MINUTE * 60,
            TimeUnit.SECONDS, new ArrayBlockingQueue<>(100),
            new NamedThreadFactory("deviceToRedisPool",false),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    public void fileToRedis(String dateStr){
        String folderName = AliOssService.getFolder()+ File.separator+dateStr;
        File fileFold = new File(folderName);
        if(fileFold.exists()){
            File[] files = fileFold.listFiles();
            for(File file : files){
                XxlJobLogger.log("读取文件开始 "+file.getAbsolutePath());
                try (BufferedReader br = Files.newBufferedReader(file.toPath(), Charset.forName("UTF-8"))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        try {
                            submitPool(line);
                        }catch (Exception e){
                            log.error("",e);
                        }
                    }
                } catch (Exception e) {
                    log.error("",e);
                    XxlJobLogger.log("读取文件异常",e);
                }
            }
        }else{
            XxlJobLogger.log("oss 文件 不存在 "+folderName);
        }
    }

    List<String> dlist = new ArrayList<>();
    @Autowired
    RtaDeviceService rtaDeviceService;
    @Autowired
    RtaTableService rtaTableService;
    private void submitPool(String deviceStr){
        dlist.add(deviceStr);

        if(dlist.size()>2000){
            List<String> mdlist = new ArrayList<>();
            mdlist.addAll(dlist);

            dlist = new ArrayList<>();

            RtaDeviceSetThread rtaDeviceSetThread = new RtaDeviceSetThread(mdlist,rtaSetSigleService,rtaDeviceService,rtaTableService);
            deviceToRedisPool.execute(rtaDeviceSetThread);

            XxlJobLogger.log("提交线程成功 "+mdlist.size());
            log.info("提交线程成功 "+mdlist.size());
        }
    }
    public Map<String, String> getMapFromRedisBatch(String key) {
        Map<String, String> map = new HashMap<>();

        // 初始化游标
        String cursor = "0";
        ScanParams scanParams = new ScanParams().count(100); // 每次读取 100 个字段

        do {
            // 使用 hscan 命令分页读取 Hash 的数据
            ScanResult<Map.Entry<String, String>> scanResult = rtaRedis.hscan(key, cursor, scanParams);
            List<Map.Entry<String, String>> entries = scanResult.getResult();

            // 处理当前页的数据
            for (Map.Entry<String, String> entry : entries) {
                map.put(entry.getKey(), entry.getValue());
            }

            // 更新游标
            cursor = String.valueOf(scanResult.getCursor());
        } while (!cursor.equals("0"));

        return map;
    }



    /**
     * 从redis获取人群包
     *
     * @param userEventReq
     * @return
     */
    public Map<String, String> getUnionFromRedisHash(UserEventReq userEventReq) {
        try {
            String key = "";
            if (PlatformEnum.IOS.getPlatform().equals(userEventReq.getOs())) {
                //caid2
                if (StringUtils.isNotBlank(userEventReq.getCaid2())) {
                    key = RedisKeyConstants.getRtaCaidUnion(String.valueOf(hashToRange(userEventReq.getCaid2(), null, null)), userEventReq.getCaid2());
                    Map<String, String> map = rtaRedis.hgetAll(key);
                    if (CollUtil.isNotEmpty(map)) {
                        userEventReq.setMatchedCaid(userEventReq.getCaid2());
                        return map;
                    }
                }
                //caid1
                if (StringUtils.isNotBlank(userEventReq.getCaid1())) {
                    key = RedisKeyConstants.getRtaCaidUnion(String.valueOf(hashToRange(userEventReq.getCaid1(), null, null)), userEventReq.getCaid1());
                    Map<String, String> map = rtaRedis.hgetAll(key);
                    if (CollUtil.isNotEmpty(map)) {
                        userEventReq.setMatchedCaid(userEventReq.getCaid1());
                        return map;
                    }
                }
            } else {
                /**
                 * 查oaId
                 */
                if (StringUtils.isNotBlank(userEventReq.getOaid())) {
                    key = RedisKeyConstants.getRtaOaidUnion(String.valueOf(hashToRange(userEventReq.getOaid(), null, null)), userEventReq.getOaid());
                    Map<String, String> map = rtaRedis.hgetAll(key);
                    if (CollUtil.isNotEmpty(map)) {
                        return map;
                    }
                }
                // ks
                if (StringUtils.isNotBlank(userEventReq.getOaidMd5())) {
                    key = RedisKeyConstants.getRtaOaidMd5Union(String.valueOf(hashToRange(userEventReq.getOaidMd5(), null, null)), userEventReq.getOaidMd5());
                    Map<String, String> map = rtaRedis.hgetAll(key);
                    if (CollUtil.isNotEmpty(map)) {
                        return map;
                    }
                }
            }


        } catch (Exception e) {
            log.error("redis获取rtaByteUnion失败", e);
        }
        return null;
    }

    //    private Set<String> getUnionFromRedis(RtaApi.Req request, UserEventReq userEventReq) {
//        RtaApi.PlatformType platformType = request.getPlatform();
//        try {
//            String key = "";
//            if (RtaApi.PlatformType.IOS.equals(platformType)) {
//                String key1 = null;
//                String key2 = null;
//                List<String> keys = new ArrayList<>();
//                // caid1
//                if (StringUtils.isNotBlank(userEventReq.getCaid1())) {
//                    key1 = RedisKeyConstants.getRtaByteCaidUnion(String.valueOf(hashToRange(userEventReq.getCaid1(), null, null)), userEventReq.getCaid1());
//                    keys.add(key1);
//                }
//                // caid2
//                if (StringUtils.isNotBlank(userEventReq.getCaid2())) {
//                    key2 = RedisKeyConstants.getRtaByteCaidUnion(String.valueOf(hashToRange(userEventReq.getCaid2(), null, null)), userEventReq.getCaid2());
//                    keys.add(key2);
//                }
//
//                // 使用 RBatch 进行批量查询
//                RBatch batch = redissonClient.createBatch();
//                for (String s : keys) {
//                    batch.getScoredSortedSet(s).valueRangeAsync(0, -1);
//                }
//
//                BatchResult<?> batchResult = batch.execute();
//
//                // 获取结果
//                List<?> responses = batchResult.getResponses();
//                for (Object respons : responses) {
//                    if (!((List<String>) respons).isEmpty()) {
//                        return new HashSet<>((List<String>) respons);
//                    }
//                }
//
//            } else {
//                /**
//                 * 查oaId
//                 */
//                if (StringUtils.isNotBlank(userEventReq.getOaid())) {
//                    key = RedisKeyConstants.getRtaByteOaidUnion(String.valueOf(hashToRange(userEventReq.getOaid(), null, null)), userEventReq.getOaid());
//                    RScoredSortedSet<Object> scoredSortedSet = redissonClient.getScoredSortedSet(key);
//                    Collection<Object> valuedRange = scoredSortedSet.valueRange(0, -1);
//
//                    if (valuedRange.isEmpty()) return null;
//
//                    return valuedRange.stream().map(Object::toString).collect(Collectors.toSet());
//                }
//            }
//
//
//        } catch (Exception e) {
//            log.error("redis获取rtaByteUnion失败", e);
//        }
//        return null;
//    }

    /**
     * 从redis获取人群包
     *
     * @param userEventReq
     * @return
     */
    private Set<String> getUnionFromRedisZset(RtaApi.Req request, UserEventReq userEventReq) {
        RtaApi.PlatformType platformType = request.getPlatform();
        try {
            String key = "";
            if (RtaApi.PlatformType.IOS.equals(platformType)) {
                //caid1
                if (StringUtils.isNotBlank(userEventReq.getCaid1())) {
                    key = RedisKeyConstants.getRtaByteCaidUnion(String.valueOf(hashToRange(userEventReq.getCaid1(), null, null)), userEventReq.getCaid1());
                    Set<String> smembers = rtaRedis.zrange(key, 0, -1);
                    if (CollUtil.isNotEmpty(smembers)) {
                        return smembers;
                    }
                }
                //caid2
                if (StringUtils.isNotBlank(userEventReq.getCaid2())) {
                    key = RedisKeyConstants.getRtaByteCaidUnion(String.valueOf(hashToRange(userEventReq.getCaid2(), null, null)), userEventReq.getCaid2());
                    Set<String> smembers = rtaRedis.zrange(key, 0, -1);
                    if (CollUtil.isNotEmpty(smembers)) {
                        return smembers;
                    }
                }

            } else {
                /**
                 * 查oaId
                 */
                if (StringUtils.isNotBlank(userEventReq.getOaid())) {
                    key = RedisKeyConstants.getRtaByteOaidUnion(String.valueOf(hashToRange(userEventReq.getOaid(), null, null)), userEventReq.getOaid());
                    return rtaRedis.zrange(key, 0, -1);
                }
            }


        } catch (Exception e) {
            log.error("redis获取rtaByteUnion失败", e);
        }
        return null;
    }

    public Set<String> getSetFromRedisBatch(String rtaBinaryDict) {
        Set<String> members = new HashSet<>();
        String cursor = "0";
        ScanParams scanParams = new ScanParams().count(100); // 每次读取 100 个元素

        do {
            // 使用 sscan 命令分页读取 SET 的数据
            ScanResult<String> scanResult = rtaRedis.sscan(rtaBinaryDict, cursor, scanParams);
            List<String> result = scanResult.getResult();

            // 处理当前页的数据
            members.addAll(result);

            // 更新游标
            cursor = String.valueOf(scanResult.getCursor());
        } while (!cursor.equals("0"));

        return members;
    }
}
