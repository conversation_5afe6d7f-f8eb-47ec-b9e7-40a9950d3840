package com.coohua.rta.redis;

import com.alibaba.fastjson.JSON;

import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.dto.req.UserEventReq;
import com.coohua.rta.entity.RtaDevice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.enums.GuiyingType;
import com.coohua.rta.service.RtaRstSaveService;
import com.coohua.rta.service.strategy.entity.ChujiaEnt;
import com.coohua.rta.utils.DateUtils;
import com.coohua.rta.utils.DoubleUtil;
import com.xxl.job.core.log.XxlJobLogger;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RtaSetSigleService {
    @Autowired
    RtaRstSaveService rtaRstSaveService;
    public Pair<ChujiaEnt,List<RtaUppriceSub>> getChujiaEnt(UserEventReq request, String reqId) {
        Pair<GuiyingType, List<RtaDevice>> rtaDevicesPair = queryHisDevices(request);

        double hcpa = 0d; //历史cpa 平均值
        double harpu = 0d; //历史arpu 平均值
        double hpv = 0d; //历史pv 平均值
        double hwithDraw = 0d; //历史提现 平均值

        if (rtaDevicesPair.getValue().size() > 0) {
            List<RtaUppriceSub> rtaUppriceSubs = new ArrayList<>();
            for (RtaDevice rtaDevice : rtaDevicesPair.getValue()) {
                Double redisPv = DoubleUtil.getIntegerNoF(rtaDevice.getPv());
                Double redisWD = DoubleUtil.getIntegerNoF(rtaDevice.getWithdrawAmount());

                hcpa = DoubleUtil.addDouble(hcpa, rtaDevice.getCpa());
                harpu = DoubleUtil.addDouble(harpu, rtaDevice.getArpu());
                hpv = DoubleUtil.addDouble(hpv, redisPv);
                hwithDraw = DoubleUtil.addDouble(hwithDraw, redisWD);

                RtaUppriceSub rtaUppriceSub = rtaRstSaveService.getRtaSub(rtaDevice, reqId, rtaDevicesPair.getKey());
                rtaUppriceSubs.add(rtaUppriceSub);
            }
            Double sz = rtaDevicesPair.getValue().size() * 1.0d;

            hcpa = DoubleUtil.divideDouble(hcpa, sz);
            harpu = DoubleUtil.divideDouble(harpu, sz);
            hpv = DoubleUtil.divideDouble(hpv, sz);
            hwithDraw = DoubleUtil.divideDouble(hwithDraw, sz);

            ChujiaEnt chujiaEnt = new ChujiaEnt();
            chujiaEnt.setHarpu(harpu);
            chujiaEnt.setHcpa(hcpa);
            chujiaEnt.setHpv(hpv);
            chujiaEnt.setHwithDraw(hwithDraw);
            chujiaEnt.setGuiyingType(rtaDevicesPair.getKey());

            return new Pair<>(chujiaEnt,rtaUppriceSubs);
        }

        return null;

    }

    public void setRedisDayUserArpu(RtaDevice rtaDevice){
        long dnum = 0;
        if(StringUtils.isNotBlank(rtaDevice.getProduct()) && rtaDevice.getLogday()!=null
                && (StringUtils.isNotBlank(rtaDevice.getCaid()) ||  StringUtils.isNotBlank(rtaDevice.getImei()) || StringUtils.isNotBlank(rtaDevice.getOaid()))
        ){
            if(StringUtils.isNotBlank(rtaDevice.getCaid()) && isValidCaid(rtaDevice)){
                dnum = saddDeviceInfo(rtaDevice.getCaid(),rtaDevice);
            }
            if(StringUtils.isNotBlank(rtaDevice.getImei()) && isValidImet(rtaDevice)){
                dnum =  dnum + saddDeviceInfo(rtaDevice.getImei(),rtaDevice);
            }
            if(StringUtils.isNotBlank(rtaDevice.getOaid()) && isValidOaid(rtaDevice)){
                dnum =  dnum + saddDeviceInfo(rtaDevice.getOaid(),rtaDevice);
            }
        }

        saveNum.addAndGet(1);
        logNum.addAndGet(1);

        printLog();
    }
    public Pair<GuiyingType,List<RtaDevice>> queryHisDevices(UserEventReq request){
        List<RtaDevice> deviceList = new ArrayList<>();
        Set<String> dset = new HashSet<>();
        GuiyingType guiyingType = null;
        long ctime = System.currentTimeMillis();
        if(StringUtils.equalsIgnoreCase("ios",request.getOs())){
            if(StringUtils.isNotBlank(request.getCaid())){
                dset = rtaRedis.smembers(request.getCaid());
                guiyingType = GuiyingType.caid;
            }
        }else{
            if(StringUtils.isNotBlank(request.getOaid())){
                dset = rtaRedis.smembers(request.getOaid());
                guiyingType = GuiyingType.oaid;
            }
            if(dset.size()==0 && StringUtils.isNotBlank(request.getOcpcDeviceId())){
                dset = rtaRedis.smembers(request.getOcpcDeviceId());
                guiyingType = GuiyingType.imei;
            }
        }

        if((System.currentTimeMillis()-ctime)>2000){
            log.info("cost "+(System.currentTimeMillis()-ctime)+""+JSON.toJSONString(dset));
        }
        if(dset.size()>0 && dset.size()<30) {
            deviceList = converDeviceSet(dset);
        }

        Pair<GuiyingType,List<RtaDevice>> pair = new Pair<>(guiyingType,deviceList);
        return pair;
    }


    private boolean isValidOaid(RtaDevice rtaDevice){
        //f3630704-51da-4e97-b182-c8f02afc4bd1
        if(StringUtils.isNotBlank(rtaDevice.getOaid()) && rtaDevice.getOaid().length()>15 && !StringUtils.equalsIgnoreCase(rtaDevice.getOaid(),rtaDevice.getImei())){
            return true;
        }
        return false;
    }

    private boolean isValidImet(RtaDevice rtaDevice){
        //f3630704-51da-4e97-b182-c8f02afc4bd1
        if(StringUtils.isNotBlank(rtaDevice.getImei()) && rtaDevice.getImei().length()>6 && !StringUtils.equalsIgnoreCase(rtaDevice.getOaid(),rtaDevice.getImei())){
            return true;
        }
        return false;
    }

    private boolean isValidCaid(RtaDevice rtaDevice){
        //f3630704-51da-4e97-b182-c8f02afc4bd1\
        if(!StringUtils.equalsIgnoreCase("ios",rtaDevice.getOs())){
            return false;
        }
        if(StringUtils.isNotBlank(rtaDevice.getCaid()) && rtaDevice.getCaid().length()>15){
            return true;
        }
        return false;
    }

    private AtomicLong logNum = new AtomicLong(0);
    private void printLog(){
        if(logNum.get()/3000>1){
            XxlJobLogger.log("已经保存"+saveNum+"条数据");
            logNum.set(0l);
        }
    }
    private AtomicLong saveNum = new AtomicLong(0);
    @Autowired
    @Qualifier("rta-redis-newJedisClusterClient")
    private JedisClusterClient rtaRedis;
    @Autowired
    RtaSwitcher rtaSwitcher;
    private long saddDeviceInfo(String key,RtaDevice rtaDevice){
        long dnum = 0;
        if(StringUtils.isNotBlank(key)){
            long ctime = System.currentTimeMillis();
            Set<String> dset = rtaRedis.smembers(key);
            if(dset.size()>0 && dset.size()<30){
                List<RtaDevice> deviceList = converDeviceSet(dset);

                boolean isCt = isContain(rtaDevice,deviceList);
                if(!isCt){
                    dnum = rtaRedis.sadd(key, JSON.toJSONString(rtaDevice));
                    rtaRedis.expire(key, DateTimeConstants.SECONDS_PER_DAY*(rtaSwitcher.redisSaveDay+1));
                }
                removeThanDay(key,deviceList);
            }else if(dset.size()>30){
                rtaRedis.del(key);
                log.info("删除key "+key+" "+dset.size());
            }else{
                dnum = rtaRedis.sadd(key, JSON.toJSONString(rtaDevice));
                rtaRedis.expire(key,DateTimeConstants.SECONDS_PER_DAY*(rtaSwitcher.redisSaveDay+1));
            }
        }
        return dnum;
    }
    public void removeThanDay(String key,List<RtaDevice> deviceList){
        for(RtaDevice rtaDevice : deviceList){
            long jiangeTm = System.currentTimeMillis() - rtaDevice.getLogday().getTime();
            if((jiangeTm/ (DateTimeConstants.MILLIS_PER_DAY))>rtaSwitcher.redisSaveDay){
                rtaRedis.srem(key,JSON.toJSONString(rtaDevice));
                log.info("移除 "+rtaDevice.getLogday()+""+rtaDevice.getProduct()+" 成功 "+key);
            }
        }
    }
    public List<RtaDevice> converDeviceSet(Set<String>  dset){
        if(dset.size()>0){
            List<RtaDevice> rlist = dset.stream().map(a-> JSON.parseObject(a,RtaDevice.class)).collect(Collectors.toList());
            return rlist;
        }
        return new ArrayList<>();
    }

    public  boolean isContain(RtaDevice rtaDevice,List<RtaDevice> deviceList){
        boolean isCt = false;
        for(RtaDevice redisDevice : deviceList){
            String newDayStr = DateUtils.formatDateForYMDSTR(rtaDevice.getLogday());
            String oldDayStr = DateUtils.formatDateForYMDSTR(redisDevice.getLogday());

            if(StringUtils.equalsIgnoreCase(rtaDevice.getProduct(),redisDevice.getProduct()) &&
                    StringUtils.equalsIgnoreCase(newDayStr,oldDayStr)){
                isCt = true;
                break;
            }
        }
        return isCt;
    }
}
