package com.coohua.rta.redis;

import com.alibaba.fastjson.JSON;
import com.coohua.rta.entity.RtaDevice;
import com.coohua.rta.service.RtaDeviceService;
import com.coohua.rta.service.RtaTableService;
import com.coohua.rta.utils.MD5Utils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Slf4j
public class RtaDeviceSetThread extends  Thread{

    private List<String> deviceList;
    private RtaSetSigleService rtaSetSigleService;
    private RtaDeviceService rtaDeviceService;
    public RtaDeviceSetThread(List<String> deviceList, RtaSetSigleService rtaSetSigleService,RtaDeviceService rtaDeviceService, RtaTableService rtaTableService){
        this.deviceList = deviceList;
        this.rtaSetSigleService = rtaSetSigleService;
        this.rtaDeviceService = rtaDeviceService;
        this.rtaTableService  = rtaTableService;
    }

    RtaTableService rtaTableService;
    @Override
    public void run() {
        long ctime = System.currentTimeMillis();
        List<RtaDevice> rtaDevicesList = new ArrayList<>();
        for(String rtaDeviceStr : deviceList){
            try {
                RtaDevice rtaDevice = JSON.parseObject(rtaDeviceStr,RtaDevice.class);

                if(StringUtils.isNotBlank(rtaDevice.getImei())){
                    rtaDevice.setImei(MD5Utils.getMd5Sum(rtaDevice.getImei()));
                }
                rtaSetSigleService.setRedisDayUserArpu(rtaDevice);
                rtaDevicesList.add(rtaDevice);
//                log.info("cost "+(System.currentTimeMillis()-ctime));
            }catch (Exception e){
                log.error("",e);
            }
        }
        rtaTableService.createIfNot(RtaTableService.rtaDevice);
        rtaDeviceService.saveBatch(rtaDevicesList);
        XxlJobLogger.log("cost "+(System.currentTimeMillis()-ctime)+" 保存 "+rtaDevicesList.size());
    }

}
