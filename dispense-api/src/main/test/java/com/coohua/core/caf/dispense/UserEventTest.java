package com.coohua.core.caf.dispense;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.ck.entity.GyRecordDataLocal;
import com.coohua.core.caf.dispense.ck.service.GyRecordDataService;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.Product;
import com.coohua.core.caf.dispense.dsp.entity.SdkProductPkg;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.OppoUserEventService;
import com.coohua.core.caf.dispense.dsp.service.ProductService;
import com.coohua.core.caf.dispense.dsp.service.SdkProductPkgService;
import com.coohua.core.caf.dispense.dto.req.OppoAciveReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseUtils;
import com.coohua.core.caf.dispense.motan.service.UserEventRpcService;
import com.coohua.core.caf.dispense.ocpc.entity.UserBid;
import com.coohua.core.caf.dispense.ocpc.mapper.UserBidMapper;
import com.coohua.core.caf.dispense.ocpc.service.RetainOneDayPeopleService;
import com.coohua.core.caf.dispense.ocpc.service.UserBidService;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import javafx.util.Pair;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = ApiMainApplication.class)
@RunWith(SpringJUnit4ClassRunner.class)
public class UserEventTest {

    @Autowired
    UserBidService userBidService;

    @Test
    public void test() {
        Map userCpa = userBidService.queryUserCpaBy("2095042353", "qzfkdd");
        //System.out.println(userCpa.getUserId());
    }

    @Autowired
    UserEventRpcService userEventRpcService;

    @Test
    public void test1() {
        String s1 = userEventRpcService.queryUserECPMBatch(null, null, null);
        System.out.println(s1);
    }

    @Autowired
    ProductService productService;

    @Test
    public void test2() {
        Product product = productService.getByCnName("合合富贵");
        System.out.println(product.getName());

    }

    @Autowired
    UserBidMapper userBidMapper;

    @Test
    public void test3() {
        Page<UserBid> userBidPage = new Page<>(1, 200);
        IPage<UserBid> userBidIPageList = userBidMapper.selectPage(userBidPage, null);
        System.out.println(userBidIPageList.getRecords().size());
    }

    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @Test
    public void test4() {
        System.out.println(ocpcSwitcher.callBackProductList.contains("qmxsg"));
        //System.out.println(ocpcSwitcher.bidCallBackSwitcher);
    }

    @Autowired
    GyRecordDataService gyRecordDataService;

    @Test
    public void test5() {
        List<GyRecordDataLocal> dataList = new ArrayList<>();
        GyRecordDataLocal gyRecordData = new GyRecordDataLocal();
        gyRecordData.setLogday(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        gyRecordData.setIdfa("test1");
        gyRecordData.setProduct("test1");
        gyRecordData.setUserId("12345677");
        gyRecordData.setOs("ios");
        dataList.add(gyRecordData);
        gyRecordDataService.saveGyRecordDataBatch(dataList);
    }

    @Resource
    private Connection hbaseConnection;
    @Resource
    private Connection toutiaoClickConnection;

    @Test
    public void test6() {

        try (Admin admin = toutiaoClickConnection.getAdmin()){
            TableName tableName = TableName.valueOf("ToutiaoClickLong");
            // 4. 触发Major Compaction（对整个表的所有列族）
            admin.majorCompact(tableName);
            CompactionState state = admin.getCompactionState(tableName);
            while (state != CompactionState.NONE) {
                Thread.sleep(5000); // 等待5秒
                state = admin.getCompactionState(tableName);
                System.out.println("Compaction state: " + state);
            }
            System.out.println("Major compaction completed.");

        }catch (IOException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Autowired
    RetainOneDayPeopleService retainOneDayPeopleService;

    @Test
    public void test7() {
        String json = "{\"accumulateDuration\":335,\"actionDefault\":false,\"actionValues\":\"1-5,2-10.161\",\"androidId\":\"27af152d877c872ed4f9df3f051ad875\",\"appId\":1367,\"eventType\":25,\"ip\":\"*************\",\"isSaveAction\":true,\"mac\":\"79077c629b81d8a068ace7f0ec6b63ee\",\"model\":\"V2002A\",\"oaid\":\"c3b12c8b847aac48be0a961abb053173bc002fd0ac0a0e43d0ce6c8caa2e7812\",\"ocpcDeviceId\":\"27af152d877c872ed4f9df3f051ad875\",\"os\":\"android\",\"pkgChannel\":\"ksdrylfc01\",\"product\":\"ylfc\",\"souceType\":\"DSP\",\"sourceDeviceId\":\"5d52381b87c0b08d\",\"timerDelay\":false,\"ua\":\"Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/83.0.4103.106 Mobile Safari/537.36\",\"userId\":\"2113228368\"}";
        UserEventReq userEventReq = JSONObject.parseObject(json, UserEventReq.class);

        Pair<String, Integer> retainOneDayPeoplePair = retainOneDayPeopleService.queryRetainOneDayPeopleByDevice(userEventReq);
        System.out.println(retainOneDayPeoplePair.getKey());
        System.out.println(retainOneDayPeoplePair.getValue());
    }


    @Test
    public void test8() {
        Map<Integer, String> juliangProductMap = ocpcSwitcher.iosJuliangProductMap;
        juliangProductMap.values().forEach(System.out::println);
        System.out.println(juliangProductMap.get(1371).split(":")[1]);
    }

    @Autowired
    HbaseClickService hbaseClickService;

    @Test
    public void test9() {
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setProduct("cffk");
        userEventReq.setOs("android");
        userEventReq.setOaid("56CC246B3C93415B890A2E39A2A7C0F2d9905c407f76e1c7bd9276fd156a417c");
        List<ToutiaoClick> toutiaoClickList = hbaseClickService.getToutiaoClickList(userEventReq);
        System.out.println(toutiaoClickList.size());
    }

    @Autowired
    OppoUserEventService oppoUserEventService;

    @Test
    public void test10() {
        OppoAciveReq oppoAciveReq = new OppoAciveReq();
        oppoAciveReq.setAdId(Long.parseLong("561256245"));
        oppoAciveReq.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq.setDataType(1);
        try {
            oppoAciveReq.setOuId(oppoUserEventService.oppoEncode("9216466DE51B4CD28987857BF2FD149C197ca4b49c59fead1ba5fb0e71fc09d9"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq.setType(0);
        oppoAciveReq.setPkg("com.yyxh.hjwl");
        oppoAciveReq.setTimestamp(new Date().getTime());

        OppoAciveReq oppoAciveReq1 = new OppoAciveReq();
        oppoAciveReq1.setAdId(Long.parseLong("561256245"));
        oppoAciveReq1.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq1.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq1.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq1.setDataType(1);
        try {
            oppoAciveReq1.setOuId(oppoUserEventService.oppoEncode("329B1F096400438795BA32CF6717EAF14aed2bb6c93687950136860fb7723265"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq1.setType(0);
        oppoAciveReq1.setPkg("com.yyxh.hjwl");
        oppoAciveReq1.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq2 = new OppoAciveReq();
        oppoAciveReq2.setAdId(Long.parseLong("561256245"));
        oppoAciveReq2.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq2.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq2.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq2.setDataType(1);
        try {
            oppoAciveReq2.setOuId(oppoUserEventService.oppoEncode("90BA2AE0059B4F20AAE9D9C0D235C58D741e5988b1b725c4925796ca04afc108"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq2.setType(0);
        oppoAciveReq2.setPkg("com.yyxh.hjwl");
        oppoAciveReq2.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq3 = new OppoAciveReq();
        oppoAciveReq3.setAdId(Long.parseLong("561256245"));
        oppoAciveReq3.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq3.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq3.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq3.setDataType(1);
        try {
            oppoAciveReq3.setOuId(oppoUserEventService.oppoEncode("2B400E37BA8C41C784E8CF49DE8DA4EB1c4aa2d23dd61b0ab9bfc15be2058802"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq3.setType(0);
        oppoAciveReq3.setPkg("com.yyxh.hjwl");
        oppoAciveReq3.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq4 = new OppoAciveReq();
        oppoAciveReq4.setAdId(Long.parseLong("561256245"));
        oppoAciveReq4.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq4.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq4.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq4.setDataType(1);
        try {
            oppoAciveReq4.setOuId(oppoUserEventService.oppoEncode("E0E8F7F1CA234BF0BE5579415D22E6CF3c1c4cb154f2d6481827a46486c4a57a"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq4.setType(0);
        oppoAciveReq4.setPkg("com.yyxh.hjwl");
        oppoAciveReq4.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq5 = new OppoAciveReq();
        oppoAciveReq5.setAdId(Long.parseLong("561256245"));
        oppoAciveReq5.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq5.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq5.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq5.setDataType(1);
        try {
            oppoAciveReq5.setOuId(oppoUserEventService.oppoEncode("9755F439466C4505AEF61C3175ADC40966bee818bc6f38cba84ec2b32ec3d552"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq5.setType(0);
        oppoAciveReq5.setPkg("com.yyxh.hjwl");
        oppoAciveReq5.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq6 = new OppoAciveReq();
        oppoAciveReq6.setAdId(Long.parseLong("561256245"));
        oppoAciveReq6.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq6.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq6.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq6.setDataType(1);
        try {
            oppoAciveReq6.setOuId(oppoUserEventService.oppoEncode("4439E77E3C6D4DB8BE3A0050C2728E7988b055aaa558f73d3cf89a1b7ed37747"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq6.setType(0);
        oppoAciveReq6.setPkg("com.yyxh.hjwl");
        oppoAciveReq6.setTimestamp(new Date().getTime());

        OppoAciveReq oppoAciveReq7 = new OppoAciveReq();
        oppoAciveReq7.setAdId(Long.parseLong("561256245"));
        oppoAciveReq7.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq7.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq7.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq7.setDataType(1);
        try {
            oppoAciveReq7.setOuId(oppoUserEventService.oppoEncode("F86DAAFEBC51484486C492917D4F1EEFd9b7efee377df704c5e9b4144268ad0e"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq7.setType(0);
        oppoAciveReq7.setPkg("com.yyxh.hjwl");
        oppoAciveReq7.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq8 = new OppoAciveReq();
        oppoAciveReq8.setAdId(Long.parseLong("561256245"));
        oppoAciveReq8.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq8.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq8.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq8.setDataType(1);
        try {
            oppoAciveReq8.setOuId(oppoUserEventService.oppoEncode("4BDB447C732C477B89F3D9484800C4C9cdc1e6c413a7e28ea7e97cab656bb739"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq8.setType(0);
        oppoAciveReq8.setPkg("com.yyxh.hjwl");
        oppoAciveReq8.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq9 = new OppoAciveReq();
        oppoAciveReq9.setAdId(Long.parseLong("561256245"));
        oppoAciveReq9.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq9.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq9.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq9.setDataType(1);
        try {
            oppoAciveReq9.setOuId(oppoUserEventService.oppoEncode("1D5DD8E511C74E9B8B3D7A7681EFE8A4561011ef8f0c201e369ea79f9d955224"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq9.setType(0);
        oppoAciveReq9.setPkg("com.yyxh.hjwl");
        oppoAciveReq9.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq10 = new OppoAciveReq();
        oppoAciveReq10.setAdId(Long.parseLong("561256245"));
        oppoAciveReq10.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq10.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq10.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq10.setDataType(1);
        try {
            oppoAciveReq10.setOuId(oppoUserEventService.oppoEncode("F61FBD0A37D746AD9936C9735935155848d1791541ff6ad43c61584b45162b91"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq10.setType(0);
        oppoAciveReq10.setPkg("com.yyxh.hjwl");
        oppoAciveReq10.setTimestamp(new Date().getTime());


        OppoAciveReq oppoAciveReq11 = new OppoAciveReq();
        oppoAciveReq11.setAdId(Long.parseLong("561256245"));
        oppoAciveReq11.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
        oppoAciveReq11.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
        oppoAciveReq11.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
        oppoAciveReq11.setDataType(1);
        try {
            oppoAciveReq11.setOuId(oppoUserEventService.oppoEncode("6F13758DCAC24C5D81BF28B6342F5A1C6ae1b36cc4eab36d095c2b60998e6538"));
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
        oppoAciveReq11.setType(0);
        oppoAciveReq11.setPkg("com.yyxh.hjwl");
        oppoAciveReq11.setTimestamp(new Date().getTime());

        oppoUserEventService.sendOppo(oppoAciveReq,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq1,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq2,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq3,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq4,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq5,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq6,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq7,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq8,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq9,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq10,"hjwl","1000500032");
        oppoUserEventService.sendOppo(oppoAciveReq11,"hjwl","1000500032");
    }

    @Autowired
    SafeDataRedisService safeDataRedisService;

    @Test
    public void test11() {
        List<String> caidListWithUserId = safeDataRedisService.getCaidListWithUserId("2147483647");
        System.out.println(caidListWithUserId);
    }

    @Autowired
    SdkProductPkgService sdkProductPkgService;

    @Test
    public void test12() {
        Map<Integer, String> juliangProductMap = ocpcSwitcher.juliangProductMap;


        for (Map.Entry<Integer, String> entry : juliangProductMap.entrySet()) {
            Integer appId = entry.getKey();
            String value = entry.getValue();
            String product = value.split(":")[0];
            String pkg = value.split(":")[1];

            SdkProductPkg sdkProductPkg = new SdkProductPkg();

            Product product1 = productService.getByName(product);

            sdkProductPkg.setProductName(product1.getRemark());
            sdkProductPkg.setProduct(product);
            sdkProductPkg.setOs("android");
            sdkProductPkg.setDelFlag(0);
            sdkProductPkg.setAppId(appId);
            sdkProductPkg.setPkgName(pkg);
            sdkProductPkg.setCreateTime(new Date());
            sdkProductPkg.setUpdateTime(new Date());

            if (sdkProductPkgService.getProductPkg(appId + "_" + "android") != null) {
                continue;
            }

            sdkProductPkgService.save(sdkProductPkg);
        }


    }
}
