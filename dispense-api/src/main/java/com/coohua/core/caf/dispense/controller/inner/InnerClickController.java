package com.coohua.core.caf.dispense.controller.inner;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.InnerPkgToProductCache;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.InnerClickReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * (。≖ˇェˇ≖。)
 * 内部导流click存储
 * @author: zkk
 * DateTime: 2020/12/3 10:17
 */
@RestController
@RequestMapping("/dispense/inner")
@DependsOn("innerPkgToProductCache")
@Slf4j
public class InnerClickController {


	@Autowired
	private ToutiaoClickService toutiaoClickService;

	@Autowired
	private AlertService alertService;

	@Autowired
	RedisClickService redisClickService;
	@Value(value= "${cache.count.enable:true}")
	private Boolean cacheCountEnable;
	@Autowired
	@Qualifier("bp-dispenseJedisClusterClient")
	private JedisClusterClient bpDispenseJedisClusterClient;
	@Autowired
	OcpcSwitcher ocpcSwitcher;
	@Autowired
	HbaseClickService hbaseClickService;
	@Autowired
	KafkaSender kafkaSender;
	/**
	 * 内部导流 click接口
	 * @param req
	 * @param request
	 * @return
	 */
	@GetMapping("/click")
	@ResponseBody
	public ReturnResult click(InnerClickReq req, HttpServletRequest request) {

		log.info("inner click request , req = {}", JSONObject.toJSONString(req));
		boolean isRp = false;
		if(ocpcSwitcher.writeRedisSwitch){
			isRp = isRepeatReport(req);//是否重复
			String rkey = getSkey(req);
			if(!isRp){
				bpDispenseJedisClusterClient.setex(rkey,2,"1");
			}
		}

		if(!isRp){
			ToutiaoClick toutiaoClick = new ToutiaoClick();
			toutiaoClick.setDsp(req.getDsp());
			//用包名替换product
			String product = req.getProduct();
			if (!StringUtils.isBlank(product)) {
				String toProduct = InnerPkgToProductCache.PKG_TO_PRODUCT_MAP.get(StringUtils.trim(product));
				product = StringUtils.isBlank(toProduct) ? product : toProduct;
			}
			toutiaoClick.setProduct(product);
			toutiaoClick.setOs(req.getOs());
			toutiaoClick.setOcpcDeviceId(req.getOcpc_device_id());
			toutiaoClick.setTs(req.getTs());
			toutiaoClick.setOaid(req.getOaid());
			toutiaoClick.setAccountName(req.getSourceProduct());
			alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-"+req.getDsp());

			Date date = new Date();
			toutiaoClick.setUpdateTime(date);
			toutiaoClick.setCreateTime(date);

			toutiaoClickService.saveClick(toutiaoClick);
//			log.info("save success isF:{}",isF);
			redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
					toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
			hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
					toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
			kafkaSender.sendClick(toutiaoClick);
		}else{
			log.info("重复数据  ");
		}
		ReturnResult returnResult = new ReturnResult();
		return returnResult;
	}

	private boolean isRepeatReport(InnerClickReq req){
		String rkey = getSkey(req);
		boolean isRp = false;//是否重复
		try {
			if(rkey!=null){
				String xy = bpDispenseJedisClusterClient.get(rkey);
				if(StringUtils.isNotBlank(xy) && "1".equalsIgnoreCase(xy)){
					isRp = true;
				}
			}
		}catch (Exception e){
			log.error("",e);
		}
		return isRp;
	}

	private String getSkey(InnerClickReq req){
		String rkey = null;
		if(StringUtils.isNotBlank(req.getOcpc_device_id())){
			rkey = req.getProduct()+" "+req.getDsp()+" "+req.getOcpc_device_id();
		}
		return rkey;
	}


}
