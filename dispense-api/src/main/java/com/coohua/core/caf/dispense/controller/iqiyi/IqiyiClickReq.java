package com.coohua.core.caf.dispense.controller.iqiyi;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/7/6
 **/
@Data
public class IqiyiClickReq {

    /**
     *
     https://ocpc-api.shinet.cn/dispense/iqiyi/click?
     dsp=iqiyi&
     product=yydxny&
     os=__OS__&
     account_id=__ADVERTISER_ID__&
     idfa=__IDFA__&
     imeiMd5=__IMEI__&
     oaid=__OAID__&
     oaid2=__OAID_MD5__&
     androidId=__ANDROIDID__&
     mac=__MAC__&
     ip=__IP__&
     ts=__TS__&
     callbackUrl=__CALLBACK_URL__&
     pid=__ORDER_PLAN_ID__&
     cid=__CREATIVE_ID__&
     gid=__ORDER_GROUP_ID__

     */

    private String dsp;//baidusem baidufeed
    private String product;//写死的
    private String os;//客户端操作系统的 类型 1 位数字,取 0~3。 0 表示 Android 1 表示 iOS 2 表示 Windows Phone 3 表示其他
    private String account_id;//账户ID

    private String idfa;// ios 设备ID
    private String imeiMd5;//(设备ID md5 值)
    private String oaid;//oaid 原值
    private String oaid2;//oaid md5
    private String androidId; // md5加密值，对原值进行标准32位小写MD5编码
    private String mac; // md5加密值，去除分隔符 ":"（例：32738C807A28），再进行标准32位小写MD5编码

    private String ip;
    private String ts;//点击时间
    private String callbackUrl;//回调地址
    private String pid;//广告ID
    private String cid;//创意ID
    private String gid;//广告系列 ID

}
