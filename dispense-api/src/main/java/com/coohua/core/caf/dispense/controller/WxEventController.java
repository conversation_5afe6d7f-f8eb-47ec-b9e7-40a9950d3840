package com.coohua.core.caf.dispense.controller;

import com.alibaba.fastjson.JSON;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.OcpcMisActive;
import com.coohua.core.caf.dispense.dsp.service.OcpcMisActiveService;
import com.coohua.core.caf.dispense.dsp.service.wmin.TTDaRenWxService;
import com.coohua.core.caf.dispense.dsp.service.wmin.TTWxService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.WxActiveReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;

@Controller
@RequestMapping("/dispense")
@Slf4j
public class WxEventController {
    //    ///pages/index.html?clue_token=aa&ad_id=dd&creative_id=mm&req_id=mddm&advertiser_id=dop
    @Autowired
    TTWxService wxService;
    @Autowired
    OcpcMisActiveService ocpcMisActiveService;
    @Autowired
    UserActiveService userActiveService;
    @Autowired
    UserEventController userEventController;
    @Autowired
    RedisEventService redisEventService;

    @Autowired
    HbaseClickService hbaseClickService;

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Autowired
    TTDaRenWxService ttdaRenWxService;
    @RequestMapping(value = "/wactive")
    @ResponseBody
    public ReturnResult queryEvent(WxActiveReq wxActiveReq,
                                   HttpServletRequest request) {

        String userId = wxActiveReq.getUserId();
        UserActive userActive = userActiveService.queryActive(userId,wxActiveReq.getProduct());
        long day = 0l;
        long ctime = 0l;
        String dsp = "zran";
        if(userActive!=null){
            ctime = (System.currentTimeMillis()-userActive.getCreateTime().getTime());
            day = (ctime / DateTimeConstants.MILLIS_PER_DAY);
        }else{
            log.warn("mc时间为空 "+userId);
        }

        String lockKey = RedisKeyConstants.getWxActiveLockKey(wxActiveReq.getProduct(), "wx", "1",wxActiveReq.getUserId());
        String s = bpDispenseJedisClusterClient.get(lockKey);

        if(StringUtils.isBlank(s)){
            if(day>1){
                log.info("wx用户 "+userId+" prudcut"+wxActiveReq.getProduct()+" 超过一天 直接忽略 ");
                wxActiveReq.setRequestId(ctime +"@day="+day);
            }else{
                //http://ocpc-api.shinet-inc.com/dispense/wactive?userId=1529652039&product=dszg&os=wmin&appId=864&openId=oa_NK5Ys9zppQSj2elNmL8gf9oJQ&eventType=0&ip=************
                if(StringUtils.isNotBlank(wxActiveReq.getClue_token())){
                    log.info("userMin = {} ", JSON.toJSONString(wxActiveReq));

                    if(StringUtils.equalsIgnoreCase("undefined",wxActiveReq.getAd_id())){
                        wxActiveReq.setAd_id("0");
                    }
                    if(StringUtils.equalsIgnoreCase("undefined",wxActiveReq.getCreative_id())){
                        wxActiveReq.setCreative_id("0");
                    }

                    wxService.sendWxActive(wxActiveReq);
                    dsp = "ttxx";
                }else if(StringUtils.isNotBlank(wxActiveReq.getClickid()) && !StringUtils.equalsIgnoreCase("undefined",wxActiveReq.getClickid())){
                    log.info("darenmin = {} ", JSON.toJSONString(wxActiveReq));

                    if(StringUtils.equalsIgnoreCase("undefined",wxActiveReq.getAd_id())){
                        wxActiveReq.setAd_id("0");
                    }
                    if(StringUtils.equalsIgnoreCase("undefined",wxActiveReq.getCreative_id())){
                        wxActiveReq.setCreative_id("0");
                    }

                    ttdaRenWxService.sendDRWxActive(wxActiveReq);
                }else{
                    //后端直接调用
                    UserEventReq userEventReq = new UserEventReq();
                    userEventReq.setEventType(0);
                    userEventReq.setProduct(wxActiveReq.getProduct());
                    userEventReq.setUserId(wxActiveReq.getUserId());
                    userEventReq.setOpenId(wxActiveReq.getOpenId());
                    userEventReq.setAppId(wxActiveReq.getAppId());
                    userEventReq.setOs(wxActiveReq.getOs());
                    //eventType=0&openId=oa_NK5RBi1EKN-sc5x_rc7yyH17w&os=wmin&product=dszg&userId=1532551783&appId=911
                    OcpcEvent ocpcEvent = redisEventService.getActiveEvent(userEventReq);
                    if(ocpcEvent==null){
                        ReturnResult rst = userEventController.activate(userEventReq,request);
                        log.info("wmin开始回传激活行为 "+userEventReq.getOpenId()+"  "+userEventReq.getUserId()+" 结果为 "+rst.getData());
                    }else{
                        log.info("wmin激活已经回传 "+userId+" @ " +JSON.toJSONString(userEventReq));
                    }
                }
            }
        }else{
            log.info("wmin激活已经激活 "+userId+" @ " +JSON.toJSONString(wxActiveReq));
        }
        ocpcMisActiveService.saveOcpcMisActive(wxActiveReq);
        if(StringUtils.isBlank(s)){
            bpDispenseJedisClusterClient.setex(lockKey,30*DateTimeConstants.SECONDS_PER_DAY,"1");
        }
        checkOc(wxActiveReq);
        return new ReturnResult();
    }
    private void checkOc(WxActiveReq wxActiveReq){
        try {
            UserEventReq userEventReq = new UserEventReq();
            userEventReq.setEventType(0);
            userEventReq.setProduct(wxActiveReq.getProduct());
            userEventReq.setUserId(wxActiveReq.getUserId());
            userEventReq.setOpenId(wxActiveReq.getOpenId());
            userEventReq.setAppId(wxActiveReq.getAppId());
            userEventReq.setOs(wxActiveReq.getOs());
            //eventType=0&openId=oa_NK5RBi1EKN-sc5x_rc7yyH17w&os=wmin&product=dszg&userId=1532551783&appId=911
            OcpcEvent ocpcEvent = redisEventService.getActiveEvent(userEventReq);
            if(ocpcEvent!=null){
                log.info("ocp dsp is "+ocpcEvent.getDsp());
                if(!ocpcEvent.getDsp().equalsIgnoreCase("ttwx") && StringUtils.isNotBlank(wxActiveReq.getClue_token())){
                    OcpcMisActive ocpcMisActive = new OcpcMisActive();
                    Date date = new Date();
                    ocpcMisActive.setCreateTime(date);
                    ocpcMisActive.setUpdateTime(date);
                    ocpcMisActive.setProduct(wxActiveReq.getProduct());
                    ocpcMisActive.setOs(wxActiveReq.getOs());
                    ocpcMisActive.setUserId(wxActiveReq.getUserId());
                    ocpcMisActive.setOaid(wxActiveReq.getRequestId());
                    ocpcMisActive.setDsp("cfff");

                    ocpcMisActive.setGid(wxActiveReq.getAdvertiser_id());
                    ocpcMisActive.setCid(wxActiveReq.getCreative_id());
                    ocpcMisActive.setMac(wxActiveReq.getClue_token());
                    ocpcMisActive.setCaid(wxActiveReq.getRequestId());
                    ocpcMisActive.setIp(wxActiveReq.getRequestId());
                    ocpcMisActive.setClickTime(date);
                    ocpcMisActiveService.save(ocpcMisActive);

                    log.info("wxdd重复回调 "+ocpcMisActive.getUserId()+" ");
                }
            }
        }catch (Exception e){
            log.error("插入未归因数据",e);
        }
    }
    //public static final String WX_APPID = "wx9ad232ecbcb0ceb1";
    //
    //public static final String WX_SECRET = "184ab0ffa597d8f09395b1c55b4f13b9"
    public static void main(String[] args){
        //获取吊起 scheme
//        https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET
        try {
            String getAckUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxbe8b1560f4536c81&secret=d3a313fd98621ef56b72b361f7546688";
            HttpConfig config =
                    HttpConfig.custom().encoding("utf-8").url(getAckUrl);
            String wxRps = HttpClientUtil.get(config);

            String wxAcktoken = JSON.parseObject(wxRps).getString("access_token");

            System.out.println(wxRps);

//            https://api.weixin.qq.com/wxa/generatescheme?access_token=ACCESS_TOKEN

            String jston = "{\n" +
                    "  \"jump_wxa\": {\n" +
                    "    \"query\": \"clue_token=wxtxstoken&ad_id=dd&creative_id=c00009&req_id=i999000&advertiser_id=1743915173516360\"\n" +
                    "  },\n" +
                    "  \"expire_type\": 1,\n" +
                    "  \"expire_interval\": 20\n" +
                    "}";
            String schUrl = "https://api.weixin.qq.com/wxa/generatescheme?access_token="+wxAcktoken;
            HttpConfig configPost = HttpConfig.custom().encoding("utf-8").url(schUrl).json(jston);
            String schRsp = HttpClientUtil.post(configPost);
            System.out.println(schRsp);
//            String schmeUrl = JSON.parseObject(schRsp).getString("access_token");
        }catch (Exception e){
            log.error("",e);
        }
    }


}
