package com.coohua.core.caf.dispense.controller.timer;

import com.coohua.core.caf.dispense.dsp.service.UserEventFailedService;
import com.coohua.core.caf.dispense.dsp.service.UserEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class ActiveReissueTimer {
    @Autowired
    UserEventService userEventService;
    @Autowired
    UserEventFailedService userEventFailedService;

    @Scheduled(cron = "0 */15 * * * ?")
    public void resendEventType(){
        userEventService.updEventNoClick();
    }

    @Scheduled(cron = "0 8/15 * * * ?")
    public void compensateEvent(){
        userEventFailedService.runUserEventError();
    }
}
