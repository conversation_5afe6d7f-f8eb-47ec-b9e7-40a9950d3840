package com.coohua.core.caf.dispense.controller.ali;

import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.ToutiaoClickReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.ocpc.service.CaidService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * @ClassName: ToutiaoClickController
 * @Description:
 * @Author: fan jin yang
 * @Date: 2020/6/23
 * @Version: 1.0.0
 **/
@RestController
@RequestMapping("/dispense/ali")
@Slf4j
public class AliClickController {

    @Autowired
    private ToutiaoClickService toutiaoClickService;

    @Autowired
    private AlertService alertService;

    @Autowired
    RedisClickService redisClickService;

    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    ConstApolloConfig constApolloConfig;
    @Autowired
    RedisRelationshipService redisRelationshipService;

    @GetMapping("/click")
    @ResponseBody
    public ReturnResult click(ToutiaoClickReq toutiaoClickReq, HttpServletRequest request) {
//        toutiaoClickService.checkLength(DspType.TOUTIAO, toutiaoClickReq);

//        log.info("click request , toutiaoClickReq = {}", JSONObject.toJSONString(toutiaoClickReq));

        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(toutiaoClickReq.getDsp());
        toutiaoClick.setProduct(toutiaoClickReq.getProduct());
        toutiaoClick.setOs(toutiaoClickReq.getOs());
        toutiaoClick.setAccountId(toutiaoClickReq.getAccount_id());
        toutiaoClick.setOcpcDeviceId(toutiaoClickReq.getOcpc_device_id());
        toutiaoClick.setIdfa2(toutiaoClickReq.getIdfa2());
        toutiaoClick.setTs(toutiaoClickReq.getTs());
        toutiaoClick.setCallbackUrl(toutiaoClickReq.getCallback_url());
        toutiaoClick.setCid(toutiaoClickReq.getCid());
        toutiaoClick.setGid(toutiaoClickReq.getGid());
        toutiaoClick.setPid(toutiaoClickReq.getPid());
        toutiaoClick.setOaid(toutiaoClickReq.getOaid());
        toutiaoClick.setOaid2(toutiaoClickReq.getOaid2());
//        toutiaoClick.setAccountName(toutiaoClickReq.getAccountName());
        toutiaoClick.setCidName(toutiaoClickReq.getCidName());
        toutiaoClick.setAidName(toutiaoClickReq.getAidName());
        toutiaoClick.setGroupName(StringUtils.isNotBlank(toutiaoClickReq.getUnion_site())?toutiaoClickReq.getUnion_site():"");
        toutiaoClick.setMac(toutiaoClickReq.getMac());
        toutiaoClick.setUnionSite(StringUtils.isNotBlank(toutiaoClickReq.getUnion_site())?toutiaoClickReq.getUnion_site():"");
        toutiaoClick.setAndroidId(toutiaoClickReq.getAndroidId() != null?toutiaoClickReq.getAndroidId():"");
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-阿里");

        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);

        if (toutiaoClickReq.getIp() != null && toutiaoClickReq.getIp().length() <= BaseConstants.IpMaxLength) {
            toutiaoClick.setIp(toutiaoClickReq.getIp());
        }
        if (StringUtils.isNotBlank(toutiaoClickReq.getUa())) {
            toutiaoClick.setUa(MD5Utils.getMd5Sum(toutiaoClickReq.getUa()));
        }
        if (toutiaoClickReq.getModel() != null && toutiaoClickReq.getModel().length() <= BaseConstants.ModelMaxLength) {
            toutiaoClick.setModel(toutiaoClickReq.getModel());
        }
        if("ios".equalsIgnoreCase(toutiaoClickReq.getOs())){
            try {
                if(StringUtils.isNotBlank(toutiaoClickReq.getCaid())){
                    String caidStr = toutiaoClickReq.getCaid();
                    if(caidStr.contains("version")){
//                        log.info("caidclick头条caid2 "+ JSON.toJSONString(toutiaoClickReq));
                        String caid = CaidService.getTTCaid(caidStr,"20220111");
                        toutiaoClick.setCaid(caid);
                    }else{
                        toutiaoClick.setCaid(toutiaoClickReq.getCaid());
                    }
                }
            } catch (Exception e) {
                log.warn("toutiao ua", e);
            }
        }
        toutiaoClick.setClickId(toutiaoClickReq.getClickId());
        boolean isF = false;
        if(isValidReq(toutiaoClickReq)){
            isF = toutiaoClickService.saveClick(toutiaoClick);
        }else{
            log.info(toutiaoClick.getOcpcDeviceId()+" 不合规 ");
        }
        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
            toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        kafkaSender.sendClick(toutiaoClick);
        //将deviceId与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),toutiaoClickReq.getOs(),toutiaoClick);
        ReturnResult returnResult = new ReturnResult();
        return returnResult;
    }


    private boolean isValidReq(ToutiaoClickReq toutiaoClickReq){
        boolean isVal = true;
        if ("__CALLBACK_URL__".equals(toutiaoClickReq.getCallback_url())) {
            isVal = false;
        }
        if(StringUtils.isEmpty(toutiaoClickReq.getOcpc_device_id())
                &&StringUtils.isEmpty(toutiaoClickReq.getOaid())
                &&StringUtils.isEmpty(toutiaoClickReq.getMac())){
            isVal = false;
        }
        return isVal;
    }
}
