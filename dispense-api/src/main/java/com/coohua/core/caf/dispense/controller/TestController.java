package com.coohua.core.caf.dispense.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.apollo.ExtEventApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.ck.entity.ArpuPvReportRecord;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.controller.vivo.VivoClickController;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.XiaomiAction;
import com.coohua.core.caf.dispense.dsp.entity.XiaomiAdvertiser;
import com.coohua.core.caf.dispense.dsp.service.*;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.XiaomiActiveReq;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.OcpcSourceType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.enums.XiaomiEventType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.kafka.ArpuPvReportSender;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.kafka.VideoAdReportEntitiy;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.StandardHttpRequestRetryHandler;
import org.apache.http.ssl.SSLContexts;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Set;

import static com.coohua.core.caf.dispense.enums.AccountActionTypeEnum.ARPU_ONE_DAY;
import static com.coohua.core.caf.dispense.enums.AccountActionTypeEnum.WATCH_VIDEO;


@Controller
@Slf4j
public class TestController {

    @Autowired
    TencentDeveloperService tencentDeveloperService;
    @Autowired
    BaiduAdvertiserService baiduAdvertiserService;
    @Autowired
    IqiyiAdvertiserService iqiyiAdvertiserService;

    @Autowired
    KuaishouUserEventService kuaishouUserEventService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    ExtEventApolloConfig extEventApolloConfig;
    @Autowired
    ThirdExtEventService thirdExtEventService;

    @Autowired
    VivoUserEventService vivoUserEventService;
    @Autowired
    ConstApolloConfig constApolloConfig;
    @Autowired
    InitService initService;
    @Autowired
    XiaomiUserEventService xiaomiUserEventService;
    @Autowired
    AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    @Autowired
    VivoClickController vivoClickController;
    @Autowired
    UserEventController userEventController;
    @Autowired
    RedisClickService redisClickService;


    @RequestMapping(value = "/dispense/user/event/test")
    @ResponseBody
    public Object test(String p1, String p2, String p3, String p4) throws Exception {
        if (!SystemInfo.isWin()) {
            log.info("非win环境不执行test");
            return "";
        }

//        Map<String, String> map = new HashMap<>();
//        map.put("1", "10");
//        map.put("3", ObjectUtils.defaultIfNull(param, "8.9"));
//
//        ActionTypes actionTypes = ActionTypes.build(map);
//
//        ToutiaoExtEvent r1 = toutiaoExtEventService.getFirstHigherExtEvent(actionTypes);
//        ToutiaoExtEvent r2 = toutiaoExtEventService.getFirstLowerExtEvent(actionTypes);
//
//        String msg =  r1.getEventValues() + " " + r2.getEventValues();
//        log.info(msg);

        // 本地测试清除用户关键行为记录
//        testClearKeyEvent();

        // 查询点击信息
//        testQueryClick();

//        testKuaishouExt();

//        testXiaomi(p1, p2, p3);

        // 刷新缓存
//        SpringContextUtils.getBean(VivoAdvertiserService.class).startInitVivoAdvertiser();
//        initService.refreshAccountAction();

        // vivo假数据回传
        vivoFakeDataCallback(p1, p2, p3, p4);

        return LocalDateTime.now().toString();
    }

    private void testKuaishouExt() {
        VideoAdReportEntitiy mqMsg = new VideoAdReportEntitiy();
        mqMsg.setAdType(4);
        mqMsg.setPrice("32");


        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setAppId(456);
        userEventReq.setUserId("*********");
        userEventReq.setProduct("yydxny");
        userEventReq.setOaid("f00b606cb931a80add94acca5d65e698b1d796729b24b252b0881457c1c04bb5");
        userEventReq.setOs("android");
        // ios时 取device_id明文，android时取 md5(imei)
        userEventReq.setOcpcDeviceId("84b911ab29ed6d35b6aad125cccc620d");
        // ios时 取device_id明文，android时取 imei明文
        userEventReq.setSourceDeviceId("***************");
        userEventReq.setEventType(ToutiaoEventTypeEnum.LTV.value);
        userEventReq.trySetOaid2AndIdfa2(false);

        OcpcEvent ocpcEvent =  redisEventService.getActiveEvent(userEventReq);

//        SpringContextUtils.getBean(KuaishouUserEventService.class).callbackExtProcess(userEventReq, ocpcEvent, mqMsg);
    }

    /** 本地测试清除用户关键行为记录 */
    private void testClearKeyEvent() {
        String product = "yyrs";
        String key = "key:event:"+product+":ios:"+ DeviceType.device.value + ":732CB6C2-2F47-4EEC-89CE-0C413615D361";
//        hbaseEventService.testDeleteHbase("key:event:yydxny:android:"+ DeviceType.device.value + ":e34e6d5d7afda4154724663310afeb11");
//        hbaseEventService.testDeleteHbase("key:event:"+product+":android:"+ DeviceType.oaid.value + ":97DE7064CC2945159BC4A48E7FC39F66339d99b1cae51893d9858ec7d63954b4");
//        hbaseEventService.testDeleteHbase("key:event:"+product+":android:"+ DeviceType.oaid2.value + ":1e415c31c923ddb1847513e9cc5e54a4");
        log.info("testClearKeyEvent key: " + key);
        hbaseEventService.testDeleteHbase(key);
    }

    /** 本地测试查询点击信息 */
    private void testQueryClick() {
        ToutiaoClick toutiaoClick = null;
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setActionDefault(false);
        userEventReq.setIsSaveAction(true);
        userEventReq.setSouceType(OcpcSourceType.OCPC);
        userEventReq.setAppId(507);
        userEventReq.setProduct("jnxfsh");
        userEventReq.setOs("android");
        userEventReq.setEventType(0);
        userEventReq.setActionValues("1-11,2-8");
        userEventReq.setUserId("60188988");
        userEventReq.setOaid("97DE7064CC2945159BC4A48E7FC39F66339d99b1cae51893d9858ec7d63954b4");
        toutiaoClick = redisClickService.getClick(userEventReq, ocpcSwitcher.readOcpcHbaseSwitch);
        log.info("toutiaoClick: {}", JSON.toJSONString(toutiaoClick));
    }

    /** 小米回调请求
     * @param p1
     * @param p2
     * @param p3*/
    private void testXiaomi(String p1, String p2, String p3) throws UnsupportedEncodingException {

        XiaomiAdvertiser advertiser = new XiaomiAdvertiser();
        advertiser.setAdvertiserId("300862");
        advertiser.setAppId(Long.valueOf("1480022"));

        XiaomiAction action = new XiaomiAction();
        action.setSecretKey("IVmrputHiXiyHnbQ");

        XiaomiActiveReq req = new XiaomiActiveReq();
//        req.setImeiMd5("91b9185dba1772851dd02b276a6c969e");
        req.setOaid("12a7f9dec57adda4");
        req.setConv_time(1635335972760L);

        req.buildParams(XiaomiEventType.parse(Integer.valueOf(p3)), action, "FqK3xF8WvgkYJGU4NDJkNjQwLWI2MzMtNDMxYS1hNTA0LTcyNDVhNWEyMDE2YlgQMTJhN2Y5ZGVjNTdhZGRhNBgFQ0xJQ0sA");

        log.info("xiaomi reqUrl: " + req.getReqUrl());

//        xiaomiUserEventService.sendXiaomi(advertiser, req, "aaa");
    }

    /**
     * vivo假数据回传
     * @param p1
     */
    private void vivoFakeDataCallback(String p1, String p2, String p3, String p4) {

        String product = p2;
        int appId = Integer.parseInt(p3);
//        Long userId = appId * 1000000L;
        String advertiserId = p4;

        for (String userIdAndOaid : p1.replaceAll("\n|\r","").split(";")) {

            String dsp = "vivo";
            String os = "android";

//            VivoClickReqBody body = new VivoClickReqBody();
//            body.setAdvertiserId(advertiserId);
//            body.setAdvertiserName("测试");
//            body.setOaid(oaid);
//            body.setClickTime(String.valueOf(System.currentTimeMillis()));
//            body.setCreativeId("30000000");
//            body.setGroupId("1");
//            body.setAdId("30000000");
//
//            List<VivoClickReqBody> reqBodyList = Arrays.asList(body);
//
//            vivoClickController.click(dsp, os, product, reqBodyList);
//
//            UserEventReq req = new UserEventReq();
//            req.setAppId(appId);
//            req.setUserId(userId.toString());
//            req.setActionDefault(false);
//            req.setIsSaveAction(true);
//            req.setEventType(0);
//            req.setOs(os);
//            req.setProduct(product);
//            req.setSouceType(OcpcSourceType.OCPC);
//            req.setOaid(oaid);
//
//            userEventController.activate(req, null);

            UserEventReq req = new UserEventReq();
            req.setAppId(appId);
            req.setUserId(userIdAndOaid.split(",")[0]);
            req.setActionDefault(false);
            req.setIsSaveAction(true);
            req.setEventType(25);
            req.setOs(os);
            req.setProduct(product);
            req.setSouceType(OcpcSourceType.OCPC);
            req.setOaid(userIdAndOaid.split(",")[1]);
            req.setActionValues("1-1,2-0.1");
            req.setAccumulateDuration(10);

            userEventController.activate(req, null);

//            userId++;
        }
    }


    @Autowired
    ToutiaoCallService toutiaoCallService;
    @RequestMapping(value = "/dispense/user/event/test2")
    @ResponseBody
    public String testUserEvent(UserEventReq userEventReq, HttpServletRequest request) throws HttpProcessException {
        redisEventService.getNewClickList(userEventReq);
/*        String url = "https://analytics.oceanengine.com/api/v2/conversion";
        HttpConfig httpConfig = HttpConfig.custom().timeout(3000);
        SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(
                SSLContexts.createDefault(),
                new String[]{"TLSv1.2"},
                null,
                SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).setRetryHandler(new StandardHttpRequestRetryHandler(2, false)).build();
        httpConfig.client(httpclient);
        JSONObject body = new JSONObject();

        body.fluentPut("event_type", "active")
                .fluentPut("context", new JSONObject()
                        .fluentPut("ad", new JSONObject()
                                .fluentPut("callback", "B.lxfaaaaaaasdddsadmknvvvvsaaassd"))
                        .fluentPut("device", new JSONObject()
                                .fluentPut("platform", "ios")
                                .fluentPut("idfv", ""))
                        .fluentPut("timestamp", System.currentTimeMillis()));

        String respContent = HttpClientUtil.post(httpConfig.url(url).json(body.toJSONString()));


        System.out.println(respContent);*/

        return "200";
    }

}
