package com.coohua.core.caf.dispense.controller;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dto.req.GyRetryDevice;
import com.coohua.core.caf.dispense.dto.req.LockRetryGyReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.hbase.HbaseLockUserActiveService;
import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
import com.coohua.core.caf.dispense.kafka.LogKafkaSender;
import com.coohua.core.caf.dispense.ocpc.entity.LcToutiaoCk;
import com.coohua.core.caf.dispense.ocpc.entity.LcUserActive;
import com.coohua.core.caf.dispense.ocpc.entity.LockGyData;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.service.LockGyDataService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcLockinfoService;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import com.coohua.core.caf.dispense.ocpc.service.VivoLockService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import com.coohua.core.caf.dispense.utils.EventDataCheck;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Controller
@RequestMapping("/dispense/user/event")
@Slf4j
public class StoreGyController {

    @Autowired
    UserActiveService userActiveService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    OcpcLockinfoService ocpcLockinfoService;
    @Autowired
    HbaseLockUserActiveService hbaseLockUserActiveService;
    @Autowired
    VivoLockService vivoLockService;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    private SafeDataRedisService safeDataRedisService;
    @Autowired
    private RedisRelationshipService redisRelationshipService;
    @Autowired
    private LogKafkaSender logKafkaSender;
    @Autowired
    HbaseUserActiveService hbaseUserActiveService;
    @Autowired
    private LockGyDataService lockGyDataService;

    @RequestMapping(value = "/storeGuiy", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult storeGuiy(UserEventReq userEventReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        EventDataCheck.replaceZto(userEventReq);
        if(StringUtils.isBlank(userEventReq.getSourceDeviceId())) {
            userEventReq.setSourceDeviceId(userEventReq.getAndroidId());
        }
        UserEventReq.setMd5Inf(userEventReq);
        userEventReq.setUa(MD5Utils.getMd5Ua(userEventReq.getUa()));

        UserActive userActive = hbaseLockUserActiveService.queryDeviceActive(userEventReq);
        if(userActive==null){
            userActive = userActiveService.queryActive(userEventReq.getUserId(),userEventReq.getProduct());
        }

        if(userActive!=null){
            LcUserActive lcUserActive = new LcUserActive();
            BeanUtils.copyProperties(userActive,lcUserActive);
            returnResult.setData("userActive",lcUserActive);
            //已经激活
        }else{
            //归因
            ToutiaoClick toutiaoClick = userActiveService.guiOcpc(userEventReq);
            //hbase没有数据 没有userActive 或者 active 的gyType 不是sdk且该用户有记录不超过10min
            if (null == toutiaoClick && (null == userActive ||
                    (!Objects.equals(userActive.getGyType(), "sdk") && System.currentTimeMillis()-userActive.getCreateTime().getTime() <= 60000l))) {
                toutiaoClick = userActiveService.guiOcpcBySdk(userEventReq);
            }
            if (toutiaoClick != null) {
                if ((DspType.INNER_OLD_PULL_NEW.name.equalsIgnoreCase(toutiaoClick.getDsp())
                        || DspType.INNER_VIDEO.name.equalsIgnoreCase(toutiaoClick.getDsp())
                        || DspType.INNER_DRAINAGE.name.equalsIgnoreCase(toutiaoClick.getDsp()))) {
                    toutiaoClick.setDsp("nodsp");
                }

                LcToutiaoCk lcToutiaoCk = new LcToutiaoCk();
                BeanUtils.copyProperties(toutiaoClick,lcToutiaoCk);
                returnResult.setData("toutiaoClick",lcToutiaoCk);
            }
        }
        return returnResult;
    }

    @RequestMapping(value = "/storeGuiy2", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult storeGuiy2(UserEventReq userEventReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        EventDataCheck.replaceZto(userEventReq);
        if(StringUtils.isBlank(userEventReq.getSourceDeviceId())) {
            userEventReq.setSourceDeviceId(userEventReq.getAndroidId());
        }
        UserEventReq.setMd5Inf(userEventReq);
        userEventReq.setUa(MD5Utils.getMd5Ua(userEventReq.getUa()));
        //  数据上报 第一步、第二部、第三部的比例
        UserActive userActive = hbaseLockUserActiveService.queryDeviceActive(userEventReq);
        redisRelationshipService.incrementCount("hbase:userActive:"+new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        if(userActive==null){
            userActive = userActiveService.queryActive(userEventReq.getUserId(),userEventReq.getProduct());
            redisRelationshipService.incrementCount("mysql:userActive:"+new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        }

        if(userActive!=null){
            LcUserActive lcUserActive = new LcUserActive();
            BeanUtils.copyProperties(userActive,lcUserActive);
            returnResult.setData("userActive",lcUserActive);
            //已经激活
        }else{
            //归因
            ToutiaoClick toutiaoClick = userActiveService.guiOcpc2(userEventReq);
            redisRelationshipService.incrementCount("hbase:toutiaoClick:"+new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
            //hbase没有数据 没有userActive 或者 active 的gyType 不是sdk且该用户有记录不超过10min
            if (null == toutiaoClick && (null == userActive ||
                    (!Objects.equals(userActive.getGyType(), "sdk") && System.currentTimeMillis()-userActive.getCreateTime().getTime() <= 60000l))) {
                toutiaoClick = userActiveService.guiOcpcBySdk(userEventReq);
            }
            if (toutiaoClick != null) {
                if ((DspType.INNER_OLD_PULL_NEW.name.equalsIgnoreCase(toutiaoClick.getDsp())
                        || DspType.INNER_VIDEO.name.equalsIgnoreCase(toutiaoClick.getDsp())
                        || DspType.INNER_DRAINAGE.name.equalsIgnoreCase(toutiaoClick.getDsp()))) {
                    toutiaoClick.setDsp("nodsp");
                }
                // 保存锁区归因数据
                LockGyData lockGyData = new LockGyData();
                buildLockGyData(lockGyData,userEventReq,toutiaoClick);
                lockGyDataService.save(lockGyData);

                LcToutiaoCk lcToutiaoCk = new LcToutiaoCk();
                BeanUtils.copyProperties(toutiaoClick,lcToutiaoCk);
                returnResult.setData("toutiaoClick",lcToutiaoCk);
            }
        }
        return returnResult;
    }

    private void buildLockGyData(LockGyData lockGyData, UserEventReq userEventReq,ToutiaoClick toutiaoClick) {
        lockGyData.setProduct(userEventReq.getProduct());
        lockGyData.setOs(userEventReq.getOs());
        lockGyData.setOaid(userEventReq.getOaid());
        lockGyData.setCaid(userEventReq.getCaid());
        lockGyData.setDsp(toutiaoClick.getDsp());
        lockGyData.setGyType(userEventReq.getGuiType());
        lockGyData.setCreateTime(new Date());
        lockGyData.setClickTime(toutiaoClick.getCreateTime());
    }


    @Resource(name = "lockRetryGy")
    ThreadPoolTaskExecutor poolTaskExecutor;

    /**
     * 锁区归因重试接口
     * @param lockRetryGyReq
     * @param request
     * @return
     */
    @RequestMapping(value = "/retryLockGuiy", method = RequestMethod.POST)
    @ResponseBody
    public ReturnResult retryLockGuiy(@RequestBody LockRetryGyReq lockRetryGyReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        if (lockRetryGyReq == null || lockRetryGyReq.getGyRetryDeviceList().isEmpty()) {
            return returnResult;
        }
        // 查找归因
        for (GyRetryDevice gyRetryDevice : lockRetryGyReq.getGyRetryDeviceList()) {
            UserEventReq userEventReq = getUserEventReq(gyRetryDevice);
            ToutiaoClick toutiaoClick = userActiveService.guiOcpc2(userEventReq);

            if (toutiaoClick != null) {
                log.info("锁区归因重试查询点击成功 {}",JSONObject.toJSONString(gyRetryDevice));
                // 发送kafka消息，通知锁区
                logKafkaSender.sendLockRetryGy(JSONObject.toJSONString(gyRetryDevice));
                // 异步查询用户是否注册，如果注册过且是自然量，则更改注册结果
                poolTaskExecutor.execute(() -> {
                    updateUserActive(gyRetryDevice,toutiaoClick);
                });
            }

        }
        return returnResult;
    }

    private void updateUserActive(GyRetryDevice lockRetryGyReq,ToutiaoClick toutiaoClick) {
        try {
            UserActive userActive = userActiveService.queryActiveByDevice(lockRetryGyReq.getDeviceId(), lockRetryGyReq.getProduct(), lockRetryGyReq.getOs());
            if (userActive != null && StringUtils.isBlank(userActive.getGyType())) {
                userActive.setSource(toutiaoClick.getDsp());
                if ("ios".equals(lockRetryGyReq.getOs())) {
                    userActive.setGyType(GuiyingType.caid.name);
                } else {
                    userActive.setGyType(GuiyingType.oaid.name);
                }
                userActive.setUpdateTime(new Date());
                boolean result = userActiveService.updateById(userActive);
                hbaseUserActiveService.saveUserActive(userActive);
                if (result) {
                    log.info("修改注册归因结果成功 userActive {}", JSONObject.toJSONString(userActive));
                }
            }
        } catch (Exception e) {
            log.error("修改注册结果异常 lockRetryGyReq {} toutiaoClick {}", JSONObject.toJSONString(lockRetryGyReq), JSONObject.toJSONString(toutiaoClick), e);
        }

    }

    private static UserEventReq getUserEventReq(GyRetryDevice lockRetryGyReq) {
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setProduct(lockRetryGyReq.getProduct());
        userEventReq.setOs(lockRetryGyReq.getOs());
        userEventReq.setOcpcDeviceId(lockRetryGyReq.getDeviceId());
        userEventReq.setSourceDeviceId(lockRetryGyReq.getDeviceId());
        if ("ios".equals(lockRetryGyReq.getOs())) {
            userEventReq.setCaid(lockRetryGyReq.getDeviceId());
        }else {
            userEventReq.setOaid(lockRetryGyReq.getDeviceId());
        }
        return userEventReq;
    }
}
