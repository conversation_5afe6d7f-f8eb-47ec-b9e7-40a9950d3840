package com.coohua.core.caf.dispense.controller.vivo;

import com.coohua.core.caf.dispense.dsp.service.VivoAdvertiserService;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@Slf4j
public class VivoOcpcController {
    @Autowired
    VivoAdvertiserService vivoAdvertiserService;

    @RequestMapping("/dispense/vivo/authcode")
    @ResponseBody
    public ReturnResult authcode(@RequestParam String clientId, @RequestParam String code, @RequestParam String state) {
        vivoAdvertiserService.updAckToken(clientId, state,code);
        return new ReturnResult();
    }

}
