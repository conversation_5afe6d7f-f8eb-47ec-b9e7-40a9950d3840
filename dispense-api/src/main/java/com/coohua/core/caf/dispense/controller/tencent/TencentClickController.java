package com.coohua.core.caf.dispense.controller.tencent;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.CaidMappingDto;
import com.coohua.core.caf.dispense.dto.req.TencentClickReq;
import com.coohua.core.caf.dispense.dto.req.TencentClickReqV2;
import com.coohua.core.caf.dispense.enums.CaidVersionEnum;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.ocpc.service.CaidService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;

@RestController
@RequestMapping("/dispense/tencent")
@Slf4j
public class TencentClickController {

    @Autowired
    private ToutiaoClickService toutiaoClickService;

    @Autowired
    RedisClickService redisClickService;
    @Autowired
    private AlertService alertService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;

    @Autowired
    ConstApolloConfig constApolloConfig;
    @Autowired
    RedisRelationshipService redisRelationshipService;

    @GetMapping("/click")
    @ResponseBody
    public TencentClickRsp click(TencentClickReq tencentClickReq, HttpServletRequest request) {

        logTestAccount(tencentClickReq.getAccount_id(), tencentClickReq);
        TencentClickRsp tencentClickRsp = new TencentClickRsp();
        if(StringUtils.equalsIgnoreCase("wdncxcx",tencentClickReq.getProduct())){
            log.info("小程序不归因 "+JSON.toJSONString(tencentClickReq));
            return tencentClickRsp;
        }
        ToutiaoClick toutiaoClick = new ToutiaoClick();

        if (tencentClickReq.getIp() != null && tencentClickReq.getIp().length() <= BaseConstants.IpMaxLength) {
            toutiaoClick.setIp(tencentClickReq.getIp());
        }
        if (StringUtils.isNotBlank(tencentClickReq.getUser_agent())) {
            toutiaoClick.setUa(MD5Utils.getMd5Sum(tencentClickReq.getUser_agent()));
            log.info("tencentua ", tencentClickReq.getUser_agent());
        }
        if (tencentClickReq.getModel() != null && tencentClickReq.getModel().length() <= BaseConstants.ModelMaxLength) {
            toutiaoClick.setModel(tencentClickReq.getModel());
        }

        if("ios".equalsIgnoreCase(tencentClickReq.getOs())){
            try {
                if(StringUtils.isNotBlank(tencentClickReq.getCaid())){
                    toutiaoClick.setCaid(CaidService.getTencentCaid(tencentClickReq.getCaid(),"1005"));
                }
            } catch (Exception e) {
                log.warn("tencent ua", e);
            }
        }

        toutiaoClick.setDsp(tencentClickReq.getDsp());
        toutiaoClick.setProduct(tencentClickReq.getProduct());
        toutiaoClick.setOs(tencentClickReq.getOs());
        toutiaoClick.setAccountId(tencentClickReq.getAccount_id());
        toutiaoClick.setOcpcDeviceId(tencentClickReq.getMuid());
        toutiaoClick.setTs(tencentClickReq.getProcess_time());
        toutiaoClick.setCid(tencentClickReq.getCreative_id());
        toutiaoClick.setGid(tencentClickReq.getCampaign_id());
        toutiaoClick.setPid(tencentClickReq.getAdgroup_id());
        toutiaoClick.setOaid(tencentClickReq.getOaid());
        toutiaoClick.setCidName(tencentClickReq.getAdgroup_name());
        toutiaoClick.setGroupName(tencentClickReq.getAdgroup_name());
        toutiaoClick.setMac(tencentClickReq.getMac());
        toutiaoClick.setOpenId(tencentClickReq.getWechat_openid());
//            toutiaoClick.setWAppId(ProductCache.getWAppid(tencentClickReq.getWechat_openid(),tencentClickReq.getProduct()));
        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);
        toutiaoClick.setClickId(tencentClickReq.getClick_id());
        toutiaoClick.setRequestParam(JSON.toJSONString(tencentClickReq));
        toutiaoClick.setClientIp(request.getRemoteAddr());
        saveClick(tencentClickRsp, toutiaoClick);

        redisRelationshipService.saveIdfaCaid(tencentClickReq.getMuid(), toutiaoClick.getCaid(),tencentClickReq.getOs(),toutiaoClick);

        return tencentClickRsp;
    }

    @GetMapping("/clickV2")
    @ResponseBody
    public TencentClickRsp clickV2(TencentClickReqV2 tencentClickReq, HttpServletRequest request) {

        logTestAccount(tencentClickReq.getAccount_id(), tencentClickReq);
        toutiaoClickService.checkLengthAndSetUaMd5(DspType.GUANGDIANTONG, tencentClickReq);


        TencentClickRsp tencentClickRsp = new TencentClickRsp();
        if(StringUtils.equalsIgnoreCase("wdncxcx",tencentClickReq.getProduct())){
            log.info("小程序不归因 "+JSON.toJSONString(tencentClickReq));
            return tencentClickRsp;
        }
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(tencentClickReq.getDsp());
        toutiaoClick.setProduct(tencentClickReq.getProduct());
        toutiaoClick.setOs(tencentClickReq.getOs());
        toutiaoClick.setAccountId(tencentClickReq.getAccount_id());
        toutiaoClick.setOcpcDeviceId(tencentClickReq.getMuid());
        toutiaoClick.setTs(tencentClickReq.getClick_time().toString());
        toutiaoClick.setCid(tencentClickReq.getCreative_id());
        toutiaoClick.setGid(tencentClickReq.getCampaign_id());
        toutiaoClick.setPid(tencentClickReq.getAdgroup_id());
        toutiaoClick.setOaid(tencentClickReq.getOaid());
        toutiaoClick.setOaid2(tencentClickReq.getHash_oaid());
        toutiaoClick.setMac(tencentClickReq.getMac());
        toutiaoClick.setAndroidId(tencentClickReq.getAndroid_id());
        toutiaoClick.setCallbackUrl(tencentClickReq.getCallback());
        toutiaoClick.setAidName(tencentClickReq.getProduct_type());
        toutiaoClick.setGroupName(tencentClickReq.getAdgroup_name());
        toutiaoClick.setCidName(tencentClickReq.getClick_id());
        toutiaoClick.setOpenId(tencentClickReq.getWechat_openid());
        toutiaoClick.setCaid(tencentClickReq.getCaid());
        toutiaoClick.setMid(tencentClickReq.getElement_info());
        toutiaoClick.setRequestParam(JSON.toJSONString(tencentClickReq));
        toutiaoClick.setClientIp(request.getRemoteAddr());

        if (tencentClickReq.getIp() != null && tencentClickReq.getIp().length() <= BaseConstants.IpMaxLength) {
            toutiaoClick.setIp(tencentClickReq.getIp());
        }
        if (StringUtils.isNotBlank(tencentClickReq.getUser_agent())) {
            toutiaoClick.setUa(tencentClickReq.getUser_agent());
//            log.info("tencentua "+tencentClickReq.getUser_agent());
        }
        if (tencentClickReq.getModel() != null && tencentClickReq.getModel().length() <= BaseConstants.ModelMaxLength) {
            toutiaoClick.setModel(tencentClickReq.getModel());
        }

        if("ios".equalsIgnoreCase(tencentClickReq.getOs())){
            try {
                if(StringUtils.isNotBlank(tencentClickReq.getCaid())){
                    toutiaoClick.setCaid(CaidService.getTencentCaid(tencentClickReq.getCaid(),"1005"));
                    String tencentCaid2 = CaidService.getTencentCaid(tencentClickReq.getCaid(), "1006");
                    toutiaoClick.setCaid2(tencentCaid2);
                    if (StringUtils.isNotBlank(tencentCaid2) && StringUtils.isNotBlank(tencentClickReq.getMuid())) {
                        CaidMappingDto caidMappingDto = new CaidMappingDto();
                        caidMappingDto.setCaid(tencentCaid2);
                        caidMappingDto.setCaidType(CaidVersionEnum.CAID_20230330.getCode());
                        caidMappingDto.setIdfa(tencentClickReq.getMuid());
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String dateFormat = sdf.format(new Date());
                        caidMappingDto.setCreateTime(dateFormat);
                        caidMappingDto.setSource("click_event");
                        kafkaSender.sendCaidMapping(JSONObject.toJSONString(caidMappingDto));
                    }
                }
            } catch (Exception e) {
                log.warn("tencent ua", e);
            }
        }
        toutiaoClick.setClickId(tencentClickReq.getClick_id());
//            toutiaoClick.setWAppId(ProductCache.getWAppid(tencentClickReq.getWechat_openid(),tencentClickReq.getProduct()));
        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);

        saveClick(tencentClickRsp, toutiaoClick);
        //将idfa与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(tencentClickReq.getMuid(), toutiaoClick.getCaid(),tencentClickReq.getOs(),toutiaoClick);
        return tencentClickRsp;
    }

    private void logTestAccount(String account_id, Object clickReq) {
        try {
            if (constApolloConfig.tencentLogTestAccounts != null && constApolloConfig.tencentLogTestAccounts.contains(account_id)) {
                log.info("腾讯测试账户 {}", JSON.toJSONString(clickReq));
            }
        } catch (Exception e) {
            log.error("腾讯测试账户异常 " + account_id, e);
        }
    }

    private void saveClick(TencentClickRsp tencentClickRsp, ToutiaoClick toutiaoClick) {
        try {
            toutiaoClickService.saveClick(toutiaoClick);
        } catch (Exception e) {
            log.error("", e);
            tencentClickRsp.setRet(500);
            tencentClickRsp.setMsg(e.getMessage());
        }
        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-腾讯");
        kafkaSender.sendClick(toutiaoClick);

    }

}
