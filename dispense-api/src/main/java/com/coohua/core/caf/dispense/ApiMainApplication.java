package com.coohua.core.caf.dispense;


import cn.hutool.core.date.DateUtil;
import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.core.caf.dispense.dsp.service.bytemin.ByteAppConfig;
import com.coohua.core.caf.dispense.dsp.service.bytemin.ByteMinThirdService;
import com.coohua.core.caf.dispense.dsp.service.bytemin.DouyinMinEcpmService;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.httpclient.EnableHttpBioClient;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;

import java.util.Date;
import java.util.List;

@SpringBootApplication(scanBasePackages = {"com.coohua.core.caf.dispense","com.coohua.rta","com.shinet.rta"})
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health",
        // DB配置
        "bp.db.dispense","bp.redis.dispense", "rta.redis","relationship.redis","bp.safe.data.redis","ap.redis.cluster",
        //rpc配置
        "ad.user.ad.api.referer",
        //sdk实时归因配置
        "ap.tf.juliang"
        })
@EnableJedisClusterClient(namespace = "bp-dispense")
@EnableJedisClusterClient(namespace = "rta-redis")
@EnableJedisClusterClient(namespace = "safe-data")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableJedisClusterClient(namespace = "relationship-redis")
@EnableMotan(namespace = "user-event")
@EnableAutoChangeApolloConfig
@EnableScheduling
@EnableCaching
@EnableHttpBioClient
@Slf4j
@EnableKafka
public class ApiMainApplication {

    public static void main(String[] args) throws Exception {
        try {
            ConfigurableApplicationContext configurableApplicationContext = SpringApplication.run(ApiMainApplication.class, args);
//            configurableApplicationContext.getBean(HbaseLockUserActiveService.class).createOcpcTable();

//            Pair<String,String> pkye = configurableApplicationContext.getBean(ByteAppConfig.class).getAppPair("lshj");
//            String hourStr = DateUtil.format(new Date(), "yyyy-MM-dd HH");

//            List<ByteEcpm> byteAdEcpmList = configurableApplicationContext.getBean(DouyinMinEcpmService.class).getDouyinMinEcmp
//                    ("123",
//                    "lshj",
//                            "_0009qoBZwlxEksDmznZInippbXfyLQqK6C5",
//                            pkye.getKey(),
//                            pkye.getValue(),
//                            hourStr);
//
//
//            log.info("日志测试手机SLS");
        }catch (Exception e){
            e.printStackTrace();
            log.error("",e);
        }
    }

}
