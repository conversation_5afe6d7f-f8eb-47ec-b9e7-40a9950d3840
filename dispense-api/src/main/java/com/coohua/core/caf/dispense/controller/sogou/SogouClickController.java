package com.coohua.core.caf.dispense.controller.sogou;

import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.dsp.entity.SogouClickReq;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@RestController
@RequestMapping("/dispense/sogou")
@Slf4j
public class SogouClickController {
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    ToutiaoClickService toutiaoClickService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    RedisRelationshipService redisRelationshipService;
    @GetMapping("/click")
    @ResponseBody
    public ReturnResult click(SogouClickReq sogouClickReq, HttpServletRequest request) {
        Date date = new Date();
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(sogouClickReq.getDsp());
        toutiaoClick.setProduct(sogouClickReq.getProduct());
        toutiaoClick.setOs(sogouClickReq.getOs());
        toutiaoClick.setAccountId(sogouClickReq.getAccountId());
        toutiaoClick.setOcpcDeviceId(sogouClickReq.getOcpcDeviceId());
        toutiaoClick.setTs(sogouClickReq.getTs());
        toutiaoClick.setCallbackUrl(sogouClickReq.getCallbackUrl());
        toutiaoClick.setCid(sogouClickReq.getCid());
        toutiaoClick.setGid(sogouClickReq.getGid());
        toutiaoClick.setPid(sogouClickReq.getPid());
        toutiaoClick.setOaid(sogouClickReq.getOaid());
        toutiaoClick.setCreateTime(date);
        toutiaoClick.setUpdateTime(date);
        log.info("sogou click-{}",toutiaoClick);
        boolean isF = false;
        try {
            isF = toutiaoClickService.saveClick(toutiaoClick);
        } catch (Exception e) {
            log.error("sogou click event save failure");
        }
        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
        //将deviceId与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),sogouClickReq.getOs(),toutiaoClick);
        log.info("sogou save success isF:{}",isF);
        ReturnResult returnResult = new ReturnResult();
        return returnResult;
    }
}
