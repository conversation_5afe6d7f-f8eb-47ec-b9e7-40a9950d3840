package com.coohua.core.caf.dispense.controller.caid;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.caf.core.util.HttpUtil;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dto.CaidMappingDto;
import com.coohua.core.caf.dispense.dto.CaidVersionEntity;
import com.coohua.core.caf.dispense.dto.req.CaidRsp;
import com.coohua.core.caf.dispense.dto.req.IosCaidReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.enums.CaidVersionEnum;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.ocpc.service.CaidService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import netscape.javascript.JSObject;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.lang.Thread.sleep;

@RestController
@RequestMapping("/dispense/user")
@Slf4j
public class CaidController {
    @Autowired
    CaidService caidService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    private KafkaSender kafkaSender;
    @Resource(name = "userActiveSave")
    ThreadPoolTaskExecutor poolTaskExecutor;

    @RequestMapping("/getCaid")
    @ResponseBody
    public ReturnResult getCaid(IosCaidReq iosCaidReq, HttpServletRequest request, HttpServletResponse response) {
        log.info("getCaid请求参数 iosCaidReq: {}",JSONObject.toJSONString(iosCaidReq));
        ReturnResult returnResult = new ReturnResult();
//        if(StringUtils.isAnyBlank(iosCaidReq.getProduct(),iosCaidReq.getUserId())){
//            returnResult.setStatus(400);
//            returnResult.setMessage("参数错误");
//        }

        if ("--".equals(iosCaidReq.getCarrierInfo()) || "nil".equals(iosCaidReq.getCarrierInfo())
          || "null".equals(iosCaidReq.getCarrierInfo()) || StringUtils.isBlank(iosCaidReq.getCarrierInfo())) {
            iosCaidReq.setCarrierInfo("unknown");
        }

        if(StringUtils.isNotBlank(iosCaidReq.getDeviceName()) && iosCaidReq.getDeviceName().length()<10){
            String dname = MD5Utils.getMd5Sum(iosCaidReq.getDeviceName());
            log.info("modefdd "+ iosCaidReq.getProduct()+" 替换deviceName "+iosCaidReq.getDeviceName()+" "+dname);
            iosCaidReq.setDeviceName(dname);
        }
        if(StringUtils.isNotBlank(iosCaidReq.getDeviceName())){
            iosCaidReq.setDeviceName(iosCaidReq.getDeviceName().toLowerCase());
        }
        if(StringUtils.isNotBlank(iosCaidReq.getModelF())){
            log.info("modef "+ iosCaidReq.getProduct()+" "+JSON.toJSONString(iosCaidReq));
            iosCaidReq.setModel(iosCaidReq.getModelF());
        }

        if(ocpcSwitcher.productscaid.contains(iosCaidReq.getProduct()) || ocpcSwitcher.productDfscaid.contains(iosCaidReq.getProduct())){
            String  ip = IpUtils.getIpAddress(request);
            String url = "https://sapi.shinet.cn/sf//ip/getCityName?product="+iosCaidReq.getProduct()+"&ip="+ip;
            // 200 MS 超时
            try {
                HttpConfig httpConfig = HttpConfig.custom().timeout(2000);
                String html = HttpClientUtil.get(httpConfig.url(url));

                JSONObject  jsObject = JSON.parseObject(html);
                String city = jsObject.getJSONObject("data").getString("city");
                log.info("开始请求caid城市 "+iosCaidReq.getProduct()+" : "+ip+" "+city);

                if(ocpcSwitcher.productDfscaid.contains(iosCaidReq.getProduct())){
                    if(ocpcSwitcher.ipLockCaids.contains(ip)){
                        log.info("ip白名单 返回空 "+iosCaidReq.getProduct()+" "+ip);
                        response.sendError(HttpStatus.UNAUTHORIZED.value());
                        return  returnResult;
                    }
                    if((city!=null && city.contains("海外"))){
                        log.info("ip海外白名单 返回空  "+city+" "+iosCaidReq.getProduct()+" "+ip);
                        response.sendError(HttpStatus.UNAUTHORIZED.value());
                        return  returnResult;
                    }
                }
                if(ocpcSwitcher.ipLockCaids.contains(ip)){
                    log.info("ip白名单 直接进入无数据");
                    returnResult.setData("");
                    return  returnResult;
                }

                if((city!=null && city.contains("海外"))){
                    returnResult.setData("");
                    log.info("ip海外白名单 直接进入无数据 "+city);
                    return  returnResult;
                }
            }catch (Exception e){
                log.error("caid 错误 "+iosCaidReq.getProduct());
            }
        }

        String caid = caidService.queryCaid(iosCaidReq);
        if(StringUtils.isNotBlank(caid)){
            caidService.setCaidRedis(iosCaidReq.getUserId(),iosCaidReq.getProduct(),caid);
            log.info("获取caid成功 request {} prdduct {}",JSONObject.toJSONString(iosCaidReq),iosCaidReq.getProduct());
            returnResult.setData(caid);
        }else {
            // 随机生成一个caid，保证游戏可以进入 保存随机caid与请求参数，后续异步请求真实的caid
            String uuidCaid = UUID.randomUUID().toString().replace("-", "");
            returnResult.setData(uuidCaid);
            poolTaskExecutor.execute(() -> {
                retryObtainCaid(uuidCaid, iosCaidReq);
            });
        }
        log.info(iosCaidReq.getUserId()+" caidrspee "+ iosCaidReq.getProduct()+" "+JSON.toJSONString(returnResult)+""+JSON.toJSONString(iosCaidReq));

        return returnResult;
    }

    private void retryObtainCaid(String uuidCaid, IosCaidReq iosCaidReq) {
        // 检查查询参数是否有空，有空则添加默认值
        if (StringUtils.isBlank(iosCaidReq.getSysFileTime())){
            iosCaidReq.setSysFileTime("1589348918.666477");
        }
        if (StringUtils.isBlank(iosCaidReq.getMachine())){
            iosCaidReq.setMachine("iPhone10,1");
        }
        String caidStr = caidService.sendCaidReq(iosCaidReq);
        if (caidStr == null || StringUtils.isBlank(caidStr)) {
            for (int i = 0; i < 5; i++) {
                try {
                    caidStr = caidService.sendCaidReq(iosCaidReq);
                    log.info("caid映射 caid重试补偿 " + i + " " + caidStr);
                    if (StringUtils.isNotBlank(caidStr)) {
                        break;
                    }
                } catch (Exception e) {
                    log.error("caid重试获取异常 ", e);
                }
            }
        }
        if (StringUtils.isNotBlank(caidStr)) {
            // 通过kafka发送，保存映射
            List<CaidRsp> caidList = JSON.parseArray(caidStr, CaidRsp.class);
            if (caidList!=null && caidList.size()>1) {
                CaidMappingDto caidMappingDto = new CaidMappingDto();
                Map<String, String> caidMap = caidList.stream().collect(Collectors.toMap(CaidRsp::getVersion, CaidRsp::getCaid));
                caidMappingDto.setCaid(uuidCaid);
                caidMappingDto.setCaidType(CaidVersionEnum.CAID_LOCAL.getCode());
                List<CaidVersionEntity> caidVersionEntities = new ArrayList<>();
                for (Map.Entry<String, String> entry : caidMap.entrySet()) {
                    CaidVersionEntity caidVersionEntity = new CaidVersionEntity();
                    CaidVersionEnum caidVersionEnum = CaidVersionEnum.getByKeyPrefix(entry.getKey());
                    caidVersionEntity.setCaid(entry.getValue());
                    caidVersionEntity.setCaidType(caidVersionEnum.getCode());
                    caidVersionEntities.add(caidVersionEntity);
                }
                caidMappingDto.setCaidVersions(caidVersionEntities);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateFormat = sdf.format(new Date());
                caidMappingDto.setCreateTime(dateFormat);
                caidMappingDto.setSource("补充任务");
                kafkaSender.sendCaidMapping(JSONObject.toJSONString(caidMappingDto));
                log.info("caid重试补偿成功 iosCaidReq {} caidMappingStr {}",JSONObject.toJSONString(iosCaidReq),JSONObject.toJSONString(caidMappingDto));
            }
        }
    }


}
