package com.coohua.core.caf.dispense.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.apollo.ExtEventApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.OcpcPayReq;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.*;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.UserMetaParam;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.ocpc.service.CaidService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import com.coohua.core.caf.dispense.utils.EventDataCheck;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;


@Controller
@RequestMapping("/dispense/user/event")
@Slf4j
public class UserEventController {

    @Autowired
    private UserEventService userEventService;

    @Resource(name = "SchedulerThreadPool")
    ThreadPoolTaskScheduler schedulerPoll;

    @Value("${checkActivate.schedule.second:15}")
    private int checkActivateSchedule;


    @Autowired
    private ProductCache productCache;
    @Autowired
    private OcpcEventService ocpcEventService;
    @Autowired
    private AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    private AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    @Autowired
    TencentDeveloperService tencentDeveloperService;
    @Autowired
    BaiduAdvertiserService baiduAdvertiserService;
    @Autowired
    IqiyiAdvertiserService iqiyiAdvertiserService;

    @Autowired
    KuaishouUserEventService kuaishouUserEventService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    ExtEventApolloConfig extEventApolloConfig;
    @Autowired
    ThirdExtEventService thirdExtEventService;

    @Autowired
    VivoUserEventService vivoUserEventService;
    @Autowired
    ConstApolloConfig constApolloConfig;
    @Autowired
    InitService initService;
    @Autowired
    OcpcPayReqService ocpcPayReqService;
    @Autowired
    private SafeDataRedisService safeDataRedisService;

    @RequestMapping("/activate")
    @ResponseBody
    public ReturnResult activate(UserEventReq userEventReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        log.info("useractive request userEventReq = {} ", JSONObject.toJSONString(userEventReq));
        productCache.checkEventProductName(userEventReq);
        if(!StringUtils.equalsIgnoreCase("ios",userEventReq.getOs()) && !StringUtils.equalsIgnoreCase("android",userEventReq.getOs())){
            log.info("新系统xx "+userEventReq.getOs()+" "+JSONObject.toJSONString(userEventReq));
        }
        EventDataCheck.replaceZto(userEventReq);
        buchCaid(userEventReq);
        //走新ocpc库
        List<ToutiaoClick> toutiaoClickList = new ArrayList<>();
        OcpcPayReq ocpcPayReq = null;

        if(StringUtils.equalsIgnoreCase("wmin",userEventReq.getOs())){
            ocpcPayReq = ocpcPayReqService.savePayReq(userEventReq);
        }
        if(!isValidReq(userEventReq)){
            log.info(" 入参不合法 "+JSON.toJSONString(userEventReq));
            return new ReturnResult();
        }

        // 校验回传行为数值的合法性
        verifyEventValues(userEventReq);

        UserEventReq.setMd5Inf(userEventReq, ocpcSwitcher.caid2Products.contains(userEventReq.getProduct()));
        if(StringUtils.isNotBlank(userEventReq.getCaid2())) {
            log.info("caid2可归因 caid:{}, caid2:{}, req:{}", userEventReq.getCaid(), userEventReq.getCaid2(), JSONObject.toJSONString(userEventReq));
        }
        userEventReq.setUa(MD5Utils.getMd5Ua(userEventReq.getUa()));

        // 根据映射获取当前用户的originalAndroidId或idfv
        if (("android".equals(userEventReq.getOs()) && StringUtils.isBlank(userEventReq.getOriginalAndroidId()))
           || ("ios".equals(userEventReq.getOs()) && StringUtils.isBlank(userEventReq.getIdfv()))) {
            String androidIdIdfv = safeDataRedisService.getAndroidIdIdfv(userEventReq.getUserId(), userEventReq.getProduct(), userEventReq.getOs());
            if (StringUtils.isNotBlank(androidIdIdfv)) {
                if ("android".equals(userEventReq.getOs())){
                    userEventReq.setOriginalAndroidId(androidIdIdfv);
                }else if ("ios".equals(userEventReq.getOs())){
                    userEventReq.setIdfv(androidIdIdfv);
                }
            }
        }

        //redis库
        boolean isAct = redisEventService.ocpcUevent(userEventReq, toutiaoClickList,ocpcPayReq);
        if (kuaishouUserEventService.isRecall(toutiaoClickList, userEventReq)) {
            userEventReq.setOcpcDeviceId(MD5Utils.getMd5Sum(userEventReq.getOcpcDeviceId()));
            redisEventService.ocpcUevent(userEventReq, toutiaoClickList,null);
        }
//        vivoUserEventService.activeEvent(userEventReq);
        returnResult.setData(isAct);
        return returnResult;
    }
    @Autowired
    CaidService caidService;
    private void buchCaid(UserEventReq userEventReq){
        if("ios".equals(userEventReq.getOs())){
            if(StringUtils.isBlank(userEventReq.getCaid()) && StringUtils.isNotBlank(userEventReq.getUserId()) && userEventReq.getUserId().length()>2){
                String caid = caidService.getCaidRedis(userEventReq.getUserId(),userEventReq.getProduct());
                if(StringUtils.isNotBlank(caid)){
                    userEventReq.setCaid(caid);
                }
            }
        }
    }
    /**
     * 校验回传值的合法性
     */
    private void verifyEventValues(UserEventReq userEventReq) {
        try {
            if (!Objects.equals(userEventReq.getEventType(), ToutiaoEventTypeEnum.KEY_EVENT.value) || StringUtils.isBlank(userEventReq.getActionValues())) {
                return;
            }

            Map<String, String> reportValues = OcpcNewCallbackRuleValidateUtil.analyzeNewValueToMap(userEventReq.getActionValues());

            String arpu = reportValues.getOrDefault("2", "0");
            if (Double.parseDouble(arpu) > constApolloConfig.legalArpuMax) {
                log.warn("校验ocpc回传数值时arpu超过上限 now:{} max:{}, req: {}", arpu, constApolloConfig.legalArpuMax, JSON.toJSONString(userEventReq));
            }

            String ecpm = reportValues.getOrDefault("3", "0");
            if (Integer.parseInt(ecpm) > constApolloConfig.legalEcpmMax) {
                log.warn("校验ocpc回传数值时ecpm超过上限 now:{} max:{}, req: {}", ecpm, constApolloConfig.legalEcpmMax, JSON.toJSONString(userEventReq));
            }

            if(StringUtils.isNotBlank(userEventReq.getMac()) && MD5Utils.macSet.contains(userEventReq.getMac())){
                log.info("macmoren "+userEventReq.getMac()+" "+userEventReq.getUserId());
                userEventReq.setMac(null);
            }
        } catch (Exception e) {
            log.error("校验ocpc回传行为数值 系统异常 " + JSON.toJSONString(userEventReq), e);
        }
    }

    private boolean isValidReq(UserEventReq userEventReq){
        boolean isVal = true;
        if(StringUtils.isBlank(userEventReq.getMac())
                && StringUtils.isBlank(userEventReq.getOcpcDeviceId())
                && StringUtils.isBlank(userEventReq.getOaid())
                && StringUtils.isBlank(userEventReq.concatIpua())
                && StringUtils.isBlank(userEventReq.getOpenId())
                && StringUtils.isBlank(userEventReq.getCaid())
                && StringUtils.isBlank(userEventReq.getUserId())
                && StringUtils.isBlank(userEventReq.getOpenId())
        ){
            isVal = false;
        }
        return isVal;
    }


    @RequestMapping("/tianwei/activate")
    @ResponseBody
    public ReturnResult tianweiActivate(UserEventReq userEventReq, HttpServletRequest request) {
        log.info("user active request userEventReq = {} ", JSONObject.toJSONString(userEventReq));
        userEventService.tianweiUEvent(userEventReq);
        ReturnResult returnResult = new ReturnResult();
        return returnResult;
    }

    @RequestMapping(value = "/checkActivate", method = RequestMethod.POST)
    @ResponseBody
    public ReturnResult check(@RequestBody UserMetaParam userMetaParam, HttpServletRequest request) {
        log.info("user checkActivate request userEventReq = {} ", JSONObject.toJSONString(userMetaParam));
        //延时15秒执行
        schedulerPoll.schedule(() -> userEventService.checkAddUserMeta(userMetaParam), DateUtils.addSeconds(new Date(), checkActivateSchedule));
        ReturnResult returnResult = new ReturnResult();
        return returnResult;
    }

    @RequestMapping(value = "/queryEvent")
    @ResponseBody
    public ReturnResult queryEvent(String appName, HttpServletRequest request) {
        log.info("user queryEvent request userEventReq = {} ", appName);
        return new ReturnResult(initService.getAccountActions(appName));
    }

    /**
     * 用户换机归因，归因至新表
     * @param userEventReq
     * @param request
     * @return
     */
    @RequestMapping(value = "/changePhoneAscribe",consumes = {"application/json"})
    @ResponseBody
    public ReturnResult changePhoneAscribe(@RequestBody UserEventReq userEventReq, HttpServletRequest request){
        ReturnResult returnResult = new ReturnResult();
        log.info("user changePhoneAscribe request userEventReq = {} ", userEventReq);
        returnResult.setData(redisEventService.changePhoneAscribe(userEventReq));
        return returnResult;
    }

}
