package com.coohua.core.caf.dispense.controller.tianwei;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dto.req.TianweiClickReq;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Optional;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/7/25 11:59
 */
@RestController
@RequestMapping("/dispense/tianwei")
@Slf4j
public class TianweiClickController {

	@Autowired
	private ToutiaoClickService toutiaoClickService;
	@Autowired
	HbaseClickService hbaseClickService;
	@Autowired
	RedisClickService redisClickService;
	@Autowired
	KafkaSender kafkaSender;
	@Autowired
	private AlertService alertService;
	@Autowired
	RedisRelationshipService redisRelationshipService;

	@GetMapping("/click")
	public TianweiClickRsp click(TianweiClickReq clickReq, HttpServletRequest request) {
		TianweiClickRsp rsp = new TianweiClickRsp();
		if (StringUtils.isNotBlank(clickReq.getImei()) || StringUtils.isNotBlank(clickReq.getIdfa())) {
			ToutiaoClick toutiaoClick = new ToutiaoClick();
			toutiaoClick.setDsp(DspType.TIANWEI.value);
			toutiaoClick.setProduct(Optional.ofNullable(clickReq.getProduct()).orElse(clickReq.getAppid()));
			toutiaoClick.setCallbackUrl(clickReq.getCallbackUrl());
			toutiaoClick.setAccountId(clickReq.getAccountId());
			Date date = new Date();
			toutiaoClick.setUpdateTime(date);
			toutiaoClick.setCreateTime(date);
			if ( StringUtils.isNotBlank(clickReq.getIdfa())) {
				toutiaoClick.setOcpcDeviceId(clickReq.getIdfa());
				toutiaoClick.setOs("ios");
			} else {
				toutiaoClick.setOs("android");
				toutiaoClick.setOcpcDeviceId(DigestUtils.md5Hex(clickReq.getImei()));
			}
			try {
				boolean isF = toutiaoClickService.saveClick(toutiaoClick);
				log.info("save success isF:{}", isF);
			} catch (Exception e) {
				log.error("TianweiClickController error req {}", JSONObject.toJSONString(clickReq),e);
				rsp.setCode(0);
				rsp.setMsg("error");
			}

			redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
					toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
			hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
					toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
			alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-天威");
			kafkaSender.sendClick(toutiaoClick);
			//将deviceId与caid存入redis缓存
			redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),toutiaoClick.getOs(),toutiaoClick);
		}
		return rsp;
	}
}
