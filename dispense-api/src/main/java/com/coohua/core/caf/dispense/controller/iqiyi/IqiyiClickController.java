package com.coohua.core.caf.dispense.controller.iqiyi;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.LogNum;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.BaiduClickReq;
import com.coohua.core.caf.dispense.dto.req.ToutiaoClickReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.enums.RspStatus;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

/**
 *
 */
@RestController
@RequestMapping("/dispense/iqiyi")
@Slf4j
public class IqiyiClickController {

    @Autowired
    private ToutiaoClickService toutiaoClickService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    private AlertService alertService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    RedisRelationshipService redisRelationshipService;

    @GetMapping("/click")
    @ResponseBody
    public ReturnResult click(IqiyiClickReq iqiyiClickReq, HttpServletRequest request) {
        if (ocpcSwitcher.needLog(LogNum.IqiyiClick)) {
            log.info("iqiyiclick "+ JSON.toJSONString(iqiyiClickReq));
        }
        // 客户端操作系统的 类型 1 位数字,取 0~3。 0 表示 Android 1 表示 iOS 2 表示 Windows Phone 3 表示其他
        String os = iqiyiClickReq.getOs();
        if ("0".equals(os)) {
            iqiyiClickReq.setOs("android");
        } else if ("1".equals(os)) {
            iqiyiClickReq.setOs("ios");
        } else {
            log.warn("iqiyiclick非法的os " + JSON.toJSONString(iqiyiClickReq));
            return new ReturnResult();
        }

        ReturnResult returnResult = new ReturnResult();
        if (isNotAllBlankOrNull(iqiyiClickReq.getImeiMd5(), iqiyiClickReq.getOaid(), iqiyiClickReq.getOaid2(), iqiyiClickReq.getIdfa()
                , iqiyiClickReq.getMac(), iqiyiClickReq.getAndroidId()
        )) {
            ToutiaoClick toutiaoClick = new ToutiaoClick();
            toutiaoClick.setDsp(iqiyiClickReq.getDsp());
            toutiaoClick.setProduct(iqiyiClickReq.getProduct());
            toutiaoClick.setOs(iqiyiClickReq.getOs());
            toutiaoClick.setAccountId(iqiyiClickReq.getAccount_id());

            if (iqiyiClickReq.getOs().equals("ios")) {
                if (!"NULL".equalsIgnoreCase(iqiyiClickReq.getIdfa())) {
                    toutiaoClick.setOcpcDeviceId(iqiyiClickReq.getIdfa());
                    toutiaoClick.setIdfa2(DigestUtils.md5Hex(iqiyiClickReq.getIdfa()));
                }
            } else if(!"NULL".equalsIgnoreCase(iqiyiClickReq.getImeiMd5())){
                toutiaoClick.setOcpcDeviceId(iqiyiClickReq.getImeiMd5());
            }
            if(!"NULL".equalsIgnoreCase(iqiyiClickReq.getOaid())) {
                toutiaoClick.setOaid(iqiyiClickReq.getOaid());
            }
            if(!"NULL".equalsIgnoreCase(iqiyiClickReq.getOaid2())) {
                toutiaoClick.setOaid2(iqiyiClickReq.getOaid2());
            }
            if(!"NULL".equalsIgnoreCase(iqiyiClickReq.getMac())){
                toutiaoClick.setMac(iqiyiClickReq.getMac());
            }
            if(!"NULL".equalsIgnoreCase(iqiyiClickReq.getAndroidId())){
                toutiaoClick.setAndroidId(iqiyiClickReq.getAndroidId());
            }

            toutiaoClick.setTs(iqiyiClickReq.getTs());
            toutiaoClick.setCid(iqiyiClickReq.getCid());
            toutiaoClick.setGid(iqiyiClickReq.getGid());
            toutiaoClick.setPid(iqiyiClickReq.getPid());
            toutiaoClick.setCallbackUrl(iqiyiClickReq.getCallbackUrl());
            Date date = new Date();
            toutiaoClick.setUpdateTime(date);
            toutiaoClick.setCreateTime(date);
            try {
                toutiaoClickService.saveClick(toutiaoClick);
            } catch (Exception e) {
                log.error("save iqiyi click event error.", e);
                returnResult.setStatus(RspStatus.FAILED.value);
            }
            redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                    toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
            hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                    toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-爱奇艺");
            kafkaSender.sendClick(toutiaoClick);
            //将deviceId与caid存入redis缓存
            redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),iqiyiClickReq.getOs(),toutiaoClick);
        }

        return returnResult;
    }

    /**
     * 判断参数不全为空或“null”
     */
    private static boolean isNotAllBlankOrNull(String... params) {
        return !Arrays.stream(params).allMatch(k-> isBlankOrNull(k));
    }

    private static boolean isBlankOrNull(String param) {
        return StringUtils.isBlank(param) || "null".equalsIgnoreCase(param);
    }

}
