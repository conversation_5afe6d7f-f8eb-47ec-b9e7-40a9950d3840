package com.coohua.core.caf.dispense.controller.timer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.VivoAdvertiser;
import com.coohua.core.caf.dispense.dsp.service.VivoAdvertiserService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.UUID;

@Slf4j
@Component
public class VivoActiveUpload {
    @Autowired
    VivoAdvertiserService vivoAdvertiserService;

    /**
     * ck sql
     * select event_dist.userid,channel,device_id,imei,oaid from ods.event_dist where device_id global  in (
     *     select device_dist.device_id from dwd.device_dist where product= 'yrrs' and logday = yesterday() and channel like '%vivo%'
     *     )  and product= 'yrrs' and logday = yesterday() and channel like '%vivo%' group by device_id,userid,imei,oaid,channel;
     * @param imeiSet
     * @param oaIdSet
     */
    public void batchSend(Set<String> imeiSet,Set<String> oaIdSet){
        imeiSet.forEach(imei->{
            ToutiaoClick toutiaoClick = new ToutiaoClick();
            toutiaoClick.setPkgChannel("com.wj.yrrs");
            toutiaoClick.setOcpcDeviceId(MD5Utils.getMd5Sum(imei));
            toutiaoClick.setAccountId("***********");
//            toutiaoClick.setOaid();
            UserEventReq userEventReq = new UserEventReq();
            userEventReq.setPkgChannel("com.wj.yrrs");
            send(toutiaoClick,userEventReq,ToutiaoEventTypeEnum.KEY_EVENT);
        });


        oaIdSet.forEach(oaid->{
            ToutiaoClick toutiaoClick = new ToutiaoClick();
            toutiaoClick.setPkgChannel("com.wj.yrrs");
            toutiaoClick.setAccountId("***********");
            toutiaoClick.setOaid(oaid);
            UserEventReq userEventReq = new UserEventReq();
            userEventReq.setPkgChannel("com.wj.yrrs");
            send(toutiaoClick,userEventReq,ToutiaoEventTypeEnum.KEY_EVENT);
        });
    }

    public String send(ToutiaoClick toutiaoClick, UserEventReq userEventReq, ToutiaoEventTypeEnum toutiaoEventTypeEnum) {
        Response response = null;
        String rspStr = null;
        try {
            String advertiserId = toutiaoClick.getAccountId();
//            VivoAdvertiser vivoAdvertiser = VivoAdvertiserService.dmap.get(advertiserId);
//            if(vivoAdvertiser==null){
//                vivoAdvertiser = vivoAdvertiserService.queryByClientId(advertiserId);
//            }
            VivoAdvertiser vivoAdvertiser = null;
            if(vivoAdvertiser==null){
                throw  new RuntimeException("vivo该账户 "+advertiserId+"无配置，请在dsp平台配置");
            }
            String url = "https://marketing-api.vivo.com.cn/openapi/v1/advertiser/behavior/upload";
            String nonce = getNonce();
            String accessToken = vivoAdvertiser.getAccessToken();
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            JSONObject jsonObject = new JSONObject();

            jsonObject.put("srcType","APP");
            jsonObject.put("pkgName",userEventReq.getPkgChannel());
            jsonObject.put("srcId",vivoAdvertiser.getSrcId());
            JSONArray jsonArray = new JSONArray();

            JSONObject deviceObject = new JSONObject();
            if(StringUtils.isNotBlank(toutiaoClick.getOaid())){
                deviceObject.put("userIdType","OAID");
                deviceObject.put("userId",toutiaoClick.getOaid());
            }else{
                deviceObject.put("userIdType","IMEI_MD5");
                deviceObject.put("userId",toutiaoClick.getOcpcDeviceId());
            }
            if(ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)){
                return "vivo激活直接略过";
            }else if(ToutiaoEventTypeEnum.START_APP.equals(toutiaoEventTypeEnum)){
                return "vivo次留直接略过";
            }else if(ToutiaoEventTypeEnum.KEY_EVENT.equals(toutiaoEventTypeEnum)){
                deviceObject.put("cvType","ACTIVATION");
            }else {
                return "vivo"+toutiaoEventTypeEnum.desc+"直接略过";
            }
            deviceObject.put("cvTime",System.currentTimeMillis());
            deviceObject.put("creativeId",toutiaoClick.getCid());
            jsonArray.add(deviceObject);

            jsonObject.put("dataList",jsonArray);

            RequestBody body = RequestBody.create(mediaType, jsonObject.toJSONString());
            Request request = new Request.Builder()
                    .url(url + "?access_token=" + accessToken + "&timestamp=" + System.currentTimeMillis() + "&nonce=" + nonce + "&advertiser_id=" + advertiserId)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            response = client.newCall(request).execute();
            rspStr = response.body().string();
            log.info("vivo回传 "+rspStr);
        }catch (Exception e){
            log.error("",e);
        }finally {
            response.close();
        }
        return rspStr;
    }

    public static String getNonce(){
        UUID uuid = UUID.randomUUID();
        return  uuid.toString().substring(0,12);
    }
}
