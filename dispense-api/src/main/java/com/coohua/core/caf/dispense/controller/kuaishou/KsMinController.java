package com.coohua.core.caf.dispense.controller.kuaishou;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.controller.UserEventController;
import com.coohua.core.caf.dispense.dsp.service.OcpcMisActiveService;
import com.coohua.core.caf.dispense.dsp.service.ksmin.KsMinReq;
import com.coohua.core.caf.dispense.dsp.service.ksmin.KsMinService;
import com.coohua.core.caf.dispense.dsp.service.wmin.TTWxService;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("/dispense/ksmin")
@Slf4j
public class KsMinController {
    @Autowired
    TTWxService wxService;
    @Autowired
    OcpcMisActiveService ocpcMisActiveService;
    @Autowired
    UserActiveService userActiveService;
    @Autowired
    UserEventController userEventController;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KsMinService ksMinService;


    @RequestMapping(value = "/bactive")
    @ResponseBody
    public ReturnResult bactive(KsMinReq ksMinReq, HttpServletRequest request) {
        //{"openId":"f1bc2e45370fcddb14bf57243ce3ac16","os":"ksmin","query":"{\"gsid\":\"9f2fcbcf1dd60bcb64b7c7ac8836221f\"}","userId":"**********"}

        log.info(ksMinReq.getProduct()+" ks激活开始 "+ksMinReq.getUserId()+" "+ JSON.toJSONString(ksMinReq)+" "+ksMinReq.getQuery());

        if(StringUtils.isNotBlank(ksMinReq.getQuery())){
            String queryStr = ksMinReq.getQuery();
            JSONObject jsonObject = JSON.parseObject(queryStr);

            String accountId = jsonObject.getString("accountId");
            String calBack = jsonObject.getString("callback");
            String product = jsonObject.getString("product");
            if(StringUtils.isNotBlank(calBack)){
                ksMinReq.setAccountId(accountId);
                ksMinReq.setProduct(product);
                ksMinReq.setCallBackUrl(calBack);
                log.info("ksmin非自然量 "+" "+ksMinReq.getQuery()+" "+ JSON.toJSONString(ksMinReq)+" ");
            }
        }
        ksMinService.sendKsActive(ksMinReq);
        return new ReturnResult();
    }


    @RequestMapping(value = "/uplck")
    @ResponseBody
    public ReturnResult uplck(KsMinReq ksMinReq, HttpServletRequest request) {
        log.info("ks视频观看 "+ksMinReq.getUserId()+" "+ JSON.toJSONString(ksMinReq));
        ksMinService.sendKxEvent(ksMinReq);
        return new ReturnResult();
    }
}
