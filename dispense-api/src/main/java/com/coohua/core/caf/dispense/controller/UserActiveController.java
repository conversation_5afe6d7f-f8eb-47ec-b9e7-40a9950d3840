package com.coohua.core.caf.dispense.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.AuthKuaishouApp;
import com.coohua.core.caf.dispense.dsp.entity.Product;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AuthKuaishouAppService;
import com.coohua.core.caf.dispense.dsp.service.ProductService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.bytemin.ByteMiniService;
import com.coohua.core.caf.dispense.dto.req.DeviceReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.WxActiveReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.hbase.HbaseLockUserActiveService;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.service.OcpcLockinfoService;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import com.coohua.core.caf.dispense.ocpc.service.VivoLockService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.ConstCls;
import com.coohua.core.caf.dispense.utils.EventDataCheck;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.coohua.core.caf.dispense.utils.OcpcEventKeyJoinUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/dispense/user/event")
@Slf4j
public class UserActiveController {
    @Autowired
    UserActiveService userActiveService;

    @ApolloJsonValue("${channel.ocpc.white.list:[\"kskhsy\"]}")
    private List<String> skipOcpcQueryCheckChannel;
    @Autowired
    private ProductService productService;

    @RequestMapping(value = "/userActive", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult userActive(UserActive userActive, HttpServletRequest request) {
        log.info("usercheckActivate request userActive = {} ", JSONObject.toJSONString(userActive));
        String androidId = userActive.getAndroidId();
        setMd5Info(userActive);
        userActiveService.activeUser(userActive, false, androidId);
        ReturnResult returnResult = new ReturnResult();
        return returnResult;
    }
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    public static void  main(String[] args){
        long d1  = DateUtil.between(DateUtil.parseDate("2023-07-01"),DateUtil.parseDate("2023-10-01"), DateUnit.DAY);

        System.out.println(d1);

        long d12  = DateUtil.between(DateUtil.parseDate("2023-04-01"),DateUtil.parseDate("2023-07-01"), DateUnit.DAY);
        System.out.println(d12);
    }
    @Autowired
    OcpcLockinfoService ocpcLockinfoService;
    @Autowired
    HbaseLockUserActiveService hbaseLockUserActiveService;
    @Autowired
    VivoLockService vivoLockService;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    ByteMiniService byteMiniService;
    @Autowired
    HbaseEventService hbaseEventService;

    @RequestMapping(value = "/guiDsp", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult guiDsp(UserEventReq userEventReq, HttpServletRequest request) {
        //log.info("beforeuserEventReq = {} ", JSONObject.toJSONString(userEventReq));
        ReturnResult returnResult = new ReturnResult();
        // 增加白名单渠道
        if (skipOcpcQueryCheckChannel.contains(userEventReq.getPkgChannel())){
            log.info("{} Target White Channel,Skip...",userEventReq.getOcpcDeviceId());
            returnResult.setData("nodsp");
            return returnResult;
        }

        if(ocpcSwitcher.baiduGy && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && userEventReq.getPkgChannel().startsWith("bd")
                && ocpcSwitcher.baiduGyChannel.contains(userEventReq.getPkgChannel())
        ){
            log.info("baiduGDSP渠道替换product "+"bd"+userEventReq.getProduct()+"@"+userEventReq.getPkgChannel());
            userEventReq.setProduct("bd"+userEventReq.getProduct());
        }
        EventDataCheck.replaceZto(userEventReq);
        if(StringUtils.isBlank(userEventReq.getSourceDeviceId())) {
            userEventReq.setSourceDeviceId(userEventReq.getAndroidId());
        }
        UserEventReq.setMd5Inf(userEventReq);
        userEventReq.setUa(MD5Utils.getMd5Ua(userEventReq.getUa()));

        if(byteMiniService.isActive(userEventReq)) {
            //小程序激活
            returnResult.setData(DspType.BYTEMIN.value);
            return returnResult;
        }

//        boolean isVflg = vivoLockService.isVivoFlg(userEventReq);
//        if(isVflg){
//            String dspStr = vivoLockService.getVivoDsp(userEventReq);
//            returnResult.setData(dspStr);
//            return returnResult;
//        }

        ToutiaoClick toutiaoClick = null;
        UserActive userActive = hbaseLockUserActiveService.queryDeviceActive(userEventReq);
        if(userActive==null){
            userActive = userActiveService.queryActive(userEventReq.getUserId(),userEventReq.getProduct());
        }

        if(false){
            if(userActive!=null){
                if ((!DspType.INNER_OLD_PULL_NEW.name.equalsIgnoreCase(userActive.getSource())
                        && !DspType.INNER_VIDEO.name.equalsIgnoreCase(userActive.getSource())
                        && !DspType.INNER_DRAINAGE.name.equalsIgnoreCase(userActive.getSource()))
                        && !"自然量".equalsIgnoreCase(userActive.getSource())
                        && !"neilaxin".equalsIgnoreCase(userActive.getSource())
                        && !"INNER_OLD_PULL".equalsIgnoreCase(userActive.getSource())
                        && !"nodsp".equalsIgnoreCase(userActive.getSource())
                        && !"自然ALIYUN量".equalsIgnoreCase(userActive.getSource())
                ) {
                    /**
                     * toutiao
                     * kuaishou
                     * guangdiantong
                     * 自然量
                     * oppo
                     * baidufeed
                     * vivo
                     * neilaxin
                     * xiaomi
                     * yingyongbao
                     * INNER_OLD_PULL
                     * 自然ALIYUN量
                     * huawei
                     * nodsp
                     */
                    returnResult.setData(userActive.getSource());
                    log.info("active归因 "+userEventReq.getPkgChannel()+" "+userEventReq.getProduct()+" "+userEventReq.getUserId()+" "+userEventReq.getOaid()+" "+userEventReq.getOs()+" "+userActive.getSource());
                } else {
                    returnResult.setData("nodsp");
                }
            }else{
                returnResult.setData("nodsp");
            }
        }else{
            toutiaoClick = userActiveService.guiOcpc(userEventReq);
            //hbase没有数据 没有userActive 或者 active 的gyType 不是sdk且该用户有记录不超过10min
            if (null == toutiaoClick && (null == userActive ||
                    (!Objects.equals(userActive.getGyType(), "sdk") && System.currentTimeMillis()-userActive.getCreateTime().getTime() <= 60000l))) {
                toutiaoClick = userActiveService.guiOcpcBySdk(userEventReq);
            }
            if (toutiaoClick != null) {
                if ((!DspType.INNER_OLD_PULL_NEW.name.equalsIgnoreCase(toutiaoClick.getDsp())
                        && !DspType.INNER_VIDEO.name.equalsIgnoreCase(toutiaoClick.getDsp())
                        && !DspType.INNER_DRAINAGE.name.equalsIgnoreCase(toutiaoClick.getDsp()))) {
                    returnResult.setData(toutiaoClick.getDsp());
                } else {
                    returnResult.setData("nodsp");
                }
            } else {
                if(userActive!=null){
                    long days = (System.currentTimeMillis()-userActive.getCreateTime().getTime())/ DateTimeConstants.MILLIS_PER_DAY;
                    if(days>20){
                        if ((!DspType.INNER_OLD_PULL_NEW.name.equalsIgnoreCase(userActive.getSource())
                                && !DspType.INNER_VIDEO.name.equalsIgnoreCase(userActive.getSource())
                                && !DspType.INNER_DRAINAGE.name.equalsIgnoreCase(userActive.getSource()))
                                && !"自然量".equalsIgnoreCase(userActive.getSource())
                                && !"neilaxin".equalsIgnoreCase(userActive.getSource())
                                && !"INNER_OLD_PULL".equalsIgnoreCase(userActive.getSource())
                                && !"nodsp".equalsIgnoreCase(userActive.getSource())
                                && !"自然ALIYUN量".equalsIgnoreCase(userActive.getSource())
                        ) {
                            /**
                             * toutiao
                             * kuaishou
                             * guangdiantong
                             * 自然量
                             * oppo
                             * baidufeed
                             * vivo
                             * neilaxin
                             * xiaomi
                             * yingyongbao
                             * INNER_OLD_PULL
                             * 自然ALIYUN量
                             * huawei
                             * nodsp
                             */
                            returnResult.setData(userActive.getSource());
                            log.info("创建时间大于20直接放过 "+userEventReq.getProduct()+" "+days+" "+userEventReq.getUserId()+" "+userEventReq.getOaid()+" "+userEventReq.getOs()+" "+userActive.getSource());
                        } else {
                            returnResult.setData("nodsp");
                        }
                    }else{
                        returnResult.setData("nodsp");
                    }
                }else{
                    log.info("nodsp非投放 os {} = {} ", userEventReq.getOs(), JSONObject.toJSONString(userEventReq));
                    returnResult.setData("nodsp");
                }
            }
        }

        if (StringUtils.isNotBlank(userEventReq.getCaid()) && ocpcSwitcher.caids.contains(userEventReq.getCaid())) {
            log.info("ios白名单设备caid  " + userEventReq.getUserId() + " " + userEventReq.getProduct() + " " + userEventReq.getCaid());
            returnResult.setData("nodsp");
        }
        ocpcLockinfoService.saveLockInfo(userEventReq,userActive,toutiaoClick,null,"guiDsp");
        return returnResult;
    }

    @Autowired
    AuthKuaishouAppService authKuaishouAppService;
    @Autowired
    RedisClickService redisClickService;
    /**
     * 五组查询归因接口，目前只用来区分快手投放和快手达人，只有ios调用
     * @return
     */
    @RequestMapping(value = "/guiDspFor5", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult guiDspFor5(UserEventReq userEventReq, HttpServletRequest request) {
        log.info("五组查询归因 userEventReq = {}",JSONObject.toJSONString(userEventReq));
        ReturnResult returnResult = new ReturnResult();
        // 先根据userId、product查询归因
        OcpcEvent ocpcEvent = null;
        List<String> orderlyKeys = OcpcEventKeyJoinUtil.generatePossibleEventKeyOrderly(userEventReq, ToutiaoEventTypeEnum.ACTIVATE_APP);
        if (orderlyKeys != null && !orderlyKeys.isEmpty()) {
            for (String orderlyKey : orderlyKeys) {
                ocpcEvent = hbaseEventService.queryUserActiveEvent(orderlyKey);
                if (ocpcEvent != null) {
                    returnResult.setData(ocpcEvent.getDsp());
                    if ("kuaishou".equals(ocpcEvent.getDsp()) && "ios".equals(ocpcEvent.getOs())) {
                        // 查询是否是快手达人账户
                        AuthKuaishouApp authKuaishouApp = authKuaishouAppService.getAuthKuaishouAppByAdvertiserId(ocpcEvent.getAccountId());
                        if (authKuaishouApp.getAccountType() == 2) {
                            returnResult.setData("kuaishoudaren");
                        }
                    }
                }
            }
        }
        // 如果归因为空，则根据各种id查询点击
        if (ocpcEvent == null) {
            ToutiaoClick click = redisClickService.getClick(userEventReq, false);
            if (click == null) {
                click = redisClickService.getClick(userEventReq, true);
            }
            if (click != null) {
                returnResult.setData(click.getDsp());
                if ("kuaishou".equals(click.getDsp()) && "ios".equals(click.getOs())) {
                    // 查询是否是快手达人账户
                    AuthKuaishouApp authKuaishouApp = authKuaishouAppService.getAuthKuaishouAppByAdvertiserId(click.getAccountId());
                    if (authKuaishouApp.getAccountType() == 2) {
                        returnResult.setData("kuaishoudaren");
                    }
                }
            }
        }
        return returnResult;
    }


    /**
     * 查询归因，判断是投放用户还是自然量用户。通过product os userId进行查询
     * @return
     */
    @RequestMapping(value = "/queryGy", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult queryGy(UserEventReq userEventReq, HttpServletRequest request) {
        log.info("查询归因 userEventReq = {}",JSONObject.toJSONString(userEventReq));
        ReturnResult returnResult = new ReturnResult();
        // 根据appId查询产品打点名
        Product product = productService.getByAppId(userEventReq.getAppId());
        if (product == null) {
            log.error("查询产品为空 userEventReq = {}",JSONObject.toJSONString(userEventReq));
            return returnResult;
        }
        userEventReq.setProduct(product.getName());
        OcpcEvent ocpcEvent = redisEventService.getActiveEvent(userEventReq);
        if (ocpcEvent == null) {
            returnResult.setData("default");
        }else {
            // 判断投放还是自然量
            if (StringUtils.isBlank(ocpcEvent.getGyType()) || GuiyingType.packageGy.name().equals(ocpcEvent.getGyType())) {
                returnResult.setData("naturalUser");
            }else {
                returnResult.setData("purchaseUser");
            }
        }
        return returnResult;
    }

    @RequestMapping(value = "/register/list", method = RequestMethod.GET)
    @ResponseBody
    public ReturnResult registerUsers(DeviceReq deviceReq, HttpServletRequest request) {
        log.info("registerUsers = {} ", JSONObject.toJSONString(deviceReq));
        EventDataCheck.replaceZto(deviceReq);
        setMd5Info(deviceReq);
        List<UserActive> userActiveList = userActiveService.deviceRegisters(deviceReq);
//        log.info(deviceReq.getProduct()+""+deviceReq.getUserId()+" 匹配到 "+ JSON.toJSONString(userActiveList));

        Set<String> pset = userActiveList.stream().map(UserActive::getProduct).collect(Collectors.toSet());
        log.info(deviceReq.getProduct() + "" + deviceReq.getUserId() + " 匹配到 " + JSON.toJSONString(pset)+" userIds:"+JSON.toJSONString(userActiveList.stream().map(UserActive::getUserId).collect(Collectors.toSet())));

        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(pset);
        return returnResult;
    }

    private void setMd5Info(DeviceReq deviceReq) {
        deviceReq.setMac(MD5Utils.getMd5Mac(deviceReq.getMac()));
        deviceReq.setAndroidId(MD5Utils.getMd5AndroidId(deviceReq.getAndroidId()));
    }

    private void setMd5Info(UserActive userActive) {
        if(StringUtils.isNotBlank(userActive.getMac()) && MD5Utils.macSet.contains(userActive.getMac())){
            log.info("macmoren "+userActive.getMac()+" "+userActive.getUserId());
            userActive.setMac(null);
        }
        if (StringUtils.isNotBlank(userActive.getOaid()) && !ConstCls.emptyMd5.equals(userActive.getOaid()) && !ConstCls.zeroMd5.equals(userActive.getOaid())) {
            String oaid2 = MD5Utils.getMd5Sum(userActive.getOaid());
            userActive.setOaid2(oaid2);
        }
        if ("ios".equalsIgnoreCase(userActive.getOs())
                && StringUtils.isNotBlank(userActive.getIdfa())
                && userActive.getIdfa().length()>3
                && !userActive.getIdfa().startsWith("00000000-0000")
                && !userActive.getIdfa().startsWith("000000-0000")
                && !userActive.getIdfa().startsWith("0000-0000") ) {
            String idfa2 = MD5Utils.getMd5Sum(userActive.getIdfa());
            userActive.setIdfa2(idfa2);
        }
        userActive.setMac(MD5Utils.getMd5Mac(userActive.getMac()));
        userActive.setAndroidId(MD5Utils.getMd5AndroidId(userActive.getAndroidId()));
    }



    @RequestMapping(value = "/minDsp")
    @ResponseBody
    public ReturnResult queryMinDsp(WxActiveReq wxActiveReq, String minOS,
                                    HttpServletRequest request) {

        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setEventType(0);
        userEventReq.setProduct(wxActiveReq.getProduct());
        userEventReq.setUserId(wxActiveReq.getUserId());
        userEventReq.setOpenId(wxActiveReq.getOpenId());
        userEventReq.setAppId(wxActiveReq.getAppId());
        userEventReq.setOs(minOS);

        OcpcEvent ocpcEvent = redisEventService.getActiveEvent(userEventReq);
        ReturnResult returnResult = new ReturnResult();
        if (Objects.nonNull(ocpcEvent)) {
            returnResult.setData(ocpcEvent.getDsp());
        } else {
            returnResult.setData("nodsp");
        }
        return returnResult;
    }

    @RequestMapping(value = "/queryGuiStatistics")
    @ResponseBody
    public ReturnResult queryGuiStatistics() {
        ReturnResult returnResult = new ReturnResult();
        returnResult.setData(userActiveService.queryGuiStatistics());
        return returnResult;
    }
}
