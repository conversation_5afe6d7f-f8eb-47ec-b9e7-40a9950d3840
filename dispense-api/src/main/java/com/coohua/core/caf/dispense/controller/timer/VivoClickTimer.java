package com.coohua.core.caf.dispense.controller.timer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coohua.caf.core.kv.DistributedLock;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.VivoAdvertiser;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dsp.service.VivoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.vivo.PageInfo;
import com.coohua.core.caf.dispense.dsp.vivo.VivoClick;
import com.coohua.core.caf.dispense.dsp.vivo.VivoMsgBean;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcClickVivo;
import com.coohua.core.caf.dispense.ocpc.service.OcpcClickVivoService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Slf4j
public class VivoClickTimer {
    @Autowired
    VivoAdvertiserService vivoAdvertiserService;

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Scheduled(cron = "0 0/15 * * * ?")
    public void startInsertClick(){
        DistributedLock.Lock lock = DistributedLock.tryLock(bpDispenseJedisClusterClient, RedisKeyConstants.VIVO_CLICK_LOCK, 15 * DateTimeConstants.MILLIS_PER_MINUTE);
        if (!lock.success()) {
            return;
        }
        LambdaQueryWrapper<VivoAdvertiser> objectQueryWrapper = new QueryWrapper<VivoAdvertiser>().lambda();
        objectQueryWrapper.eq(VivoAdvertiser::getDelFlag,0);
        List<VivoAdvertiser> vivoAdvertiserList = vivoAdvertiserService.list(objectQueryWrapper);

        for(VivoAdvertiser vivoAdvertiser : vivoAdvertiserList){
            try {
                Date date = new Date();
                List<VivoClick> vivoClicks = queryVivoClicks(vivoAdvertiser.getClientId(),vivoAdvertiser.getAccessToken(),0,date);
                int lastId = saveToClicks(vivoAdvertiser,vivoClicks);

                while (lastId!=0){
                    vivoClicks = queryVivoClicks(vivoAdvertiser.getClientId(),vivoAdvertiser.getAccessToken(),lastId,date);
                    lastId = saveToClicks(vivoAdvertiser,vivoClicks);
                }
            }catch (Exception e){
                log.error("",e);
            }
        }
    }
    @Autowired
    ToutiaoClickService toutiaoClickService;
    @Autowired
    OcpcClickVivoService ocpcClickVivoService;
    private Integer saveToClicks(VivoAdvertiser vivoAdvertiser,List<VivoClick> vivoClicks){
        AtomicInteger lastClickId = new AtomicInteger(0);
        List<OcpcClickVivo> ocpcClickVivos = new ArrayList<OcpcClickVivo>();
        vivoClicks.forEach(vivoClick -> {
            lastClickId.set(vivoClick.getId());
            OcpcClickVivo ocpcClickVivo = new OcpcClickVivo();
            ocpcClickVivo.setAccountId(vivoAdvertiser.getClientId());
            ocpcClickVivo.setCid(vivoClick.getAdId());
            ocpcClickVivo.setPid(vivoClick.getPlanId());
            ocpcClickVivo.setDsp(DspType.vivo.name);

            ocpcClickVivo.setOaid(vivoClick.getOaid());
            ocpcClickVivo.setOcpcDeviceId(vivoClick.getImei());
            ocpcClickVivo.setOs("android");
            ocpcClickVivo.setPkgChannel(vivoClick.getAppPackage());
            ocpcClickVivo.setProduct(vivoAdvertiser.getProductName());
            ocpcClickVivo.setCreateTime(new Date(vivoClick.getDownBegin()));
            ocpcClickVivo.setUpdateTime(new Date());
            ocpcClickVivo.setId(new Long(vivoClick.getId()));
            ToutiaoClick toutiaoClick = new ToutiaoClick();
            BeanUtils.copyProperties(ocpcClickVivo,toutiaoClick);
            toutiaoClickService.saveClick(toutiaoClick);
            if(ocpcClickVivoService.getById(new Long(vivoClick.getId()))==null){
                ocpcClickVivos.add(ocpcClickVivo);
            }
        });
        ocpcClickVivoService.saveBatch(ocpcClickVivos);
        return lastClickId.get();
    }

    public List<VivoClick> queryVivoClicks(String advertiserId, String accessToken, int lastId, Date reportDate) {
        List<VivoClick> vivoClicks = new ArrayList<>();
        String url = "https://marketing-api.vivo.com.cn/openapi/v1/adstatement/downloadImei/query";
        String nonce = getNonce();
        Response response = null;
        String rspStr = "";
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("lastId",lastId);
            String reportDateStr = DateUtils.formatDateForYMDSTR(reportDate);
            jsonObject.put("reportDate",reportDateStr);
            jsonObject.put("pageSize",100);
            RequestBody body = RequestBody.create(mediaType, jsonObject.toJSONString());
            Request request = new Request.Builder()
                    .url(url + "?access_token=" + accessToken + "&timestamp=" + System.currentTimeMillis() + "&nonce=" + nonce + "&advertiser_id=" + advertiserId)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            response = client.newCall(request).execute();
            rspStr = response.body().string();

            VivoMsgBean vivoMsgBean = JSON.parseObject(rspStr, VivoMsgBean.class);
            if(vivoMsgBean.getCode()==0){
                PageInfo pageInfo = vivoMsgBean.getData().getPageInfo();
                vivoClicks = vivoMsgBean.getData().getList();
            }
        }catch (Exception e){
            log.error("",e);
        }finally {
            if(response!=null){
                response.close();
            }
        }

        return vivoClicks;
    }

    public static String getNonce(){
        UUID uuid = UUID.randomUUID();
        return  uuid.toString().substring(0,12);
    }
}
