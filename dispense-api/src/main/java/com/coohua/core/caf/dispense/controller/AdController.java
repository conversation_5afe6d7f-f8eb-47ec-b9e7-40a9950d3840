package com.coohua.core.caf.dispense.controller;


import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;


@Controller
@RequestMapping("/dispense/ad")
@Slf4j
public class AdController {


    @RequestMapping("/adUpload")
    @ResponseBody
    public ReturnResult activate( HttpServletRequest request) {
	    ReturnResult returnResult = new ReturnResult();
	    log.info("ad upload ");
        return returnResult;
    }
}
