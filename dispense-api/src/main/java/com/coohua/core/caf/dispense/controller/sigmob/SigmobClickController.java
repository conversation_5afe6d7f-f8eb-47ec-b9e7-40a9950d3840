package com.coohua.core.caf.dispense.controller.sigmob;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.*;
import com.coohua.core.caf.dispense.dto.req.SigmobClickReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/11/25 20:36
 * @Description: sigmob监控链接请求Controller
 */
@RestController
@RequestMapping("/dispense/sigmob")
@Slf4j
public class SigmobClickController {

    @Autowired
    private ToutiaoClickService toutiaoClickService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    private AlertService alertService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    RedisRelationshipService redisRelationshipService;


    @GetMapping("/click")
    @ResponseBody
    public SigmobClickRep click(SigmobClickReq sigmobClickReq, HttpServletRequest request) {
        log.info("Sigmob平台调用监控链接所传递参数为:" + JSONObject.toJSONString(sigmobClickReq));
        toutiaoClickService.checkLengthAndSetUaMd5(DspType.SIGMOB, sigmobClickReq);

        SigmobClickRep sigmobClickRep = new SigmobClickRep();
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(sigmobClickReq.getDsp());
        toutiaoClick.setProduct(sigmobClickReq.getProduct());
        toutiaoClick.setAccountId(sigmobClickReq.getAccount_id());
        toutiaoClick.setClickId(sigmobClickReq.getClickId());
        toutiaoClick.setIdfa2(sigmobClickReq.getIdfaMd5());
        toutiaoClick.setCaid(sigmobClickReq.getCaid());
        toutiaoClick.setCaid2(sigmobClickReq.getCaid2());
        toutiaoClick.setOaid(sigmobClickReq.getOaidMd5());
        toutiaoClick.setOaid2(sigmobClickReq.getOrOaidMd5());
        toutiaoClick.setIp(sigmobClickReq.getIp());
        toutiaoClick.setUa(sigmobClickReq.getUa());
        toutiaoClick.setCallbackUrl(sigmobClickReq.getCallback());

        //Sigmob 传值ios=1，android=2
        if (sigmobClickReq.getOs().equals("2")){
            toutiaoClick.setOs("android");
        }else if (sigmobClickReq.getOs().equals("1")){
            toutiaoClick.setOs("ios");
        }else {
            log.error("Sigmob监测链接返回OS值不在限定范围内: "+sigmobClickReq.getOs());
        }

        toutiaoClick.setModel(sigmobClickReq.getModel());


        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);

        // 将请求的相关参数 进行存储
        saveClick(sigmobClickRep, toutiaoClick);

        // 将idfa与caid存入redis缓存
        if (sigmobClickReq.getCaid() != null && sigmobClickReq.getCaid().equals(sigmobClickReq.getCaid2())) {
            redisRelationshipService.saveIdfaCaid(sigmobClickReq.getIdfaMd5(), toutiaoClick.getCaid(), sigmobClickReq.getOs(), toutiaoClick);
        } else if (sigmobClickReq.getCaid() != null) {
            // caid 与caid2不想等则存储2份映射
            redisRelationshipService.saveIdfaCaid(sigmobClickReq.getIdfaMd5(), toutiaoClick.getCaid(), sigmobClickReq.getOs(), toutiaoClick);
            redisRelationshipService.saveIdfaCaid(sigmobClickReq.getIdfaMd5(), toutiaoClick.getCaid2(), sigmobClickReq.getOs(), toutiaoClick);
        }

        return sigmobClickRep;
    }

    private void saveClick(SigmobClickRep sigmobClickRep, ToutiaoClick toutiaoClick) {
        try {
            toutiaoClickService.saveClick(toutiaoClick);
        } catch (Exception e) {
            log.error("sigmob接收监控链接保存数据库失败:", e);
            sigmobClickRep.setRet(500);
            sigmobClickRep.setMsg(e.getMessage());
        }
        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(), toutiaoClick.getOaid2(), toutiaoClick.getMac(), toutiaoClick);
        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(), toutiaoClick.getOaid2(), toutiaoClick.getMac(), toutiaoClick);
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-Sigmob");
        kafkaSender.sendClick(toutiaoClick);

    }


    @Autowired
    SigmobUserEventService sigmobUserEventService;
    @GetMapping("/callbackTest")
    @ResponseBody
    public void click(Integer eventType,String callback,String dsp) {
        UserEventReq userEventReq=new UserEventReq();
        userEventReq.setEventType(eventType);

        ToutiaoClick toutiaoClick=new ToutiaoClick();
        toutiaoClick.setCallbackUrl(callback);
        toutiaoClick.setDsp(dsp);

        UserEvent userEvent=new UserEvent();


        sigmobUserEventService.activeEvent(userEventReq,toutiaoClick,userEvent);
    }

}
