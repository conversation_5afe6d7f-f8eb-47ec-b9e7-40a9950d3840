package com.coohua.core.caf.dispense.controller;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.service.bytemin.ByteMiniService;
import com.coohua.core.caf.dispense.dsp.service.OcpcMisActiveService;
import com.coohua.core.caf.dispense.dsp.service.wmin.TTWxService;
import com.coohua.core.caf.dispense.dto.req.ByteActiveReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Set;

@Controller
@RequestMapping("/dispense/byte")
@Slf4j
public class ByteActiveController {
    @Autowired
    TTWxService wxService;
    @Autowired
    OcpcMisActiveService ocpcMisActiveService;
    @Autowired
    UserActiveService userActiveService;
    @Autowired
    UserEventController userEventController;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    ByteMiniService byteMiniService;


    @RequestMapping(value = "/bactive")
    @ResponseBody
    public ReturnResult bactive(ByteActiveReq byteActiveReq, HttpServletRequest request) {
        if(StringUtils.equalsIgnoreCase(byteActiveReq.getProduct(),"lshj")){
            StringBuffer sbstr = new StringBuffer();
            Set<String> keyset = request.getParameterMap().keySet();
            for (String key : keyset) {
                String[] values = request.getParameterValues(key);
                for (String value : values) {
                    sbstr.append(key).append("=").append(value).append("&");
                }
            }
            log.info("minlshj 开始回传 "+sbstr);
        }
        log.info(byteActiveReq.getProduct()+" byte激活开始 "+byteActiveReq.getUserId()+" "+ JSON.toJSONString(byteActiveReq));
        if(StringUtils.isNotBlank(byteActiveReq.getClickid()) && !StringUtils.equalsIgnoreCase("undefined",byteActiveReq.getClickid())){
            log.info(byteActiveReq.getProduct()+" byte投放激活开始 "+byteActiveReq.getUserId()+" "+ JSON.toJSONString(byteActiveReq));
            byteMiniService.sendByteAct(byteActiveReq);
        }else{
            log.info(byteActiveReq.getProduct()+" byte激活开始 "+byteActiveReq.getUserId()+" 非投放用户 "+ JSON.toJSONString(byteActiveReq));
        }
        return new ReturnResult();
    }


    @RequestMapping(value = "/uplck")
    @ResponseBody
    public ReturnResult uplck(ByteActiveReq byteActiveReq, HttpServletRequest request) {
        log.info("byte回传看广告信息 "+byteActiveReq.getUserId()+" "+ JSON.toJSONString(byteActiveReq));
        byteMiniService.sendByteEvent(byteActiveReq,false);
        return new ReturnResult();
    }

    @RequestMapping(value = "/minuplck")
    @ResponseBody
    public ReturnResult minuplck(ByteActiveReq byteActiveReq, HttpServletRequest request) {
        log.info("byte回传看广告信息minuplck "+byteActiveReq.getUserId()+" "+ JSON.toJSONString(byteActiveReq));
        byteMiniService.sendByteEvent(byteActiveReq,true);
        return new ReturnResult();
    }
}
