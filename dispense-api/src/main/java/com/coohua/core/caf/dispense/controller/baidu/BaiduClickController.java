package com.coohua.core.caf.dispense.controller.baidu;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dto.req.BaiduClickReq;
import com.coohua.core.caf.dispense.dto.req.ToutiaoClickReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.enums.RspStatus;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.jcraft.jsch.MAC;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

/**
 * http://ocpc.baidu.com/developer/d/guide/?iurl=app%2Fapp-interface%2FREADME1.html
 */
@RestController
@RequestMapping("/dispense/baidu")
@Slf4j
public class BaiduClickController {

    @Autowired
    private ToutiaoClickService toutiaoClickService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    private AlertService alertService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    RedisRelationshipService redisRelationshipService;

    @GetMapping("/click")
    @ResponseBody
    public ReturnResult click(BaiduClickReq baiduClickReq, HttpServletRequest request) {
//        log.info("baiduclick "+ JSON.toJSONString(baiduClickReq));
        ReturnResult returnResult = new ReturnResult();
        String product = baiduClickReq.getProduct();

        if(ocpcSwitcher.baiduGy && ocpcSwitcher.baiduGyChannel.contains(baiduClickReq.getAccount_id())){
            product = "bd"+product;
        }
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(baiduClickReq.getDsp());
        toutiaoClick.setProduct(product);
        toutiaoClick.setOs(baiduClickReq.getOs());
        toutiaoClick.setAccountId(baiduClickReq.getAccount_id());
        toutiaoClick.setAccountName(baiduClickReq.getAccount_name());
        if (baiduClickReq.getOs().equals("ios")) {
            if (!"NULL".equalsIgnoreCase(baiduClickReq.getIdfa())) {
                toutiaoClick.setOcpcDeviceId(baiduClickReq.getIdfa());
                toutiaoClick.setIdfa2(DigestUtils.md5Hex(baiduClickReq.getIdfa()));
            }
        } else if(!"NULL".equalsIgnoreCase(baiduClickReq.getImeiMd5())){
            toutiaoClick.setOcpcDeviceId(baiduClickReq.getImeiMd5());
        }

        toutiaoClick.setTs(baiduClickReq.getTs());
        toutiaoClick.setCid(baiduClickReq.getUid());
        toutiaoClick.setGid(baiduClickReq.getAccount_id());
        toutiaoClick.setPid(baiduClickReq.getPid());
        if(!"NULL".equalsIgnoreCase(baiduClickReq.getOaid())) {
            toutiaoClick.setOaid(baiduClickReq.getOaid());
        }
        if(!"NULL".equalsIgnoreCase(baiduClickReq.getOaid2())) {
            toutiaoClick.setOaid2(baiduClickReq.getOaid2());
        }
        if(!"NULL".equalsIgnoreCase(baiduClickReq.getMac())){
            toutiaoClick.setMac(baiduClickReq.getMac());
        }
        if(!"NULL".equalsIgnoreCase(baiduClickReq.getAndroidId())){
            toutiaoClick.setAndroidId(baiduClickReq.getAndroidId());
        }
        toutiaoClick.setCallbackUrl(baiduClickReq.getCallbackUrl());
        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);
        //新增渠道包参数
        if(!"NULL".equals(baiduClickReq.getPkg_channel())){
            toutiaoClick.setPkgChannel(baiduClickReq.getPkg_channel());
        }
        try {
            toutiaoClickService.saveClick(toutiaoClick);
        } catch (Exception e) {
            log.error("save baidu click event error.", e);
            returnResult.setStatus(RspStatus.FAILED.value);
        }
        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-百度");
        kafkaSender.sendClick(toutiaoClick);
        //将deviceId与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),baiduClickReq.getOs(),toutiaoClick);
        return returnResult;
    }

    public static void main(String[] args){
        String mac = "02:00:00:00:00:01";
        String rstr = mac.replaceAll(":", "").toUpperCase();
        System.out.println(MD5Utils.getMd5Sum(rstr));
    }

    private boolean isValidReq(ToutiaoClickReq toutiaoClickReq) {
        boolean isVal = true;
        if ("__CALLBACK_URL__".equals(toutiaoClickReq.getCallback_url())) {
            isVal = false;
        }
        if (StringUtils.isEmpty(toutiaoClickReq.getOcpc_device_id()) && StringUtils.isEmpty(toutiaoClickReq.getOaid())) {
            isVal = false;
        }
        return isVal;
    }

    /**
     * 判断参数不全为空或“null”
     */
    private static boolean isNotAllBlankOrNull(String... params) {
        return !Arrays.stream(params).allMatch(k-> isBlankOrNull(k));
    }

    private static boolean isBlankOrNull(String param) {
        return StringUtils.isBlank(param) || "null".equalsIgnoreCase(param);
    }

}
