package com.coohua.core.caf.dispense.controller.vivo;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.VivoClickReqBody;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * https://ad.vivo.com.cn/help?id=352
 */
@RestController
@RequestMapping("/dispense/vivo")
@Slf4j
public class VivoClickController {

    @Autowired
    private ToutiaoClickService toutiaoClickService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    private AlertService alertService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    RedisRelationshipService redisRelationshipService;
    //
    @PostMapping("/click")
    @ResponseBody
    public VivoResponse click(@RequestParam String dsp, @RequestParam String os, @RequestParam String product, @RequestBody List<VivoClickReqBody> reqBodyList) {
        log.info("vivo click start {} {} {} {}", dsp, os, product, JSON.toJSONString(reqBodyList));
        VivoResponse vivoResponse = new VivoResponse();
        if (reqBodyList.size() < 1) {
            log.error("vivoclick 接收失败 异常的body {} {} {} {}", dsp, os, product, JSON.toJSONString(reqBodyList));
            return vivoResponse;
        }

        int listSize = reqBodyList.size();

        reqBodyList = reqBodyList.stream()
                .filter(reqBody-> {
                    if (StringUtils.isAllBlank(reqBody.getImei(), reqBody.getOaid())) {
                        log.error("vivoclick 接收失败 imei和oaid均为空 {} {} {} listSize{} {}", dsp, os, product, listSize, JSON.toJSONString(reqBody));
                        return false;
                    }

                    if (!Objects.equals(dsp, "vivo")) {
                        log.error("vivoclick 接收失败 异常的dsp {} {} {} listSize{} {}", dsp, os, product, listSize, JSON.toJSONString(reqBody));
                        return false;
                    }
                    if (!Objects.equals(os, "android")) {
                        log.error("vivoclick 接收失败 异常的os {} {} {} listSize{} {}", dsp, os, product, listSize, JSON.toJSONString(reqBody));
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());

        if (reqBodyList.size() > 0) {
            log.info("vivoclick 接收成功 {} {} {} listSize{} {}", dsp, os, product, listSize, JSON.toJSONString(reqBodyList));
        }

        for (VivoClickReqBody reqBody : reqBodyList) {
            ToutiaoClick toutiaoClick = new ToutiaoClick();
            toutiaoClick.setDsp(dsp);
            toutiaoClick.setProduct(product);
            toutiaoClick.setOs(os);
            toutiaoClick.setAccountId(reqBody.getAdvertiserId());
            toutiaoClick.setAccountName(reqBody.getAdvertiserName());
            toutiaoClick.setOcpcDeviceId(reqBody.getImei());
            toutiaoClick.setOaid(reqBody.getOaid());
            toutiaoClick.setTs(reqBody.getClickTime());
            toutiaoClick.setCid(reqBody.getCreativeId());
            toutiaoClick.setGid(reqBody.getGroupId());
            toutiaoClick.setPid(reqBody.getAdId());
            Date date = new Date();
            toutiaoClick.setUpdateTime(date);
            toutiaoClick.setCreateTime(date);
            //新增渠道包参数
//            if(!"NULL".equals(baiduClickReq.getPkg_channel())){
//                toutiaoClick.setPkgChannel(baiduClickReq.getPkg_channel());
//            }
            try {
                toutiaoClickService.saveClick(toutiaoClick);
            } catch (Exception e) {
                log.error("save vivo click event error.", e);
                vivoResponse.setCode(-1);
            }
            redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                    toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
            hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                    toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-vivo");
            kafkaSender.sendClick(toutiaoClick);
            //将deviceId与caid存入redis缓存
            redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),os,toutiaoClick);
        }

        return vivoResponse;
    }

    @Data
    public static class VivoResponse {
        private Integer code = 0; //状态码：0-成功；-1 失败
        private String message;
    }

}
