package com.coohua.core.caf.dispense.cache;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/8/14 16:40
 */
@Component
@Slf4j
public class ClickCache {

	/**
	 * 缓存已处理过的key
	 */
	@Value("${call.processed.expire:6}")
	private int callBackProcessedExpire;

	/**
	 * 缓存已处理的用户 或设备
	 *
	 * @return
	 */
	@Bean(name = "callBackProcessedCache")
	public LoadingCache<String, Integer> initUserProcessedCache() {
		Caffeine<Object, Object> cacheBuilder = Caffeine.newBuilder().maximumSize(700000);
		cacheBuilder.expireAfterWrite(callBackProcessedExpire, TimeUnit.HOURS);
		// 写入后4小时过期
		LoadingCache<String, Integer> cache = cacheBuilder.recordStats().build(new CacheLoader<String, Integer>() {
			@Override
			public Integer load(@NonNull String key) {
				try {
					return 1;
				} catch (Exception e) {
					log.error("user createTime error ! key={}", key, e);
				}
				return -1;
			}
		});
		return cache;
	}
}
