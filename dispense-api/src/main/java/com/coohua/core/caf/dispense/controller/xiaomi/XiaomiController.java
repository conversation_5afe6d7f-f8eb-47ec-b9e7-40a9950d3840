package com.coohua.core.caf.dispense.controller.xiaomi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.OppoClickReq;
import com.coohua.core.caf.dispense.dto.req.XiaomiClickReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;

@RestController
@RequestMapping("/dispense/xiaomi")
@Slf4j
public class XiaomiController {
    @Autowired
    private ToutiaoClickService toutiaoClickService;

    @Autowired
    private AlertService alertService;

    @Autowired
    RedisClickService redisClickService;

    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    RedisRelationshipService redisRelationshipService;
    @GetMapping("/click")
    @ResponseBody
    public Object click(XiaomiClickReq req, HttpServletRequest request) {
        log.info("xiaomiClick {}", JSON.toJSONString(req));

        if (!Objects.equals(req.getDsp(), DspType.XIAOMI.value)) {
            log.error("xiaomiClick dsp非法 {}", JSON.toJSONString(req));
            return new JSONObject()
                    .fluentPut("code", 10001)
                    .fluentPut("failMsg", "illegal dsp")
                    .fluentPut("success", false);
        }

        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(req.getDsp());
        toutiaoClick.setProduct(req.getProduct());
        toutiaoClick.setOs(req.getOs());
        toutiaoClick.setOcpcDeviceId(req.getImei());
        toutiaoClick.setOaid(req.getOaid());
        toutiaoClick.setTs(req.getClick_time());
        toutiaoClick.setCid(req.getAdid());
        toutiaoClick.setPid(req.getCampaign_id());
        toutiaoClick.setAccountId(req.getCustomer_id());
        toutiaoClick.setCallbackUrl(req.getCallback());
        toutiaoClick.setAndroidId(req.getAndroidId());


        // 设置了该字段会进入渠道包相关逻辑，实际上oppo文档中标明该click事件下发的包名不可靠，故直接弃用
//        toutiaoClick.setPkgChannel(oppoClickReq.getPkg());
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-xiaomi");

        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);

        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        kafkaSender.sendClick(toutiaoClick);
        toutiaoClickService.saveClick(toutiaoClick);
        //将deviceId与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),req.getOs(),toutiaoClick);
        return new JSONObject()
                .fluentPut("code", 0)
                .fluentPut("failMsg", "")
                .fluentPut("success", true);
    }

}
