package com.coohua.core.caf.dispense.controller.ximalaya;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.XimalayaClickReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;

@RestController
@RequestMapping("/dispense/ximalaya")
@Slf4j
public class XimalayaController {
    @Autowired
    private ToutiaoClickService toutiaoClickService;

    @Autowired
    private AlertService alertService;

    @Autowired
    RedisClickService redisClickService;

    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    RedisRelationshipService redisRelationshipService;
    @GetMapping("/click")
    @ResponseBody
    public Object click(XimalayaClickReq req, HttpServletRequest request) {
        String originStr = JSON.toJSONString(req);
        // 喜马拉雅对于未取到的值会回传_IDFA_等，需要用null替换掉
        // {"accountId":"*********","androidid":"38aeb416aa85800c","callback_url":"https://ad.ximalaya.com/ad-action?uid=535430&timestamp=*************&ip=*************&os=android&imei_md5=_IMEI_MD5_&oaid=8bb6f4a477bb5950&androidid=38aeb416aa85800c&materialid=4110742&idfa=_IDFA_&type=act&invokeid=*********&responseid=*************","deviceId":"_IMEI_MD5_","dsp":"ximalaya","height":"2166","ip":"*************","materialId":"_MATERIAL_ID_","material_name":"_MATERIAL_NAME_","model":"M2101K9C","oaid":"8bb6f4a477bb5950","os":"android","planId":"_PLAN_ID_","plan_name":"_PLAN_NAME_","product":"xfyzc2","taskId":"_TASK_ID_","task_name":"_TASK_NAME_","timestamp":"*************","wh":"0.49","width":"1080"}
        req.formatNull();
        log.info("ximalayaClick 原始值:{} 处理后值:{}", originStr, JSON.toJSONString(req));

        if (!Objects.equals(req.getDsp(), DspType.XIMALAYA.value)) {
            log.error("ximalayaClick dsp非法 {}", JSON.toJSONString(req));
            return new JSONObject()
                    .fluentPut("code", 10001)
                    .fluentPut("failMsg", "illegal dsp")
                    .fluentPut("success", false);
        }

        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(req.getDsp());
        toutiaoClick.setProduct(req.getProduct());
        toutiaoClick.setOs(req.getOs());
        toutiaoClick.setAccountId(req.getAccountId());

        toutiaoClick.setOcpcDeviceId(req.getDeviceId());
        toutiaoClick.setOaid(req.getOaid());
        toutiaoClick.setAndroidId(req.getAndroidid());
        toutiaoClick.setTs(req.getTimestamp());
        toutiaoClick.setGid(req.getTaskId());
        toutiaoClick.setPid(req.getPlanId());
        toutiaoClick.setCid(req.getMaterialId());
        toutiaoClick.setCallbackUrl(req.getCallback_url());


        // 设置了该字段会进入渠道包相关逻辑，实际上oppo文档中标明该click事件下发的包名不可靠，故直接弃用
//        toutiaoClick.setPkgChannel(oppoClickReq.getPkg());
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-ximalaya");

        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);

        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        kafkaSender.sendClick(toutiaoClick);
        toutiaoClickService.saveClick(toutiaoClick);
        //将deviceId与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),req.getOs(),toutiaoClick);
        return new JSONObject()
                .fluentPut("code", 0)
                .fluentPut("failMsg", "")
                .fluentPut("success", true);
    }

}
