package com.coohua.core.caf.dispense.controller.kuaishou;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.dsp.service.*;
import com.coohua.core.caf.dispense.dto.CaidMappingDto;
import com.coohua.core.caf.dispense.dto.req.KuaishouClickReq;
import com.coohua.core.caf.dispense.dto.req.ToutiaoClickReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.enums.CaidVersionEnum;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.ocpc.service.CaidService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.github.benmanes.caffeine.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

/**
 *
 **/
@RestController
@RequestMapping("/dispense/kuaishou")
@Slf4j
public class KuaishouClickController {

    public static String caidVersion = "20220111";
    public static String caidVersion2 = "20230330";
    public static String caidVersion3 = "20250325";

    @Resource(name = "callBackProcessedCache")
    LoadingCache<String, Integer> callBackCache;
    @Value(value= "${cache.count.enable:true}")
    private Boolean cacheCountEnable;
    @Value(value= "${ocpc.log.kuaishou.oaid2:true}")
    private Boolean logOaid2;

    @Autowired
    RedisClickService redisClickService;
    @Autowired
    ToutiaoClickService toutiaoClickService;
    @Autowired
    KuaishouClickService kuaishouClickService;
    @Autowired
    private AlertService alertService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    private static AtomicLong logOaid2Count = new AtomicLong(0);

    @Autowired
    ConstApolloConfig constApolloConfig;
    @Autowired
    RedisRelationshipService redisRelationshipService;

    @GetMapping("/click")
    @ResponseBody
    public ReturnResult click(KuaishouClickReq kuaishouClickReq, HttpServletRequest request) {
//        toutiaoClickService.checkLength(DspType.KUAISHOU, kuaishouClickReq);

        ReturnResult returnResult = new ReturnResult();
        boolean isF = false;
        if (StringUtils.isNotBlank(kuaishouClickReq.getImeiMd5()) ||
                StringUtils.isNotBlank(kuaishouClickReq.getOaid()) ||
                StringUtils.isNotBlank(kuaishouClickReq.getIdfaMd5())||
                StringUtils.isNotBlank(kuaishouClickReq.getCaid())||
                StringUtils.isNotBlank(kuaishouClickReq.getMac()) ||
                StringUtils.isNotBlank(kuaishouClickReq.getUa()) ||
                StringUtils.isNotBlank(kuaishouClickReq.getIp()) ||
                StringUtils.isNotBlank(kuaishouClickReq.getModel())
        ) {
            ToutiaoClick toutiaoClick = new ToutiaoClick();
            toutiaoClick.setDsp(kuaishouClickReq.getDsp());
            toutiaoClick.setProduct(kuaishouClickReq.getProduct());
            toutiaoClick.setOs(kuaishouClickReq.getOs());
            toutiaoClick.setAccountId(StringUtils.isNotBlank(kuaishouClickReq.getAccount_id()) ? kuaishouClickReq.getAccount_id() : kuaishouClickReq.getAccount());
            if(StringUtils.isNotBlank(kuaishouClickReq.getImeiMd5())){
                toutiaoClick.setOcpcDeviceId(kuaishouClickReq.getImeiMd5().toLowerCase());
            }else if(StringUtils.isNotBlank(kuaishouClickReq.getIdfaMd5())){
                toutiaoClick.setOcpcDeviceId(kuaishouClickReq.getIdfaMd5().toLowerCase());
            }

            toutiaoClick.setTs(kuaishouClickReq.getTs());
            toutiaoClick.setCid(kuaishouClickReq.getCid());
            if ("1".equals(kuaishouClickReq.getCid())){
                if ("1".equals(kuaishouClickReq.getTaskType()) || "1".equals(kuaishouClickReq.getTask_type())) {
                    // log.info("快手视频任务点击存储 taskType {}", kuaishouClickReq.getTaskType());
                    toutiaoClick.setCid("video");
                }else if ("2".equals(kuaishouClickReq.getTaskType()) || "2".equals(kuaishouClickReq.getTask_type())) {
                    // log.info("快手直播任务点击存储 taskType {}", kuaishouClickReq.getTaskType());
                    toutiaoClick.setCid("live");
                }
            }
            toutiaoClick.setGid(kuaishouClickReq.getAid());
            toutiaoClick.setPid(kuaishouClickReq.getDid());
            toutiaoClick.setAidName(kuaishouClickReq.getAidName());
            toutiaoClick.setOaid(kuaishouClickReq.getOaid());
            toutiaoClick.setOaid2(kuaishouClickReq.getOaid2());
            toutiaoClick.setMid(kuaishouClickReq.getMid());
            toutiaoClick.setCallbackUrl(kuaishouClickReq.getCallbackUrl());
            toutiaoClick.setCsite(kuaishouClickReq.getCsite());
            toutiaoClick.setRequestParam(JSONObject.toJSONString(kuaishouClickReq));
            toutiaoClick.setClientIp(request.getRemoteAddr());
            Date date = new Date();
            toutiaoClick.setUpdateTime(date);
            toutiaoClick.setCreateTime(date);
            /*if(StringUtils.isNotBlank(kuaishouClickReq.getMid())) {
                log.info("kuaishou click mid, {}", JSONObject.toJSON(kuaishouClickReq));
            }*/
            if(ocpcSwitcher.ksipua){
                if (kuaishouClickReq.getIp() != null && kuaishouClickReq.getIp().length() <= BaseConstants.IpMaxLength) {
                    toutiaoClick.setIp(kuaishouClickReq.getIp());
                }
                if (StringUtils.isNotBlank(kuaishouClickReq.getUa())) {
                    toutiaoClick.setUa(MD5Utils.getMd5Sum(kuaishouClickReq.getUa()));
                }
                if (kuaishouClickReq.getModel() != null && kuaishouClickReq.getModel().length() <= BaseConstants.ModelMaxLength) {
                    toutiaoClick.setModel(kuaishouClickReq.getModel());
                }

                toutiaoClick.setMac(kuaishouClickReq.getMac());
                toutiaoClick.setAndroidId(kuaishouClickReq.getAndroidId() != null ? kuaishouClickReq.getAndroidId() : "");
            }
            if("ios".equalsIgnoreCase(kuaishouClickReq.getOs())){
                try {

                    if(StringUtils.isNotBlank(kuaishouClickReq.getCaid())){
//                        log.info("kscaid "+JSON.toJSONString(kuaishouClickReq));
                        String ksCaid = CaidService.getKSCaid(kuaishouClickReq.getCaid(), caidVersion);
                        if (ksCaid == null || StringUtils.isBlank(ksCaid)) {
                            ksCaid = CaidService.getKSCaid(kuaishouClickReq.getCaid(), caidVersion2);
                            // 将caid与idfa的映射通过Kafka发送
                            if (kuaishouClickReq.getIdfaMd5() != null && StringUtils.isNotBlank(kuaishouClickReq.getIdfaMd5())) {
                                CaidMappingDto caidMappingDto = new CaidMappingDto();
                                caidMappingDto.setCaid(ksCaid);
                                caidMappingDto.setCaidType(CaidVersionEnum.CAID_20230330.getCode());
                                caidMappingDto.setIdfa(kuaishouClickReq.getIdfaMd5());
                                caidMappingDto.setSource("click_event");
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                String dateFormat = sdf.format(new Date());
                                caidMappingDto.setCreateTime(dateFormat);
                                kafkaSender.sendCaidMapping(JSONObject.toJSONString(caidMappingDto));
                            }
                            toutiaoClick.setCaid(ksCaid);
                            toutiaoClick.setCaid2(CaidService.getKSCaid(kuaishouClickReq.getCaid(),caidVersion3));
                        }else {
                            toutiaoClick.setCaid2(CaidService.getKSCaid(kuaishouClickReq.getCaid(),caidVersion2));
                            if (kuaishouClickReq.getIdfaMd5() != null && StringUtils.isNotBlank(kuaishouClickReq.getIdfaMd5())) {
                                CaidMappingDto caidMappingDto = new CaidMappingDto();
                                caidMappingDto.setCaid(CaidService.getKSCaid(kuaishouClickReq.getCaid(),caidVersion2));
                                caidMappingDto.setCaidType(CaidVersionEnum.CAID_20230330.getCode());
                                caidMappingDto.setIdfa(kuaishouClickReq.getIdfaMd5());
                                caidMappingDto.setSource("click_event");
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                String dateFormat = sdf.format(new Date());
                                caidMappingDto.setCreateTime(dateFormat);
                                kafkaSender.sendCaidMapping(JSONObject.toJSONString(caidMappingDto));
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("toutiao ua", e);
                }
            }

            if (logOaid2 && StringUtils.isNotBlank(toutiaoClick.getOaid2()) && logOaid2Count.getAndIncrement()%1000 == 0) {
                log.info("快手click事件 {}", JSON.toJSONString(toutiaoClick));
            }

            if(checkCacheCount(toutiaoClick) && cacheCountEnable){
                return returnResult;
            }
            //快手逻辑
            if(!ocpcSwitcher.ksHbaseGy){
                redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                        toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
            }
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-快手");
            hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                    toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);
            //快手如果存在另外一个版本的caid则也进行存储
            if (StringUtils.isNotBlank(toutiaoClick.getCaid2())) {
                hbaseClickService.saveHbaseClick4Ks(toutiaoClick.getProduct(),toutiaoClick.getCaid2(),toutiaoClick);
            }
            kafkaSender.sendClick(toutiaoClick);
            //将deviceId与caid存入redis缓存
            redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(), kuaishouClickReq.getOs(),toutiaoClick);
            try {
                toutiaoClickService.saveClick(toutiaoClick);
            } catch (Exception e) {
                log.error("保存快手点击事件异常 " + JSON.toJSONString(toutiaoClick), e);
            }
        }else{
            log.info("ksclickskip "+JSON.toJSONString(kuaishouClickReq));
        }

        return returnResult;
    }

    private boolean checkCacheCount(ToutiaoClick toutiaoClick) {
        try {
            String key = DigestUtils.md5Hex(toutiaoClick.getCallbackUrl());
            Integer cacheCount = callBackCache.getIfPresent(key);
            if (cacheCount != null && cacheCount != -1) {
                log.info("click事件已存在:{}", toutiaoClick);
                return true;
            }
            callBackCache.put(key, 1);
        }catch (Exception e){
            log.error(" saveCache count error" ,e);
        }
        return false;

    }


    private boolean isValidReq(ToutiaoClickReq toutiaoClickReq) {
        boolean isVal = true;
        if ("__CALLBACK_URL__".equals(toutiaoClickReq.getCallback_url())) {
            isVal = false;
        }
        if (StringUtils.isEmpty(toutiaoClickReq.getOcpc_device_id()) && StringUtils.isEmpty(toutiaoClickReq.getOaid())) {
            isVal = false;
        }
        return isVal;
    }
}
