package com.coohua.core.caf.dispense.controller.bytemin;

import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.ocpc.entity.ByteMinconf;
import com.coohua.core.caf.dispense.ocpc.service.ByteMinconfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("/dispense/bytemin")
@Slf4j
public class ByteMinConfController {
    @Autowired
    ByteMinconfService byteMinconfService;
    @RequestMapping(value = "/minconf")
    @ResponseBody
    public ReturnResult minconf(ByteMinconf byteMinconf, HttpServletRequest request) {
        byteMinconfService.addUpdMinconf(byteMinconf);
        return new ReturnResult();
    }
}
