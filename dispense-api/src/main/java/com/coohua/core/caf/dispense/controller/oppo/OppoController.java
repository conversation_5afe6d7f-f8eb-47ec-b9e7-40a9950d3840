package com.coohua.core.caf.dispense.controller.oppo;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.OppoClickReq;
import com.coohua.core.caf.dispense.dto.req.OppoStoreClickReq;
import com.coohua.core.caf.dispense.dto.resp.ReturnResult;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;

@RestController
@RequestMapping("/dispense/oppo")
@Slf4j
public class OppoController {
    @Autowired
    private ToutiaoClickService toutiaoClickService;

    @Autowired
    private AlertService alertService;

    @Autowired
    RedisClickService redisClickService;

    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    RedisRelationshipService redisRelationshipService;
    // https://ocpc-api.shinet.cn/dispense/oppo/click?dsp=oppo&product=wsbzp&account_id=********&os=$os$&os_version=$os_version$&pkg=$pkg$&imei=$imei$&oaid=$OAID$&ad_id=$ad_id$&ad_name=$ad_name$&req_id=$req_id$
    @GetMapping("/click")
    @ResponseBody
    public ReturnResult click(OppoClickReq oppoClickReq, HttpServletRequest request) {
        log.info("oppoClick {}", JSON.toJSONString(oppoClickReq));
        Long ts = System.currentTimeMillis();

        if (!Objects.equals(oppoClickReq.getDsp(), DspType.OPPO.value)) {
            log.error("oppoClick dsp非法 {}", JSON.toJSONString(oppoClickReq));
            return new ReturnResult();
        }

        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(oppoClickReq.getDsp());
        toutiaoClick.setProduct(oppoClickReq.getProduct());
        toutiaoClick.setOs(oppoClickReq.getOs());
        toutiaoClick.setAccountId(oppoClickReq.getAccount_id());
        toutiaoClick.setPid(oppoClickReq.getAd_id());
        toutiaoClick.setOcpcDeviceId(oppoClickReq.getImei());
        toutiaoClick.setTs(ts.toString());
        toutiaoClick.setOaid(oppoClickReq.getOaid());
        toutiaoClick.setAccountName(oppoClickReq.getAd_name());
        // 设置了该字段会进入渠道包相关逻辑，实际上oppo文档中标明该click事件下发的包名不可靠，故直接弃用
//        toutiaoClick.setPkgChannel(oppoClickReq.getPkg());
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-oppo");

        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);

        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        kafkaSender.sendClick(toutiaoClick);
        toutiaoClickService.saveClick(toutiaoClick);
        //将deviceId与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),oppoClickReq.getOs(),toutiaoClick);
        return new ReturnResult();
    }

    // https://ocpc-api.shinet.cn/dispense/oppo/storeClick?dsp=oppo&product=wsbzp&account_id=********&os=android&imei=__IMEI__&oaid=__OAID__&ad_id=__ADID__&ts=__TS__&androidid=__ANDROIDID__
    @GetMapping("/storeClick")
    @ResponseBody
    public ReturnResult storeClick(OppoStoreClickReq clickReq, HttpServletRequest request) {
        log.info("oppoStoreClick {}", JSON.toJSONString(clickReq));
        Long ts = System.currentTimeMillis();

        if (!Objects.equals(clickReq.getDsp(), DspType.OPPO.value)) {
            log.error("oppoStoreClick dsp非法 {}", JSON.toJSONString(clickReq));
            return new ReturnResult();
        }

        if (clickReq.getImei() != null && clickReq.getImei().startsWith("__")) {
            clickReq.setImei("");
        }

        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(clickReq.getDsp());
        toutiaoClick.setProduct(clickReq.getProduct());
        toutiaoClick.setOs(clickReq.getOs());
        toutiaoClick.setAccountId(clickReq.getAccount_id());
        toutiaoClick.setPid(clickReq.getAd_id());
        toutiaoClick.setOcpcDeviceId(clickReq.getImei());
        toutiaoClick.setOaid(clickReq.getOaid());
        toutiaoClick.setTs(clickReq.getTs());
        toutiaoClick.setAndroidId(clickReq.getAndroidid());
        // 设置了该字段会进入渠道包相关逻辑，实际上oppo文档中标明该click事件下发的包名不可靠，故直接弃用
//        toutiaoClick.setPkgChannel(oppoClickReq.getPkg());
        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "点击监测-oppo");

        Date date = new Date();
        toutiaoClick.setUpdateTime(date);
        toutiaoClick.setCreateTime(date);
        toutiaoClick.setClientIp(request.getRemoteAddr());
        toutiaoClick.setRequestParam(JSON.toJSONString(clickReq));
        redisClickService.saveRedisClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        hbaseClickService.saveHbaseClick(toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId(),
                toutiaoClick.getOaid(), toutiaoClick.getMac(), toutiaoClick);

        kafkaSender.sendClick(toutiaoClick);
        toutiaoClickService.saveClick(toutiaoClick);
        //将deviceId与caid存入redis缓存
        redisRelationshipService.saveIdfaCaid(toutiaoClick.getIdfa2(), toutiaoClick.getCaid(),clickReq.getOs(),toutiaoClick);
        return new ReturnResult();
    }

}
