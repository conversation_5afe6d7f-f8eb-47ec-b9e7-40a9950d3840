spring.datasource.driverClass = com.mysql.cj.jdbc.Driver
spring.datasource.url = *************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.datasource.username = dispenseus
spring.datasource.password = yI2gbNL1PneDLscj
spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.maxActive = 200
spring.datasource.initialSize = 5
spring.datasource.minIdle = 30
spring.datasource.maxWait = 10000
spring.datasource.timeBetween = 60000
spring.datasource.minEvictableIdle = 300000
spring.datasource.validationQuery = SELECT 'x'
spring.datasource.testWhileIdle = true
spring.datasource.keepAlive = true
spring.datasource.testOnBorrow = true
spring.datasource.testOnReturn = true
spring.datasource.poolPreparedStatements = true
spring.datasource.maxPoolPreparedStatementPerConnectionSize = 20
spring.datasource.removeAbandoned = false
spring.datasource.removeAbandonedTime = 6000
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl

ocpc.spring.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
ocpc.spring.datasource.username = dispenseus
ocpc.spring.datasource.password = yI2gbNL1PneDLscj

rta.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
rta.datasource.username = coohua
rta.datasource.password = Q9lMQNnkCUw7

#×î´ó¹¤×÷Ïß³ÌÊý£¬Ä¬ÈÏ200¡£
server.tomcat.max-threads=500
#×î´óÁ¬½ÓÊýÄ¬ÈÏÊÇ10000
server.tomcat.max-connections=10000
#µÈ´ý¶ÓÁÐ³¤¶È£¬Ä¬ÈÏ100¡£
server.tomcat.accept-count=100
#×îÐ¡¹¤×÷¿ÕÏÐÏß³ÌÊý£¬Ä¬ÈÏ10¡£
server.tomcat.min-spare-threads=100


spring.redis2.cluster.nodes=172.16.51.165:9720,172.16.51.166:9720,172.16.51.167:9720,172.16.51.168:9720,172.16.51.169:9720
spring.redis2.lettuce.pool.max-active = 100
spring.redis2.timeout = 2000