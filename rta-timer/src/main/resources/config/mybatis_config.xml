<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//ibatis.apache.org//DTD Config 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-config.dtd">
<configuration>
    <settings>
        <!-- 全局映射器启用缓存 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 查询时，关闭关联对象即时加载以提高性能 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 设置关联对象加载的形态，此处为按需加载字段(加载字段由SQL指 定)，不会加载关联表的所有字段，以提高性能 -->
        <setting name="aggressiveLazyLoading" value="false"/>
        <!-- 对于未知的SQL查询，允许返回不同的结果集以达到通用的效果 -->
        <setting name="multipleResultSetsEnabled" value="true"/>
        <!-- 允许使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true"/>
        <!-- 允许使用自定义的主键值(比如由程序生成的UUID 32位编码作为键值)，数据表的PK生成策略将被覆盖 -->
        <setting name="useGeneratedKeys" value="true"/>
        <!-- 给予被嵌套的resultMap以字段-属性的映射支持 -->
        <setting name="autoMappingBehavior" value="FULL"/>
        <!-- 配置默认的执行器，SIMPLE 执行器没有什么特别之处，REUSE 执行器重用预处理语句，BATCH 执行器重用语句和批量更新 -->
        <setting name="defaultExecutorType" value="REUSE"/>
        <!-- 数据库超过3秒仍未响应则超时 -->
        <setting name="defaultStatementTimeout" value="3600"/>
        <!-- 是否启用下划线与驼峰式命名规则的映射（如first_name => firstName） -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!--&lt;!&ndash; 设置增强的SQL语言驱动，替换默认的驱动  &ndash;&gt;-->
        <!--<setting name="defaultScriptingLanguage" value="com.hbc.fastsql.extend.ExtendLanguageDriver"/>-->
    </settings>
</configuration>