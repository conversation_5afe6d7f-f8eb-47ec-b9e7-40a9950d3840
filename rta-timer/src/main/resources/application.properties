spring.datasource.driverClass = com.mysql.cj.jdbc.Driver
spring.datasource.url = *************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.datasource.username = dispenseus
spring.datasource.password = yI2gbNL1PneDLscj
spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.maxActive = 200
spring.datasource.initialSize = 5
spring.datasource.minIdle = 30
spring.datasource.maxWait = 10000
spring.datasource.timeBetween = 60000
spring.datasource.minEvictableIdle = 300000
spring.datasource.validationQuery = SELECT 'x'
spring.datasource.testWhileIdle = true
spring.datasource.keepAlive = true
spring.datasource.testOnBorrow = true
spring.datasource.testOnReturn = true
spring.datasource.poolPreparedStatements = true
spring.datasource.maxPoolPreparedStatementPerConnectionSize = 20
spring.datasource.removeAbandoned = false
spring.datasource.removeAbandonedTime = 6000
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl

ocpc.spring.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
ocpc.spring.datasource.username = dispenseus
ocpc.spring.datasource.password = yI2gbNL1PneDLscj

rta.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
rta.datasource.username = coohua
rta.datasource.password = b3HPYoZ!7#D


xxl.job.admin.addresses = http://*************:8080/xxl-job-admin
xxl.job.executor.appname = rta-timer
xxl.job.executor.logpath = /data/logs/xxl-job/rta-timer
xxl.job.executor.logretentiondays = 3
xxl.job.executor.port = -1