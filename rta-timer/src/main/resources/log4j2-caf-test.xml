<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xi="http://www.w3.org/2001/XInclude">

    <Properties>
        <Property name="LOG_HOME">${sys:app.logging.path}</Property>
        <Property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] [%level] [%X{column1}-%X{column2}] [%c{1.}] - %msg%xEx%n</Property>
        <Property name="PERF_PATTERN">%d{HH:mm:ss} [%X{column1}-%X{column2}] - %msg%xEx%n</Property>
        <Property name="OUTPUT_LOG_LEVEL">${sys:app.logging.level}</Property>
        <Property name="EVERY_FILE_SIZE">200 MB</Property>
    </Properties>

    <DynamicThresholdFilter key="isDebug" defaultThreshold="ERROR"
                            onMatch="ACCEPT" onMismatch="NEUTRAL">
        <KeyValuePair key="true" value="DEBUG"/>
    </DynamicThresholdFilter>

    <xi:include href="log4j2-appenders-loghub-test.xml" />

    <loggers>
        <Logger name="com.shinet" level="${OUTPUT_LOG_LEVEL}" additivity="false">
            <AppenderRef ref="STDOUT"/>
            <AppenderRef ref="appAppender"/>
            <AppenderRef ref="appLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </Logger>
        <Logger name="com.coohua.caf" level="${OUTPUT_LOG_LEVEL}" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="frameworkAppender"/>
            <AppenderRef ref="frameworkLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </Logger>
        <Logger name="framework" level="${OUTPUT_LOG_LEVEL}" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="frameworkAppender"/>
            <AppenderRef ref="frameworkLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </Logger>
        <Logger name="trace" level="${OUTPUT_LOG_LEVEL}" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="traceAppender"/>
            <AppenderRef ref="traceLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </Logger>
        <Logger name="error" level="${OUTPUT_LOG_LEVEL}" additivity="false">
            <AppenderRef ref="errorAppender" />
        </Logger>
        <Logger name="access" level="info" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="accessAppender" />
            <AppenderRef ref="accessLogHubAppender" />
            <AppenderRef ref="errorAppender" />
        </Logger>
        <logger name="performance"  level="info" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="performanceAppender" />
            <AppenderRef ref="errorAppender" />
        </logger>
        <!--<logger name="serviceStatsLog"  level="info" additivity="false">-->
        <!--<AppenderRef ref="STDOUT"/>-->
        <!--<AppenderRef ref="performanceAppender" />-->
        <!--<AppenderRef ref="errorAppender" />-->
        <!--</logger>-->
        <logger name="com.ctrip.framework.apollo"  level="info" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="traceAppender"/>
            <AppenderRef ref="traceLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </logger>
        <logger name="com.alibaba.druid.filter.stat.StatFilter"  level="info" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="performanceAppender" />
            <AppenderRef ref="errorAppender" />
        </logger>
        <logger name="druid.sql.Connection"  level="info" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="traceAppender"/>
            <AppenderRef ref="traceLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </logger>
        <logger name="druid.sql.DataSource"  level="info" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="traceAppender"/>
            <AppenderRef ref="traceLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </logger>
        <logger name="com.ctrip.framework.apollo"  level="debug" additivity="false">
<!--            <AppenderRef ref="STDOUT"/>-->
            <AppenderRef ref="traceAppender"/>
            <AppenderRef ref="traceLogHubAppender"/>
            <AppenderRef ref="errorAppender" />
        </logger>
        <ROOT level="${OUTPUT_LOG_LEVEL}" additivity="true">
            <AppenderRef ref="STDOUT"/>
            <AppenderRef ref="traceAppender"/>
            <AppenderRef ref="traceLogHubAppender"/>
            <AppenderRef ref="errorAppender"/>
        </ROOT>
    </loggers>
</configuration>