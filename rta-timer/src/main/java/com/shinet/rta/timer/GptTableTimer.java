package com.shinet.rta.timer;

import com.coohua.rta.service.RedisLockService;
import com.coohua.rta.service.RtaTableService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;

@Component
@Slf4j
public class GptTableTimer {
    @Autowired
    com.coohua.rta.service.RtaTableService rtaTableService;

    @Autowired
    @Qualifier("rta-redisJedisClusterClient")
    private JedisClusterClient rtaRedis;

    @Scheduled(cron = "0 58 23 * * ?")
    public void renameTables(){
        if (RedisLockService.tryGetDistributedLock(rtaRedis,"rta.lock1", "haha", DateTimeConstants.MILLIS_PER_MINUTE * 5)) {
            //gpt_chat_info
            rtaTableService.renameTable(RtaTableService.rtaSubUpTableName);

            rtaTableService.renameTable(RtaTableService.rtaUpTableName);

            rtaTableService.renameTable(RtaTableService.rtaDevice);

        }
    }
}
