package com.shinet.rta.timer;

import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.rta.redis.RtaRedisService;
import com.coohua.rta.service.AliOssService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2023/6/7 11:12
 * @description
 */
@Component
@Slf4j
public class RtaArpuDataTimer {
    @Autowired
    RtaRedisService rtaRedisService;
    @XxlJob("rtaDataToReidsTimer")
    public ReturnT<?> rtaDataToReidsTimer(String param) {
        //oss 拉取源数据
//        rtaRedisService.fileToRedis();
        return ReturnT.SUCCESS;
    }

    @XxlJob("rtaDataOssTimer")
    public ReturnT<?> rtaDataOssTimer(String param) {
        //oss 拉取源数据
       Integer rnum = 3;
        if(StringUtils.isNotBlank(param)){
            rnum = Integer.parseInt(param);
        }
        try {
            File file = new File(AliOssService.getFolder());
            FileUtils.deleteDirectory(file);

            log.info("删除文件成功 "+file.getAbsolutePath());
            XxlJobLogger.log("删除文件成功 "+file.getAbsolutePath());
        }catch (Exception e){
            log.error("",e);
        }

        for(int i=0;i<rnum;i++){
            String dateStr = DateUtils.formatDateForYMDSTR(new Date(System.currentTimeMillis()-i* DateTimeConstants.MILLIS_PER_DAY));
            AliOssService.downloadFile(dateStr);
            XxlJobLogger.log("开始下载文件到本地 "+dateStr);
        }

        for(int i=0;i<rnum;i++){
            String dateStr = DateUtils.formatDateForYMDSTR(new Date(System.currentTimeMillis()-i* DateTimeConstants.MILLIS_PER_DAY));
            XxlJobLogger.log("开始读取文件到redis "+dateStr);
            rtaRedisService.fileToRedis(dateStr);
        }
        return ReturnT.SUCCESS;
    }
}