package com.shinet.rta.consumer;

import com.coohua.rta.service.ocpc.RtaUserArpuService;
import com.pepper.metrics.integration.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @since 2021/6/4
 */
@Slf4j
@Component
public class AdEcpmKafkaConsumer implements InitializingBean {

    // 启动参数 -Dkafka_open=false
    @Value("${kafka_open:false}")
    private boolean isStartKafka;

    @Autowired
    private RtaUserArpuService rtaUserArpuService;

    private AtomicLong count = new AtomicLong(0);

    private static final AtomicInteger threadId = new AtomicInteger();

    public static final ExecutorService POOL = Executors.newFixedThreadPool(5);

    public static final ExecutorService EXECUTOR = new ThreadPoolExecutor(
            1,
            1,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1000),
            r -> new Thread(r,"work-" + threadId.getAndIncrement()),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    public void subscribe() {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "172.16.200.5:9092,172.16.200.6:9092,172.16.200.7:9092");
        //默认值为30000ms，可根据自己业务场景调整此值，建议取值不要太小，防止在超时时间内没有发送心跳导致消费者再均衡。
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 25000);
        //每次poll的最大数量。
        //注意该值不要改得太大，如果poll太多数据，而不能在下次poll之前消费完，则会触发一次负载均衡，产生卡顿。
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 20000);
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024 * 1024 * 2);
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 1000);
        //消息的反序列化方式。
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        //当前消费实例所属的Consumer Group，请在控制台创建后填写。
        //属于同一个Consumer Group的消费实例，会负载消费消息。
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "gid_rta_ad");
        // 关闭自动提交
//        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 500);
        //构造消息对象，即生成一个消费实例。
        ThreadUtil.addThreadPool("default", EXECUTOR);

        for (int i = 0; i < 5; i++) {
            POOL.submit(() -> processConsume(props));
        }
    }

    private ThreadPoolExecutor getPool(int poolNum) {
        return new ThreadPoolExecutor(poolNum, poolNum,
                20L, TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(3000),
                new ThreadPoolExecutor.DiscardOldestPolicy());
    }

    private void processConsume(Properties props) {
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        //设置Consumer Group订阅的Topic，可订阅多个Topic。如果GROUP_ID_CONFIG相同，那建议订阅的Topic设置也相同。
        List<String> subscribedTopics = new ArrayList<>();
        //每个Topic需要先在控制台进行创建。
        subscribedTopics.add("bp_ad_kafka");
        consumer.subscribe(subscribedTopics);
        List<String> flist = new ArrayList<>();
        //循环消费消息。
        while (true){
            try {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(1));
                //必须在下次poll之前消费完这些数据, 且总耗时不得超过SESSION_TIMEOUT_MS_CONFIG 的值。
                //建议开一个单独的线程池来消费消息，然后异步返回结果。
                if (records.count() > 0) {
                    for (ConsumerRecord<String, String> record : records) {
                        flist.add(record.value());
                    }
                    if (flist.size() > 2000){
                        psToCk(flist);
                        count.getAndAdd(flist.size());
                        flist = new ArrayList<>();
                    }
                    consumer.commitAsync();
                }
            } catch (Exception e) {
                try {
                    consumer.commitSync();
                } catch (Throwable ignore) {
                    log.error("", e);
                }
                //更多报错信息，参见常见问题文档。
                log.error("", e);
            }
        }
    }

    private void psToCk(List<String> flist){
        rtaUserArpuService.saveBatch(flist,true);
    }

    @Override
    public void afterPropertiesSet() {
        if (isStartKafka) {
            log.info("Init Kafka Consumer...");
            new Thread(new Runnable() {
                long oldValue = 0L;

                @Override
                public void run() {
                    while (true) {
                        try {
                            long newValue = count.longValue();
                            long consume = newValue - this.oldValue;
                            if (consume > 0) {
                                log.info("RTA-AD KafkaDataConsumer: MSG.SEC={}", consume);
                            }
                            this.oldValue = newValue;
                            TimeUnit.SECONDS.sleep(1);
                        } catch (InterruptedException e) {
                            log.error("", e);
                        }
                    }
                }
            }).start();

            new Thread(this::subscribe).start();
            log.info("Started Kafka Consumer...");
        }
    }
}
