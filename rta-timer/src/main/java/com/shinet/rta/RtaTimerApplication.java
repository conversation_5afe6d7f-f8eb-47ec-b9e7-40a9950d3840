package com.shinet.rta;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.core.caf.dispense.rta.entity.UserArpuEntity;
import com.coohua.rta.service.ocpc.RtaEventGuiyService;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.shinet.rta.config.Log4j2ContextInitializerOwn;
import com.shinet.rta.timer.RtaArpuDataTimer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.httpclient.EnableHttpBioClient;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;

@SpringBootApplication(scanBasePackages = {"com.coohua.core.caf.dispense","com.coohua.rta","com.shinet.rta"})
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health","rta.switcher",
		// DB配置
		"bp.db.dispense",
		"bp.redis.dispense",
		"rta.redis"
})
@EnableJedisClusterClient(namespace = "bp-dispense")
@EnableJedisClusterClient(namespace = "rta-redis")
@EnableHttpBioClient
@EnableAutoChangeApolloConfig
@EnableScheduling
@EnableCaching
@EnableHttpBioClient
@Slf4j
public class RtaTimerApplication {

	public static void main(String[] args) {
		try {
			SpringApplication springApplication = new SpringApplication(RtaTimerApplication.class);
			springApplication.addInitializers(new Log4j2ContextInitializerOwn());
			ConfigurableApplicationContext context = springApplication.run();
			//**********
			//**********
			//**********
//			String[] dst = new String[]{
//					"**********"
//			};
//
//			for(String udi : dst){
//				UserArpuEntity userArpuEntity = new UserArpuEntity();
//				userArpuEntity.setProduct("xxqsc");
//				userArpuEntity.setOs("android");
//				userArpuEntity.setUserId(Long.parseLong(udi));
//				context.getBean(RtaEventGuiyService.class).sendRtaKeyEvent(userArpuEntity);
//			}
			log.info("启动成功");
		}catch (Exception e){
			log.error("",e);
		}
	}

}
