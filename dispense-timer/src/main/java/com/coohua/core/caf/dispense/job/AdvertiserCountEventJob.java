package com.coohua.core.caf.dispense.job;


import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.constant.TimerConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthKuaishouApp;
import com.coohua.core.caf.dispense.dsp.entity.CvrProduct;
import com.coohua.core.caf.dispense.dsp.service.AuthKuaishouAppService;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.CvrProductService;
import com.coohua.core.caf.dispense.dsp.service.TencentDeveloperService;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.ocpc.entity.DailyAdvertiserAccount;
import com.coohua.core.caf.dispense.ocpc.entity.ProductAdvertiserPool;
import com.coohua.core.caf.dispense.ocpc.service.DailyAdvertiserAccountService;
import com.coohua.core.caf.dispense.ocpc.service.ProductAdvertiserPoolService;
import com.coohua.core.caf.dispense.ocpc.service.UserExposureEcpmNewService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.IterateUtils;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AdvertiserCountEventJob implements InitializingBean {

    @Autowired
    private DailyAdvertiserAccountService dailyAdvertiserAccountService;
    @Autowired
    private ProductAdvertiserPoolService productAdvertiserPoolService;
    @Autowired
    private CvrProductService cvrProductService;
    @Resource(name = "queryCvrProductJobTaskPool")
    private ThreadPoolTaskExecutor executorQuery;
    @Autowired
    private UserExposureEcpmNewService userExposureEcpmNewService;
    @Autowired
    private OcpcSwitcher ocpcSwitcher;
    @Autowired
    private AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    private AuthKuaishouAppService authKuaishouAppService;
    @Autowired
    private TencentDeveloperService tencentDeveloperService;

    private static final Map<String, Integer> DSP_KEY_COUNT_LIMIT = ImmutableMap.of("toutiao", 40, "kuaishou", 20, "guangdiantong", 20);

    private static final Map<String, Consumer<List<ProductAdvertiserPool>>> DSP_QUERY_MAP = new HashMap<>();

    @Override
    public void afterPropertiesSet(){
        DSP_QUERY_MAP.put("toutiao", authToutiaoAdvertiserService::setEventValues);
        DSP_QUERY_MAP.put("kuaishou", authKuaishouAppService::setEventValues);
        DSP_QUERY_MAP.put("guangdiantong", tencentDeveloperService::setEventValues);
        refreshAdvertiserEventValues();
    }

    /**
     * 统计单个账户 1天 某个产品的激活和关健行为 数
     */
    @Scheduled(cron = "0 2/5 * * * ?")
    public void countDailyAccount() {
        if(SystemInfo.isTest()) {
            return;
        }
        try {
            Date today = DateUtils.getStartOfDate(new Date(System.currentTimeMillis() - TimerConstants.FIVE_MINUTE));
            countAndSaveDailyAccountByDate(today);
        /*for(int i = 3; i > 0 ; i--) {
            Date date = new Date(today.getTime() - i * TimerConstants.ONE_DAY);
            countAndSaveDailyAccountByDate(date);
        }*/
        }catch (Exception ex) {
            log.error("统计单个账户激活和关键行为 error", ex);
        }
    }

    private void countAndSaveDailyAccountByDate(Date today) {
        Date tomorrow = new Date(today.getTime() + TimerConstants.ONE_DAY);
        long time = System.currentTimeMillis();
        String todayStr = DateUtils.formatDate(today), tomorrowStr = DateUtils.formatDate(tomorrow);
        log.info("查询数据区间, today:{}, tomorrow:{}", todayStr, tomorrowStr);
        List<DailyAdvertiserAccount> list =  dailyAdvertiserAccountService.getBaseMapper().queryDailyCount(today, tomorrow);
        long time2 = System.currentTimeMillis();
        log.info("查询数据区间 end, today:{}, tomorrow:{}, size:{}, cost:{}", todayStr, tomorrowStr, list.size(), time2 - time);
        if(CollectionUtils.isNotEmpty(list)) {
            list.forEach(o -> o.setDataDate(today));
            IterateUtils.iterateByStepSize(list, 1000, data -> {
                String inSql = data.stream().map(k -> String.format("('%s','%s')", k.getProduct(), k.getAccountId())).collect(Collectors.joining(","));
                Map<String, DailyAdvertiserAccount> map = data.stream().collect(Collectors.toMap(o -> o.getProduct()+"_" + o.getAccountId(), o->o, (o1, o2) -> o1));
                //查询已经存在的内容
                List<DailyAdvertiserAccount> existsList = dailyAdvertiserAccountService.lambdaQuery().select(DailyAdvertiserAccount::getId, DailyAdvertiserAccount::getProduct, DailyAdvertiserAccount::getAccountId).eq(DailyAdvertiserAccount::getDataDate, today)
                        .last(" and (product, account_id) in (" + inSql + ")").list();
                if(CollectionUtils.isNotEmpty(existsList)) {
                    existsList.forEach(o -> {
                        String key = o.getProduct() + "_" + o.getAccountId();
                        DailyAdvertiserAccount query = map.get(key);
                        o.setKeyCount(query.getActiveCount());
                        o.setKeyCount(query.getKeyCount());
                        map.remove(key);
                    });
                    dailyAdvertiserAccountService.updateBatchById(existsList);
                }
                if(!map.isEmpty()) {
                    List<DailyAdvertiserAccount> insertList = new ArrayList<>(map.values());
                    dailyAdvertiserAccountService.saveBatch(insertList);
                }
            });
        }
        long time3 = System.currentTimeMillis();
        log.info("处理数据区间end, today:{}, tomorrow:{}, size:{}, cost:{}", todayStr, tomorrowStr, list.size(), time3 - time2);

        Date dayBefore = new Date(today.getTime() - 2 * TimerConstants.ONE_DAY);
        /**
         * 快手账户3天内>=20个转化，进入我们的策略
         * 头条账户3天内>=40个转化，进入我们的策略
         * 广点通账户3天内>=20个转化，进入我们的策略
         */
        List<ProductAdvertiserPool> accountList = productAdvertiserPoolService.getBaseMapper().queryKeyCountList(dayBefore);
        //头条过滤7的不 不用过滤，产品确认过
        List<ProductAdvertiserPool> validAccountList = accountList.stream().filter(o -> DSP_KEY_COUNT_LIMIT.containsKey(o.getDsp()) && o.getKeyCount() >= DSP_KEY_COUNT_LIMIT.get(o.getDsp())).collect(Collectors.toList());
        long time4 = System.currentTimeMillis();
        log.info("查询可结束学习期的账户, dayBefore:{}, size:{}, valid:{}, cost:{}", DateUtils.formatDate(dayBefore), accountList.size(), validAccountList.size(), time4 - time3);

        if(CollectionUtils.isNotEmpty(validAccountList)) {
            IterateUtils.iterateByStepSize(validAccountList, 1000, data -> {
                long saveTime = System.currentTimeMillis();
                String inSql = data.stream().map(k -> String.format("('%s','%s')", k.getProduct(), k.getAccountId())).collect(Collectors.joining(","));
                List<String> existsList = productAdvertiserPoolService.lambdaQuery().last(" where (product, account_id) in (" + inSql + ")").list().stream().map(k -> k.getProduct() + "_" + k.getAccountId()).collect(Collectors.toList());
                List<ProductAdvertiserPool> insertList = data.stream().filter(k -> !existsList.contains(k.getProduct() + "_" + k.getAccountId())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(insertList)) {
                    productAdvertiserPoolService.saveBatch(insertList);
                }
                log.info("插入可结束学习期的账户, size:{}, cost:{}", insertList.size(), System.currentTimeMillis() - saveTime);
            });
        }
    }

    @Scheduled(fixedDelay = 60000)
    public void refreshAdvertiserEventValues() {
        if(SystemInfo.isTest()) {
            return;
        }
        log.info("刷新cvr账户要求 start");
        List<ProductAdvertiserPool> list = productAdvertiserPoolService.lambdaQuery().list();
        try {
            for (String key : DSP_KEY_COUNT_LIMIT.keySet()) {
                List<ProductAdvertiserPool> dspList = list.stream().filter(o -> Objects.equals(key, o.getDsp())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dspList)) {
                    DSP_QUERY_MAP.get(key).accept(dspList);
                    IterateUtils.iterateByStepSize(dspList, 500, productAdvertiserPoolService::updateBatchById);
                }
            }
        }catch (Exception ex) {
            log.error("刷新cvr账户要求 error", ex);
        }
        log.info("刷新cvr账户要求 end");
    }

    @Scheduled(cron = "0 0/5 * * * ?")
    public void countCvr() {
        if(SystemInfo.isTest()) {
            return;
        }
        Date endTime = DateUtils.getVersionDate(LocalDateTime.now(), 5);
        Date startTime = new Date(endTime.getTime() - TimerConstants.FIVE_MINUTE);

        List<CvrProduct> productList = cvrProductService.getAll();

        log.info("需要查询统计cvr产品 start, 数量:{}", productList.size());
        List<CompletableFuture> taskList = new ArrayList<>();
        try {
            for (CvrProduct cvrProduct : productList) {
                taskList.add(CompletableFuture.runAsync(() -> {
                    userExposureEcpmNewService.dealProductOcpcEvent3(cvrProduct, startTime, endTime, TimerConstants.FIVE_MINUTE, ocpcSwitcher.cvrActiveHour);
                }, executorQuery));
            }
        }catch (Exception ex) {
            log.error("统计产品cvr error", ex);
        }
        CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        log.info("需要查询统计cvr产品 end");
    }

    //
    public void nxdxzRelease() {
        Date endTime = DateUtils.getVersionDate(LocalDateTime.now(), 5);
        Date startTime = new Date(endTime.getTime() - TimerConstants.FIVE_MINUTE);
        userExposureEcpmNewService.releaseByProduct("nxdxz", "toutiao", startTime, ocpcSwitcher.cvrActiveHour);
    }
}
