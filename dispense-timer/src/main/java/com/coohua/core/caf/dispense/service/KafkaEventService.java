package com.coohua.core.caf.dispense.service;

import com.coohua.core.caf.dispense.dsp.entity.EventEntity;
import com.coohua.core.caf.dispense.ocpc.service.UserExposureEcpmNewService;
import com.coohua.core.caf.dispense.utils.EventUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KafkaEventService {

    @Resource(name = "partitionJobTaskPool")
    private ThreadPoolTaskExecutor executor;
    @Autowired
    private UserExposureEcpmNewService userExposureEcpmNewService;

    private Set<String> AD_TYPE_SET;

    public void doProcess(List<String> eventStr){
        CompletableFuture.runAsync(()->{
            //1.DEVICE/USER为空 3.非MVP
            try {
                //存所有的看一下 仅存为视频的内容
                List<EventEntity> eventEntityList = EventUtils.filter(eventStr).parallelStream()
                        .filter(r -> "exposure".equals(r.getProperties().getAd_action())
                                && AD_TYPE_SET.contains(r.getProperties().getAd_type())
                                && StringUtils.isNotBlank(r.getProperties().getExtend1()) && StringUtils.isNumeric(r.getProperties().getExtend1().replace(".", ""))
                                && StringUtils.isNotBlank(r.getProperties().getUserId())
                        ).collect(Collectors.toList());
                if (eventEntityList.isEmpty()){
                    return;
                }

                String trace = UUID.randomUUID().toString().replaceAll("-","");
                log.info("{} ===> Risk Solve Batch Size:{}, Now Start",trace,eventEntityList.size());
                userExposureEcpmNewService.saveUserDayEcpm(eventEntityList, trace);
            }catch (Exception e){
                log.error("Solve Batch EVENT Error:",e);
            }
        }, executor);

    }

    public void setAD_TYPE_SET(Set<String> AD_TYPE_SET) {
        this.AD_TYPE_SET = AD_TYPE_SET;
    }
}
