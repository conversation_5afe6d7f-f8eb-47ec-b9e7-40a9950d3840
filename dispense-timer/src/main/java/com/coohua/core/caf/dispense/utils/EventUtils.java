package com.coohua.core.caf.dispense.utils;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.EventEntity;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/25
 */
public class EventUtils {

    private final static List<String> needProcessEventList = new ArrayList<String>(){{
//        add("WebClick");
//        add("Startup");
//        add("AppUse");
//        add("AppStatus");
//        add("AppPageView");
        add("AdData");
    }};

    private final static List<String> needProcessAdActionList = new ArrayList<String>(){{
        add("reward");
        add("click");
        add("exposure");
        add("video_cache_finish");
    }};

    public static List<EventEntity> filter(List<String> eventStr){
        return eventStr.stream()
                .map(EventUtils::apply)
                .filter(event -> "mvp".equals(event.getProject()))
                .filter(event -> needProcessEventList.contains(event.getEvent()))
                .filter(event -> needProcessAdActionList.contains(event.getProperties().getAd_action()))
                .collect(Collectors.toList());
    }

    private static EventEntity apply(String str) {
        return JSON.parseObject(str, EventEntity.class);
    }

    public static Map<String,Set<String>> getUserIdMap(List<EventEntity> eventEntityList){
        Map<String,List<EventEntity>> entityMap = eventEntityList.stream().collect(Collectors.groupingBy(r -> r.getProperties().getProduct()));
        Map<String,Set<String>> resMap = new HashMap<>();
        entityMap.forEach((product,list) -> resMap.put(product,getUserIdList(list)));
        return resMap;
    }

    public static Set<String> getUserIdList(List<EventEntity> eventEntityList){
        return eventEntityList.stream()
                .filter(r -> Strings.noEmptyExNull(r.getProperties().getUserId()))
                .map(r-> r.getProperties().getUserId())
                .collect(Collectors.toSet());
    }

    public static Set<String> getDeviceIdList(List<EventEntity> eventEntityList){
        return eventEntityList.stream()
                .filter(r -> Strings.noEmptyExNull(r.getProperties().get$device_id()) || Strings.noEmptyExNull(r.getProperties().getImei()))
                .map(r-> {
                    String deviceId = r.getProperties().get$device_id();
                    if (Strings.isEmpty(deviceId)){
                        deviceId = r.getProperties().getImei();
                    }
                    return deviceId;
                }).collect(Collectors.toSet());
    }
}
