package com.coohua.core.caf.dispense.service;

import com.coohua.core.caf.dispense.constant.SystemInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 拿打点数据来做
 */
@Slf4j
@Component
public class UserEventAdKafkaConsumer implements InitializingBean {


    private ExecutorService executorService;
    private long oldValue = 0;
    private AtomicLong count = new AtomicLong(0);

    private static final String TOPIC = "bp_ad";
    private static final String CONSUMER = "dispense_timer_exposure_ecpm";

    private static final String SERVERS = "alikafka-pre-cn-zvp2j2vhf007-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-zvp2j2vhf007-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-zvp2j2vhf007-3-vpc.alikafka.aliyuncs.com:9092";
    //private static final String SERVERS = "172.16.200.27:9092,172.16.200.3:9092,172.16.200.252:9092";

    @Autowired
    private KafkaEventService kafkaEventService;

    @Override
    public void afterPropertiesSet() {
        if(SystemInfo.isTest()) {
            return;
        }
        // 初始化线程池
        executorService = Executors.newFixedThreadPool(12);
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, SERVERS);
        //默认值为30000ms，可根据自己业务场景调整此值，建议取值不要太小，防止在超时时间内没有发送心跳导致消费者再均衡。
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 25000);
        //每次poll的最大数量。
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        //注意该值不要改得太大，如果poll太多数据，而不能在下次poll之前消费完，则会触发一次负载均衡，产生卡顿。
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 20000);
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024*2);//2M
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 2097152);
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 100);
        //消息的反序列化方式。
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.GROUP_ID_CONFIG, CONSUMER);
        //当前消费实例所属的Consumer Group，请在控制台创建后填写。
        //属于同一个Consumer Group的消费实例，会负载消费消息。
        kafkaProcessor(props);
        CompletableFuture.runAsync(() ->{
            while (true) {
                try {
                    long newValue = count.longValue();
                    log.info("KafkaDataConsumer: MSG.SEC={}", newValue - this.oldValue);
                    this.oldValue = newValue;
                    TimeUnit.SECONDS.sleep(1);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
            }
        });
    }

    private void kafkaProcessor(Properties properties) {
        for (int i = 0; i < 12; i++) {
            executorService.execute(() -> {
                KafkaConsumer<String, String> consumer = new KafkaConsumer<>(properties);
                consumer.subscribe(Collections.singletonList(TOPIC));
                List<String> flist = new ArrayList<>();
                try {
                    while (true) {
                        ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(1).toMillis());
                        if (records.count() > 0) {
                            for (ConsumerRecord<String, String> record : records) {
                                flist.add(record.value());
                                count.getAndIncrement();
                            }
                            if (flist.size() > 20000){
                                kafkaEventService.doProcess(flist);
                                flist = new ArrayList<>();
                            }
                            consumer.commitSync();
                        }
                    }
                } catch (Exception e) {
                    log.error("Kafka consumer error.", e);
                } finally {
                    try {
                        consumer.commitSync();
                    } finally {
                        consumer.close();
                    }
                }
            });
        }
    }

}
