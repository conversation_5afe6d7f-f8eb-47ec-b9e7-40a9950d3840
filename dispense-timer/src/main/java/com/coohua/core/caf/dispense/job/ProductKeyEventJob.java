package com.coohua.core.caf.dispense.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.enums.UserEcpmStatus;
import com.coohua.core.caf.dispense.ocpc.entity.UserExposureEcpmNew;
import com.coohua.core.caf.dispense.ocpc.service.UserExposureEcpmNewService;
import com.coohua.core.caf.dispense.service.KafkaEventService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Component
public class ProductKeyEventJob {
    @Autowired
    private UserExposureEcpmNewService userExposureEcpmNewService;
    @Resource(name = "sendJobTaskPool")
    private ThreadPoolTaskExecutor executor;
    @Resource(name = "queryToutiaoJobTaskPool")
    private ThreadPoolTaskExecutor executorQuery;
    @Autowired
    private KafkaEventService kafkaEventService;

    private static final String AD_TYPE_LIST_URL = "http://bp-ap-admin.coohua.com/config/getAdTypeDist";

    //@Scheduled(cron = "0 0/5 * * * ?")
    @Scheduled(fixedDelay = 300000)
    public void query4SendSchedule() {
        if(SystemInfo.isTest()) {
            return;
        }
        Date date = new Date();
        String time = DateUtils.formatDateForYMD(date);
        log.info("查询5分前回调比例 {} start", time);
        try {
            userExposureEcpmNewService.dealProductOcpcEvent(date, executorQuery);
        }catch (Exception ex) {
            log.error("query4SendSchedule {} ex", time, ex);
        }
        log.info("查询5分前回调比例 {} end", time);
    }

    @Scheduled(fixedDelay = 10000)
    public void send2ThirdSchedule() {
        if(SystemInfo.isTest()) {
            return;
        }
        Date date = new Date();
        String time = DateUtils.formatDateForYMD(date);
        log.info("定时发送关键行为到第三方 {} start", time);
        try {
            List<UserExposureEcpmNew> list = userExposureEcpmNewService.lambdaQuery()
                    .eq(UserExposureEcpmNew::getStatus, UserEcpmStatus.WAIT.getValue())
                    .le(UserExposureEcpmNew::getSendTime, date).list();
            userExposureEcpmNewService.send2Third(list, executor);
        }catch (Exception ex) {
            log.error("send2ThirdSchedule {} ex", time, ex);
        }
        log.info("定时发送关键行为到第三方 {} end", time);
    }

    @PostConstruct
    @Scheduled(cron = "2 0/1 * * * ?")
    public void reloadVideoAdType() {
        if(SystemInfo.isTest()) {
            return;
        }
        log.info("reloadVideoAdType start");
        HttpConfig httpConfig = HttpConfig.custom().timeout(2000);
        String html = null;
        try {
            html = HttpClientUtil.get(httpConfig.url(AD_TYPE_LIST_URL));
            JSONObject jsObject = JSON.parseObject(html);
            if (0 == jsObject.getIntValue("code")) {
                Set<String> adTypes = jsObject.getJSONArray("result").stream().map(o -> (JSONObject) o).filter(o -> "视频".equals(o.getString("subTypeName"))).map(o -> o.getString("adType")).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(adTypes)) {
                    kafkaEventService.setAD_TYPE_SET(adTypes);
                }
                log.info("查询成功，adType size:{}", adTypes.size());
            } else {
                log.info("查询失败，result:{}", html);
            }
        }catch (Exception ex) {
            log.error("查询广告类型报错成功，url:{}, html:{}", AD_TYPE_LIST_URL, html);
        }
        log.info("reloadVideoAdType end");
    }
}
