package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ocpc.entity.DailyAdvertiserAccount;
import com.coohua.core.caf.dispense.ocpc.mapper.DailyAdvertiserAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class DailyAdvertiserAccountService extends ServiceImpl<DailyAdvertiserAccountMapper, DailyAdvertiserAccount> {

}
