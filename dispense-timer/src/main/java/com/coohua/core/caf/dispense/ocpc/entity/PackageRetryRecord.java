package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="PackageRetryRecord对象", description="自然量重新归因记录")
public class PackageRetryRecord {
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Integer appId;
    private String product;
    private String os;
    private String userId;
    private String oaid;
    private String oaid2;
    private String idfa2;
    private String caid;
    private Date oriCreateTime;
    private String gyType;
    private String dsp;
    private Date attributionTime;
    private Date clickTime;
}
