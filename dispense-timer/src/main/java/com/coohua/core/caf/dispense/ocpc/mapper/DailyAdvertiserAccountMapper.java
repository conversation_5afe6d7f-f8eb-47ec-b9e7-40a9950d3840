package com.coohua.core.caf.dispense.ocpc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.core.caf.dispense.ocpc.entity.DailyAdvertiserAccount;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface DailyAdvertiserAccountMapper extends BaseMapper<DailyAdvertiserAccount> {

    @Select("select dsp, product, account_id, sum(if(event_type = 0, 1, 0)) active_count, sum(if(event_type = 25, 1, 0)) key_count from ocpc_event where create_time >= #{today} and create_time < #{tomorrow} and event_type in (0, 25) and account_id is not null group by dsp, product, account_id")
    List<DailyAdvertiserAccount> queryDailyCount(@Param("today") Date today, @Param("tomorrow") Date tomorrow);
}
