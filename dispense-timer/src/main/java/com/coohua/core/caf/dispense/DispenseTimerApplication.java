package com.coohua.core.caf.dispense;

import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.coohua.core.caf.dispense.log.Log4j2ContextInitializerOwn;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.httpclient.EnableHttpBioClient;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;


@SpringBootApplication(scanBasePackages = {"com.coohua.core.caf.dispense"})
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health",
		// DB配置
		"bp.db.dispense","bp.redis.dispense", "rta.redis","relationship.redis","bp.safe.data.redis","ap.redis.cluster",
		//rpc配置
		"ad.user.ad.api.referer",
		//sdk实时归因配置
		"ap.tf.juliang"
})
@EnableJedisClusterClient(namespace = "bp-dispense")
@EnableJedisClusterClient(namespace = "rta-redis")
@EnableJedisClusterClient(namespace = "safe-data")
@EnableJedisClusterClient(namespace = "ap-cluster")
@EnableJedisClusterClient(namespace = "relationship-redis")
@EnableMotan(namespace = "user-event")
@EnableAutoChangeApolloConfig
@EnableScheduling
@EnableCaching
@EnableHttpBioClient
@Slf4j
public class DispenseTimerApplication {

	public static void main(String[] args) {
		SpringApplication springApplication = new SpringApplication(DispenseTimerApplication.class);
		// springApplication.addInitializers(new Log4j2ContextInitializerOwn());
		ConfigurableApplicationContext configurableApplicationContext = springApplication.run(args);

	}

}
