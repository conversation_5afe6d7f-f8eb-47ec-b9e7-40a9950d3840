package com.coohua.core.caf.dispense.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
@Slf4j
public class ThreadPoolConf {

	@Bean(name = "partitionJobTaskPool")
	public ThreadPoolTaskExecutor productPoolConf(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(16);
		//指定最大线程数
		pool.setMaxPoolSize(200);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("partitionJobTaskPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("partitionJobTaskPool 已初始化");
		return pool;
	}

	@Bean(name = "sendJobTaskPool")
	public ThreadPoolTaskExecutor sendPoolConf(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(16);
		//指定最大线程数
		pool.setMaxPoolSize(200);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("sendJobTaskPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("sendJobTaskPool 已初始化");
		return pool;
	}

	@Bean(name = "queryToutiaoJobTaskPool")
	public ThreadPoolTaskExecutor queryToutiaoPoolConf(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(16);
		//指定最大线程数
		pool.setMaxPoolSize(200);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("queryToutiaoJobTaskPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("queryToutiaoJobTaskPool 已初始化");
		return pool;
	}

	@Bean(name = "queryCvrProductJobTaskPool")
	public ThreadPoolTaskExecutor queryCvrProductJobTaskPool(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(16);
		//指定最大线程数
		pool.setMaxPoolSize(200);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("queryCvrProductJobTaskPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("queryCvrProductJobTaskPool 已初始化");
		return pool;
	}

	@Bean(name = "retrySdkGyPool")
	public ThreadPoolTaskExecutor retrySdkGyPoolConf(){
		ThreadPoolTaskExecutor pool = new ThreadPoolTaskExecutor();
		//核心线程数目
		pool.setCorePoolSize(16);
		//指定最大线程数
		pool.setMaxPoolSize(200);
		//队列中最大的数目
		pool.setQueueCapacity(5000);
		//线程名称前缀
		pool.setThreadNamePrefix("retrySdkGyPool_");
		//rejection-policy：当pool已经达到max size的时候，如何处理新任务
		pool.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());// + discard 策略
		//线程空闲后的最大存活时间
		pool.setKeepAliveSeconds(30);
		pool.initialize();
		log.info("retrySdkGyPool 已初始化");
		return pool;
	}
}
