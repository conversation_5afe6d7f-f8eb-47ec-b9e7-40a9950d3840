package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ocpc.entity.PackageRetryRecord;
import com.coohua.core.caf.dispense.ocpc.mapper.PackageRetryRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PackageRecordService extends ServiceImpl<PackageRetryRecordMapper, PackageRetryRecord> {
    @Autowired
    private PackageRetryRecordMapper packageRetryRecordMapper;

    public PackageRetryRecord getPackageRetryRecord(String os,String product,String userId){
        QueryWrapper<PackageRetryRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("os",os);
        queryWrapper.eq("product",product);
        queryWrapper.eq("user_id",userId);
        return packageRetryRecordMapper.selectOne(queryWrapper);
    }

    public void savePackageRetryRecord(PackageRetryRecord packageRetryRecord){
        packageRetryRecordMapper.insert(packageRetryRecord);
    }
}
