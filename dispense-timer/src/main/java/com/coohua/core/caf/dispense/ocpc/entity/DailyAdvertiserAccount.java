package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="DailyAdvertiserAccount对象", description="账户自然日激活和回传数")
public class DailyAdvertiserAccount implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "dsp")
    private String dsp;
    @ApiModelProperty(value = "product")
    private String product;
    @ApiModelProperty(value = "日期 格式yyyy-MM-dd")
    private Date dataDate;
    @ApiModelProperty(value = "账户id")
    private String accountId;
    @ApiModelProperty(value = "激活数")
    private Integer activeCount;
    @ApiModelProperty(value = "关键行为回调数")
    private Integer keyCount;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
