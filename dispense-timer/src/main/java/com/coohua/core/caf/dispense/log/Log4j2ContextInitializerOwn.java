package com.coohua.core.caf.dispense.log;

import com.aliyun.openservices.log.log4j2.LoghubAppender;
import com.coohua.caf.core.logging.Log4j2ContextInitializer;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/26
 */
@ConditionalOnClass(Log4j2ContextInitializer.class)
public class Log4j2ContextInitializerOwn implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    private ConfigurableApplicationContext applicationContext;

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
        this.applicationContext = configurableApplicationContext;
        try {
            LoggerContext loggerContext = LoggerContext.getContext(false);
            Configuration logConfiguration = loggerContext.getConfiguration();
            Map<String, Appender> appenderMap = logConfiguration.getAppenders();
            for (Appender appender : appenderMap.values()) {
                if (appender instanceof LoghubAppender) {
                    LoghubAppender loghubAppender = (LoghubAppender) appender;
                    // 设置SOURCE
                    loghubAppender.setSource("dispense-timer");
                }
            }
        } catch (Exception ex) {
            throw new IllegalStateException("Could not initialize Log4J2 logging from tk", ex);
        }
    }
}
