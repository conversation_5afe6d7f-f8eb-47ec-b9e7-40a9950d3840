package com.coohua.core.caf.dispense.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.LogNum;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.ck.entity.GyRecordDataLocal;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthToutiaoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.OcpcMisActiveService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.*;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.kafka.LogKafkaSender;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.PackageRetryRecord;
import com.coohua.core.caf.dispense.ocpc.entity.ToutiaoSdkGyRetry;
import com.coohua.core.caf.dispense.ocpc.entity.UserExposureEcpmNew;
import com.coohua.core.caf.dispense.ocpc.service.OceanengineSdkService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.ocpc.service.PackageRecordService;
import com.coohua.core.caf.dispense.ocpc.service.ToutiaoSdkGyRetryService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.ConstCls;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RetrySdkGyJob {

    @Autowired
    ToutiaoSdkGyRetryService toutiaoSdkGyRetryService;
    @Autowired
    OceanengineSdkService oceanengineSdkService;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    OcpcMisActiveService ocpcMisActiveService;
    @Autowired
    LogKafkaSender logKafkaSender;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Resource(name = "retrySdkGyPool")
    private ThreadPoolTaskExecutor executor;
    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Autowired
    PackageRecordService packageRecordService;

    private static final ExecutorService uploadAppExecutor = new ThreadPoolExecutor(
            32,
            // 最大线程数 (提高到核心线程数的1.5-2倍)
            64,
            // 空闲线程存活时间 (保持原样)
            60,
            TimeUnit.SECONDS,
            // 使用有界队列替代SynchronousQueue (增加缓冲能力)
            new ArrayBlockingQueue<>(500),
            // 使用更安全的拒绝策略
            new ThreadPoolExecutor.CallerRunsPolicy()
    );


    @Scheduled(cron = "5 0/5 * * * ?")
    public void retrySdkGyJob() {

        // 查询所有要重新进行sdk归因的数据
        List<ToutiaoSdkGyRetry> toutiaoSdkGyRetryList = toutiaoSdkGyRetryService.queryRetrySdkGy();

        if (toutiaoSdkGyRetryList == null || toutiaoSdkGyRetryList.isEmpty()) {
            return;
        }
        log.info("retrySdkGyJob start size {}", toutiaoSdkGyRetryList.size());
        List<List<ToutiaoSdkGyRetry>> partition = ListUtils.partition(toutiaoSdkGyRetryList, 10);
        List<CompletableFuture<List<ToutiaoSdkGyRetry>>> futures = new ArrayList<>();
        for (List<ToutiaoSdkGyRetry> toutiaoSdkGyRetries : partition) {
            // 异步回传sdk
            CompletableFuture<List<ToutiaoSdkGyRetry>> completableFuture = CompletableFuture.supplyAsync(()->retrySdkGySync(toutiaoSdkGyRetries));
            futures.add(completableFuture);
        }

        // 合并所有结果
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );

        // 阻塞等待并收集结果
        List<List<ToutiaoSdkGyRetry>> results = allFutures.thenApply(v ->
                futures.stream()
                        .map(CompletableFuture::join) // 获取每个任务结果
                        .collect(Collectors.toList())
        ).join(); // 阻塞直到所有完成


        for (List<ToutiaoSdkGyRetry> result : results) {
            for (ToutiaoSdkGyRetry toutiaoSdkGyRetry : result) {
                toutiaoSdkGyRetry.setRetryCount(toutiaoSdkGyRetry.getRetryCount() + 1);
                toutiaoSdkGyRetry.setUpdateTime(new Date());
            }
            toutiaoSdkGyRetryService.updateBatchById(result);
        }

        log.info("toutiaoSdkGyRetryList upadte end");
    }

    /**
     * 自然量归因重试
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void packageGyRetryJob() {
        // 重试5分钟前的待归因队列
        packageGyRetry(5);
        // 重试10分钟前的待归因队列
        packageGyRetry(10);
        packageGyRetry(15);
        packageGyRetry(20);
        packageGyRetry(30);
        packageGyRetry(40);
        packageGyRetry(50);
        packageGyRetry(60);

    }

    private void packageGyRetry(int minutes) {
        // 获取当前时间
        LocalTime currentTime = LocalTime.now().minusMinutes(minutes);
        // 定义格式 (HH: 24小时制, mm: 分钟)
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        String key = "package:gy:retry:"+formatter.format(currentTime);
        String userEventReqListStr = bpDispenseJedisClusterClient.get(key);
        if (StringUtils.isBlank(userEventReqListStr)) {
            return;
        }
        List<UserEventReq> userEventReqList = JSONObject.parseArray(userEventReqListStr, UserEventReq.class);
        Map<String, UserEventReq> userEventReqMap = userEventReqList.stream().collect(Collectors.toMap(
                userEventReq -> userEventReq.getProduct() + "_" + userEventReq.getOs() + "_" + userEventReq.getUserId(),
                // Value生成器：对象本身
                userEventReq -> userEventReq,
                // 合并函数：处理Key冲突（这里保留新值）
                (oldValue, newValue) -> newValue));
        ConcurrentHashMap<String, UserEventReq> concurrentHashMap = new ConcurrentHashMap<>(userEventReqMap);
        List<CompletableFuture> taskList = new ArrayList<>();
        log.info("归因重试 key {} minutes {}",key,minutes);
        for (int i = 0; i < userEventReqList.size(); i++) {
                int j = i;
                taskList.add(CompletableFuture.runAsync(() -> {
                    extracted(userEventReqList.get(j),concurrentHashMap);
                },uploadAppExecutor));
        }
        CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        if (userEventReqMap.isEmpty()) {
            return;
        }
        List<UserEventReq> userEventReqs = new ArrayList<>(userEventReqMap.values());
        // 更新redis
        bpDispenseJedisClusterClient.setex(key, Math.toIntExact(bpDispenseJedisClusterClient.ttl(key)), JSONObject.toJSONString(userEventReqs));
    }

    private void extracted(UserEventReq userEventReq,ConcurrentHashMap<String, UserEventReq> userEventReqMap) {
        List<ToutiaoClick> newClickList = getNewClickList(userEventReq);
        if (newClickList == null || newClickList.isEmpty()) {
            return;
        }
        // 获取原先的归因 没有归因的数据直接返回
        OcpcEvent activeEvent = getActiveEvent(userEventReq);
        if (activeEvent == null) {
            return;
        }
        // 查询结果是否被保存，保存了则不进行二次保存，没保存则直接保存
        PackageRetryRecord packageRetryRecord = packageRecordService.getPackageRetryRecord(userEventReq.getOs(), userEventReq.getProduct(), userEventReq.getUserId());
        if (packageRetryRecord != null) {
            return;
        }
        //保证每个渠道只有一个点击事件
        newClickList = ocpcEventService.getDspClickList(newClickList);
        if (newClickList.size() > 1) {
            newClickList = ocpcEventService.sortClickList(newClickList);
        }
        PackageRetryRecord savePackageRetryRecordEntity = new PackageRetryRecord();
        buildPackageRetryRecord(savePackageRetryRecordEntity, activeEvent, newClickList, userEventReq);
        packageRecordService.savePackageRetryRecord(savePackageRetryRecordEntity);
        userEventReqMap.remove(userEventReq.getProduct() + "_" +userEventReq.getOs() + "_" + userEventReq.getUserId());
    }

    private void buildPackageRetryRecord(PackageRetryRecord savePackageRetryRecordEntity, OcpcEvent activeEvent, List<ToutiaoClick> newClickList, UserEventReq userEventReq) {
        savePackageRetryRecordEntity.setAppId(activeEvent.getAppId());
        savePackageRetryRecordEntity.setProduct(activeEvent.getProduct());
        savePackageRetryRecordEntity.setOs(activeEvent.getOs());
        savePackageRetryRecordEntity.setUserId(activeEvent.getUserId());
        savePackageRetryRecordEntity.setOaid(activeEvent.getOaid());
        savePackageRetryRecordEntity.setOaid2(activeEvent.getOaid2());
        savePackageRetryRecordEntity.setIdfa2(activeEvent.getIdfa2());
        savePackageRetryRecordEntity.setCaid(activeEvent.getCaid());
        savePackageRetryRecordEntity.setDsp(newClickList.get(0).getDsp());
        savePackageRetryRecordEntity.setGyType(userEventReq.getGuiType());
        savePackageRetryRecordEntity.setAttributionTime(new Date());
        savePackageRetryRecordEntity.setClickTime(newClickList.get(0).getCreateTime());
        savePackageRetryRecordEntity.setOriCreateTime(activeEvent.getCreateTime());
    }

    private List<ToutiaoSdkGyRetry> retrySdkGySync(List<ToutiaoSdkGyRetry> toutiaoSdkGyRetryList) {
        for (ToutiaoSdkGyRetry toutiaoSdkGyRetry : toutiaoSdkGyRetryList) {
            if (toutiaoSdkGyRetry.getEventType() != 0) {
                continue;
            }
            UserEventReq userEventReq = new UserEventReq();
            BeanUtils.copyProperties(toutiaoSdkGyRetry, userEventReq);

            //激活 核心行为 次留 按照最近点击时间归因
            OcpcEvent ocpcEvent = getActiveEvent(userEventReq);
            if (ocpcEvent != null && StringUtils.equalsIgnoreCase(ocpcEvent.getUserId(), userEventReq.getUserId()) &&
                    StringUtils.equalsIgnoreCase(ocpcEvent.getProduct(), userEventReq.getProduct()) &&
                    StringUtils.equalsIgnoreCase(ocpcEvent.getOs(), userEventReq.getOs())) {
                log.info("sdk重试归因 ocpc重复激活 " + JSON.toJSONString(userEventReq) + "@" + JSON.toJSONString(ocpcEvent));
                continue;
            }


            List<ToutiaoClick> newClickList = getNewClickList(userEventReq);

            if ((newClickList == null || newClickList.isEmpty()) && toutiaoSdkGyRetry.getRetryCount() > 0) {
                // 默认渠道包进行归因
                //根据开关判断是否走默认归因 且 sdk归因无需重试
                //如果没有查询到点击事件，且渠道包不是商店渠道，则根据渠道包生成默认的归因
                if (StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
                    toutiaoSdkGyRetry.setDelFlag(1);
                    ToutiaoClick defaultClick = getDefaultClickList(userEventReq);
                    //保存归因
                    ocpcEventService.saveUserEvent(defaultClick, userEventReq, "", "");
                    GyRecordDataLocal gyRecordData = fillGyRecordData(userEventReq);
                    gyRecordData.setGyType(GuiyingType.packageGy.name);
                    //通过Kafka记录埋点数据
                    logKafkaSender.sendRecordData(JSONObject.toJSONString(gyRecordData));
                    ocpcMisActiveService.saveOcpcMisActive(userEventReq, null);
                }
            }

            if (newClickList == null || newClickList.isEmpty()) {
                continue;
            }

            //记录没归因到的数据
            log.info("sdk重试归因查询成功 {}", JSON.toJSONString(toutiaoSdkGyRetry));
            ocpcMisActiveService.saveOcpcMisActive(userEventReq, newClickList.get(0));
            toutiaoSdkGyRetry.setDelFlag(1);


            // 重新进行归因
            if (authToutiaoAdvertiserService.checkActionCount(newClickList.get(0), userEventReq)) {
                //若为头条正常归因  非直接归因
                log.info("sdk重试产生归因 {}", JSONObject.toJSONString(newClickList.get(0)));
                boolean isAct = ocpcEventService.guiyingDspClick(newClickList.get(0), userEventReq, true);
                GyRecordDataLocal gyRecordData = fillGyRecordData(userEventReq);
                gyRecordData.setGyType(userEventReq.getGuiType());
                logKafkaSender.sendRecordData(JSONObject.toJSONString(gyRecordData));
            }
        }
        return toutiaoSdkGyRetryList;
    }

    private OcpcEvent getActiveEvent(UserEventReq userEventReq) {
        return getActiveEventByRedis(userEventReq);
    }

    private OcpcEvent getActiveEventByRedis(UserEventReq request) {

        OcpcEvent ocpcEvent = null;
        try {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("redisgetocpcEvent product is null ! {}", JSONObject.toJSONString(request));
                return ocpcEvent;
            }
            String key = "";
            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                // 如果是android 设备, 但传值为"0000-0000-0000-0000","000000-0000-0000-000000","00000000-0000-0000-0000-000000000000"就不走这个 OcpcDeviceId
                if ("android".equals(request.getOs()) &&
                        (request.getSourceDeviceId().contains("0000-0000-0000-0000") ||
                                request.getSourceDeviceId().contains("000000-0000-0000-000000") ||
                                request.getSourceDeviceId().contains("00000000-0000-0000-0000-000000000000"))) {
                    log.warn("android设备传sourceDeviceId值为未知值,不使用OcpcDeviceId进行数据匹配:"+ JSONObject.toJSONString(request));
                } else {
                    key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.device.value, request.getOcpcDeviceId());
                    String s = bpDispenseJedisClusterClient.get(key);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                        request.setGuiType(GuiyingType.deive.name);
                    }
                }
            }
            /**
             * 按idfa_md5查询
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.idfa2.value,request.getIdfa2());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.idfa.name);
                }
            }
            /**
             * click不存在再查oaId
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getOaid())&& !ConstCls.emptyMd5.equals(request.getOaid())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.oaid.value,request.getOaid());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.oaid.name);
                }
            }
            /**
             * oaid2 = md5(oaid)
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getOaid2())&& !ConstCls.emptyMd5.equals(request.getOaid2())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.oaid2.value, request.getOaid2());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.oaid.name);
                }
            }
            /**
             * 设备号和 oaId 查不到 再用mac地址查
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getMac())&& !ConstCls.emptyMd5.equals(request.getMac())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.mac.value,request.getMac());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("macevent归因成功 " + product+" "+request.getMac());
                    request.setGuiType(GuiyingType.mac.name);
                }
            }
            /**
             * androidId查
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getAndroidId())&& !ConstCls.emptyMd5.equals(request.getAndroidId())) {
                String androidIdMd5 = MD5Utils.getMd5Sum(request.getAndroidId());
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.androidId.value,androidIdMd5);
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("androidId归因成功 " + product+" "+request.getAndroidId());
                    request.setGuiType(GuiyingType.androidId.name);
                }
            }

            /**
             * androidIdMD5查
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getAndroidMd5Id())&& !ConstCls.emptyMd5.equals(request.getAndroidMd5Id())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.androidId.value,request.getAndroidMd5Id());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
//                    log.info("androidId归因成功 " + product+" "+request.getAndroidId());
                    request.setGuiType(GuiyingType.androidId.name);
                }
            }
            /**
             * 查ipua
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.concatIpua())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.ipua.value,request.concatIpua());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("redisActive归因成功ipua " + product+" "+request.concatIpua());
                    request.setGuiType(GuiyingType.ipua.name);
                }
            }


            if (ocpcEvent == null && StringUtils.isNotBlank(request.getOpenId())&& !ConstCls.emptyMd5.equals(request.getOpenId())) {
                key = RedisKeyConstants.getActiveOpenIdKey(product,request.getWAppId(),request.getOpenId());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("openId 归因成功 " + product+" "+request.getAndroidId());
                    request.setGuiType(GuiyingType.openid.name);
                }
            }

            //caid
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getCaid())&& !ConstCls.emptyMd5.equals(request.getCaid())) {
                key = RedisKeyConstants.getActiveCaidKey(product,request.getCaid());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("caid 归因成功 " + product+" "+request.getCaid());
                    request.setGuiType(GuiyingType.caid.name);
                }
            }
            /**
             * caid2 = md5(caid)
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getCaid2())&& !ConstCls.emptyMd5.equals(request.getCaid2())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.caid2.value, request.getCaid2());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.caid.name);
                }
            }
            //USERid
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getUserId())) {
                key = RedisKeyConstants.getActiveUidKey(product,request.getOs(),request.getUserId());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("userId激活归因成功 " + product+" "+request.getOs()+" uid:"+request.getUserId());
                    request.setGuiType(GuiyingType.uid.name);
                }
            }
            if (ocpcEvent != null && ocpcSwitcher.needLog(LogNum.RedisGetOcpcEvent)) {
                log.info("redis getocpcEvent success req={} ,rsp={} key={}", JSONObject.toJSONString(request),JSONObject.toJSONString(ocpcEvent),key);
            }
        } catch (Exception e) {
            log.error("redis getocpcEvent error ", e);
        }

        return ocpcEvent;
    }

    private GyRecordDataLocal fillGyRecordData(UserEventReq userEventReq) {
        GyRecordDataLocal gyRecordData = new GyRecordDataLocal();
        gyRecordData.setLogday(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        gyRecordData.setOs(userEventReq.getOs());
        gyRecordData.setProduct(userEventReq.getProduct());
        gyRecordData.setUserId(userEventReq.getUserId());
        gyRecordData.setAndroidId(userEventReq.getAndroidId());
        gyRecordData.setOaid(userEventReq.getOaid());
        gyRecordData.setCaid(userEventReq.getCaid() != null ? userEventReq.getCaid() : userEventReq.getCaid2());
        gyRecordData.setIdfa(userEventReq.getIdfa());
        String sourceDeviceId = userEventReq.getSourceDeviceId();
        if (StringUtils.isNotBlank(sourceDeviceId) && (sourceDeviceId.length()<6 || sourceDeviceId.contains("0000") || sourceDeviceId.contains("null")
                || "default".equals(sourceDeviceId) || "undefined".equals(sourceDeviceId))) {
            gyRecordData.setDeviceId(null);
        }else {
            gyRecordData.setDeviceId(sourceDeviceId);
        }
        gyRecordData.setMac(userEventReq.getMac());
        gyRecordData.setIpua(userEventReq.concatIpua());
        gyRecordData.setOpenid(userEventReq.getOpenId());
        gyRecordData.setIunod(userEventReq.concatIpuaMd());
        gyRecordData.setIpmd(userEventReq.concatIpMd());
        gyRecordData.setIp(userEventReq.concatIp());
        return gyRecordData;
    }

    private ToutiaoClick getDefaultClickList(UserEventReq userEventReq) {
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setProduct(userEventReq.getProduct());
        toutiaoClick.setOs(userEventReq.getOs());
        toutiaoClick.setIdfa2(userEventReq.getIdfa2());
        toutiaoClick.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
        toutiaoClick.setOaid(userEventReq.getOaid());
        toutiaoClick.setOaid2(userEventReq.getOaid2());
        toutiaoClick.setPkgChannel(StringUtils.isNotBlank(userEventReq.getPkgChannel()) ? userEventReq.getPkgChannel() : userEventReq.getPkg_channel());
        toutiaoClick.setUa(userEventReq.getUa());
        if (userEventReq.getPkgChannel().startsWith("ks") || userEventReq.getPkgChannel().endsWith("ks")) {
            toutiaoClick.setDsp("kuaishou");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "bd", "cbd") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "bd", "cbd")) {
            toutiaoClick.setDsp("baidufeed");
        } else if (userEventReq.getPkgChannel().startsWith("gdt") || userEventReq.getPkgChannel().endsWith("gdt")) {
            toutiaoClick.setDsp("guangdiantong");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "csj", "tt") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "csj", "tt")) {
            toutiaoClick.setDsp("toutiao");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "oppo") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "oppo")) {
            toutiaoClick.setDsp("oppo");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "vivo") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "vivo")) {
            toutiaoClick.setDsp("vivo");
        } else {
            toutiaoClick.setDsp("other");
        }
        toutiaoClick.setGuiType(GuiyingType.packageGy.name);
        userEventReq.setGuiType(GuiyingType.packageGy.name);
        return toutiaoClick;
    }

    public List<ToutiaoClick> getNewClickList(UserEventReq userEventReq) {
        List<ToutiaoClick> clickList = new ArrayList<>();
        //查询精准归因
        ToutiaoClick accurateClick = redisClickService.getAccurateClick(userEventReq, ocpcSwitcher.readOcpcHbaseSwitch);
        if (accurateClick != null) {
            clickList.add(accurateClick);
            // log.info("redis精准匹配成功 click {}", JSONObject.toJSONString(accurateClick));
        }
        if (ocpcSwitcher.ksHbaseGy && clickList.isEmpty()) {
            //查询hbase，hbase只存储3天的数据
            ToutiaoClick accurateClick2 = redisClickService.getAccurateClick(userEventReq, true);
            if (accurateClick2 != null) {
                clickList.add(accurateClick2);
                // log.info("hbase精准匹配成功 click {}", JSONObject.toJSONString(accurateClick2));
            }
        }
        // 请求穿山甲归因sdk
        if (StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
            ToutiaoClick sdkClick = oceanengineSdkService.querySdkClick(userEventReq, false, false);
            if (null != sdkClick) {
                //精准点击未找到，直接使用sdk归因匹配
                if (clickList.isEmpty()) {
                    log.info("未找到精准点击 使用sdk进行归因 click {}", JSONObject.toJSONString(sdkClick));
                    clickList.add(sdkClick);
                    //找到精准点击且渠道为头条，替换为sdk归因匹配
                } else if ("toutiao".equals(clickList.get(0).getDsp())) {
                    log.info("精准点击为头条渠道 替换sdk进行归因 click {} gyType {}", JSONObject.toJSONString(sdkClick),clickList.get(0).getGuiType());
                    // 替换为sdkClick；
                    sdkClick.setGuiType(clickList.get(0).getGuiType());
                    clickList.set(0, sdkClick);
                }
            }
        }

        //如果精准归因和sdk归因均没有结果则查询非精准归因
        if (clickList.isEmpty()) {
            ToutiaoClick obscureClick = redisClickService.getObscureClick(userEventReq, ocpcSwitcher.readOcpcHbaseSwitch);
            if (null != obscureClick) {
                clickList.add(obscureClick);
                // log.info("redis非精准匹配成功 click {}", JSONObject.toJSONString(obscureClick));
            }
            if (ocpcSwitcher.ksHbaseGy && clickList.isEmpty()) {
                ToutiaoClick obscureClick2 = redisClickService.getObscureClick(userEventReq, true);
                if (obscureClick2 != null) {
                    clickList.add(obscureClick2);
                    // log.info("hbase非精准匹配成功 click {}", JSONObject.toJSONString(obscureClick2));
                }
            }
        }
        return clickList;
    }


}
