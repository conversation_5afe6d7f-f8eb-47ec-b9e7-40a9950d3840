package com.coohua.core.caf.dispense.controller;

import com.coohua.core.caf.dispense.job.RetrySdkGyJob;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class TestController {

    @Autowired
    RetrySdkGyJob retrySdkGyJob;

    @RequestMapping(value = "/dispense/job/sdk/test")
    @ResponseBody
    public String test() {
        retrySdkGyJob.retrySdkGyJob();
        return "200";
    }
}
