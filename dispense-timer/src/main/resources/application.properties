spring.datasource.driverClass = com.mysql.cj.jdbc.Driver
spring.datasource.url = *************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
spring.datasource.username = dispenseus
spring.datasource.password = yI2gbNL1PneDLscj
spring.datasource.type = com.alibaba.druid.pool.DruidDataSource
spring.datasource.maxActive = 1000
spring.datasource.initialSize = 1
spring.datasource.minIdle = 30
spring.datasource.maxWait = 10000
spring.datasource.timeBetween = 60000
spring.datasource.minEvictableIdle = 300000
spring.datasource.validationQuery = SELECT 'x'
spring.datasource.testWhileIdle = true
spring.datasource.keepAlive = true
spring.datasource.testOnBorrow = true
spring.datasource.testOnReturn = true
spring.datasource.poolPreparedStatements = true
spring.datasource.maxPoolPreparedStatementPerConnectionSize = 20
spring.datasource.removeAbandoned = false
spring.datasource.removeAbandonedTime = 60000
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl

ocpc.spring.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT&allowMultiQueries=true
ocpc.spring.datasource.username = dispenseus
ocpc.spring.datasource.password = yI2gbNL1PneDLscj


spring.click.datasource.username = default
spring.click.datasource.password = Phw7a7A4
spring.click.datasource.url = ********************************************************************
spring.click.datasource.maxActive = 200
spring.click.datasource.initialSize = 2
spring.click.datasource.minIdle = 1
spring.click.datasource.maxWait = 2000
spring.click.datasource.testWhileIdle = false
spring.click.datasource.timeBetweenEvictionRunsMillis = 600000
spring.click.datasource.minEvictableIdleTimeMillis = 300000
spring.click.datasource.keepAlive = true
spring.click.datasource.poolPreparedStatements = true
spring.click.datasource.maxPoolPreparedStatementPerConnectionSize = 20
spring.click.datasource.removeAbandoned = true
spring.click.datasource.removeAbandonedTime = 1800
spring.click.datasource.driverClass = ru.yandex.clickhouse.ClickHouseDriver
app.logging.path=/data/coohua/logs/dispense-timer

rta.datasource.url = *********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=CTT
rta.datasource.username = coohua
rta.datasource.password = Q9lMQNnkCUw7