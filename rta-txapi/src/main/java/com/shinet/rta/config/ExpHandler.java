package com.shinet.rta.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.HttpMediaTypeNotSupportedException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

//@ControllerAdvice
@Slf4j
public class ExpHandler {
//    @ExceptionHandler(value = HttpMediaTypeNotSupportedException.class)
    public String nologExp(HttpMediaTypeNotSupportedException exceptiond, HttpServletRequest request, HttpServletResponse httpServletResponse) throws IOException {
        String rspStrs = request.getRequestURL().toString();
        log.info("bytertareq " + rspStrs);
        return "200";
    }
}
