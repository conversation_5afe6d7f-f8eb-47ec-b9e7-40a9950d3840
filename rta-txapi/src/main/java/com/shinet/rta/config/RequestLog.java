package com.shinet.rta.config;

import com.coohua.core.caf.dispense.kafka.LogKafkaSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

//@Component
@Aspect
@Slf4j
public class RequestLog {
    @Autowired
    LogKafkaSender logKafkaSender;

    @Around(value = "@annotation(org.springframework.web.bind.annotation.PostMapping)", argNames = "jp")
    public Object logAopWritePost(ProceedingJoinPoint jp) throws Throwable {
        return logAopWrite(jp);
    }

    @Around(value = "@annotation(org.springframework.web.bind.annotation.GetMapping)", argNames = "jp")
    public Object logAopWriteGet(ProceedingJoinPoint jp) throws Throwable {
        return logAopWrite(jp);
    }

    @Around(value = "@annotation(org.springframework.web.bind.annotation.RequestMapping)", argNames = "jp")
    public Object logAopWrite(ProceedingJoinPoint jp) throws Throwable {
        long curtime = System.currentTimeMillis();
        HttpServletRequest request = null;
        StringBuilder sbstr = new StringBuilder();
        Object[] args = jp.getArgs();
        try {
            for (Object obj : args) {
                if (obj instanceof HttpServletRequest) {
                    request = (HttpServletRequest) obj;
                }
            }
            RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
            if (requestAttributes == null) {
                return jp.proceed();
            }
            if (request == null) {
                request = ((ServletRequestAttributes) requestAttributes).getRequest();
            }
        } catch (Exception e) {
            log.error("", e);
            throw e;
        }
        boolean isP = true;

        if(StringUtils.contains( request.getRequestURL(),"/error")){
            isP = false;
        }
        if(isP){
            log.info("bytertareqstart " + request.getRequestURL().toString());
        }

        Object object = jp.proceed();// 执行该方法
        if(isP){
            try {
                String rspStrs = request.getRequestURL().toString() + "?" + sbstr.toString()  + "\tcost [" + (System.currentTimeMillis() - curtime) + "] rsp "+object.toString();
                log.info("bytertareq " + rspStrs);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return object;
    }
}
