package com.shinet.rta.config;

import com.aliyun.openservices.log.log4j2.LoghubAppender;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;

import java.util.Map;

public class Log4j2ContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
        try {
            LoggerContext loggerContext = LoggerContext.getContext(false);
            Configuration logConfiguration = loggerContext.getConfiguration();
            Map<String, Appender> appenderMap = logConfiguration.getAppenders();
            for (Appender appender : appenderMap.values()) {
                if (appender instanceof LoghubAppender) {
                    LoghubAppender loghubAppender = (LoghubAppender) appender;
                    loghubAppender.setSource("rta-txapi");
                }
            }
        } catch (Exception ex) {
            throw new IllegalStateException("Could not initialize Log4J2 logging from Ex:", ex);
        }
    }
}