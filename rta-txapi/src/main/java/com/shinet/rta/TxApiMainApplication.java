package com.shinet.rta;


import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.shinet.rta.config.Log4j2ContextInitializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.httpclient.EnableHttpBioClient;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;

@SpringBootApplication(scanBasePackages = {"com.coohua.core.caf.dispense","com.coohua.rta","com.shinet.rta"})
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health","rta.switcher",
        // DB配置
        "bp.db.dispense",
        "bp.redis.dispense",
        "rta.redis"
})
@EnableJedisClusterClient(namespace = "bp-dispense")
@EnableJedisClusterClient(namespace = "rta-redis")
@EnableAutoChangeApolloConfig
@EnableScheduling
@EnableCaching
@EnableHttpBioClient
@Slf4j
public class TxApiMainApplication {

    public static void main(String[] args) {
        try {
            SpringApplication springApplication = new SpringApplication(TxApiMainApplication.class);
            springApplication.addInitializers(new Log4j2ContextInitializer());
            ConfigurableApplicationContext context = springApplication.run();
//            RtaProtos.RtaRequest request = RtaProtos.RtaRequest.newBuilder().setDevice(
//                    RtaProtos.RtaRequest.Device.newBuilder()
//                            .setOs(RtaProtos.RtaRequest.OperatingSystem.OS_IOS)
//                    .setOaid("f4437c5a-7663-4c38-9bd9-729fdeb68e54").build()).build();
//            context.getBean(TencentRtaController.class).byteRta(request);
            log.info("启动 TxApiMainApplication333 完成");
        }catch (Exception e){
            log.error("",e);
        }
    }

}
