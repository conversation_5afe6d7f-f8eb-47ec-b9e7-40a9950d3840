package com.shinet.rta.controller;


import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.CaidService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.dto.resp.PChildsClssT;
import com.coohua.rta.entity.RtaCfg;
import com.coohua.rta.entity.RtaProductArpu;
import com.coohua.rta.entity.RtaUpprice;
import com.coohua.rta.entity.RtaUppriceSub;
import com.coohua.rta.enums.DspType;
import com.coohua.rta.enums.PlatFmType;
import com.coohua.rta.service.RtaCounterPrint;
import com.coohua.rta.service.cache.RtaCacheSch;
import com.coohua.rta.service.strategy.TxStrPriceFltService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.Set;


@Controller
@RequestMapping("/dispense/rta/")
@Slf4j
public class TencentRtaController {
    @Autowired
    TxStrPriceFltService txStrPriceFltService;
    @Autowired
    RtaSwitcher rtaSwitcher;

//    @RequestMapping(value = "/tencentRta", consumes = {"application/x-protobuf"}, produces = {"application/x-protobuf"})
//    public @ResponseBody
//    RtaProtos.RtaResponse byteRta(@RequestBody RtaProtos.RtaRequest request) {
//        if (rtaSwitcher.rtaclog) {
//            log.info("tencentRta请求 " + request.toString());
//        }
//        RtaProtos.RtaResponse.Builder builderRsp = RtaProtos.RtaResponse.newBuilder();
//        try {
////            if (rtaSwitcher.directJj) {
////                builderRsp.addOutTargetId("12002");
////                builderRsp.setCode(0);//竞价
////                RtaCounterPrint.jingjiaNUm.incrementAndGet();
////                RtaProtos.RtaResponse rsp = builderRsp.build();
////                return rsp;
////            }
//
//            PlatFmType platFmType = PlatFmType.android;
//            RtaProtos.RtaRequest.OperatingSystem operatingSystem = request.getDevice().getOs();
//            if (RtaProtos.RtaRequest.OperatingSystem.OS_IOS.equals(operatingSystem)) {
//                platFmType = PlatFmType.ios;
//            }
//
//            RtaProtos.RtaResponse rspTS = getTestRsp(request, builderRsp);
//            if (rspTS != null) {
//                return rspTS;
//            }
//            List<RtaCfg> rtaCfgList = rtaCacheSch.getRtaCfg(platFmType, DspType.GUANGDIANTONG);
//
//            List<RtaCfg> bidRtaCfgList = new ArrayList<>();
//            rtaCfgList.forEach(rtaCfg -> {
//                boolean isBid = needReqRtaId(request, rtaCfg);
//                if (isBid) {
//                    bidRtaCfgList.add(rtaCfg);
//                }
//            });
//
//
//            if (rtaSwitcher.priceFt) {
//                String targetId = "";
//                if (bidRtaCfgList.size() > 0) {
//                    long dnum = 0;
//                    for (RtaCfg rtaCfg : bidRtaCfgList) {
//                        if (StringUtils.isNotBlank(rtaCfg.getRtaId())) {
//                            PChildsClssT<RtaUpprice, RtaUppriceSub, RtaProductArpu> pChildsClssT = txStrPriceFltService.getTxFlt(request, rtaCfg);
//                            RtaProtos.RtaResponse.TargetInfo.Builder targetInfoBuilder = RtaProtos.RtaResponse.TargetInfo.newBuilder();
//
//                            if(pChildsClssT.t.getUppriceFloat()>=5d){
//                                pChildsClssT.t.setUppriceFloat(4d);
//                            }
//                            if(pChildsClssT.t.getUppriceFloat()<=0.2d){
//                                pChildsClssT.t.setUppriceFloat(0.25d);
//                            }
//
//                            targetInfoBuilder.setUserWeightFactor(pChildsClssT.t.getUppriceFloat().floatValue());
//                            targetInfoBuilder.setOutTargetId(rtaCfg.getRtaId() + "");
//                            builderRsp.addTargetInfos(targetInfoBuilder.build());
//                            targetId = rtaCfg.getRtaId();
//                            dnum++;
//                        }
//                    }
//                    /**
//                     * request_id:  "Jo825w_uEe2xLlJUAFwvSQ"
//                     * code:  0
//                     * out_target_id:  "sample"
//                     * target_infos: <
//                     *   out_target_id: "sample"
//                     *   user_weight_factor: 2.0 // 提升2倍竞争力
//                     * >
//                     */
//                    if (dnum > 0) {
//                        builderRsp.addOutTargetId(targetId);
//                        builderRsp.setCode(0);//竞价
//                        RtaCounterPrint.jingjiaNUm.incrementAndGet();
//                    } else {
//                        builderRsp.setCode(2);//不竞价
//                        RtaCounterPrint.bujinjiaNum.incrementAndGet();
//                    }
//                } else {
//                    builderRsp.setCode(2);//不竞价
//                }
//            } else {
//                if (bidRtaCfgList.size() > 0) {
//                    builderRsp.addOutTargetId(rtaCfgList.get(0).getRtaId());
//                    builderRsp.setCode(0);//竞价
//                    RtaProtos.RtaResponse.TargetInfo.Builder targetInfoBuilder = RtaProtos.RtaResponse.TargetInfo.newBuilder();
//                    targetInfoBuilder.setUserWeightFactor(0.3f);
//                    targetInfoBuilder.setOutTargetId("50");
//                    builderRsp.addTargetInfos(targetInfoBuilder.build());
//                    RtaCounterPrint.jingjiaNUm.incrementAndGet();
//                } else {
//                    builderRsp.setCode(2);//不竞价
//                    RtaCounterPrint.bujinjiaNum.incrementAndGet();
//                }
//            }
//        } catch (Exception e) {
//            builderRsp.addOutTargetId("5002");
//            builderRsp.setCode(0);//竞价
//            RtaCounterPrint.jingjiaNUm.incrementAndGet();
//        }
//        RtaProtos.RtaResponse rsp = builderRsp.build();
//        return rsp;
//    }

//    private boolean isCtOa(String oaid, Set<String> oascList) {
//        boolean isCtOa = false;
//        for (String oacfg : oascList) {
//            String md5oa = MD5Utils.getMd5Sum(oacfg);
//
////            log.info("biduitxrta "+oaid+" "+md5oa+" "+oacfg);
//            if (StringUtils.isNotBlank(oaid) && (StringUtils.equalsIgnoreCase(oaid, md5oa) || StringUtils.equalsIgnoreCase(oaid, oacfg))) {
//                isCtOa = true;
//                break;
//            }
//        }
//        return isCtOa;
//    }

//    private RtaProtos.RtaResponse getTestRsp(RtaProtos.RtaRequest request, RtaProtos.RtaResponse.Builder builderRsp) {
////        log.info("tencentRta测试2 "+rtaSwitcher.isTxRtaTest);
//        if (rtaSwitcher.isTxRtaTest) {
//            String oaid = request.getDevice().getOaidMd5Sum();
//            String imei = request.getDevice().getImeiMd5Sum();
//
////            log.info("tencentRta测试3 "+oaid+" " + request.toString());
//            if ((StringUtils.isNotBlank(oaid) && isCtOa(oaid, rtaSwitcher.jingjiaOaids)) || (StringUtils.isNotBlank(imei) && isCtOa(imei, rtaSwitcher.jingjiaOaids))) {
//                Random random = new Random();
//                Double dfloat = DoubleUtil.getBoundDouble(random.nextInt(4) * 1d,1d, 2d,rtaSwitcher.boundrate);
//
//                if(dfloat>=5d){
//                    dfloat = 4d;
//                }
//                if(dfloat<=0.2d){
//                    dfloat = 0.25d;
//                }
//                RtaProtos.RtaResponse.TargetInfo.Builder targetInfoBuilder = RtaProtos.RtaResponse.TargetInfo.newBuilder();
//                targetInfoBuilder.setUserWeightFactor(dfloat.floatValue());
//                targetInfoBuilder.setOutTargetId("50");
//                builderRsp.addTargetInfos(targetInfoBuilder.build());
//                builderRsp.setCode(0);
//                RtaCounterPrint.jingjiaNUm.incrementAndGet();
//                builderRsp.addOutTargetId("50");
//
//                RtaProtos.RtaResponse rsp = builderRsp.build();
//
////                log.info("tencentRta测试4 "+dfloat+" " + request.toString());
//                return rsp;
//            } else if ((StringUtils.isNotBlank(oaid) && isCtOa(oaid, rtaSwitcher.exldOaids)) || StringUtils.isNotBlank(imei) && isCtOa(imei, rtaSwitcher.exldOaids) ) {
//                builderRsp.setCode(2);//不竞价
//                RtaProtos.RtaResponse rsp = builderRsp.build();
//                RtaCounterPrint.bujinjiaNum.incrementAndGet();
////                log.info("tencentRta测试5 "+request.getDevice().getOaid()+" " + request.toString());
//                return rsp;
//            }
//
//        }
//        return null;
//    }
//
//
//    @Autowired
//    RedisEventService redisEventService;
//    @Autowired
//    RtaCacheSch rtaCacheSch;
//
//    private boolean needReqRtaId(RtaProtos.RtaRequest request, RtaCfg rtaCfg) {
//        RtaProtos.RtaRequest.OperatingSystem operatingSystem = request.getDevice().getOs();
//
//        UserEventReq userEventReq = new UserEventReq();
//        userEventReq.setProduct(rtaCfg.getProduct());
//
//        OcpcEvent activeEvt = null;
//
//
//        if (RtaProtos.RtaRequest.OperatingSystem.OS_IOS.equals(operatingSystem)) {
//            userEventReq.setOs("ios");
//            java.util.List<RtaProtos.RtaRequest.Device.QaidInfo> qaidInfos = request.getDevice().getQaidInfosList();
//            String caidStr = "";
//            for (RtaProtos.RtaRequest.Device.QaidInfo qaidInfo : qaidInfos) {
//                String qaid = qaidInfo.getQaid();
//                String oversinStr = qaidInfo.getOriginVersion();
//                if (StringUtils.equalsIgnoreCase(oversinStr, CaidService.caidVersion)) {
//                    caidStr = qaid;
//                }
//            }
//            if (StringUtils.isNotBlank(caidStr)) {
//                userEventReq.setCaid(caidStr);
//            }
//
//            if (StringUtils.isNotBlank(request.getDevice().getIdfaMd5Sum())) {
//                userEventReq.setIdfa(request.getDevice().getIdfaMd5Sum());
//                userEventReq.setIdfa2(request.getDevice().getIdfaMd5Sum());
//            }
//        } else {
//            userEventReq.setOs("android");
//            if (StringUtils.isNotBlank(request.getDevice().getOaid())) {
//                userEventReq.setOaid(request.getDevice().getOaid());
//                userEventReq.setOaid2(request.getDevice().getOaid());
//            }
//
//            if (StringUtils.isNotBlank(request.getDevice().getIdfaMd5Sum())) {
//                userEventReq.setOcpcDeviceId(request.getDevice().getIdfaMd5Sum());
//                userEventReq.setSourceDeviceId(request.getDevice().getIdfaMd5Sum());
//            }
//
////            if(StringUtils.isNotBlank(request.getDevice().getAndroidIdMd5Sum())){
////                userEventReq.setAndroidMd5Id(request.getDevice().getAndroidIdMd5Sum());
////            }
//        }
//        if (StringUtils.isNotBlank(request.getDevice().getIp())) {
//            userEventReq.setIp(request.getDevice().getIp());
//        }
//        activeEvt = redisEventService.getActiveEvent(userEventReq);
////        if(activeEvt!=null){
////            log.info("tencentrta归因成功 "+ JSON.toJSONString(userEventReq));
////        }
//        return activeEvt == null;
//    }

//    public static void main(String[] args){
//        List<String> alist = Lists.newArrayList(new String[]{
//                "6b0aeb2de13f02ae",
//                "93825cad342d9212",
//                "8781f76ff7b8a21a",
//                "a6d49c0aac5dd029",
//                "b8d4ebc06a2c4901",
//                "042716a6ed6c0621",
//                "59773acb01f99e59",
//                "9336821bcfa7fb3d",
//                "312df5045cae8968",
//                "a4412c33b2284af2"
//        });
//
//        for(String str : alist){
//            System.out.println(MD5Utils.getMd5Sum(str));
//        }
//    }
}
