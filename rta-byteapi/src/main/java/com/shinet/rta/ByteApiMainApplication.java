package com.shinet.rta;


import com.coohua.caf.core.apollo.EnableAutoChangeApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.shinet.rta.config.Log4j2ContextInitializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;
import top.zrbcool.pepper.boot.httpclient.EnableHttpBioClient;
import top.zrbcool.pepper.boot.jediscluster.EnableJedisClusterClient;
import top.zrbcool.pepper.boot.motan.EnableMotan;

@SpringBootApplication(scanBasePackages = {"com.coohua.rta","com.shinet.rta"})
@EnableApolloConfig(value = {"application", "caf.log.level", "caf.base.registry", "ad.base.health","rta.switcher",
        // DB配置
        "rta.redis.new",
        //rpc配置
        "ad.user.ad.api.referer",
        "relationship.redis"
        })
@EnableJedisClusterClient(namespace = "rta-redis-new")
@EnableMotan(namespace = "user-event")
@EnableAutoChangeApolloConfig
@EnableScheduling
@EnableCaching
@EnableHttpBioClient
@Slf4j
public class ByteApiMainApplication {

    public static void main(String[] args) {
        try{
            SpringApplication springApplication = new SpringApplication(ByteApiMainApplication.class);
            springApplication.addInitializers(new Log4j2ContextInitializer());
            ConfigurableApplicationContext configurableApplicationContext = springApplication.run(args);
//            RtaApi.Req req = RtaApi.Req.newBuilder()
//                    .setDevice(RtaApi.Device.newBuilder()
//                            .setCaid("1231312")
//                            .setDeviceOsType(RtaApi.DeviceOsType.DEVICE_IOS).build()).build();
//            configurableApplicationContext.getBean(RedisEventService.class).getActiveEventByRedis(req);
        }catch (Exception e){
            log.error("",e);
        }

        log.info("rta日志测试手机SLS");
    }

}
