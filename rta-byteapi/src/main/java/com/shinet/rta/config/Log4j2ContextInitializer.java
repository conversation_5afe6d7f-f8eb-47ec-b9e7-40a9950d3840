package com.shinet.rta.config;

import com.aliyun.openservices.log.log4j2.LoghubAppender;
import lombok.SneakyThrows;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.ConfigurationFactory;
import org.apache.logging.log4j.core.config.ConfigurationSource;
import org.apache.logging.log4j.core.config.Configurator;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.util.ResourceUtils;

import java.io.InputStream;
import java.net.InetAddress;
import java.net.URL;
import java.util.Map;

public class Log4j2ContextInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @SneakyThrows
    @Override
    public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
        String hostName = InetAddress.getLocalHost().getHostName();
        String urlName = "";

        if (hostName.contains("hs-rta-byte")){
            urlName = "log4j2-caf-hs-pro.xml";
        }else {
            urlName = "log4j2-caf-pro.xml";
        }

        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        URL configUrl = classLoader.getResource(urlName);
        if (configUrl == null) {
            throw new IllegalStateException("Cannot find log4j2 config: " + urlName);
        }
        // 优先使用 自定义 log4j2 配置文件
        try(InputStream configStream = configUrl.openStream()) {
            ConfigurationSource source = new ConfigurationSource(configStream, configUrl);
            LoggerContext loggerContext = Configurator.initialize(null, source);
            Configuration logConfiguration = ConfigurationFactory.getInstance().getConfiguration(loggerContext, source);
//            loggerContext.start(logConfiguration);
            //        //原代码
//        try {

//            LoggerContext loggerContext = LoggerContext.getContext(false);
//            Configuration logConfiguration = loggerContext.getConfiguration();
            Map<String, Appender> appenderMap = logConfiguration.getAppenders();
            for (Appender appender : appenderMap.values()) {
                if (appender instanceof LoghubAppender) {
                    LoghubAppender loghubAppender = (LoghubAppender) appender;
                    loghubAppender.setSource("rta-byteapi");
                }
            }
        } catch (Exception ex) {
            throw new IllegalStateException("Could not initialize Log4J2 logging from Ex:", ex);
        }
    }
}