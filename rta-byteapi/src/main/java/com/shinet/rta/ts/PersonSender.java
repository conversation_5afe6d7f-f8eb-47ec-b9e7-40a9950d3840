package com.shinet.rta.ts;

import com.coohua.rta.dto.req.BaiduRtaRequest;
import com.coohua.rta.dto.req.BaiduRtaResponse;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.dto.req.kuaishou.OsType;
import com.coohua.rta.dto.req.kuaishou.Req;
import com.coohua.rta.dto.req.kuaishou.Resp;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

public class PersonSender {

    public void sendPerson() {
        try {
            /**
             *      * platform: ANDROID
             *      * did: "0007f455b478ad0d3f5edfc4968abe44"
             *      * slot_id: 12345
             *      * did_type: IMEI_MD5
             *      * device_type: PHONE
             *      * req_id: "1234easdf"
             *      * source: "union"
             *      * rta_ids: 123456
             *      * device {
             *      *   imei_md5: "0007f455b478ad0d3f5edfc4968abe44"
             *      * }
             *      * @param request
             *      * @return
             *      */
//            String url = "http://localhost:8080/dispense/rta/byte";
            String url = "http://localhost:8080/dispense/rta/byte/test";
//			String url = "http://rta-ks.shinet.cn/dispense/rta/test/byteRta";
//			String url = "http://rta-ks.shinet.cn/dispense/rta/byte";
//			String url = "http://localhost:8080/dispense/rta/jsbyteRta";
            HttpPost httpPost = new HttpPost(url);

            httpPost.setHeader("Content-Type", "application/x-protobuf");

            RtaApi.Req.Builder reqBuilder = RtaApi.Req.newBuilder();
//			reqBuilder.addRtaIds(123456L);
//			reqBuilder.setPlatform(RtaApi.PlatformType.IOS);
//			RtaApi.Device.Builder deviceBuilder = RtaApi.Device.newBuilder();
//			deviceBuilder.setOaid("81feb078-e47b-4c02-8cfa-7eef994b3ee4");
//			deviceBuilder.setIdfa("2216916B-5327-463A-802B-DE167ACCB84");
//			reqBuilder.setDidType(RtaApi.DidType.IDFA);
//			reqBuilder.setDevice(deviceBuilder.build());

            reqBuilder.addRtaIds(123456L);
            reqBuilder.addRtaIds(22902L);
            reqBuilder.addRtaIds(22903L);
            reqBuilder.addRtaIds(22904L);
            reqBuilder.setPlatform(RtaApi.PlatformType.ANDROID);
            RtaApi.Device.Builder deviceBuilder = RtaApi.Device.newBuilder();
            deviceBuilder.setOaid("81feb078-e47b-4c02-8cfa-7eef994b3ee4");
//			deviceBuilder.setIdfa("2216916B-5327-463A-802B-DE167ACCB84");
            reqBuilder.setDidType(RtaApi.DidType.OAID);
            reqBuilder.setDevice(deviceBuilder.build());
            byte[] personContent = reqBuilder.build().toByteArray();

            httpPost.setEntity(new ByteArrayEntity(personContent));

            HttpClient httpClient = HttpClientBuilder.create().build();
            HttpResponse response = httpClient.execute(httpPost);

            int code = response.getStatusLine().getStatusCode();
            System.out.println("response code: " + code);

            HttpEntity entity = response.getEntity();

            byte[] responseContent = EntityUtils.toByteArray(entity);

            RtaApi.Rsp fromPerson = RtaApi.Rsp.parseFrom(responseContent);
            System.out.println("id: " + fromPerson.getStatusCode());
            System.out.println("bidtype: " + fromPerson.getBidType());
            System.out.println("name: " + fromPerson.toString() + fromPerson.getUserInfosList());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void sendKsPersonTest() {
        try {
            String url = "http://localhost:8080/dispense/rta/ks";
//			String url = "http://rta-ks.shinet.cn/dispense/rta/test/byteRta";
//			String url = "http://rta-ks.shinet.cn/dispense/rta/byte";
//			String url = "http://localhost:8080/dispense/rta/jsbyteRta";
            HttpPost httpPost = new HttpPost(url);

            httpPost.setHeader("Content-Type", "application/x-protobuf");
            Req.Builder reqBuilder = Req.newBuilder();

            reqBuilder.setId("123");
            reqBuilder.setOsType(OsType.ANDROID);
//            reqBuilder.setDidMd5("0f4e4715b415a794082644e92f72779b");
            // 安卓 传oaid 或 oaidMd5
            reqBuilder.setOaid("482c4a5fae758f8a690c6b33d76b3da9d08dc9c06c21b7052f1138b007ce53d3");
            reqBuilder.setOaidMd5("********************************");
            // ios 传caid
                reqBuilder.setLastCaid("53c8726839fec69514d0f7091c257461");
                reqBuilder.setCurrentCaid("53c8726839fec69514d0f7091c257461");
            byte[] personContent = reqBuilder.build().toByteArray();

            httpPost.setEntity(new ByteArrayEntity(personContent));

            HttpClient httpClient = HttpClientBuilder.create().build();
            HttpResponse response = httpClient.execute(httpPost);

            int code = response.getStatusLine().getStatusCode();
            System.out.println("response code: " + code);

            HttpEntity entity = response.getEntity();

            byte[] responseContent = EntityUtils.toByteArray(entity);

            Resp fromPerson = Resp.parseFrom(responseContent);

            if (fromPerson != null) {


                JsonFormat.Printer printer = JsonFormat.printer().includingDefaultValueFields();
                String jsonString = null;

                try {
                    // 将 protobuf 对象转换为 JSON 字符串
                    jsonString = printer.print(fromPerson);
                    System.out.println(jsonString);
                } catch (InvalidProtocolBufferException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void sendBaiduPersonTest() {
        try {
            String url = "http://localhost:8080/dispense/rta/baidu";
//			String url = "http://rta-ks.shinet.cn/dispense/rta/test/byteRta";
//			String url = "http://rta-ks.shinet.cn/dispense/rta/byte";
//			String url = "http://localhost:8080/dispense/rta/jsbyteRta";
            HttpPost httpPost = new HttpPost(url);

            httpPost.setHeader("Content-Type", "application/x-protobuf");

            BaiduRtaRequest.RtaApiRequest.Builder reqBuilder = BaiduRtaRequest.RtaApiRequest.newBuilder();

            reqBuilder.setQid(1L);
            reqBuilder.setOsType(BaiduRtaRequest.OsType.ANDROID);
            reqBuilder.setDeviceIdMd5("aavx");
            reqBuilder.setSignTime(123312L);
            reqBuilder.setToken("1123312aavx");
            BaiduRtaRequest.DeviceInfo.Builder deviceBuilder = BaiduRtaRequest.DeviceInfo.newBuilder();
            deviceBuilder.setOaid(ByteString.copyFromUtf8("333fac037281e4f7"));
//			deviceBuilder.setIdfa("2216916B-5327-463A-802B-DE167ACCB84");

            reqBuilder.setDeviceInfo(deviceBuilder.build());
            byte[] personContent = reqBuilder.build().toByteArray();

            httpPost.setEntity(new ByteArrayEntity(personContent));

            HttpClient httpClient = HttpClientBuilder.create().build();
            HttpResponse response = httpClient.execute(httpPost);

            int code = response.getStatusLine().getStatusCode();
            System.out.println("response code: " + code);

            HttpEntity entity = response.getEntity();

            byte[] responseContent = EntityUtils.toByteArray(entity);

            BaiduRtaResponse.RtaApiResponse fromPerson = BaiduRtaResponse.RtaApiResponse.parseFrom(responseContent);

            if (fromPerson != null) {


                JsonFormat.Printer printer = JsonFormat.printer().includingDefaultValueFields();
                String jsonString = null;

                try {
                    // 将 protobuf 对象转换为 JSON 字符串
                    jsonString = printer.print(fromPerson);
                    System.out.println(jsonString);
                } catch (InvalidProtocolBufferException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) {
        new PersonSender().sendKsPersonTest();
    }

}
