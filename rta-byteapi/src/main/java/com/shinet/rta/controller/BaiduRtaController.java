package com.shinet.rta.controller;

import com.coohua.rta.dto.req.BaiduRtaRequest;
import com.coohua.rta.dto.req.BaiduRtaResponse;
import com.coohua.rta.service.RtaBaiduService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
    @RequestMapping("/dispense/rta/")
@Slf4j
public class BaiduRtaController {

    @Autowired
    RtaBaiduService rtaBaiduService;

    @RequestMapping(value = "/baidu", consumes = {"application/x-protobuf"}, produces = {"application/x-protobuf"})
    public @ResponseBody BaiduRtaResponse.RtaApiResponse byteRta(@RequestBody BaiduRtaRequest.RtaApiRequest request) {
        return rtaBaiduService.getRtaRsp(request);
    }

}
