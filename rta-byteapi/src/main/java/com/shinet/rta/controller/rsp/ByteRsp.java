/**
 * Copyright 2023 bejson.com
 */
package com.shinet.rta.controller.rsp;

import java.util.List;

/**
 * Auto-generated: 2023-11-24 11:26:44
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class ByteRsp {

    private int status_code;
    private int bid_type;
    private List<User_infos> user_infos;
    private String req_id;

    public static ByteRsp success() {
        ByteRsp byteRsp = new ByteRsp();
        byteRsp.setStatus_code(0);
        return byteRsp;
    }

    public void setStatus_code(int status_code) {
        this.status_code = status_code;
    }

    public int getStatus_code() {
        return status_code;
    }

    public void setBid_type(int bid_type) {
        this.bid_type = bid_type;
    }

    public int getBid_type() {
        return bid_type;
    }

    public void setUser_infos(List<User_infos> user_infos) {
        this.user_infos = user_infos;
    }

    public List<User_infos> getUser_infos() {
        return user_infos;
    }

    public void setReq_id(String req_id) {
        this.req_id = req_id;
    }

    public String getReq_id() {
        return req_id;
    }

}