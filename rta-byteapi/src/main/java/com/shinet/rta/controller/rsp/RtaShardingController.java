package com.shinet.rta.controller.rsp;

import com.coohua.rta.service.sharding.RtaShardingService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/rta/sharding")
public class RtaShardingController {

    @Resource
    private RtaShardingService rtaShardingService;

    @PostMapping("test")
    @ResponseBody
    public ByteRsp test() {
        rtaShardingService.testHash();
        return ByteRsp.success();
    }

}
