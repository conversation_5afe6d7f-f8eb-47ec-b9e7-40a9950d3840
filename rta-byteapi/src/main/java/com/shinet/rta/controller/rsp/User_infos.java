/**
  * Copyright 2023 bejson.com 
  */
package com.shinet.rta.controller.rsp;
import java.util.List;

/**
 * Auto-generated: 2023-11-24 11:26:44
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class User_infos {

    private long rta_id;
    private boolean is_interested;
    private List<User_scores> user_scores;
    public void setRta_id(long rta_id) {
         this.rta_id = rta_id;
     }
     public long getRta_id() {
         return rta_id;
     }

    public void setIs_interested(boolean is_interested) {
         this.is_interested = is_interested;
     }
     public boolean getIs_interested() {
         return is_interested;
     }

    public void setUser_scores(List<User_scores> user_scores) {
         this.user_scores = user_scores;
     }
     public List<User_scores> getUser_scores() {
         return user_scores;
     }

}