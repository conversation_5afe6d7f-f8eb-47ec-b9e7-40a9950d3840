package com.shinet.rta.controller;


import com.coohua.rta.config.RtaSwitcher;
import com.coohua.rta.dto.req.RtaApi;
import com.coohua.rta.service.RtaByteConfigProductService;
import com.coohua.rta.service.RtaByteService;
import com.coohua.rta.service.RtaCounterPrint;
import com.coohua.rta.service.cache.RtaCacheSch;
import com.coohua.rta.service.strategy.StrPriceUpflMagService;
import com.coohua.rta.service.strategy.StrRtaNorepeatService;
import com.coohua.rta.service.strategy.StrRtaSkipNorepeatService;
import com.coohua.rta.utils.ABShardingUtil;
import com.coohua.rta.vo.RtaByteConfigProductVo;
import com.shinet.rta.controller.rsp.ByteRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Controller
@RequestMapping("/dispense/rta/")
@Slf4j
public class ByteRtaController {
    @Autowired
    RtaCacheSch rtaCacheSch;
    /**
     * platform: ANDROID
     * did: "0007f455b478ad0d3f5edfc4968abe44"
     * slot_id: 12345
     * did_type: IMEI_MD5
     * device_type: PHONE
     * req_id: "1234easdf"
     * source: "union"
     * rta_ids: 123456
     * device {
     * imei_md5: "0007f455b478ad0d3f5edfc4968abe44"
     * }
     *
     * @param request
     * @return
     */
//    @Autowired
//    RedisEventService redisEventService;

    @Autowired
    StrRtaNorepeatService strRtaNorepeatService;
    @Autowired
    StrRtaSkipNorepeatService strRtaSkipNorepeatService;


    @Value("${rta.logflag:false}")
    public boolean logFlag;
    @Autowired
    StrPriceUpflMagService strPriceUpflMagService;
    @Autowired
    RtaSwitcher rtaSwitcher;

    @Resource
    private RtaByteService rtaByteService;
    @Resource
    private RtaByteConfigProductService rtaByteConfigProductService;

    @RequestMapping(value = "/byteRta", consumes = {"application/x-protobuf"}, produces = {"application/x-protobuf"})
    public @ResponseBody RtaApi.Rsp byteRtaOld(@RequestBody RtaApi.Req request) {
        log.info("byteRtaReq: {}", request);
        RtaApi.EnableStrategy enableStrategy = request.getEnableStrategy();
        Integer strgNum = enableStrategy.getNumber();
        RtaApi.Rsp rsp = null;
        if (logFlag) {
            log.info("byteRtaDevice:{\"oaid\":\"{}\",\"imei_md5\":\"{}\"}", request.getDevice().getOaid(), request.getDevice().getImeiMd5());
        }

        if (rtaSwitcher.rtaStrgy == 1) {
            rsp = strRtaNorepeatService.getRtaRsp(request, strgNum, request.getReqId());
        } else if (rtaSwitcher.rtaStrgy == 5) {
            rsp = strRtaSkipNorepeatService.getRtaRsp(request, strgNum, request.getReqId());
        } else {
            rsp = strPriceUpflMagService.getRtaRsp(request);
        }
        if (rsp.getUserInfosList().size() > 0) {
            RtaCounterPrint.jingjiaNUm.incrementAndGet();
        } else {
            RtaCounterPrint.bujinjiaNum.incrementAndGet();
        }

        return rsp;
    }

    @RequestMapping(value = "/byte", consumes = {"application/x-protobuf"}, produces = {"application/x-protobuf"})
    public @ResponseBody RtaApi.Rsp byteRta(@RequestBody RtaApi.Req request) {
//        log.info("byteRtaReq: {}", request);
//        log.info("byteRtaDevice:{\"oaid\":\"{}\",\"slotId\":\"{}\", \"caid1\":\"{}\", \"caid2\":\"{}\", \"rtaIds\":\"{}\"}", request.getDevice().getOaid(),request.getSlotId(), request.getDevice().getCaid1(), request.getDevice().getCaid2(), request.getRtaIdsList());
//        log.info("byteRtaDevice:{\"os\":\"{}\",\"oaid\":\"{}\",\"slotId\":\"{}\", \"caid1\":\"{}\", \"caid2\":\"{}\"}", request.getPlatform(), request.getDevice().getOaid(),request.getSlotId(), request.getDevice().getCaid1(), request.getDevice().getCaid2());

        RtaApi.Rsp rsp = null;
        try {
            rsp = rtaByteService.getRtaRsp(request);
        } catch (Exception e) {
            log.error("byteRta错误", e);
            return RtaApi.Rsp.newBuilder().setStatusCode(500).build();
        }

        return rsp;
    }


    @RequestMapping(value = "/byte/test", consumes = {"application/x-protobuf"}, produces = {"application/x-protobuf"})
    public @ResponseBody RtaApi.Rsp byteRtatest(@RequestBody RtaApi.Req request) {

        RtaApi.Rsp rsp = null;
        try {
            List<String> deviceList = ABShardingUtil.getDeviceList();
            List<String> strings = deviceList.subList(0, 1000);
            for (String string : strings) {
                RtaApi.Req.Builder reqBuilder = RtaApi.Req.newBuilder();
                reqBuilder.setPlatform(RtaApi.PlatformType.ANDROID);
                RtaApi.Device.Builder deviceBuilder = RtaApi.Device.newBuilder();
                deviceBuilder.setOaid(string);
                reqBuilder.setDidType(RtaApi.DidType.OAID);
                reqBuilder.setDevice(deviceBuilder.build());
                rsp = rtaByteService.getRtaRsp(reqBuilder.build());
            }
        } catch (Exception e) {
            log.error("byteRta错误", e);
            return RtaApi.Rsp.newBuilder().setStatusCode(500).build();
        }

        return rsp;
    }


    @RequestMapping(value = "/test/byteRta", consumes = {"application/x-protobuf"}, produces = {"application/x-protobuf"})
    public @ResponseBody RtaApi.Rsp testByteRta(@RequestBody RtaApi.Req request) {
        RtaApi.EnableStrategy enableStrategy = request.getEnableStrategy();
        Integer strgNum = enableStrategy.getNumber();
        log.info("byteRtaReq: {}", request);
        log.info("byteRtaDevice:{\"oaid\":\"{}\",\"imei_md5\":\"{}\", \"idfa\":\"{}\", \"rtaIds\":\"{}\"}", request.getDevice().getOaid(), request.getDevice().getImeiMd5(), request.getDevice().getIdfa(), request.getRtaIdsList());

        RtaApi.Rsp rsp = strRtaNorepeatService.getTestRsp(request, strgNum, request.getReqId());

        return rsp;
    }

//    @PostMapping("/saveRtaIdAndProductBatch")
//    @ResponseBody
//    public ByteRsp saveRtaIdAndProductBatch(@RequestBody RtaByteConfigProductVo rtaByteConfigProductVo) {
//
//        rtaByteConfigProductService.saveRtaIdAndProductBatch(rtaByteConfigProductVo);
//
//        return ByteRsp.success();
//    }


    @PostMapping("/convertAndSaveRtaBatch/filePath")
    @ResponseBody
    public ByteRsp convertAndSaveRtaBatchByFilePath(@RequestBody RtaByteConfigProductVo rtaByteConfigProductVo) {

        rtaByteConfigProductService.convertAndSaveRtaIdAndProductBatch(rtaByteConfigProductVo);

        return ByteRsp.success();
    }

    @PostMapping("/test/templete")
    @ResponseBody
    public ByteRsp testTemplete() {

        List<String> deviceList = ABShardingUtil.getDeviceList();
//        List<String> deviceList = new ArrayList<>();
        deviceList.add("2577e0715ba00d0ec3dc03bf9c31bf091beafe0ea95c3e01d5659ec4cc348ada");
        deviceList.add("01bb6fac5c11f745dde9178a7c57341bb4fcd0fbb479990f45efa73290bf8fbd");
        deviceList.add("01eba22f-04ce-4022-94ee-e5c83703b88a");
        deviceList.add("01EE124B60BE4E44B30B2D445D9580C2d55202e5169304aa5854534f79407011");
        deviceList.add("0223B9A6E73E428CA4332FA6B0E6AB1F31e4212995795cfc16a42c674dc6a4a2");
        deviceList.add("0232f1ee-cbe5-4bb5-873a-360d5ac02ca6");
        deviceList.add("029B6E136EEA4BFDA361A43F720B1BD158c71c4f786046394fcc84a3639f0c59");
        deviceList.add("029d980de8764a610e362ec4a22f0f380135ba86e4307640440adbac1c5d74a8");
        deviceList.add("030249bbd423850d");
        deviceList.add("0365698bb8a03e0c");
        deviceList.add("9f83096f9b587a3f");
        deviceList.add("50a6fa9b28d21cda");
        deviceList.stream().forEach(e -> {
            RtaApi.Req.Builder reqBuilder = RtaApi.Req.newBuilder();

            reqBuilder.addRtaIds(123456L);
            reqBuilder.addRtaIds(22902L);
            reqBuilder.addRtaIds(22903L);
            reqBuilder.addRtaIds(22904L);
            reqBuilder.setPlatform(RtaApi.PlatformType.ANDROID);
            RtaApi.Device.Builder deviceBuilder = RtaApi.Device.newBuilder();
            deviceBuilder.setOaid(e);
            reqBuilder.setDidType(RtaApi.DidType.OAID);
            reqBuilder.setDevice(deviceBuilder.build());
            RtaApi.Req build = reqBuilder.build();

            rtaByteService.getRtaRsp(build);

        });


        return ByteRsp.success();
    }




}
