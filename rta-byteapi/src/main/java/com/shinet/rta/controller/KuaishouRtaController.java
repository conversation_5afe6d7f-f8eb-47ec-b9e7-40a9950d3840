package com.shinet.rta.controller;


import com.coohua.rta.dto.req.kuaishou.OsType;
import com.coohua.rta.dto.req.kuaishou.Req;
import com.coohua.rta.dto.req.kuaishou.Resp;
import com.coohua.rta.service.RtaKuaishouService;
import com.coohua.rta.utils.ABShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;

@Controller
@RequestMapping("/dispense/rta/")
@Slf4j
public class KuaishouRtaController {

    @Autowired
    RtaKuaishouService rtaKuaishouService;

    @PostMapping(value = "/ks", consumes = {"application/x-protobuf"}, produces = {"application/x-protobuf"})
    public @ResponseBody Resp byteRta(@RequestBody Req request) {
        return rtaKuaishouService.getRtaRsp(request);
    }

    @PostMapping("test")
    public void test() {
//        List<String> deviceList = ABShardingUtil.getDeviceList();
        List<String> deviceList = new ArrayList<>();
        deviceList.add("1d5a3aed98df69a4"); // 0
        deviceList.add("31C08FB6FED149BCA7845695C8E474162eeaa956097f7dc6e18e6735eb6ba08a"); // 0
        deviceList.add("e4817e1d-9c3d-4549-924f-c0d4419de71e"); // 1
        deviceList.add("ea4e80b5537bd097"); // 1
        deviceList.add("b51f190281f842cc4714c2f192cf1fff3748f849354e3f523937296993fa591d"); // 2
        deviceList.add("ced2f347-a5cd-410d-b602-047b2ebd17a9"); // 2
        deviceList.forEach(device->{
            Req.Builder reqBuilder = Req.newBuilder();

            reqBuilder.setId("123");
            reqBuilder.setOsType(OsType.ANDROID);
//            reqBuilder.setDidMd5("0f4e4715b415a794082644e92f72779b");
            // 安卓 传oaid 或 oaidMd5
            reqBuilder.setOaid(device);
            reqBuilder.setOaidMd5("15c53eb1bfc27fdee59f4ad688faa2f2");
            rtaKuaishouService.getRtaRsp(reqBuilder.build());
        });
    }

}
