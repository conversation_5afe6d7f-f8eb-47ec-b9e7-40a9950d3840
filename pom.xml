<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>caf-boot-parent</artifactId>
        <groupId>com.coohua.caf</groupId>
        <version>2.1.16</version>
    </parent>

    <groupId>com.coohua</groupId>
    <artifactId>core-caf-dispense</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <revision>0.0.1-SNAPSHOT</revision>
        <flatten-maven-plugin.version>1.0.1</flatten-maven-plugin.version>
    </properties>

    <modules>
        <module>dispense-biz</module>
        <module>dispense-api</module>
        <module>rta-biz</module>
        <module>rta-byteapi</module>
        <module>rta-timer</module>
        <module>rta-txapi</module>
        <module>dispense-job</module>
        <module>dispense-timer</module>
    </modules>

    <profiles>
        <profile>
            <id>deploy-all</id>
            <modules>
                <module>dispense-biz</module>
                <module>dispense-api</module>
                <module>rta-byteapi</module>
                <module>rta-biz</module>
                <module>rta-timer</module>
                <module>rta-txapi</module>
                <module>dispense-job</module>
                <module>dispense-timer</module>
            </modules>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
<!--            &lt;!&ndash; https://mvnrepository.com/artifact/org.jetbrains.kotlin/kotlin-stdlib &ndash;&gt;-->
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>1.5.31</version>
            </dependency>
            <!--  protobuf 支持 Java 核心包-->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.12.0</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.8.6</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.spring4all</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
            <version>1.9.0.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.9.0</version>
        </dependency>
        <!--使用mybatis-plus代替原版mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.2.0</version>
        </dependency>
        <!--mybatis-plus代码生成器-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.2.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.29</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>coohua_releases</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>coohua_snapshots</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>coohua_3rdparty</id>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/thirdparty/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>${flatten-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase><!--指定绑定的声明周期阶段-->
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <!-- https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-maven-plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.5</version>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>coohua_releases</id>
            <name>releases</name>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>coohua_snapshots</id>
            <name>snapshots</name>
            <url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>


</project>
