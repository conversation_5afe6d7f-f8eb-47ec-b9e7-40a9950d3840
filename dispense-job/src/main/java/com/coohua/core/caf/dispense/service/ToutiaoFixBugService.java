package com.coohua.core.caf.dispense.service;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthToutiaoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.kafka.VideoAdReportEntitiy;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ToutiaoFixBugService {

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    RedissonClient redissonClient;
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    OcpcEventService ocpcEventService;

    private static final List<Integer> ValidAdTypes = Arrays.asList(3, 4);

    private static final AtomicLong log1 = new AtomicLong(0);
    private static final AtomicLong log2 = new AtomicLong(0);
    private static final AtomicLong log3 = new AtomicLong(0);

    public static final Map<Integer, AtomicLong> numMap = new ConcurrentHashMap<>();
    static {
        for (int i = 0; i <= 20; i++) {
            numMap.put(i, new AtomicLong(0));
        }
    }
    public static void incrNum(int i) {
        try {
            numMap.get(i).addAndGet(1);
        } catch (Exception e) {
            log.info("incrNumError", e);
        }
    }

    public static void main(String[] args) {
        int accNum = 11;
        BigDecimal accArpu = new BigDecimal("6.78");
        BigDecimal ecpm = accArpu.multiply(new BigDecimal(1000)).divide(new BigDecimal(accNum), 0, BigDecimal.ROUND_FLOOR);

        String format = String.format("1-%s,2-%s", accNum, accArpu.setScale(2, BigDecimal.ROUND_DOWN).toString());
        String format1 = String.format("1-%s,3-%s", accNum, ecpm.intValue());
        log.info(format);
        log.info(format1);
    }

    public void fixBug(String msg) {

        VideoAdReportEntitiy mqMsg = JSON.parseObject(msg, VideoAdReportEntitiy.class);

        int num = 0;
        incrNum(num++);

        if (log1.getAndIncrement() % 10000 == 0) {
            log.info("{} fixBug log1当前消息内容为 {}", log1.get(), msg);
        }

        // http://confluence.coohua.com/pages/viewpage.action?pageId=7675382
        // Table_plaque(3,"插屏"),
        // Rewarded_video(4,"激励视频"),
        // 目前仅这两种情况传给了项目组，并进行累计arpu计算
        if (!ValidAdTypes.contains(mqMsg.getAdType())) {
            return;
        }

        if (log2.getAndIncrement() % 10000 == 0) {
            log.info("{} {} fixBug log2当前消息内容为 {}", log1.get(), log2.get(), msg);
        }

        incrNum(num++);

        // 类似回传逻辑，构造userEventReq请求事件的参数
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setAppId(mqMsg.getAppId());
        userEventReq.setUserId(mqMsg.getUserId());
        userEventReq.setProduct(mqMsg.getProduct());
        userEventReq.setOaid(mqMsg.getOaid());
        userEventReq.setOs(mqMsg.getOs());
        // ios时 取device_id明文，android时取 md5(imei)
        userEventReq.setOcpcDeviceId("ios".equals(mqMsg.getOs()) ? mqMsg.getDeviceId() : MD5Utils.getMd5Sum(ObjectUtils.defaultIfNull(mqMsg.getImei(), mqMsg.getDeviceId())));
        // ios时 取device_id明文，android时取 imei明文
        userEventReq.setSourceDeviceId("ios".equals(mqMsg.getOs()) ? mqMsg.getDeviceId() : ObjectUtils.defaultIfNull(mqMsg.getImei(), mqMsg.getDeviceId()));
        userEventReq.setEventType(ToutiaoEventTypeEnum.KEY_EVENT.value);

        OcpcEvent ocpcEvent = null;
        ocpcEvent = hbaseEventService.getActiveEventByHbase(userEventReq);
        if (ocpcEvent == null) {
            ocpcEvent = redisEventService.getActiveEventByRedis(userEventReq);
        }
        if (ocpcEvent != null) {
            incrNum(num++);

            if (log3.getAndIncrement() % 1000 == 0) {
                log.info("{} {} {} fixBug log3当前消息内容为 {}", log1.get(), log2.get(), log3.get(), msg);
            }

            // 非头条激活的不处理
            if (!DspType.TOUTIAO.value.equals(ocpcEvent.getDsp())) {
                return;
            }

            incrNum(num++);

            // 计算累计arpu值（单位为分）
            BigDecimal accArpu = calculateAccArpu(userEventReq.getUserId(), ocpcEvent, mqMsg.getPrice());
            accArpu = accArpu.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_DOWN);

            if (accArpu.doubleValue() >= 0.5) {
                log.info("fixBug23  " + accArpu);
            }
            if (accArpu.doubleValue() >= 2) {
                log.info("fixBug24  " + accArpu);
            }

            // 计算累计次数
            int accNum = calculateAccNum(userEventReq.getUserId(), ocpcEvent);

            // 晚于 2022-07-02 13:30:38 的不继续处理
            if (mqMsg.getTimestamp() > 1656739838000L) {
                return;
            }

            incrNum(num++);


            Long minutes = (mqMsg.getTimestamp() - ocpcEvent.getClickTime().getTime()) / 1000 / 60;

            // 大于1天
            if (minutes > 1440) {
                return;
            }

            incrNum(num++);


            AuthToutiaoAdvertiser advertiser = authToutiaoAdvertiserService.getAdvertiser(ocpcEvent.getAccountId());
            if (Objects.equals(advertiser.getEventTypes(), "1,2")) {
                userEventReq.setActionValues(String.format("1-%s,2-%s", accNum, accArpu.setScale(2, BigDecimal.ROUND_DOWN).toString()));
                userEventReq.setAccumulateDuration(minutes.intValue());
            } else if (Objects.equals(advertiser.getEventTypes(), "1,3")) {
                BigDecimal ecpm = accArpu.multiply(new BigDecimal(1000)).divide(new BigDecimal(accNum), 0, BigDecimal.ROUND_FLOOR);
                userEventReq.setActionValues(String.format("1-%s,3-%s", accNum, ecpm.intValue()));
                userEventReq.setAccumulateDuration(minutes.intValue());
            } else {
                log.info("fixBug4  " + advertiser.getEventTypes());
                return;
            }

            incrNum(num++);

//            System.out.println("头条临时补数0519请求 " + log1.get());
            log.info("1头条临时补数0701 {} {} 账户配置:{} {}", userEventReq.getActionValues(), userEventReq.getAccumulateDuration(), advertiser.getEventTypes(), advertiser.getEventValues());

            ToutiaoClick toutiaoClick = RedisEventService.convertEventToClick(ocpcEvent);

            tryLockAndRun(userEventReq, ocpcEvent, advertiser, toutiaoClick, num);

        } else {
            log.info("找不到对应的激活事件 {}", msg);
        }
    }

    private static AtomicLong num = new AtomicLong(0);

    private void tryLockAndRun(UserEventReq userEventReq, OcpcEvent ocpcEvent, AuthToutiaoAdvertiser advertiser, ToutiaoClick toutiaoClick, int num) {

        String lockKey = RedisKeyConstants.getUserFixBugLockKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), userEventReq.getUserId());

        incrNum(num++);

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(3, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("toutiaoFixBug获取redis分布式锁0失败 {}", ocpcEvent.getUserId());
                return;
            }
            incrNum(num++);


            if (authToutiaoAdvertiserService.checkActionCount(toutiaoClick, userEventReq)) {
//                if (num.addAndGet(1) > 200) {
//                    log.info("头条临时补数0701 取消 已达上限 max:{} now:{}", 200, num.get());
//                    return;
//                }
                incrNum(num++);

                log.info("头条临时补数0701 {} {} {} {} {}-{}, other:{} ", ocpcEvent.getUserId(), ocpcEvent.getAccountId(), ocpcEvent.getProduct(),
                        userEventReq.getActionValues(), advertiser.getEventTypes(), advertiser.getEventValues(), JSON.toJSONString(userEventReq));
                boolean isAct = ocpcEventService.guiyingDspClick(toutiaoClick, userEventReq, true);
            }

        } catch (Exception e) {
            log.warn("toutiaoFixBug获取redis分布式锁0异常 {}", ocpcEvent.getUserId());
            return;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    private BigDecimal calculateAccArpu(String userId, OcpcEvent ocpcEvent, String price) {

        // 目前以消息队列中取到的userId
        String lockKey = RedisKeyConstants.getUserAccArpuLockKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), userId);

        if (!Objects.equals(userId, ocpcEvent.getUserId())) {
            log.info("消息队列中userId与redis中匹配到的激活事件userId不同 mq:{} redis:{}", userId, ocpcEvent.getUserId());
        }

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(3, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("toutiaoFixBug获取redis分布式锁失败 {}", ocpcEvent.getUserId());
                return null;
            }

            // 单位 分
            return hbaseEventService.addUserArpu(userId, ocpcEvent, price, HbaseEventService.tableName_TempUserArpu);

        } catch (Exception e) {
            log.warn("toutiaoFixBug获取redis分布式锁异常 {}", ocpcEvent.getUserId());
            return null;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }

    }

    private Integer calculateAccNum(String userId, OcpcEvent ocpcEvent) {

        // 目前以消息队列中取到的userId
        String lockKey = RedisKeyConstants.getUserAccNumLockKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), userId);

        if (!Objects.equals(userId, ocpcEvent.getUserId())) {
            log.info("消息队列中userId与redis中匹配到的激活事件userId不同 mq:{} redis:{}", userId, ocpcEvent.getUserId());
        }

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(3, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("toutiaoFixBug获取redis分布式锁失败 {}", ocpcEvent.getUserId());
                return null;
            }

            // 单位 分
            return hbaseEventService.addUserNum(userId, ocpcEvent);

        } catch (Exception e) {
            log.warn("toutiaoFixBug获取redis分布式锁异常 {}", ocpcEvent.getUserId());
            return null;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }

    }


}
