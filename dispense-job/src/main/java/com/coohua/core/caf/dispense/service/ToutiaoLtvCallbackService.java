package com.coohua.core.caf.dispense.service;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.KuaishouExtApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.apollo.ToutiaoLtvApolloConfig;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthToutiaoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.KuaishouUserEventService;
import com.coohua.core.caf.dispense.dsp.service.TencentUserActionService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.KuaishouEventType;
import com.coohua.core.caf.dispense.ocpc.entity.kafka.VideoAdReportEntitiy;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.ToutiaoLtvService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 头条回传 LTV0 数据
 * <AUTHOR>
 */
@Service
@Slf4j
public class ToutiaoLtvCallbackService {

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    ToutiaoLtvApolloConfig apolloConfig;
    @Autowired
    KuaishouExtApolloConfig kuaishouExtApolloConfig;
    @Autowired
    RedissonClient redissonClient;
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    ToutiaoLtvService toutiaoLtvService;
    @Autowired
    KuaishouUserEventService kuaishouUserEventService;
    @Autowired
    TencentUserActionService tencentUserActionService;
    @Resource(name = "jobForToutiaoLtvCallback")
    ThreadPoolTaskExecutor jobForToutiaoLtvCallbackExecutor;

    // Table_plaque(3,"插屏"),
    // Rewarded_video(4,"激励视频"),
    private static final Integer ChaPin = 3;
    private static final Integer JiLiShiPin = 4;
    private static final List<Integer> ValidAdTypes = Arrays.asList(ChaPin,JiLiShiPin);

    private static final AtomicLong log1 = new AtomicLong(0);
    private static final AtomicLong log2 = new AtomicLong(0);
    private static final AtomicLong log3 = new AtomicLong(0);

    public void callbackLtv(String msg) {
        VideoAdReportEntitiy mqMsg = JSON.parseObject(msg,VideoAdReportEntitiy.class);

        if (log1.getAndIncrement() % 1000 == 0) {
            log.info("{} log1当前消息内容为 {}", log1.get(), msg);
        }

        // http://confluence.coohua.com/pages/viewpage.action?pageId=7675382
        // Table_plaque(3,"插屏"),
        // Rewarded_video(4,"激励视频"),
        // 目前仅这两种情况传给了项目组，并进行累计arpu计算
        if (!ValidAdTypes.contains(mqMsg.getAdType())) {
            return;
        }

        if (log2.getAndIncrement() % 1000 == 0) {
            log.info("{} {} log2当前消息内容为 {} {}", log1.get(), log2.get(), JSON.toJSONString(apolloConfig.toutiaoLtvEnableProducts), msg);
        }

        // 非开启的产品不回传
        if (apolloConfig.illegalProduct(mqMsg.getProduct()) && kuaishouExtApolloConfig.illegalProduct(mqMsg.getProduct())) {
            return;
        }

        if (log3.getAndIncrement() % 100 == 0) {
            log.info("{} {} {} log3当前消息内容为 {}", log1.get(), log2.get(), log3.get(), msg);
        }

        // 类似回传逻辑，构造userEventReq请求事件的参数
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setAppId(mqMsg.getAppId());
        userEventReq.setUserId(mqMsg.getUserId());
        userEventReq.setProduct(mqMsg.getProduct());
        userEventReq.setOaid(mqMsg.getOaid());
        userEventReq.setOs(mqMsg.getOs());
        // ios时 取device_id明文，android时取 md5(imei)
        userEventReq.setOcpcDeviceId("ios".equals(mqMsg.getOs()) ? mqMsg.getDeviceId() : MD5Utils.getMd5Sum(mqMsg.getImei()));
        // ios时 取device_id明文，android时取 imei明文
        userEventReq.setSourceDeviceId("ios".equals(mqMsg.getOs()) ? mqMsg.getDeviceId() : mqMsg.getImei());
        userEventReq.setEventType(ToutiaoEventTypeEnum.LTV.value);
        userEventReq.trySetOaid2AndIdfa2(ocpcSwitcher.caid2Products.contains(mqMsg.getProduct()));

        OcpcEvent ocpcEvent =  redisEventService.getActiveEvent(userEventReq);

        if (ocpcEvent != null) {
            // 快手的走 回传快手衍生事件 的逻辑
            if (DspType.KUAISHOU.value.equals(ocpcEvent.getDsp())) {
                if (!JiLiShiPin.equals(mqMsg.getAdType())) {
                    return;
                }
                kuaishouUserEventService.callbackExtProcess(userEventReq, ocpcEvent, mqMsg.getProduct(), mqMsg.getPrice(), mqMsg.getTimestamp(), KuaishouEventType.ECPM);
                kuaishouUserEventService.callbackIAAProcess(userEventReq, ocpcEvent, mqMsg.getProduct(), mqMsg.getPrice(), mqMsg.getTimestamp(), KuaishouEventType.EVENT_MINIGAME_IAA);
                return;
            }
            // 广点通的走 回传用户广告变现价值 的逻辑
            if (DspType.GUANGDIANTONG.value.equals(ocpcEvent.getDsp())) {
                if (!JiLiShiPin.equals(mqMsg.getAdType())) {
                    return;
                }
                tencentUserActionService.callbackLtv(userEventReq, ocpcEvent, mqMsg.getProduct(), mqMsg.getPrice(), mqMsg.getTimestamp());
                return;
            }

            // 非头条的不处理
            if (!DspType.TOUTIAO.value.equals(ocpcEvent.getDsp())) {
                return;
            }

            if (apolloConfig.illegalProduct(mqMsg.getProduct())) {
                return;
            }

            AuthToutiaoAdvertiser advertiser = authToutiaoAdvertiserService.getAdvertiser(ocpcEvent.getAccountId());
            // 判断必须是次留双出价才允许回传
            if (!needCallback(advertiser)) {
                log.info("非回传LTV账户 {}", advertiser.getAdvertiserId());
                return;
            }

            // 计算累计arpu值（单位为分）
            BigDecimal accArpu = calculateAccArpu(userEventReq.getUserId(), ocpcEvent, mqMsg.getPrice());

            UserEvent userEvent = new UserEvent();
            userEvent.setEventType(userEventReq.getEventType());

            jobForToutiaoLtvCallbackExecutor.execute(()-> {
                // 回传到头条
                toutiaoCallService.touTiaoLtv(userEventReq, ocpcEvent, userEvent, accArpu);
                // 入库
                toutiaoLtvService.saveToutiaoLtv(userEventReq, ocpcEvent, userEvent.getReqUrl(), userEvent.getReqRsp(), accArpu);
            });

        } else {
//            log.info("找不到对应的激活事件 {}", msg);
        }
    }

    private boolean needCallback(AuthToutiaoAdvertiser advertiser) {
        return advertiser != null
//                && advertiser.getEventType() == null
//                && StringUtils.isBlank(advertiser.getEventTypes())
                && Objects.equals(advertiser.getCallbackLtv(), 1);
    }

    private BigDecimal calculateAccArpu(String userId, OcpcEvent ocpcEvent, String price) {

        // 目前以消息队列中取到的userId
        String lockKey = RedisKeyConstants.getUserAccArpuLockKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), userId);

        if (!Objects.equals(userId, ocpcEvent.getUserId())) {
            log.info("消息队列中userId与redis中匹配到的激活事件userId不同 mq:{} redis:{}", userId, ocpcEvent.getUserId());
        }

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(3, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("toutiaoLtv获取redis分布式锁失败 {}", ocpcEvent.getUserId());
                return null;
            }

            // 单位 分
            return hbaseEventService.addUserArpu(userId, ocpcEvent, price);

        } catch (Exception e) {
            log.warn("toutiaoLtv获取redis分布式锁异常 {}", ocpcEvent.getUserId());
            return null;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }

    }


}
