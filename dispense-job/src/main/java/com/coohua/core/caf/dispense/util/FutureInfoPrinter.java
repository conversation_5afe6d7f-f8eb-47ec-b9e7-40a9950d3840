package com.coohua.core.caf.dispense.util;


import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Slf4j
public class FutureInfoPrinter {
    public static void printFutereInfo(List<Future<String>> resultList){
        //遍历任务的结果
        int completeNum = 0;
        int sleepNum = 0;
        try {
            log.info(resultList.size()+" sleep 15 seconds");
            TimeUnit.SECONDS.sleep(15);
        }catch (Exception e){
            log.error("",e);
        }

        log.info(resultList.size()+" 条future 进行运算");

        for (Future<String> fs : resultList){
            try{
                //40分钟
                while(!fs.isDone() && !fs.isCancelled() && sleepNum<(100*60)/3){
                    TimeUnit.SECONDS.sleep(3);
                    sleepNum++;
                    log.info("fs 未执行完成 睡眠 "+sleepNum+" 次 ,剩余{},已经完成{}",(resultList.size()-completeNum),completeNum);
                };//Future返回如果没有完成，则一直循环等待，直到Future返回完成
                completeNum++;
                log.info("当前正在执行的返回结果是:{},fs 执行完成, 剩余{},已经完成{}",fs.get(),(resultList.size()-completeNum),completeNum);   //打印各个线程（任务）执行的结果
            }catch(Exception e){
                log.error("",e);
            }
        }
    }
}
