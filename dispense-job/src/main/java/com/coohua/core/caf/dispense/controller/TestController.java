package com.coohua.core.caf.dispense.controller;

import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.service.ToutiaoExtraCallbackTimer;
import com.coohua.core.caf.dispense.service.ToutiaoFixBugService;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * https://ad.vivo.com.cn/help?id=352
 */
@RestController
@RequestMapping("/dispenseJob")
@Slf4j
public class TestController {

    @Autowired
    ToutiaoClickService toutiaoClickService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    private AlertService alertService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    KafkaSender kafkaSender;
    @Autowired
    ToutiaoExtraCallbackTimer toutiaoExtraCallbackTimer;

    @RequestMapping("/test")
    @ResponseBody
    public String test() {

        Map<Integer, AtomicLong> numMap = ToutiaoFixBugService.numMap;

        String res = "";
        for (int i = 0; i <= 20; i++) {
            res += i + "=" + numMap.get(i).get() + "\n";
        }
        return res;

//        toutiaoExtraCallbackTimer.toutiaoExtraCallback();
//        return LocalDateTime.now().toString();
    }

}
