package com.coohua.core.caf.dispense.service;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RedissonService {

    @Autowired
    RedissonClient redissonClient;

    public <T> T lockCall(String lockKey, String logPrefix, Callable<T> callable) {
        return lockCall(lockKey, logPrefix, 3, callable);
    }
    public <T> T lockCall(String lockKey, String logPrefix, int lockSecond, Callable<T> callable) {

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(lockSecond, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("{} 获取redis分布式锁失败 {}", logPrefix, lockKey);
                return null;
            }

            return callable.call();

        } catch (Exception e) {
            log.warn(logPrefix + " 获取redis分布式锁异常 " + lock<PERSON>ey, e);
            return null;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

    public boolean lockRun(String lockKey, String logPrefix, Runnable runnable) {
        return lockRun(lockKey, logPrefix, 3, runnable);
    }
    public boolean lockRun(String lockKey, String logPrefix, int lockSecond, Runnable runnable) {

        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            locked = lock.tryLock(lockSecond, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("{} 获取redis分布式锁失败 {}", logPrefix, lockKey);
                return false;
            }

            runnable.run();
            return true;

        } catch (Exception e) {
            log.warn(logPrefix + " 获取redis分布式锁异常 " + lockKey, e);
            return false;
        } finally {
            if (locked) {
                lock.unlock();
            }
        }
    }

}
