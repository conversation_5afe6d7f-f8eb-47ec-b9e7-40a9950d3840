package com.coohua.core.caf.dispense.ck.mapper;

import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

public interface CkToutiaoClickMapper {
    @Select({"select * from ods.toutiao_click_dist where logday = '${logday}' and os='${os}' and product='${product}'  and oaid='${oaid}' limit 0,1;"})
    ToutiaoClick queryOaid(@Param("logday")String logday, @Param("os")String os, @Param("product")String product, @Param("oaid")String oaid);


    @Select({"select * from ods.toutiao_click_dist where logday = '${logday}' and os='${os}' and product='${product}' and ocpc_device_id='${ocpcDeviceId}' limit 0,1; "})
    ToutiaoClick queryDeviceId(@Param("logday")String logday, @Param("os")String os, @Param("product")String product,
                                   @Param("ocpcDeviceId")String ocpcDeviceId);

    @Select({"select * from ods.toutiao_click_dist where logday = '${logday}' and os='${os}' and product='${product}' and android_id='${androidId}' limit 0,1;"})
    ToutiaoClick queryAndroidId(@Param("logday")String logday, @Param("os")String os, @Param("product")String product,  @Param("androidId")String androidId);

    @Select({"select * from ods.toutiao_click_dist where logday = '${logday}' and os='${os}' and product='${product}' and mac='${mac}' limit 0,1; "})
    ToutiaoClick queryMac(@Param("logday")String logday, @Param("os")String os, @Param("product")String product,@Param("mac")String mac);


    @Select({"select * from ods.toutiao_click_dist where ${logdaySql} ${devSql}; "})
    List<ToutiaoClick> queryByDevList(@Param("logdaySql")String logdaySql, @Param("devSql")String devSql);

    @Select({"select product from dwd.product_map_dist"})
    Set<String> queryProductList();
}
