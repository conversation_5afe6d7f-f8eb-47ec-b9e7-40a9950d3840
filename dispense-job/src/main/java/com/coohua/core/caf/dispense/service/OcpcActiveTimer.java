package com.coohua.core.caf.dispense.service;

import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.ck.CkGuiyingService;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.dsp.entity.KuaishouClick;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.mapper.KuaishouClickMapper;
import com.coohua.core.caf.dispense.dsp.mapper.ToutiaoClickMapper;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventMapper;
import com.coohua.core.caf.dispense.dsp.service.UserEventService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OcpcActiveTimer {
    @Autowired
    UserActiveService userActiveService;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    ProductCache productCache;
    @Autowired
    HbaseUserActiveService hbaseUserActiveService;
    @Autowired
    CkGuiyingService ckGuiyingService;

    ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("userActiveSourceBu", null, false);
    ExecutorService executorService = new ThreadPoolExecutor(
            10
            , 10,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            threadFactory,
            RejectPolicy.CALLER_RUNS.getValue()
    );

    /**
     * CK 14天补数
     */
//    @PostConstruct
    public void ckActiveSourceBu() {
        executorService.submit(() -> {
            productCache.initProductNameCache();
            while (true) {
                try {
                    long ctime = System.currentTimeMillis();
                    ckBuByT();
                    log.info("ck补数完成，总耗时 " + (System.currentTimeMillis() - ctime));
                    TimeUnit.SECONDS.sleep(3);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        });
    }
    @Autowired
    UserEventService userEventService;
    @Autowired
    UserEventMapper userEventMapper;

    @Autowired
    KuaishouClickMapper kuaishouClickMapper;
    @Autowired
    ToutiaoClickMapper toutiaoClickMapper;
    public void buUserEvent(){
        Date dateD = DateUtils.parse("2021-12-01",DateUtils.PATTERN_YMD);
        Date endDateD = DateUtils.parse("2021-12-02",DateUtils.PATTERN_YMD);
        List<OcpcEvent> dlist = ocpcEventService.lambdaQuery()
                .gt(OcpcEvent::getCreateTime,dateD)
                .le(OcpcEvent::getCreateTime,endDateD)
                .eq(OcpcEvent::getOs,"ios").eq(OcpcEvent::getEventType,0).list();

        dlist.forEach(ocpcEvent -> {
            try {
                QueryWrapper<UserEvent> objectQueryWrapper = new QueryWrapper<UserEvent>();
                objectQueryWrapper.lambda().eq(UserEvent::getUserId,ocpcEvent.getUserId());
                objectQueryWrapper.lambda().eq(UserEvent::getOs,ocpcEvent.getOs());
                objectQueryWrapper.lambda().eq(UserEvent::getProduct,ocpcEvent.getProduct());
                objectQueryWrapper.lambda().eq(UserEvent::getEventType,ocpcEvent.getEventType());
                objectQueryWrapper.lambda().gt(UserEvent::getCreateTime,dateD);
                long countNum = userEventMapper.selectCount(objectQueryWrapper);

                if(countNum==0){
                    log.info("开始补充 "+ocpcEvent.getProduct()+" "+ocpcEvent.getUserId()+" 的数据");
                    UserEventReq userEventReq = new UserEventReq();

                    userEventReq.setProduct(ocpcEvent.getProduct());
                    userEventReq.setOcpcDeviceId(ocpcEvent.getOcpcDeviceId());
                    userEventReq.setOs(ocpcEvent.getOs());
                    userEventReq.setEventType(ocpcEvent.getEventType());

                    ToutiaoClick toutiaoClick = userActiveService.guiOcpc(userEventReq);

                    try {
                        if(DspType.KUAISHOU.name.equals(toutiaoClick.getDsp())){
                            KuaishouClick kuaishouClick = new KuaishouClick();
                            BeanUtils.copyProperties(toutiaoClick, kuaishouClick);
                            kuaishouClick.setId(null);
                            kuaishouClickMapper.insert(kuaishouClick);
                            UserEvent newUserEvent = new UserEvent();
                            BeanUtils.copyProperties(ocpcEvent,newUserEvent);
                            newUserEvent.setId(null);
                            newUserEvent.setClickId(kuaishouClick.getId());

                            //get ideaday
                            newUserEvent.setPlanId(kuaishouClick.getGid());
                            newUserEvent.setGroupId(kuaishouClick.getPid());
                            newUserEvent.setCreativeId(kuaishouClick.getCid());
                            newUserEvent.setMid(kuaishouClick.getMid());

                            newUserEvent.setReqRsp("补数1202");
                            userEventMapper.insert(newUserEvent);
                        }else{
                            ToutiaoClick newToutiaoClick = new ToutiaoClick();
                            BeanUtils.copyProperties(toutiaoClick, newToutiaoClick);
                            newToutiaoClick.setId(null);
                            toutiaoClickMapper.insert(newToutiaoClick);
                            UserEvent newUserEvent = new UserEvent();
                            Long id = newToutiaoClick.getId();
                            BeanUtils.copyProperties(ocpcEvent,newUserEvent);
                            newUserEvent.setId(null);
                            newUserEvent.setClickId(newToutiaoClick.getId());

                            newUserEvent.setPlanId(toutiaoClick.getPid()+"");
                            newUserEvent.setCreativeId(toutiaoClick.getCid());
                            newUserEvent.setGroupId(toutiaoClick.getGid());
                            newUserEvent.setMid(toutiaoClick.getMid());

                            if(DspType.TOUTIAO.name.equals(toutiaoClick.getDsp())){
                                newUserEvent.setUnionSite(toutiaoClick.getGroupName());
                            }
                            newUserEvent.setReqRsp("补数1202");
                            userEventMapper.insert(newUserEvent);
                        }

                    }catch (Exception e){
                        log.error("双写ocpcEvent成功异常", e);
                    }
                }
            }catch (Exception e){
                log.error("",e);
            }
        });
    }

    /**
     * ocpc event userId纬度补数
     */
    @PostConstruct
    public void userActiveSourceBu() {
        if (SystemInfo.isWin()) {
            return;
        }
        executorService.submit(() -> {
            while (true) {
                try {
                    productCache.initProductNameCache();
                    Date beforeDate = new Date(System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_DAY);
                    long countNum = userActiveService.sumZiranCount(beforeDate, null, false);
                    int pageNum = 1000;

                    for (int i = 0; i <= countNum / pageNum; i++) {
                        try {
                            List<UserActive> userActiveList = userActiveService.queryZiranSourceActive(beforeDate, pageNum, i * pageNum, false);
                            log.info("开始第 " + pageNum + " 页，剩余" + (countNum / pageNum - i));

                            buByEvent(userActiveList);
                        } catch (Exception e) {
                            log.error("", e);
                        }
                    }

                    TimeUnit.SECONDS.sleep(3);
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        });
    }

    /**
     * hbase redis 补数
     */
//    @PostConstruct
    public void activeUserByD() {
//        if (SystemInfo.isWin()) {
//            return;
//        }
//        executorService.submit(() -> {
//            while (true) {
//                productCache.initProductNameCache();
//                Date beforeDate = new Date(System.currentTimeMillis() - DateTimeConstants.MILLIS_PER_DAY);
//                long countNum = userActiveService.sumZiranCount(beforeDate, null, true);
//                int pageNum = 1000;
//
//                for (int i = 0; i <= countNum / pageNum; i++) {
//                    try {
//                        List<UserActive> userActiveList = userActiveService.queryZiranSourceActive(beforeDate, pageNum, i * pageNum, true);
//                        log.info("开始第 " + pageNum + " 页，剩余" + (countNum / pageNum - i));
//                        userActiveList.forEach(userActive -> {
//                            try {
//                                userActiveService.activeUser(userActive, true);
//                            } catch (Exception e) {
//                                log.error("", e);
//                            }
//                        });
//                    } catch (Exception e) {
//                        log.error("", e);
//                    }
//                }
//
//                TimeUnit.SECONDS.sleep(3);
//
//            }
//        });
    }

    private List<UserActive> buByEvent(List<UserActive> userActiveList) {
        long ctime = System.currentTimeMillis();
        Set<Long> uidSet = userActiveList.stream().map(UserActive::getUserId).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(uidSet)) {
            return null;
        }

        List<OcpcEvent> ocpcEvents = ocpcEventService.lambdaQuery()
                .in(OcpcEvent::getUserId, uidSet)
                .gt(OcpcEvent::getCreateTime, new Date(System.currentTimeMillis() - 2 * DateTimeConstants.MILLIS_PER_DAY))
                .eq(OcpcEvent::getEventType, 0).list();


        List<UserActive> updateList = new ArrayList();
        List<UserActive> noteUpdateList = new ArrayList();

        userActiveList.forEach(userActive -> {
            try {
                if (userActive.getUserId() != null && userActive.getAppId() != null) {
                    String product = productCache.getPname(userActive.getAppId());
                    boolean isCt = false;
                    for (OcpcEvent ocpcEvent : ocpcEvents) {
                        String productOcpc = ocpcEvent.getProduct();
                        String userIdOcpc = ocpcEvent.getUserId();

                        if (!isCt && userActive.getUserId().equals(Long.parseLong(userIdOcpc)) && product.equalsIgnoreCase(productOcpc)) {
                            userActive.setSource(ocpcEvent.getDsp());
                            userActive.setAccountId(ocpcEvent.getAccountId());
                            userActive.setAcDesc("event补充 " + ocpcEvent.getId());
                            updateList.add(userActive);
                            isCt = true;
                        }
                    }

                    if (!isCt && (userActive.getAcDesc() == null || !"CK归因失败".equalsIgnoreCase(userActive.getAcDesc()) || "参数不合法".equalsIgnoreCase(userActive.getAcDesc()))) {
                        noteUpdateList.add(userActive);
                    }
                }
            } catch (Exception e) {
                log.error("", e);
            }

        });

        if (updateList.size() > 0) {
            userActiveService.updateBatchById(updateList);

            log.info("更新userActive成功 " + updateList.size() + " 成功 耗时 " + (System.currentTimeMillis() - ctime));
        }

        return noteUpdateList;
    }

    private void ckBuByT() {
        Date beforeDate = DateUtils.getDayBeginDate(new Date());
        long countNum = userActiveService.sumZiranCount(beforeDate);
        int pageNum = 50;
        for (int i = 0; i <= countNum / pageNum; i++) {
            try {
                List<UserActive> alNotUpdateList = userActiveService.queryZiranSourceActive(beforeDate, pageNum, i * pageNum, true);
                if (alNotUpdateList.size() > 0) {
                    ckGuiyingService.activeUser(alNotUpdateList, 14, true);
                }
                log.info("beforeDatedf完成第 " + i + " 剩余 " + (countNum / pageNum - i) + " 页");
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }
}
