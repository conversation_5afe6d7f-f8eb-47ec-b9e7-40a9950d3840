package com.coohua.core.caf.dispense.service;

import com.arronlong.httpclientutil.common.HttpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class OcpcHttpClientReqService {

    public static String sendHttpCt(List<String> reqList){
        long ctime = System.currentTimeMillis();
        for(String reqUrl : reqList){
            try {
                String[] reqStrs = reqUrl.split("\\?");
                System.out.println(reqUrl);

                Map<String,String> dmap = new HashMap<>();
                String parmStr = reqUrl.substring(reqStrs[0].length()+1,reqUrl.length());

                String[] reqParsStrs =parmStr.split("&");

                for(String reqPar : reqParsStrs){
                    String[] pars = reqPar.split("=");
                    if(pars.length==2){
                        dmap.put(pars[0],pars[1]);
                    }
                }
                dmap.put("isSendKafka","false");
                String html = doGetHeader(reqStrs[0],dmap);
                log.info("响应为 "+html);
            }catch (Exception e){
                log.error("",e);
            }
        }
        log.info("總共 "+reqList.size()+" 耗時"+(System.currentTimeMillis()-ctime));
        return "";
    }
    public static String doGetHeader(String url, Map<String, String> param) {

        // 创建Httpclient对象
        CloseableHttpClient httpclient = getHttpClient();

        String resultString = "";
        CloseableHttpResponse response = null;
        try {
            // 创建uri
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, param.get(key));
                }
            }
            URI uri = builder.build();

            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);
            // 执行请求
            response = httpclient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            log.error("",e);

        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultString;
    }

    private static CloseableHttpClient getHttpClient(){
        CloseableHttpClient httpclient = HttpClients.custom().build();
        return httpclient;
    }
    public static void main(String[] args) throws Exception{
        try {
            //http://ocpc-api.shinet.cn/dispense/toutiao/click?dsp=toutiao&product=snbcy&os=android&account_id=****************&ocpc_device_id=&ts=*************&callback_url=CIjwy_eWnIsDEKuQgvaWnIsDGJvrwMrOjaUHIJvrwMrOjaUHKAAwDjiljaLEA0IpMWJjOGZkMWItNWRlYi00MzZkLTg1MTMtZGE0ZTJhMGE1MzJjdTMyMTlIgNKTrQNQAIgBApABApgBAA==&cid=****************&gid=****************&pid=****************&oaid=8213BD7A422E4151BEC826D177D586A5c3680620b516eaa63ffd7a7e1048f1ba&union_site=**********&androidId=32c7603b6a9158d0064f0c35420e1586&mac=667328a75e3c821097cb83fbd4f5e8de&oaid2=1f87a5784db6019d2c036ffad1523bec&ip=***************&ua=Mozilla/5.0 (Linux; Android 10; PBEM00 Build/QKQ1.190918.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.92 Mobile Safari/537.36&model=PBEM00&
            String reqUrl = "http://localhost:8080/dispense/toutiao/click?dsp=toutiao&product=snbcy&os=android&account_id=****************&ocpc_device_id=&ts=*************&callback_url=CIjwy_eWnIsDEKuQgvaWnIsDGJvrwMrOjaUHIJvrwMrOjaUHKAAwDjiljaLEA0IpMWJjOGZkMWItNWRlYi00MzZkLTg1MTMtZGE0ZTJhMGE1MzJjdTMyMTlIgNKTrQNQAIgBApABApgBAA==&cid=****************&gid=****************&pid=****************&oaid=8213BD7A422E4151BEC826D177D586A5c3680620b516eaa63ffd7a7e1048f1ba&union_site=**********&androidId=32c7603b6a9158d0064f0c35420e1586&mac=667328a75e3c821097cb83fbd4f5e8de&oaid2=1f87a5784db6019d2c036ffad1523bec&ip=***************&ua=Mozilla/5.0 (Linux; Android 10; PBEM00 Build/QKQ1.190918.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/77.0.3865.92 Mobile Safari/537.36&model=PBEM00&";
            String[] reqStrs = reqUrl.split("\\?");
            System.out.println(reqUrl);

            Map<String,String> dmap = new HashMap<>();
            String parmStr = reqUrl.substring(reqStrs[0].length()+1,reqUrl.length());

            String[] reqParsStrs =parmStr.split("&");

            for(String reqPar : reqParsStrs){
                String[] pars = reqPar.split("=");
                if(pars.length==2){
                    dmap.put(pars[0],pars[1]);
                }
            }
            dmap.put("isSendKafka","false");
            String html = doGetHeader(reqStrs[0],dmap);

            System.out.println(html);
        }catch (Exception e){
            log.error("",e);
        }
    }
}
