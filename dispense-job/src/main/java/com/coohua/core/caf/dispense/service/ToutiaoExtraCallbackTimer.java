package com.coohua.core.caf.dispense.service;

import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadUtil;
import com.coohua.core.caf.dispense.apollo.ToutiaoExtraApolloConfig;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.dsp.entity.AuthToutiaoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 头条额外回传 (将头条关键行为数据回传到注册和下单上)
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ToutiaoExtraCallbackTimer {

    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    ProductCache productCache;
    @Autowired
    RedissonService redissonService;
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    ToutiaoExtraApolloConfig apolloConfig;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("toutiaoExtraCallback", null, false);
    ExecutorService executorService = new ThreadPoolExecutor(
            7
            , 10,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            threadFactory,
            RejectPolicy.CALLER_RUNS.getValue()
    );

    @PostConstruct
    public void toutiaoExtraCallback() {
        if (SystemInfo.isWin()) {
            // win 环境下不执行
            return;
        }
        executorService.submit(() -> {
            while (true) {
                try {
                    if (!apolloConfig.toutiaoExtraEnableAll && CollectionUtils.isEmpty(apolloConfig.toutiaoExtraEnableAdvertiserIds)) {
                        TimeUnit.SECONDS.sleep(3);
                        continue;
                    }

                    List<Long> ocpcEventIds = ocpcEventService.lambdaQuery()
                            .select(OcpcEvent::getId)
                            .eq(OcpcEvent::getDsp, DspType.TOUTIAO.value)
                            .eq(OcpcEvent::getEventType, ToutiaoEventTypeEnum.KEY_EVENT.value)
                            // 头条额外回传（关键行为回传到注册和下单）标记, 0待回传 1已回传
                            .eq(OcpcEvent::getToutiaoExtraTag, 0)
                            .in(!apolloConfig.toutiaoExtraEnableAll, OcpcEvent::getAccountId, apolloConfig.toutiaoExtraEnableAdvertiserIds)
                            // 只查询1小时内的
                            .ge(OcpcEvent::getCreateTime, LocalDateTime.now().minusHours(1).toDate())
                            .list()
                            .stream()
                            .map(OcpcEvent::getId)
                            .collect(Collectors.toList());

//                    ocpcEventIds = ocpcEventIds.stream().limit(1).collect(Collectors.toList());

                    log.info("头条extra回传 待回传记录总数 {}", ocpcEventIds.size());

                    CompletableFuture[] futureArray = ocpcEventIds.stream()
                            .map(ocpcEventId -> CompletableFuture.runAsync(() -> {
                                String lockKey = RedisKeyConstants.TOUTIAO_EXTRA_LOCK_KEY + ocpcEventId;
                                redissonService.lockRun(lockKey, "头条extra回传", () -> callback(ocpcEventId));
                            }, executorService))
                            .toArray(CompletableFuture[]::new);

                    CompletableFuture.allOf(futureArray).join();

                    TimeUnit.SECONDS.sleep(3);
                } catch (Exception e) {
                    log.error("toutiaoExtraCallback异常", e);
                    TimeUnit.SECONDS.sleep(3);
                }
            }
        });
    }

    private void callback(Long ocpcEventId) {

        try {
            OcpcEvent keyEvent = ocpcEventService.getById(ocpcEventId);

            if (!Objects.equals(keyEvent.getToutiaoExtraTag(), 0)) {
                // 若该记录标记为已回传过，不重复处理
                return;
            }

            AuthToutiaoAdvertiser advertiser = authToutiaoAdvertiserService.getAdvertiser(keyEvent.getAccountId());
            boolean isNormalAccount = advertiser != null && Objects.equals(advertiser.getAccountType(), 1);
            // 非普通账户不回传 (达人账户不回传)
            if (!isNormalAccount) {
                // 原关键行为事件标记为已回传过
                ocpcEventService.lambdaUpdate().set(OcpcEvent::getToutiaoExtraTag, 1).eq(OcpcEvent::getId, keyEvent.getId()).update();
                return;
            }

            extraCallback(keyEvent, ToutiaoEventTypeEnum.REGISTER);
            extraCallback(keyEvent, ToutiaoEventTypeEnum.COMPLETE_ORDER);
            if (apolloConfig.toutiaoExtraArpu0EnableAll || apolloConfig.toutiaoExtraArpu0EnableAdvertiserIds.contains(advertiser.getAdvertiserId().longValue())) {
                extraCallback(keyEvent, ToutiaoEventTypeEnum.ARPU0);
            }

        } catch (Exception e) {
            log.error("头条extra回传 回传时异常 " + ocpcEventId, e);
        }

    }

    /**
     * 头条关键行为额外回传到注册和下单
     */
    public void extraCallback(OcpcEvent keyEvent, ToutiaoEventTypeEnum eventTypeEnum) {
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setAppId(keyEvent.getAppId());
        userEventReq.setUserId(keyEvent.getUserId());
        userEventReq.setProduct(keyEvent.getProduct());
        userEventReq.setOs(keyEvent.getOs());
        userEventReq.setEventType(eventTypeEnum.value);

        Date now = new Date();
        OcpcEvent extraEvent = new OcpcEvent();
        BeanUtils.copyProperties(keyEvent, extraEvent);
        extraEvent.setEventType(eventTypeEnum.value);
        extraEvent.setCreateTime(now);
        extraEvent.setUpdateTime(now);

        ToutiaoClick toutiaoClick = RedisEventService.convertEventToClick(extraEvent);

        UserEvent userEvent = new UserEvent();
        BeanUtils.copyProperties(extraEvent, userEvent);

        // 回调请求头条
        toutiaoCallService.touTiaoActive(userEventReq, toutiaoClick, userEvent);

        if (StringUtils.isBlank(userEvent.getReqRsp())) {
            throw new RuntimeException("额外回传请求头条失败");
        }

        // 原关键行为事件标记为已回传过
        ocpcEventService.lambdaUpdate().set(OcpcEvent::getToutiaoExtraTag, 1).eq(OcpcEvent::getId, keyEvent.getId()).update();

        // 新额外回传事件保存
        extraEvent.setReqUrl(userEvent.getReqUrl());
        extraEvent.setReqRsp(userEvent.getReqRsp());
        ocpcEventService.save(extraEvent);

    }

}
