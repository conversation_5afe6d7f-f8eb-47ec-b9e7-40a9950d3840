package com.coohua.core.caf.dispense.service;

import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.util.FutureInfoPrinter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 补数 消费逻辑
 */
@Slf4j
@Component
public class C2NoLockConsumer implements InitializingBean {

    @Autowired
    StartUpEventConsumeService startUpEventConsumeService;

    private AtomicLong count = new AtomicLong(0);
    static final int saveTnum = 12;
    public static final ExecutorService POOL = Executors.newFixedThreadPool(saveTnum);

    public void subscribe() {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-zvp2j2vhf007-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-zvp2j2vhf007-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-zvp2j2vhf007-3-vpc.alikafka.aliyuncs.com:9092");
        //默认值为30000ms，可根据自己业务场景调整此值，建议取值不要太小，防止在超时时间内没有发送心跳导致消费者再均衡。
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 25000);
//        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 3000);
        //每次poll的最大数量。
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        //注意该值不要改得太大，如果poll太多数据，而不能在下次poll之前消费完，则会触发一次负载均衡，产生卡顿。
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 20000);
//        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 2097152);//2M
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024*2);//2M
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 2097152);
//        props.put(ConsumerConfig.RECEIVE_BUFFER_CONFIG, 1048576 * 20);//2M
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 100);
        //消息的反序列化方式。
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        //当前消费实例所属的Consumer Group，请在控制台创建后填写。
        //属于同一个Consumer Group的消费实例，会负载消费消息。
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "start_up_event");
//        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 1000);


        for (int i = 0; i < saveTnum; i++) {
            POOL.submit(() -> processConsume(props));
        }
    }

    private ThreadPoolExecutor getPool(int poolNum) {
        return new ThreadPoolExecutor(poolNum, poolNum,
                20L, TimeUnit.MINUTES,
                new ArrayBlockingQueue<>(3000),
                new ThreadPoolExecutor.DiscardOldestPolicy());
    }

    private void processConsume(Properties props) {
        int poolNum = 16;
        ExecutorService executorHourService = getPool(poolNum);
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        //设置Consumer Group订阅的Topic，可订阅多个Topic。如果GROUP_ID_CONFIG相同，那建议订阅的Topic设置也相同。
        List<String> subscribedTopics = new ArrayList<>();
        //每个Topic需要先在控制台进行创建。
        subscribedTopics.add("start_up");

//        TopicPartition p = new TopicPartition("core_user_event", 0);//只消费分区号为2的分区
//        TopicPartition p1 = new TopicPartition("core_user_event", 1);
//        TopicPartition p2 = new TopicPartition("core_user_event", 2);
//        TopicPartition p3 = new TopicPartition("core_user_event", 3);
//        TopicPartition p4 = new TopicPartition("core_user_event", 4);
//        TopicPartition p8 = new TopicPartition("core_user_event", 8);
//        TopicPartition p9 = new TopicPartition("core_user_event", 9);
//        TopicPartition p13 = new TopicPartition("core_user_event", 13);
//        TopicPartition p15 = new TopicPartition("core_user_event", 15);
//
//        consumer.assign(Arrays.asList(p));

        consumer.subscribe(subscribedTopics);
        List<Future<String>> resultList = new ArrayList<>();
        List<String> flist = new ArrayList<>();
        //循环消费消息。
        while (true) {
            try {
                ConsumerRecords<String, String> records = consumer.poll(100);
                //必须在下次poll之前消费完这些数据, 且总耗时不得超过SESSION_TIMEOUT_MS_CONFIG 的值。
                //建议开一个单独的线程池来消费消息，然后异步返回结果。
                if (records.count() > 0) {
                    for (ConsumerRecord<String, String> record : records) {
                        flist.add(record.value());
                    }
                    int batchSize = 100;
                    if (flist.size() > batchSize) {
                        List<String> finalFlist = flist;
                        Future<String> future = executorHourService.submit(()->startUpEventConsumeService.consume(finalFlist));
                        resultList.add(future);

                        if (resultList.size() >= poolNum) {
                            FutureInfoPrinter.printFutereInfo(resultList);
                            resultList = new ArrayList<>();
                            executorHourService.shutdownNow();
                            executorHourService = null;
                            executorHourService = getPool(poolNum);

                        }
                        flist = new ArrayList<>();
                    }
                }
                consumer.commitSync();
            } catch (Exception e) {
                try {
                    Thread.sleep(100);
                } catch (Throwable ignore) {
                    log.error("", ignore);
                }
                //更多报错信息，参见常见问题文档。
                log.error("", e);
            }


        }
    }

    @Override
    public void afterPropertiesSet() {
        if (SystemInfo.isWin()) {
            return;
        }
        new Thread(new Runnable() {
            long oldValue = 0L;

            @Override
            public void run() {
                while (true) {
                    try {
                        long newValue = count.longValue();
                        log.info("KafkaDataConsumer: MSG.SEC={}", newValue - this.oldValue);
                        this.oldValue = newValue;
                        TimeUnit.SECONDS.sleep(1);
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
            }
        }).start();
        new Thread(this::subscribe).start();
    }
}