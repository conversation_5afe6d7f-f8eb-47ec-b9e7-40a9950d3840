package com.coohua.core.caf.dispense.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.coohua.core.caf.dispense.apollo.KuaishouExtApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.service.KuaishouUserEventService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.KuaishouEventType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.kafka.CoreEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.kafka.EventProperties;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.LocalDateTime;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 应用启动数据 消费处理逻辑
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class StartUpEventConsumeService {

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    KuaishouExtApolloConfig kuaishouExtApolloConfig;
    @Autowired
    RedissonClient redissonClient;
    @Autowired
    KuaishouUserEventService kuaishouUserEventService;

    private static final AtomicLong log1 = new AtomicLong(0);
    private static final AtomicLong log2 = new AtomicLong(0);
    private static final AtomicLong log3 = new AtomicLong(0);

    // 用户每次启动之后 上报埋点是否正常
    private static final String AppStatus = "AppStatus";

    /**
     * 处理应用启动数据
     */
    public String consume(List<String> flist) {
        flist.parallelStream().forEach(msg ->{
            try {
                consume(msg);
            } catch (Exception e) {
                log.error("消费单个startUp异常",e);
            }
        });
        return "";
    }

    public void consume(String msg) throws Exception {

        if (log1.getAndIncrement() % 100 == 0) {
            log.info("应用启动数据 " + msg);
        }

        CoreEventReq mqMsgObj = JSON.parseObject(msg, CoreEventReq.class);
        if (mqMsgObj.getSendTime() == null || mqMsgObj.getSendTime().before(LocalDateTime.now().minusMinutes(10).toDate())) {
            if (log2.getAndIncrement() % 100 == 0) {
                String time = mqMsgObj.getSendTime() == null ? "null" : DateUtils.formatDate(mqMsgObj.getSendTime());
                log.info("应用启动数据 发送时间较早不再回传 {} {}" , time ,msg);
            }
            // 初始数据已全部消费，仅打印日志即可，不用再跳过
//            return;
        }
        // 目前topic中有 Startup 和 AppStatus 两种事件，只使用其中的AppStatus事件
        if (!Objects.equals(AppStatus, mqMsgObj.getEvent())) {
            return;
        }
        EventProperties mqMsg = mqMsgObj.getProperties();

        if (mqMsg == null) {
            if (log3.getAndIncrement() % 100 == 0) {
                log.info("应用启动数据 properties数据为空 " + msg);
            }
        }

        // 非开启的产品不回传
        if (kuaishouExtApolloConfig.illegalProduct(mqMsg.getProduct())) {
            return;
        }

        // 类似回传逻辑，构造userEventReq请求事件的参数
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setUserId(mqMsg.getUserId());
        userEventReq.setProduct(mqMsg.getProduct());
        userEventReq.setAppId(ObjectUtils.defaultIfNull(ProductCache.getAppId(mqMsg.getProduct()), -1));
        userEventReq.setOs(mqMsg.$os);
        // ios时 取device_id明文，android时取 md5(imei)
        userEventReq.setOcpcDeviceId("ios".equals(mqMsg.$os) ? mqMsg.$device_id : MD5Utils.getMd5Sum(mqMsg.getImei()));
        // ios时 取device_id明文，android时取 imei明文
        userEventReq.setSourceDeviceId("ios".equals(mqMsg.$os) ? mqMsg.$device_id : mqMsg.getImei());
        userEventReq.setOaid(mqMsg.getOaid());
        userEventReq.setMac(mqMsg.getMac());
        userEventReq.setAndroidId(mqMsg.getAndroid_id());
        userEventReq.setEventType(ToutiaoEventTypeEnum.LTV.value);

        UserEventReq.setMd5Inf(userEventReq);

        OcpcEvent ocpcEvent = redisEventService.getActiveEvent(userEventReq);

        if (ocpcEvent != null) {
            // 快手的走 回传快手衍生事件 的逻辑
            if (DspType.KUAISHOU.value.equals(ocpcEvent.getDsp())) {
                kuaishouUserEventService.callbackExtProcess(userEventReq, ocpcEvent, mqMsg.getProduct(), "0", mqMsgObj.getSendTime().getTime(), KuaishouEventType.ACTIVE_NUM);
                return;
            }
        }

    }

    public static void main(String[] args) {

        String msg = "{\"distinct_id\":\"bb146c3bca8a4644\",\"event\":\"Startup\",\"lib\":{\"$lib\":\"Android\",\"$lib_method\":\"code\",\"$lib_version\":\"5.2.1\"},\"project\":\"mvp\",\"properties\":{\"$app_version\":\"1.1.0\",\"$carrier\":\"中国联通\",\"$device_id\":\"bb146c3bca8a4644\",\"$is_first_day\":true,\"$lib\":\"Android\",\"$lib_version\":\"5.2.1\",\"$manufacturer\":\"blackshark\",\"$model\":\"SHARK PRS-A0\",\"$os\":\"android\",\"$os_version\":\"11\",\"$screen_height\":2400,\"$screen_width\":1080,\"android_id\":\"bb146c3bca8a4644\",\"channel\":\"kslmkxhgd12f\",\"imei\":\"bb146c3bca8a4644\",\"ip\":\"************\",\"mac\":\"0A:1E:75:B4:2F:52\",\"oaid\":\"1fbc44d43f44c47f\",\"product\":\"kxhgd\",\"userId\":\"571783728\"},\"sendTime\":1648224271134,\"type\":\"track\"}\n";

        CoreEventReq mqMsgObj = JSON.parseObject(msg, CoreEventReq.class);
        EventProperties mqMsg = mqMsgObj.getProperties();

        // 类似回传逻辑，构造userEventReq请求事件的参数
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setUserId(mqMsg.getUserId());
        userEventReq.setProduct(mqMsg.getProduct());
//        userEventReq.setAppId(ProductCache.getAppId(mqMsg.getProduct()));
        userEventReq.setOs(mqMsg.$os);
        // ios时 取device_id明文，android时取 md5(imei)
        userEventReq.setOcpcDeviceId("ios".equals(mqMsg.$os) ? mqMsg.$device_id : MD5Utils.getMd5Sum(mqMsg.getImei()));
        // ios时 取device_id明文，android时取 imei明文
        userEventReq.setSourceDeviceId("ios".equals(mqMsg.$os) ? mqMsg.$device_id : mqMsg.getImei());
        userEventReq.setOaid(mqMsg.getOaid());
        userEventReq.setMac(mqMsg.getMac());
        userEventReq.setAndroidId(mqMsg.getAndroid_id());
        userEventReq.setEventType(ToutiaoEventTypeEnum.LTV.value);

        UserEventReq.setMd5Inf(userEventReq);

        System.out.println(JSON.toJSONString(userEventReq, SerializerFeature.PrettyFormat));
    }


}
