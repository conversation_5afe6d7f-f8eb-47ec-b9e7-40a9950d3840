package com.coohua.core.caf.dispense.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @Date 2020/7/2 15:08
 */
@Slf4j
@Component
public class ApiUtils {
    private static final int timeOut = 300000;

    public static JSONObject httpExec(HttpEntityEnclosingRequestBase httpEntity) {
        String json = httpExecForStr(httpEntity);
        return JSONObject.parseObject(json);
    }

    public static String httpExecForStr(HttpEntityEnclosingRequestBase httpEntity) {
        CloseableHttpResponse response = null;
        CloseableHttpClient client =  HttpClients.createDefault();;
        long dtime = System.currentTimeMillis();
        try {
//            client = HttpClientBuilder.create().build();
            RequestConfig requestConfig = RequestConfig.custom().setConnectionRequestTimeout(timeOut)
                    .setSocketTimeout(timeOut).setConnectTimeout(timeOut).build();
            httpEntity.setConfig(requestConfig);
//            client = HttpClientPoolFactory.getInstance().createHttpClient();
            //client = HttpClientBuilder.create().build();
            response = client.execute(httpEntity);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
                StringBuffer result = new StringBuffer();
                String line = "";
                while ((line = bufferedReader.readLine()) != null) {
                    result.append(line);
                }
                bufferedReader.close();
                String json = result.toString();
                log.info("httpcost "+httpEntity.getURI()+" "+(System.currentTimeMillis()-dtime));
                return json;
            }
        } catch (IOException e) {
            log.error("", e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if (client != null) {
                      client.close();
                }
            } catch (IOException e) {
                log.error("", e);
            }
        }
        return null;
    }


    public static JSONObject _httpPost(String url, Map data) {
        HttpPost httpEntity = new HttpPost(url);
        httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));
        return httpExec(httpEntity);
    }

    public static JSONObject _httpPost(String access_token, String url, Map data) {
        HttpPost httpEntity = new HttpPost(url);
        httpEntity.setHeader("Access-Token", access_token);
        httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));
        return httpExec(httpEntity);
    }

    public static JSONObject postForm(String accessToken, String url, Map<String, Object> data) {
        ContentType contentType = ContentType.MULTIPART_FORM_DATA;
        HttpPost httpEntity = new HttpPost(url);
        httpEntity.setHeader("Access-Token", accessToken);
//        httpEntity.setHeader("Content-Type", "multipart/form-data");
//        httpEntity.setHeader("X-Debug-Mode", "1");

        MultipartEntityBuilder entityBuilder = MultipartEntityBuilder.create();
        data.entrySet().forEach(entry-> {
            if (entry.getValue() instanceof File) {
                entityBuilder.addPart(entry.getKey(), new FileBody((File) entry.getValue()));
            } else {
                entityBuilder.addTextBody(entry.getKey(), String.valueOf(entry.getValue()));
            }
        });

        httpEntity.setEntity(entityBuilder.build());
        return httpExec(httpEntity);
    }

    public static JSONObject refreshAccessToken(String url, Map data) {
        HttpPost httpEntity = new HttpPost(url);
        httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));

        return httpExec(httpEntity);
    }


    public static JSONObject postBody(String accessToken, String url, String jsonString) {
        HttpPost httpEntity = new HttpPost(url);
        httpEntity.setHeader("Access-Token", accessToken);
        httpEntity.setHeader("Content-Type", "application/json");
        httpEntity.setEntity(new StringEntity(jsonString, ContentType.APPLICATION_JSON));

        return httpExec(httpEntity);
    }

    public static String _httpGet(String access_token, String url, Map data) {
        HttpEntityEnclosingRequestBase httpEntity = new HttpEntityEnclosingRequestBase() {
            @Override
            public String getMethod() {
                return "GET";
            }
        };

        httpEntity.setHeader("Access-Token", access_token);
        httpEntity.setURI(URI.create(url));
        httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(data), ContentType.APPLICATION_JSON));

        return httpExecForStr(httpEntity);
    }

    public static <T> T execAndRetry(Callable<T> callable) {
        return execAndRetry(callable, 1);
    }

    /**
     *
     * @param retryNum 重试次数 表示首次执行callable后重试的次数
     */
    public static <T> T execAndRetry(Callable<T> callable, int retryNum) {
        T res = null;
        do {
            try {
                res = callable.call();
            } catch (Exception e) {
                log.info("execAndRetryException", e);
            }
        } while (res == null && retryNum-- > 0);
        return res;
    }


}
