package com.coohua.core.caf.dispense.ck.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

@Slf4j
@Configuration
@MapperScan(basePackages = {"com.coohua.core.caf.dispense.ck.mapper"}, sqlSessionFactoryRef = "clickHouseSqlSessionFactory10")
public class Click12DataSourceConfig {


    @Value("${spring.click.datasource.username}")
    private String userName;

    @Value("${spring.click.datasource.password}")
    private String passWord;

    @Value("${spring.click.datasource.url}")
    private String url;

    @Value("${spring.click.datasource.driverClass}")
    private String driverClass;

    @Value("${spring.click.datasource.maxActive}")
    private Integer maxActive;

    @Value("${spring.click.datasource.maxWait}")
    private Integer maxWait;

    @Value("${spring.click.datasource.initialSize}")
    private Integer initialSize;

    @Value("${spring.click.datasource.testWhileIdle}")
    private Boolean testWhileIdle;



    @Bean(name = "datasourceClickHouse10")
    public DataSource dataSource() throws SQLException{
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setUsername(userName);
        druidDataSource.setPassword(passWord);
        druidDataSource.setUrl(url);
        druidDataSource.setDriverClassName(driverClass);
        druidDataSource.setMaxActive(maxActive);
        // 配置从连接池获取连接等待超时的时间
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setTestWhileIdle(testWhileIdle);
        // 打开后，增强timeBetweenEvictionRunsMillis的周期性连接检查，minIdle内的空闲连接，每次检查强制验证连接有效性. 参考：https://github.com/alibaba/druid/wiki/KeepAlive_cn
        druidDataSource.init();
        return druidDataSource;
    }

    @Bean(name="clickHouseSqlSessionFactory10")
    @ConditionalOnBean(name = "datasourceClickHouse10")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceClickHouse10") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        return mybatisSqlSessionFactoryBean.getObject();
    }

}
