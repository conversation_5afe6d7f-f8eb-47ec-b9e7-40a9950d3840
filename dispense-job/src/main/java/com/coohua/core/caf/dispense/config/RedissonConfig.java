package com.coohua.core.caf.dispense.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
@Configuration
public class RedissonConfig {

    @Value("${app.jedis-cluster.bp-dispense.address}")
    public String address;

    @Bean
    public RedissonClient getRedissonClient() {

        Config config = new Config();
        String prefix = "redis://";

        String[] nodeAddress = Arrays.stream(address.split(",")).map(k -> prefix + k).collect(Collectors.toList()).toArray(new String[0]);

        //指定使用集群部署方式
        config.useClusterServers()
                .addNodeAddress(nodeAddress);

        //创建客户端(发现这一非常耗时，基本在2秒-4秒左右)
        return Redisson.create(config);
    }

}
