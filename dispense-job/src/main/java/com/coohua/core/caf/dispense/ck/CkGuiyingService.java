package com.coohua.core.caf.dispense.ck;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.ck.mapper.CkToutiaoClickMapper;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.mapper.UserActiveMapper;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CkGuiyingService {
    @Autowired
    CkToutiaoClickMapper ckToutiaoClickMapper;
    @Autowired
    UserActiveService userActiveService;
    @Autowired
    UserActiveMapper userActiveMapper;
    @Autowired
    HbaseUserActiveService hbaseUserActiveService;

    public void activeUser(List<UserActive> userActiveList,int historyDays,boolean isNew){
        int failNum = 0;
        int successNum = 0;
        String logdaySql = getLogdaySql(historyDays);

        String deviceSql = getDeviceSql(userActiveList,isNew);
        if(deviceSql.length()<=10){
            return;
        }
        List<ToutiaoClick>  toutiaoClickList = ckToutiaoClickMapper.queryByDevList(logdaySql,deviceSql);

        if (toutiaoClickList.size()>0){
            Map<String,ToutiaoClick> ocpcDeviceMap = toutiaoClickList.stream().filter(toutiaoClick -> {
                return StringUtils.isNotBlank(toutiaoClick.getOcpcDeviceId()) && StringUtils.isNotBlank(toutiaoClick.getOs()) && StringUtils.isNotBlank(toutiaoClick.getProduct());
            }).collect(Collectors.toMap(toutiaoClick -> {
                return toutiaoClick.getOs()+"@"+toutiaoClick.getProduct()+"@"+toutiaoClick.getOcpcDeviceId();
            }, Function.identity(),(t1,t2)->t1));
            Map<String,ToutiaoClick> macDeviceMap = toutiaoClickList.stream().filter(toutiaoClick -> {
                return StringUtils.isNotBlank(toutiaoClick.getMac()) && StringUtils.isNotBlank(toutiaoClick.getOs()) && StringUtils.isNotBlank(toutiaoClick.getProduct());
            }).collect(Collectors.toMap(toutiaoClick -> {
                return toutiaoClick.getOs()+"@"+toutiaoClick.getProduct()+"@"+toutiaoClick.getMac();
            }, Function.identity(),(t1,t2)->t1));
            Map<String,ToutiaoClick> oaidDeviceMap = toutiaoClickList.stream().filter(toutiaoClick -> {
                return StringUtils.isNotBlank(toutiaoClick.getOaid()) && StringUtils.isNotBlank(toutiaoClick.getOs()) && StringUtils.isNotBlank(toutiaoClick.getProduct());
            }).collect(Collectors.toMap(toutiaoClick -> {
                return toutiaoClick.getOs()+"@"+toutiaoClick.getProduct()+"@"+toutiaoClick.getOaid();
            }, Function.identity(),(t1,t2)->t1));
            Map<String,ToutiaoClick> androidIdDeviceMap = toutiaoClickList.stream().filter(toutiaoClick -> {
                return StringUtils.isNotBlank(toutiaoClick.getAndroidId()) && StringUtils.isNotBlank(toutiaoClick.getOs()) && StringUtils.isNotBlank(toutiaoClick.getProduct());
            }).collect(Collectors.toMap(toutiaoClick -> {
                return toutiaoClick.getOs()+"@"+toutiaoClick.getProduct()+"@"+toutiaoClick.getAndroidId();
            }, Function.identity(),(t1,t2)->t1));

            for(UserActive userActive : userActiveList) {
                UserEventReq userEventReq = userActiveService.getURq(userActive);
                ToutiaoClick toutiaoClick = getByTk(ocpcDeviceMap,macDeviceMap,oaidDeviceMap,androidIdDeviceMap,userEventReq);

                if(toutiaoClick!=null){
                    userActive.setAccountId(toutiaoClick.getAccountId());
                    userActive.setSource(toutiaoClick.getDsp());
                    userActive.setUpdateTime(toutiaoClick.getCreateTime());
                    userActive.setAcDesc("CK归因成功 clicktime "+DateUtils.formatDateYMD(toutiaoClick.getCreateTime()));
                    hbaseUserActiveService.saveUserActive(userActive);
                    successNum++;
                }else{
                    userActive.setAcDesc("CK归因失败");
                    failNum++;
                }
            }
            userActiveService.updateBatchById(userActiveList);
        }

        if(toutiaoClickList.size()==0 && StringUtils.isNotBlank(deviceSql)){
            userActiveList.forEach(userActive -> {
                userActive.setAcDesc("CK归因失败");
            });
            userActiveService.updateBatchById(userActiveList);
        }
        log.info("ck归因完成 "+userActiveList.size()+" 条 成功 "+successNum+" 失败 "+failNum);
    }

    private String getDeviceSql(List<UserActive> userActiveList,boolean isNew){
        String deviceSql = "";
        Set<String> descSet = new HashSet<>();
        {
            descSet.add("参数不合法");
            if(isNew){
                descSet.add("CK归因失败");
            }
        }
        for(UserActive userActive : userActiveList){
            if(descSet.contains(userActive.getAcDesc())){
                continue;
            }
            UserEventReq userEventReq = userActiveService.getURq(userActive);
            if(StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getProduct()) && (
                    StringUtils.isNotBlank(userEventReq.getMac()) ||
                            StringUtils.isNotBlank(userEventReq.getAndroidId()) ||
                            StringUtils.isNotBlank(userEventReq.getOcpcDeviceId()) ||
                            StringUtils.isNotBlank(userEventReq.getOaid())
            )){

                if("ios".equalsIgnoreCase(userEventReq.getOs()) && userEventReq.getOcpcDeviceId()!=null){
                    if(StringUtils.isNotBlank(userEventReq.getOcpcDeviceId())){
                        deviceSql = deviceSql+" or (os='"+userEventReq.getOs()+"' and product='"+userEventReq.getProduct()+"' and ocpc_device_id='"+userEventReq.getOcpcDeviceId()+"') ";
                    }
                    if(StringUtils.isNotBlank(userEventReq.getMac())){
                        deviceSql = deviceSql+" or (os='"+userEventReq.getOs()+"' and product='"+userEventReq.getProduct()+"' and mac='"+userEventReq.getMac()+"') ";
                    }
                }else if(StringUtils.isNotBlank(userEventReq.getOaid())
                        || StringUtils.isNotBlank(userEventReq.getOcpcDeviceId())
                        || StringUtils.isNotBlank(userEventReq.getMac())
                        || StringUtils.isNotBlank(userEventReq.getAndroidId())
                ){
                    if(StringUtils.isNotBlank(userEventReq.getOcpcDeviceId())){
                        deviceSql = deviceSql+" or (os='"+userEventReq.getOs()+"' and product='"+userEventReq.getProduct()+"' and ocpc_device_id='"+userEventReq.getOcpcDeviceId()+"') ";
                    }
                    if( StringUtils.isNotBlank(userEventReq.getAndroidId())){
                        deviceSql = deviceSql+" or (os='"+userEventReq.getOs()+"' and product='"+userEventReq.getProduct()+"' and android_id='"+userEventReq.getAndroidId()+"') ";
                    }
                    if(StringUtils.isNotBlank(userEventReq.getOaid())){
                        deviceSql = deviceSql+" or (os='"+userEventReq.getOs()+"' and product='"+userEventReq.getProduct()+"' and oaid='"+userEventReq.getOaid()+"') ";
                    }
                    if(StringUtils.isNotBlank(userEventReq.getMac())){
                        deviceSql = deviceSql+" or (os='"+userEventReq.getOs()+"' and product='"+userEventReq.getProduct()+"' and ocpc_device_id='"+userEventReq.getOcpcDeviceId()+"') ";
                    }
                }
            }else{
                userActive.setAcDesc("参数不合法");
                int updNum = userActiveMapper.updateById(userActive);
                log.warn("参数不合法 "+ JSON.toJSONString(userActive));
            }

        }

        deviceSql = deviceSql.replaceFirst("or","");
        deviceSql = "and ( "+deviceSql+")";
        return deviceSql;
    }


    public ToutiaoClick getByTk(Map<String,ToutiaoClick> ocpcDeviceMap,
                                Map<String,ToutiaoClick>macDeviceMap,
                                Map<String,ToutiaoClick> oaidDeviceMap,
                                Map<String,ToutiaoClick> androidIdDeviceMap,
                                UserEventReq userEventReq){

        ToutiaoClick toutiaoClick = null;
        if(StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getProduct()) && (
                StringUtils.isNotBlank(userEventReq.getMac()) ||
                        StringUtils.isNotBlank(userEventReq.getAndroidId()) ||
                        StringUtils.isNotBlank(userEventReq.getOcpcDeviceId()) ||
                        StringUtils.isNotBlank(userEventReq.getOaid())
        )){

            if("ios".equalsIgnoreCase(userEventReq.getOs()) && userEventReq.getOcpcDeviceId()!=null){
                if(toutiaoClick == null && StringUtils.isNotBlank(userEventReq.getOcpcDeviceId())){
                    toutiaoClick = ocpcDeviceMap.get(userEventReq.getOs()+"@"+userEventReq.getProduct()+"@"+userEventReq.getOcpcDeviceId());
                }
                if(toutiaoClick == null && StringUtils.isNotBlank(userEventReq.getMac())){
                    toutiaoClick = macDeviceMap.get(userEventReq.getOs()+"@"+userEventReq.getProduct()+"@"+userEventReq.getMac());
                }
            }else if(StringUtils.isNotBlank(userEventReq.getOaid())
                    || StringUtils.isNotBlank(userEventReq.getOcpcDeviceId())
                    || StringUtils.isNotBlank(userEventReq.getMac())
            ){
                if(toutiaoClick == null && StringUtils.isNotBlank(userEventReq.getOcpcDeviceId())){
                    toutiaoClick = ocpcDeviceMap.get(userEventReq.getOs()+"@"+userEventReq.getProduct()+"@"+userEventReq.getOcpcDeviceId());
                }
                if(toutiaoClick == null &&  StringUtils.isNotBlank(userEventReq.getAndroidId())){
                    toutiaoClick = androidIdDeviceMap.get(userEventReq.getOs()+"@"+userEventReq.getProduct()+"@"+userEventReq.getAndroidId());
                }
                if(toutiaoClick == null && StringUtils.isNotBlank(userEventReq.getOaid())){
                    toutiaoClick = oaidDeviceMap.get(userEventReq.getOs()+"@"+userEventReq.getProduct()+"@"+userEventReq.getOaid());
                }
                if(toutiaoClick == null && StringUtils.isNotBlank(userEventReq.getMac())){
                    toutiaoClick = macDeviceMap.get(userEventReq.getOs()+"@"+userEventReq.getProduct()+"@"+userEventReq.getMac());
                }
            }
        }
        return toutiaoClick;
    }
    private String getLogdaySql(int historyDays){
        String logdaySql = "logday in (";
        for(int i=0;i<historyDays;i++){
            Date ddate = new Date(System.currentTimeMillis()-i* DateTimeConstants.MILLIS_PER_DAY);
            String logday = DateUtils.formatDateYMD(ddate);

            logdaySql = logdaySql +"'"+logday+"',";
        }
        logdaySql = logdaySql+"'"+DateUtils.formatDateYMD(new Date())+"')";
        return logdaySql;
    }
}
