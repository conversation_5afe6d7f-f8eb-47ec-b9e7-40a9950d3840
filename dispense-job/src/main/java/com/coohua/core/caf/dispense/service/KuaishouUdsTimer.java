package com.coohua.core.caf.dispense.service;

import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.KuaishouEventType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.KuaishouUdsAdvertiser;
import com.coohua.core.caf.dispense.ocpc.entity.KuaishouUdsLog;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.KuaishouUdsAdvertiserService;
import com.coohua.core.caf.dispense.ocpc.service.KuaishouUdsLogService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.util.ApiUtils;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 快手 undirectshort 回传(多触点转化数据回传, 将头条关键行为数据回传到快手)
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class KuaishouUdsTimer {

    /**
     * 快手uds回传 公用的token
     */
    @Value("${ocpc.kuaishou.uds.token:7e4318ed4e0b4e57b96d810f1007216a}")
    public String kuaishouUdsToken;

    public static final String KuaishouUdsUrl = "https://ad.partner.gifshow.com/api/v2/undirectshort/track";

    /**
     * 文档地址： https://yiqixie.qingque.cn/d/home/<USER>
     * 频次限制：单客户不超过1000次/秒
     */
    public static RateLimiter rateLimiter;

    private static RateLimiter getRateLimiter() {
        if (rateLimiter == null) {
            rateLimiter = RateLimiter.create(800);
        }
        return rateLimiter;
    }

    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    ProductCache productCache;
    @Autowired
    KuaishouUdsAdvertiserService kuaishouUdsAdvertiserService;
    @Autowired
    KuaishouUdsLogService kuaishouUdsLogService;
    @Autowired
    RedissonService redissonService;

    ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("kuaishouUdsCallback", null, false);
    ExecutorService executorService = new ThreadPoolExecutor(
            3
            , 10,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            threadFactory,
            RejectPolicy.CALLER_RUNS.getValue()
    );

    /**
     * 快手uds回传
     */
    @PostConstruct
    public void kuaishouUdsCallback() {
        if (SystemInfo.isWin()) {
            return;
        }
        executorService.submit(() -> {
            while (true) {
                try {
                    Set<String> kuaishouUdsAdvertiserProductCns = kuaishouUdsAdvertiserService.getKuaishouUdsAdvertiserMap().keySet();

                    if (CollectionUtils.isEmpty(kuaishouUdsAdvertiserProductCns)) {
                        TimeUnit.SECONDS.sleep(3);
                        continue;
                    }

                    Map<String, String> productEn2CnMap = productCache.getProductEn2CnMap(kuaishouUdsAdvertiserProductCns);

                    if (CollectionUtils.isEmpty(productEn2CnMap.keySet())) {
                        TimeUnit.SECONDS.sleep(3);
                        continue;
                    }

                    List<Long> ocpcEventIds = ocpcEventService.lambdaQuery()
                            .select(OcpcEvent::getId)
                            .eq(OcpcEvent::getDsp, DspType.TOUTIAO.value)
                            .eq(OcpcEvent::getEventType, ToutiaoEventTypeEnum.KEY_EVENT.value)
                            // kuaishouUdsTag 是否回传过的标记 0待回传 1已回传
                            .eq(OcpcEvent::getKuaishouUdsTag, 0)
                            .in(OcpcEvent::getProduct, productEn2CnMap.keySet())
                            // 只查询1小时内的
                            .ge(OcpcEvent::getCreateTime, LocalDateTime.now().minusHours(1).toDate())
                            .list()
                            .stream()
                            .map(OcpcEvent::getId)
                            .collect(Collectors.toList());

//                    ocpcEvents = ocpcEvents.stream().limit(1).collect(Collectors.toList());

                    log.info("快手uds回传 待回传记录总数 {}", ocpcEventIds.size());

                    CompletableFuture[] futureArray = ocpcEventIds.stream()
                            .map(ocpcEventId -> CompletableFuture.runAsync(() -> {
                                String lockKey = RedisKeyConstants.KUAISHOU_UDS_LOCK_KEY + ocpcEventId;
                                redissonService.lockRun(lockKey, "快手uds回传", () -> callback(ocpcEventId, productEn2CnMap));
                            }, executorService))
                            .toArray(CompletableFuture[]::new);

                    CompletableFuture.allOf(futureArray).join();

                    TimeUnit.SECONDS.sleep(3);
                } catch (Exception e) {
                    log.error("kuaishouUdsCallback异常", e);
                    TimeUnit.SECONDS.sleep(3);
                }
            }
        });
    }

    private void callback(Long ocpcEventId, Map<String, String> productEn2CnMap) {

        try {
            OcpcEvent ocpcEvent = ocpcEventService.getById(ocpcEventId);

            if (!Objects.equals(ocpcEvent.getKuaishouUdsTag(), 0)) {
                // 若该记录标记为已回传过，不重复处理
                return;
            }

            String productCn = productEn2CnMap.get(ocpcEvent.getProduct());

            if (StringUtils.isBlank(productCn)) {
                log.warn("快手uds回传 产品不存在 {}", ocpcEvent.getProduct());
                return;
            }

            KuaishouUdsAdvertiser kuaishouUdsAdvertiser = kuaishouUdsAdvertiserService.getKuaishouUdsAdvertiser(productCn);

            if (kuaishouUdsAdvertiser == null || kuaishouUdsAdvertiser.getAdvertiserId() == null) {
                log.warn("快手uds回传 产品下未配置广告主id {}", productCn);
                return;
            }

            Long advertiserId = kuaishouUdsAdvertiser.getAdvertiserId();

            boolean isIos = "ios".equalsIgnoreCase(ocpcEvent.getOs());

            String reqUrl = KuaishouUdsUrl + "?token=" + kuaishouUdsToken;
            JSONObject body = new JSONObject()
                    .fluentPut("accountMd5", MD5Utils.getMd5Sum(advertiserId.toString()))
                    .fluentPut("eventType", KuaishouEventType.REGISTER.name)
                    .fluentPut("eventTime", ocpcEvent.getCreateTime().getTime())
                    .fluentPut("imeiMd5", isIos || ocpcEvent.getSourceDeviceId() == null ? null : MD5Utils.getMd5Sum(ocpcEvent.getSourceDeviceId()))
                    .fluentPut("oaid", ocpcEvent.getOaid() == null ? null : ocpcEvent.getOaid())
                    .fluentPut("macMd5", ocpcEvent.getMacId() == null ? null : ocpcEvent.getMacId())
                    .fluentPut("androididMd5", ocpcEvent.getAndroidId() == null ? null : ocpcEvent.getAndroidId())
                    .fluentPut("idfaMd5", isIos && ocpcEvent.getSourceDeviceId() != null ? MD5Utils.getMd5Sum(ocpcEvent.getSourceDeviceId()) : null);

            JSONObject response;
            int retryNum = 2;
            do {
                getRateLimiter().acquire();
                response = ApiUtils._httpPost(reqUrl, body);
            } while (response == null && retryNum-- > 0);

            if (response == null) {
                log.info("快手uds回传 重试请求后依然返回空值 {} {}", reqUrl, body.toJSONString());
                return;
            }

            if (Objects.equals(response.getInteger("result"), 1)) {
                log.info("快手uds回传 回传成功 {} {}", advertiserId, body.toJSONString());
                ocpcEventService.lambdaUpdate().set(OcpcEvent::getKuaishouUdsTag, 1).eq(OcpcEvent::getId, ocpcEvent.getId()).update();
                KuaishouUdsLog log = new KuaishouUdsLog();
                BeanUtils.copyProperties(ocpcEvent, log);
                log.setId(null);
                log.setReqUrl(reqUrl);
                log.setReqData(body.toJSONString());
                log.setRes(response.toJSONString());
                log.setUdsAccountId(advertiserId);
                kuaishouUdsLogService.save(log);
            } else {
                log.error("快手uds回传 回传失败 {} {} {}", advertiserId, response.toJSONString(), body.toJSONString());
            }

        } catch (Exception e) {
            log.error("快手uds回传 单个回传时异常 " + ocpcEventId, e);
        }

    }


}
