package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

@Data
public class TencentClickReq {
    private String dsp;//头条
    private String product;//写死的
    private String os;//写死的
    private String account_id;//账户ID

    private String click_id;//(点击ID)
    private Long click_time;//(点击时间)
    private String advertiser_id;//(广告主ID)
    private String  muid;//(设备ID)
    private String mac;//(mac地址)
    private String ip;//(IP地址)
    private String user_agent;//(用户代理)
    private String app_type;//(应用类型)
    private String appid;//(应用ID)
    private String android_id;//(安卓ID)
    private String campaign_id;//(推广计划ID)
    private String adgroup_id;//(广告ID)
    private String creative_id;//(素材ID)
    private String deeplink_url;//(应用直达url)
    private String agent_id;//(代理商ID)
    private String oaid;//(移动终端补充设备标识)
    private String  adgroup_name;//(广告名称)
    private String  click_sku_id;//(点击的SKUID)
    private String  process_time;//(请求时间)
    private String  product_type;//(商品类型)
    private String request_id;//(请求ID)
    private String ipv6;//(IPV6地址)
    private String wechat_openid;
    private String model;//(手机型号)
    private String caid;//(手机型号)
    private String caid2;//(手机型号)
}
