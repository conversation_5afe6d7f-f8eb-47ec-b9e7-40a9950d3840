package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WeekendUpArpu implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String groupName;
    private String productName;
    private Integer appId;
    private String product;
    private String os;
    private Integer strategyId;
    private String strategyName;
    private Integer statue;
    private Date updateTime;
    private Date createTime;
    private String describ;
    private Integer delFlag;
}
