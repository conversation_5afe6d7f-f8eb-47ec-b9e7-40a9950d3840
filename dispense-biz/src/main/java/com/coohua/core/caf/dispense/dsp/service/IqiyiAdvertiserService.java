package com.coohua.core.caf.dispense.dsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.AccountAction;
import com.coohua.core.caf.dispense.dsp.entity.IqiyiAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.mapper.IqiyiAdvertiserMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.enums.DspType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.matchNewRule;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class IqiyiAdvertiserService extends ServiceImpl<IqiyiAdvertiserMapper, IqiyiAdvertiser> implements IService<IqiyiAdvertiser> {

    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    // <advertiserId, BaiduAdvertiser>
    private Map<String, IqiyiAdvertiser> iqiyiAdvertiserMap = Maps.newHashMap();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();

    //    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void initAccountActionConfig() {
        List<IqiyiAdvertiser> list = lambdaQuery()
                .isNotNull(IqiyiAdvertiser::getAdvertiserId)
                .eq(IqiyiAdvertiser::getDelFlag, DELFLAG.weishanchu.value)
                .list();

        iqiyiAdvertiserMap = list.stream().collect(Collectors.toMap(k-> k.getAdvertiserId(), k->k, (oldVal, newVal)-> oldVal));

        appMap = list.stream()
                .filter(a -> a.getProductName() != null)
                .collect(Collectors.groupingBy(IqiyiAdvertiser::getProductName,
                        Collectors.collectingAndThen(Collectors.toList(), apps -> apps.stream()
                                .flatMap(this::convertAction)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()))));
    }

    private Stream<AccountAction> convertAction(IqiyiAdvertiser iqiyiAdvertiser) {
        try {
            // 无配置
            if (StringUtils.isBlank(iqiyiAdvertiser.getEventTypes())) {
                return null;
            }

            if (StringUtils.isBlank(iqiyiAdvertiser.getEventValues()) || StringUtils.isBlank(iqiyiAdvertiser.getEventTypes())) {
                return Stream.empty();
            }

            return AuthKuaishouAdvertiserService.buildEventStandardConsideringMultiEcpm(iqiyiAdvertiser.getEventTypes(),
                    iqiyiAdvertiser.getEventValues(), new BigInteger(iqiyiAdvertiser.getAdvertiserId()),
                    iqiyiAdvertiser.getMaxAccumulatePeriod());

        }catch (Exception e){
            log.error("爱奇艺广告主配置信息解析出错 ",e);
        }

        return null;
    }

    public IqiyiAdvertiser getByAccountId(String advertiserId){
        return iqiyiAdvertiserMap.get(advertiserId);
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        return appMap;
    }

    public List<AccountAction> queryActionConfig(String appName) {
        return appMap.getOrDefault(appName, Lists.newArrayList());
    }

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();

            String advertiserId = toutiaoClick.getAccountId();
            IqiyiAdvertiser advertiser = getByAccountId(advertiserId);

            // 若无配置或未配置行为参数 仅当默认值时上报
            if (advertiser == null || StringUtils.isBlank(advertiser.getEventTypes()) || StringUtils.isBlank(advertiser.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, advertiser.getEventTypes(),
                        advertiser.getEventValues(), firstTimeJudge, dspType.value, advertiser.getProductName(),
                        advertiserId, advertiser.getMaxAccumulatePeriod());
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, advertiser.getEventTypes(),
                        advertiser.getEventValues(), firstTimeJudge, dspType.value, advertiser.getProductName(),
                        advertiserId, advertiser.getMaxAccumulatePeriod());
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }


}
