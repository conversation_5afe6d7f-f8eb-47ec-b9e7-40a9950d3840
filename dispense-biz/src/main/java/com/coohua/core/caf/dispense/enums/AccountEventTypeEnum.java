package com.coohua.core.caf.dispense.enums;

public enum AccountEventTypeEnum {
	pay("6", "付费关键行为"),
	;
	private String value;
	public String desc;

	AccountEventTypeEnum(String value,String desc) {
		this.value = value;
		this.desc = desc;
	}

	public static AccountEventTypeEnum getType(String value) {
		if (value != null) {
			AccountEventTypeEnum[] otypes = AccountEventTypeEnum.values();
			for (AccountEventTypeEnum memberType : otypes) {
				if (value.equals(memberType.value)) {
					return memberType;
				}
			}
		}
		return null;
	}
}
