package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.constant.HBaseParameterEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.CellUtil;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class HbaseUtils {
    public static String getCellValStr(Result res){
        List<Cell> cells = res.listCells();
        if(cells!=null && cells.size()>0){
            byte[] val = res.getValue(Bytes.toBytes("family"),Bytes.toBytes("qualifier"));
            return Bytes.toString(val);
        }
        return null;
    }

    public static List<String> getListValStr(Result res){
        List<Cell> cells = res.listCells();
        List<String> list = new ArrayList<>();
        if(cells!=null && !cells.isEmpty()){
            for (Cell cell : cells) {
                byte[] valueBytes = CellUtil.cloneValue(cell);
                String string = Bytes.toString(valueBytes);
                list.add(string);
            }
        }
        log.info("查询ListValStr size {} {}", list.size(), list);
        return list;
    }
}
