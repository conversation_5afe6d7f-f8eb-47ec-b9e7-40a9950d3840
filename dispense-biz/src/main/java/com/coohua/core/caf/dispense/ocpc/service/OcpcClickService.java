package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.KuaishouClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoClickService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcClick;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcClickMapper;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcClickMapperExt;
import com.coohua.core.caf.dispense.utils.ConstCls;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-29
 */
@Service
@Slf4j
public class OcpcClickService extends ServiceImpl<OcpcClickMapper, OcpcClick> {

	@Autowired
	OcpcClickMapperExt ocpcClickMapperExt;

	@Resource(name = "ocpcToDspClick")
	ThreadPoolTaskExecutor ocpcToDspClickExecutor;

	@Autowired
	ToutiaoClickService toutiaoClickService;
	@Autowired
	KuaishouClickService kuaishouClickService;

	public static String wbappStartStr = "wbapp-";
	@Autowired
	BigclickService bigclickService;
	/**
	 * 写入头条click事件到ocpcClick
	 *
	 * @param toutiaoClick
	 */
	public boolean saveClick(ToutiaoClick toutiaoClick) {
		try {
			long result = 0 ;
			if(!toutiaoClick.getProduct().startsWith(wbappStartStr)){
				Integer appId = ProductCache.getAppId(toutiaoClick.getProduct());
				if(!bigclickService.isDateQuery(appId)){
					result = saveClickByBigTable(appId,toutiaoClick);
				}
				if(bigclickService.isReadByDate(appId)){
					saveClickByDataTable(appId,toutiaoClick);
				}
			}
			return result == 1;
		} catch (Exception e) {
			log.error("OCPC_NEW ocpc saveClick ", e);
		}
		return false;
	}

	private long saveClickByBigTable(Integer appId,ToutiaoClick toutiaoClick){
		String tableEnd = bigclickService.getTableEnd(appId,toutiaoClick.getOcpcDeviceId());
		long result = 0;
		String appSds = appId+"";
		if(appSds.equals(tableEnd)){
			result = ocpcClickMapperExt.insertIntoClick(toutiaoClick, tableEnd);
		}else{
			//有配置 bigtable
			result = ocpcClickMapperExt.insertIntoClick(toutiaoClick, tableEnd);
			if(bigclickService.isDoubleWrite(appId)){
				result = ocpcClickMapperExt.insertIntoClick(toutiaoClick, appSds);
			}
		}
		return result;
	}

	private long saveClickByDataTable(Integer appId,ToutiaoClick toutiaoClick){
		String tableEnd = bigclickService.getTableSaveDateEnd(appId,toutiaoClick.getOcpcDeviceId());
		long result = ocpcClickMapperExt.insertIntoClick(toutiaoClick, tableEnd);
		return result;
	}


}
