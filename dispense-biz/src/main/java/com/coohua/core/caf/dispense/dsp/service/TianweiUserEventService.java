package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/7/25 13:25
 */
@Service
@Slf4j
public class TianweiUserEventService {
	@Autowired
	private AlertService alertService;
	@Autowired
	private UserEventMapper userEventMapper;

	public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick) {

		try {
			String reqUrl = toutiaoClick.getCallbackUrl();
			String html = HttpClientUtil.get(HttpConfig.custom().timeout(3000).url(reqUrl));

			try {
				saveUserEvent(toutiaoClick,userEventReq, reqUrl, html);
			} catch (Exception e) {
				log.error("save user event failure userEventReq:{},reqUrl:{},respContent:{}", userEventReq, null, null, e);
			}

			JSONObject jobj = JSON.parseObject(html);
			int code = jobj.getInteger("code");
			log.info("天威上报   " + html);
			if (code == 1) {
				alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "天威回调成功");
			} else {
				alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "天威回调失败");
			}
		} catch (Exception e) {
			log.error("天威上报异常: {}, toutiaoClick: {}", userEventReq, toutiaoClick, e);
			return false;
		}
		return true;
	}

	private void saveUserEvent(ToutiaoClick toutiaoClick,UserEventReq userEventReq, String reqUrl, String rspStr) {
		UserEvent userEvent = new UserEvent();

		Date date = new Date();
		userEvent.setAppId(userEventReq.getAppId());
		userEvent.setCreateTime(date);
		userEvent.setUpdateTime(date);
		userEvent.setUserId(userEventReq.getUserId());
		//不传则默认激活上报
		userEvent.setEventType(Optional.ofNullable(userEventReq.getEventType()).orElse(0));
		userEvent.setProduct(userEventReq.getProduct());
		if(toutiaoClick!=null){
			userEvent.setClickId(toutiaoClick.getId());
			userEvent.setAccountId(toutiaoClick.getAccountId());
			userEvent.setAccountName(toutiaoClick.getAidName());
			userEvent.setDsp(toutiaoClick.getDsp());
		}
//        userEvent.setEventTypeName();
		userEvent.setOaid(userEventReq.getOaid());
		userEvent.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
		userEvent.setOs(userEventReq.getOs());
		userEvent.setReqUrl(reqUrl);
		userEvent.setReqRsp(rspStr);
		userEventMapper.insert(userEvent);
	}
}
