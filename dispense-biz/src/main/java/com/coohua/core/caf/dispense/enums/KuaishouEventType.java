package com.coohua.core.caf.dispense.enums;

public enum KuaishouEventType {
    ACTIVATE_APP(0, "1", "激活"),//	         30天内第一次打开APP
    REGISTER(25, "143", "注册"),// 此项其实是关键行为	         用户点击广告后注册成为APP的新用户
    COMPLETE_ORDER(20, "14", "提交订单"),//	           用户点击下单生成订单
    PURCHASE(2, "3", "付费"),//	         用户完成付费
    ADD_TO_CART(22, "13", "加入购物车"),//	     用户将商品加入购物车
    START_APP(6, "7", "次日留存"),//	     用户激活后第二天打开APP的行为
//    KEY_EVENT(25,"6","优质用户"),//	     关键页面访问
//    KEY_EVENT(25,"85","观看广告"),//  首日观看广告行为

    /** 衍生过程回传相关 start */
    ACTIVE_NUM(-5, "5", "活跃次数", false),
    WATCH_AD(-85, "85", "广告观看", false),
    ECPM(-176, "176", "广告eCPM", false),
    /** 衍生过程回传相关 end */


    /** 衍生定义回传相关 start */
    WATCH_AND_ARPU(357, "192", "广告观看加ARPU", false),
    WATCH_AND_ECPM(356, "191", "广告观看加eCPM", false),
    /** 衍生定义回传相关 end */
    EVENT_MINIGAME_IAA(231,"8","快手广告变现")
    ;
    public Integer value;
    public String name;
    public String desc;
    /**
     * 是否是项目组上报时的合法值，部分eventType仅限后端达成特定条件时发送到三方
     */
    public Boolean reportValid;

    KuaishouEventType(Integer value, String name, String desc) {
        this(value, name, desc, true);
    }

    KuaishouEventType(Integer value, String name, String desc, boolean reportValid) {
        this.value = value;
        this.name = name;
        this.desc = desc;
        this.reportValid = reportValid;
    }

    public static KuaishouEventType getStatus(Integer value) {

        return getStatus(value, true);
    }

    public static KuaishouEventType getStatus(Integer value, boolean onlyReportValid) {
        if (value != null) {
            KuaishouEventType[] otypes = KuaishouEventType.values();
            for (KuaishouEventType memberType : otypes) {
                if (onlyReportValid && !memberType.reportValid) {
                    continue;
                }
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
