package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/7/6
 **/
@Data
public class BaiduClickReq {
    // dsp=baidu&product=aishangxiaoshuiguo&os=android&account_id=********&
    // imeiMd5={{IMEI_MD5}}&idfa={{IDFA}}&ts={{TS}}&callback_url={{CALLBACK_URL}}&pid={{PLAN_ID}}&uid={{UNIT_ID}}&aid={{IDEA_ID}}&oaid={{OAID}}
    private String dsp;//baidusem baidufeed
    private String product;//写死的
    private String os;//写死的
    private String account_id;//账户ID

    private String pid;//计划ID
    private String uid;//单元ID
    private String aid;//创意ID
    private String  imeiMd5;//(设备ID md5 值)
    private String idfa;// ios 设备ID
    private String oaid;//oaid 原值
    private String oaid2;//oaid md5
    private String ts;//点击时间
    private String callbackUrl;//回调地址
    private String mac; // md5加密值，去除分隔符 ":"（例：32738C807A28），再进行标准32位小写MD5编码
    private String androidId; // md5加密值，对原值进行标准32位小写MD5编码
    //用来区分搜索和信息流
    private String account_name;
    /**
     * 渠道包标识
     */
    private String pkg_channel;
}
