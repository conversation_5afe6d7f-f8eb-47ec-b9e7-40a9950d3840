package com.coohua.core.caf.dispense.kafka;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.entity.UserActiveMqSaveErrorRecord;
import com.coohua.core.caf.dispense.ocpc.mapper.UserActiveMqSaveErrorRecordMapper;
import com.coohua.core.caf.dispense.ocpc.service.UserActiveService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
public class UserActiveConsumer {


    @Resource
    private UserActiveService userActiveService;

    @Resource
    private UserActiveMqSaveErrorRecordMapper errorRecordMapper;

    private static final int MAX_RETRIES = 3;

    @KafkaListener(topics = "user_active_login",
            groupId = "dispense_user_active_login",
            containerFactory = "userActiveListenerFactory")
    public void consumeUserActive(@Payload String message,
                                  @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                  @Header(KafkaHeaders.RECEIVED_PARTITION_ID) int partition,
                                  @Header(KafkaHeaders.OFFSET) long offset,
                                  Acknowledgment acknowledgment) {
        try {
            log.info("user_active_login_consume user active message:{}", message);
            processBusinessLogic(message, partition, offset);
        } catch (Exception e) {
            // 增加重试
            log.warn("user_active_login_consume failed at first attempt, starting retry mechanism. MessageKey: {}, Error: {}",message, e.getMessage());

            int retryCount = 0;
            boolean success = false;

            while (retryCount < MAX_RETRIES && !success) {
                retryCount++;
                try {
                    log.info("user_active_login_consume Attempting retry {}/{}...", retryCount, MAX_RETRIES);
                    Thread.sleep(5L);
                    processBusinessLogic(message, partition, offset);
                    success = true;
                    log.info("user_active_login_consume Retry {}/{} was successful.", retryCount, MAX_RETRIES);
                } catch (InterruptedException ie) {
                    log.warn("user_active_login_consume Retry interrupted, stopping further retries.", ie);
                    Thread.currentThread().interrupt(); // Preserve interruption status
                    break;
                } catch (Exception retryException) {
                    log.warn("user_active_login_consume Retry attempt {}/{} failed.", retryCount, MAX_RETRIES, retryException);
                }
            }

            if (!success) {
                log.error("user_active_login_consume All {} retries failed for message. Persisting to error table.", MAX_RETRIES, e);
                buildAndSaveErrorRecord(topic, message, e);
            }
        } finally {
            acknowledgment.acknowledge();
        }
    }


    /**
     * 对象转换、保存userActive
     * @param message
     * @param partition
     * @param offset
     */
    private void processBusinessLogic(String message, int partition, long offset) {
        if (StringUtils.isBlank(message)) {
            log.error("user_active_login_consume Received empty message in UserActiveConsumer, partition: {}, offset: {}", partition, offset);
            return;
        }

        UserActive userActive = JSONObject.parseObject(message, UserActive.class);

        if (userActive == null) {
            log.error("user_active_login_consume Parsed user active is null, message: {}, partition: {}, offset: {}", message, partition, offset);
            return;
        }

        userActiveService.activeUserForMq(userActive);
    }

    /**
     * 构建失败消息
     * @param topic
     * @param message
     * @param e
     */
    private void buildAndSaveErrorRecord(String topic, String message, Exception e) {
        try {
            String stackTrace = ExceptionUtils.getStackTrace(e);
            UserActiveMqSaveErrorRecord record = UserActiveMqSaveErrorRecord.builder()
                    .topic(topic)
                    .messageBody(message)
                    .errorStackTrace(stackTrace)
                    .createTime(new Date())
                    .build();
            errorRecordMapper.insert(record);
        } catch (Exception dbException) {
            log.error("user_active_login_consumeCRITICAL: Failed to save error record to database! Message: {}, DBError: {}", message, dbException);
        }
    }
}

