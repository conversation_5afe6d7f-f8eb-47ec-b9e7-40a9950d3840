package com.coohua.core.caf.dispense.dto.req;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.entity.ThirdExtEvent;
import com.coohua.core.caf.dispense.enums.TencentActionType;
import com.coohua.core.caf.dispense.ocpc.service.CaidService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
/**
 * 请求广点通 事件组装
 */
@Data
public class AddTecentActiveReqV2 {
    private List<TencentActionV2> actions;

    public static AddTecentActiveReqV2 getTencentEventReq(UserEventReq userEventReq, String os, TencentActionType... action_types){
        String imei = userEventReq.getOcpcDeviceId();
        String oaid = userEventReq.getOaid();
        String androidId = userEventReq.getAndroidId();
        String mac = userEventReq.getMac();

        AddTecentActiveReqV2 tecentActive = new AddTecentActiveReqV2();
        List<TencentActionV2> tencentActionList = new ArrayList<>();

        for (TencentActionType actionType : action_types) {
            String action_type = actionType.name;

            TencentActionV2 tencentAction = new TencentActionV2();

            tencentAction.setOuter_action_id(UUID.randomUUID().toString());
            tencentAction.setAction_time((System.currentTimeMillis())/1000);
            tencentAction.setAction_type(action_type);

            if(TencentActionType.START_APP.name.equals(action_type)) {
                tencentAction.setAction_param(new JSONObject().fluentPut("length_of_stay",1));
            }

            // 自定义关键行为 需额外传字段
            if(TencentActionType.KEY_EVENT.name.equals(action_type)) {
                tencentAction.setCustom_action("UV_CORE_ACTION");
            }

            if(TencentActionType.PURCHASE.name.equals(action_type)&&userEventReq.getPayAmount()!=null&&userEventReq.getPayAmount()>0) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("value",userEventReq.getPayAmount());
                tencentAction.setAction_param(jsonObject);
                log.info("ocpc支付 腾讯2.0  "+jsonObject.toJSONString());
            }
            boolean isIos = os!=null && "ios".equals(os);

            TencentUserEV2 tencentUserEV2 = new TencentUserEV2();

            if (!isIos) {
                tencentUserEV2.setHash_imei(getNotNullVal(imei));
                tencentUserEV2.setOaid(getNotNullVal(oaid));
                tencentUserEV2.setHash_android_id(getNotNullVal(androidId));
                tencentUserEV2.setHash_mac(getNotNullVal(mac));
                tencentUserEV2.setWechat_openid(getNotNullVal(userEventReq.getOpenId()));
                tencentUserEV2.setWechat_app_id(getNotNullVal(userEventReq.getWAppId()));
            } else {
                // 包含-时认为是idfa原值
                if(StringUtils.isNotBlank(imei)){
                    if (imei.contains("-") && imei.length() == 36) {
                        imei = MD5Utils.getMd5Sum(imei);
                    }
                    tencentUserEV2.setHash_idfa(imei);
                }

                if(StringUtils.isNotBlank(userEventReq.getCaid())){
                    tencentUserEV2.setCaid(userEventReq.getCaid());
                    tencentUserEV2.setCaid_version(CaidService.tencentQAIDVersion);
                }
            }

            tencentAction.setUser_id(tencentUserEV2);
            tencentActionList.add(tencentAction);
        }

        tecentActive.setActions(tencentActionList);

        return tecentActive;
    }

    private static String getNotNullVal(String input) {
        if (StringUtils.isNotBlank(input) && !"null".equals(input)) {
            return input;
        }
        return null;
    }

    /**
     * 衍生回传参数
     */
    public static AddTecentActiveReqV2 getTencentExtEventReq(UserEventReq userEventReq, String os, ThirdExtEvent thirdExtEvent, long callbackSecond){
        String imei = userEventReq.getOcpcDeviceId();
        String oaid = userEventReq.getOaid();
        String androidId = userEventReq.getAndroidId();
        String mac = userEventReq.getMac();

        TencentActionType tencentActionType = TencentActionType.getStatus(thirdExtEvent.getToutiaoEventType().intValue());

        AddTecentActiveReqV2 tecentActive = new AddTecentActiveReqV2();
        List<TencentActionV2> tencentActionList = new ArrayList<>();

        // 腾讯衍生回传时 action_type=CUSTOM 且 custom_action=PV_CORE_ACTION
        String action_type = "CUSTOM";
        String custom_action = "PV_CORE_ACTION";

        TencentActionV2 tencentAction = new TencentActionV2();

        tencentAction.setOuter_action_id(UUID.randomUUID().toString());
        tencentAction.setAction_time(callbackSecond);
        tencentAction.setAction_type(action_type);
        tencentAction.setCustom_action(custom_action);
        tencentAction.setAction_param(genParams(thirdExtEvent, tencentActionType));

        boolean isIos = os!=null && "ios".equals(os);

        TencentUserEV2 tencentUserEV2 = new TencentUserEV2();

        if (!isIos) {
            tencentUserEV2.setHash_imei(getNotNullVal(imei));
            tencentUserEV2.setOaid(getNotNullVal(oaid));
            tencentUserEV2.setHash_android_id(getNotNullVal(androidId));
            tencentUserEV2.setHash_mac(getNotNullVal(mac));
            tencentUserEV2.setWechat_openid(getNotNullVal(userEventReq.getOpenId()));
            tencentUserEV2.setWechat_app_id(getNotNullVal(userEventReq.getWAppId()));
        } else {
            // 包含-时认为是idfa原值
            if(StringUtils.isNotBlank(imei)){
                if (imei.contains("-") && imei.length() == 36) {
                    imei = MD5Utils.getMd5Sum(imei);
                }
                tencentUserEV2.setHash_idfa(imei);
            }
            if(StringUtils.isNotBlank(userEventReq.getCaid())){
                tencentUserEV2.setCaid(userEventReq.getCaid());
                tencentUserEV2.setCaid_version(CaidService.tencentQAIDVersion);
            }
        }

        tencentAction.setUser_id(tencentUserEV2);
        tencentActionList.add(tencentAction);

        tecentActive.setActions(tencentActionList);

        return tecentActive;
    }

    private static JSONObject genParams(ThirdExtEvent thirdExtEvent, TencentActionType tencentActionType) {
        return new TencentExtParams()
                .setKey_type(tencentActionType.extKeyType)
                .setIs_og_convert(ObjectUtils.defaultIfNull(thirdExtEvent.getIsGaConvert(), 0L))
                .setDepth(thirdExtEvent.getDepth())
                .setKey_action1(thirdExtEvent.getActionType1())
                .setValue1(thirdExtEvent.getValue1())
                .setKey_action2(thirdExtEvent.getActionType2())
                .setValue2(thirdExtEvent.getValue2())
                .setKey_action3(thirdExtEvent.getActionType3())
                .setValue3(thirdExtEvent.getValue3())
                .toJSONObject();
    }

    /**
     * ltv回传参数
     */
    public static AddTecentActiveReqV2 getTencentLtvReq(UserEventReq userEventReq, String os, long callbackSecond, int ecpm){
        String imei = userEventReq.getOcpcDeviceId();
        String oaid = userEventReq.getOaid();
        String androidId = userEventReq.getAndroidId();
        String mac = userEventReq.getMac();


        AddTecentActiveReqV2 tencentActive = new AddTecentActiveReqV2();
        List<TencentActionV2> tencentActionList = new ArrayList<>();

        String action_type = TencentActionType.LTV.name;

        TencentActionV2 tencentAction = new TencentActionV2();

        tencentAction.setOuter_action_id(UUID.randomUUID().toString());
        tencentAction.setAction_time(callbackSecond / 1000);
        tencentAction.setAction_type(action_type);
        tencentAction.setAction_param(new JSONObject()
                .fluentPut("claim_type", 4)
                .fluentPut("value", ecpm)
        );

        boolean isIos = os!=null && "ios".equals(os);

        TencentUserEV2 tencentUserEV2 = new TencentUserEV2();

        if (!isIos) {
            tencentUserEV2.setHash_imei(getNotNullVal(imei));
            tencentUserEV2.setOaid(getNotNullVal(oaid));
            tencentUserEV2.setHash_android_id(getNotNullVal(androidId));
            tencentUserEV2.setHash_mac(getNotNullVal(mac));
            tencentUserEV2.setWechat_openid(getNotNullVal(userEventReq.getOpenId()));
            tencentUserEV2.setWechat_app_id(getNotNullVal(userEventReq.getWAppId()));
        } else {
            // 包含-时认为是idfa原值
            if(StringUtils.isNotBlank(imei)){
                if (imei.contains("-") && imei.length() == 36) {
                    imei = MD5Utils.getMd5Sum(imei);
                }
                tencentUserEV2.setHash_idfa(imei);
            }
            if(StringUtils.isNotBlank(userEventReq.getCaid())){
                tencentUserEV2.setCaid(userEventReq.getCaid());
                tencentUserEV2.setCaid_version(CaidService.tencentQAIDVersion);
            }
        }

        tencentAction.setUser_id(tencentUserEV2);
        tencentActionList.add(tencentAction);

        tencentActive.setActions(tencentActionList);

        return tencentActive;
    }

    public static AddTecentActiveReqV2 getTencentEventReq2(UserEventReq userEventReq, String os, TencentActionType... action_types) {
        String imei = userEventReq.getOcpcDeviceId();
        String oaid = userEventReq.getOaid();
        String androidId = userEventReq.getAndroidId();
        String mac = userEventReq.getMac();

        AddTecentActiveReqV2 tecentActive = new AddTecentActiveReqV2();
        List<TencentActionV2> tencentActionList = new ArrayList<>();

        for (TencentActionType actionType : action_types) {
            String action_type = actionType.name;

            TencentActionV2 tencentAction = new TencentActionV2();

            tencentAction.setOuter_action_id(UUID.randomUUID().toString());
            tencentAction.setAction_time((System.currentTimeMillis())/1000);
            tencentAction.setAction_type(action_type);

            if(TencentActionType.START_APP.name.equals(action_type)) {
                tencentAction.setAction_param(new JSONObject().fluentPut("length_of_stay",1));
            }

            // 自定义关键行为 需额外传字段
            if(TencentActionType.KEY_EVENT.name.equals(action_type)) {
                tencentAction.setCustom_action("UV_CORE_ACTION");
            }

            if(TencentActionType.PURCHASE.name.equals(action_type)&&userEventReq.getPayAmount()!=null&&userEventReq.getPayAmount()>0) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("value",userEventReq.getPayAmount());
                tencentAction.setAction_param(jsonObject);
                log.info("ocpc支付 腾讯2.0  "+jsonObject.toJSONString());
            }
            boolean isIos = os!=null && "ios".equals(os);

            TencentUserEV2 tencentUserEV2 = new TencentUserEV2();

            if (!isIos) {
                tencentUserEV2.setHash_imei(getNotNullVal(imei));
                tencentUserEV2.setOaid(getNotNullVal(oaid));
                tencentUserEV2.setHash_android_id(getNotNullVal(androidId));
                tencentUserEV2.setHash_mac(getNotNullVal(mac));
                tencentUserEV2.setWechat_openid(getNotNullVal(userEventReq.getOpenId()));
                tencentUserEV2.setWechat_app_id(getNotNullVal(userEventReq.getWAppId()));
            } else {
                // 包含-时认为是idfa原值
                if(StringUtils.isNotBlank(imei)){
                    if (imei.contains("-") && imei.length() == 36) {
                        imei = MD5Utils.getMd5Sum(imei);
                    }
                    tencentUserEV2.setHash_idfa(imei);
                }

                if(StringUtils.isNotBlank(userEventReq.getCaid())){
                    tencentUserEV2.setCaid(userEventReq.getCaid());
                    tencentUserEV2.setCaid_version(CaidService.tencentQAIDVersion2);
                }
            }

            tencentAction.setUser_id(tencentUserEV2);
            tencentActionList.add(tencentAction);
        }

        tecentActive.setActions(tencentActionList);

        return tecentActive;
    }
}

@Setter
@Getter
@Accessors(chain = true)
class TencentExtParams {

    private Integer key_type;
    private Long is_og_convert;
    private Long depth;
    private Long key_action1;
    private Long value1;
    private Long key_action2;
    private Long value2;
    private Long key_action3;
    private Long value3;

    public JSONObject toJSONObject() {
        return (JSONObject) JSONObject.toJSON(this);
    }
}

@Data
class TencentActionV2{
    //  客户唯一行为 id
    private String outer_action_id;
    private long action_time;
    private TencentUserEV2 user_id;
    private String action_type;
    private String custom_action;
    private JSONObject action_param;
}
@Data
class TencentUserEV2{
    private String hash_imei;
    private String hash_idfa;
    private String hash_android_id;
    private String hash_mac;
    private String oaid;
    private String wechat_app_id;
    private String wechat_openid;
    private String caid;
    private String caid_version;
}
