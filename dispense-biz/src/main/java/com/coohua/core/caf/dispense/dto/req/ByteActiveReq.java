package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

@Data
public class ByteActiveReq {
    //projectid=__PROJECT__&promotionid=__PROMOTION__&clickid=__CLICKID__&requestid=__REQUESTID__
    String promotionid;
    String clickid;
    String requestid;
    String accountId;
    String openId;
    String userId;
    Integer appId;
    String product;
    String paramObj;
    public static String os="byte";

    public static String getOs() {
        return os;
    }

    public static void setOs(String os) {
        ByteActiveReq.os = os;
    }
}
