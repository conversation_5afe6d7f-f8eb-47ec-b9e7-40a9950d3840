package com.coohua.core.caf.dispense.utils;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

@Slf4j
public class DateUtils {
    /**
     * 获取今天0点事件
     *
     * @return
     */
    public static Date getNowDayDate() {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String format = dateFormat.format(new Date());
            Date now = dateFormat.parse(format);
            return now;
        } catch (Exception e) {
            log.error(" getNowDayDate error ", e);
        }
        return new Date();
    }
    public static final String PATTERN_YMD = "yyyy-MM-dd";
    public static final String PATTERN_YMDSTR = "yyyyMMdd";
    public static final String PATTERN_YHMS = "yyyy-MM-dd HH:mm:ss";
    public static final String PATTERN_YH = "yyyy-MM-ddHH:00:00";
    public static final String PATTERN_HMS = "HH:mm:ss";

    public static String formatDateForHMS(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(PATTERN_HMS).format(date);
    }

    public static String formatDateForHH(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(PATTERN_YH).format(date);
    }

    public static String formatDateForYMDSTR(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(PATTERN_YMDSTR).format(date);
    }

    public static String formatDate(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(PATTERN_YHMS).format(date);
    }


    public static String formatDateYMD(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(PATTERN_YMD).format(date);
    }

    public static Date getHourDayDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat df = new SimpleDateFormat(PATTERN_YMD);

        String dstr  = df.format(calendar.getTime());

        return parse(dstr,PATTERN_YMD);
    }

    public static Date getHourBeginDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:00:00");

        String dstr  = df.format(calendar.getTime());

        return parse(dstr,"yyyy-MM-dd HH:00:00");
    }

    public static Date getDayBeginDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd 00:00:00");

        String dstr  = df.format(calendar.getTime());

        return parse(dstr,"yyyy-MM-dd 00:00:00");
    }

    public static String getDayEndDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

        String dstr  = df.format(calendar.getTime());

        return dstr;
    }

    public static Date getDayEndDDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd 23:59:59");

        String dstr  = df.format(calendar.getTime());

        return parse(dstr,"yyyy-MM-dd 23:59:59");
    }

    public static Date parse(String dateStr, String format) {
        SimpleDateFormat f = new SimpleDateFormat(format);
        try {
            Date date =  f.parse(dateStr);
            return date;
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    /**
     * 获取当天的开始时间
     *
     * @return
     */
    public static long getStartOfDay() {
        return getStartOfDay(new Date());
    }
    /**
     * 获取某天的开始时间
     *
     * @param date
     * @return
     */
    public static long getStartOfDay(Date date) {
        DateTime dateTime = new DateTime(date);
        DateTime startOfDay = dateTime.withTimeAtStartOfDay();
        return startOfDay.getMillis();
    }


    public static Date getStartOfDate(Date date) {
        DateTime dateTime = new DateTime(date);
        DateTime startOfDay = dateTime.withTimeAtStartOfDay();
        return startOfDay.toDate();
    }

    /**
     * 获取当天的结束时间
     *
     * @return
     */
    public static long getEndOfDay() {
        return getEndOfDay(new Date());
    }

    /**
     * 获取某天的结束时间
     *
     * @param date
     * @return
     */
    public static long getEndOfDay(Date date) {
        DateTime dateTime = new DateTime(date);
        DateTime endOfDay = dateTime.millisOfDay().withMaximumValue();
        return endOfDay.getMillis();
    }

    /**
     * 计算两个日期的相隔天数
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int getBetweenDay(long startTime, long endTime) {
        DateTime startDay = new DateTime(startTime);
        DateTime endDay = new DateTime(endTime);
        return Days.daysBetween(startDay, endDay).getDays();
    }

    /**
     * 对比两个时间是否同一天
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static boolean isEqualsSameDay(long startTime, long endTime) {
        LocalDate startDay = new LocalDate(startTime);
        LocalDate endDay = new LocalDate(endTime);
        return startDay.equals(endDay);
    }

    public static Date addTime(Date startDate, long millis) {
        return new Date(startDate.getTime() + millis);
    }

    public static String formatDateForYMD(Date date) {
        if (date == null) {
            throw new IllegalArgumentException("date is null");
        }

        return new SimpleDateFormat(PATTERN_YHMS).format(date);
    }

    public static Boolean isSameDay(Date date1,Date date2){
        if (date1 == null || date2 == null){
            throw new IllegalArgumentException("date is null");
        }
        Calendar instance1 = Calendar.getInstance();
        instance1.setTime(date1);
        Calendar instance2 = Calendar.getInstance();
        instance2.setTime(date2);
        return instance1.get(Calendar.DAY_OF_YEAR) == instance2.get(Calendar.DAY_OF_YEAR) &&
                instance1.get(Calendar.YEAR) == instance2.get(Calendar.YEAR) &&
                instance1.get(Calendar.ERA) == instance2.get(Calendar.ERA);
    }

    /**
     * 日期小时分钟字符串
     *
     * @return
     */
    public static Date getVersionDate(LocalDateTime dateTime, int intervalMinute) {
        int minute = dateTime.getMinute() /  intervalMinute * intervalMinute;
        return Date.from(dateTime.withMinute(minute).withSecond(0).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static void main(String[] args) {
        //System.out.println(isSameDay(new Date(System.currentTimeMillis()-2*60*60*1000),new Date()));
        System.out.println(formatDate(getVersionDate(LocalDateTime.now(), 5)));
    }
}
