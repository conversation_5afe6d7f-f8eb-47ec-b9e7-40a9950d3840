package com.coohua.core.caf.dispense.enums;

import lombok.Getter;

@Getter
public enum AccountActionTypeEnum {
	WATCH_VIDEO(1, "1", "播放视频次数"),
	ARPU_ONE_DAY(2, "2", "arpu"),
	ECPM(3, "3", "ecpm"),
	GAME_ACTION(4, "4", "游戏行为"),
	KA_RATE(7, "7", "比例回传"),
	;
	public Integer value;
	private String typeKey;
	public String desc;

	AccountActionTypeEnum(Integer value, String typeKey, String desc) {
		this.value = value;
		this.typeKey = typeKey;
		this.desc = desc;
	}

	public static AccountActionTypeEnum findTypeByCode(Integer code) {

		if (code == null) {
			return null;
		}

		for (AccountActionTypeEnum type : values()) {
			if (type.value.equals(code)) {
				return type;
			}
		}

		return null;
	}
}
