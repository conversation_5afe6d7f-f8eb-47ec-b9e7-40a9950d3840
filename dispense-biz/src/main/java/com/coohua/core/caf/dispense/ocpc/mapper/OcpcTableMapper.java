package com.coohua.core.caf.dispense.ocpc.mapper;

import com.coohua.core.caf.dispense.ocpc.entity.Tables;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface OcpcTableMapper {
    @Update("create table if not exists  `${tablePre}` like ${tablePre}_tmp")
    int createOrderUnionTable(@Param("tablePre") String tablePre);
    @Update("rename table  ${tablePre} to ${tablePre}_${tableEnd}")
    void renameTable(@Param("tablePre") String tablePre,@Param("tableEnd") String tableEnd);
    @Update("truncate table  ${tablePre}_${tableEnd}")
    void truncateOrderUnionTable(@Param("tablePre") String tablePre,@Param("tableEnd") String tableEnd);
    @Update("drop table  ${tablePre}_${tableEnd}")
    void dropOrderUnionTable(@Param("tablePre") String tablePre,@Param("tableEnd") String tableEnd);


    @Select("select * from `information_schema`.tables where table_name ='${tablePre}_${tableEnd}'")
    List<Tables> queryTablesByEnd(@Param("tablePre") String tablePre, @Param("tableEnd") String tableEnd);

    @Select("select * from `information_schema`.tables where table_name like '${tablePre}_%'")
    List<Tables> queryAllClickTables(@Param("tablePre") String tablePre);
}
