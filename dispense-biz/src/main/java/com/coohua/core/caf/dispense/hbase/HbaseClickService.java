package com.coohua.core.caf.dispense.hbase;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.apollo.LogNum;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.component.ThreadPollComponent;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.kafka.dto.CaidMappingGyData;
import com.coohua.core.caf.dispense.ocpc.entity.MatchClickBroad;
import com.coohua.core.caf.dispense.ocpc.service.MatchClickBroadService;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import com.coohua.core.caf.dispense.utils.ConstCls;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.utils.HBaseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

@Component
@Slf4j
public class HbaseClickService {

    @Resource
    private Connection hbaseConnection;

    @Autowired
    ConstApolloConfig constApolloConfig;
    @Autowired
    RedisRelationshipService redisRelationshipService;
    @Autowired
    SafeDataRedisService safeDataRedisService;

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    static String tableName = "ToutiaoClick";
    static String toutiaoClickLong = "ToutiaoClickLong";
    @Resource
    private Connection toutiaoClickConnection;
    @Autowired
    MatchClickBroadService matchClickBroadService;
    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Autowired
    KafkaSender kafkaSender;

    //    @PostConstruct
    public HTableDescriptor createOcpcTable() {
        try (Admin admin = hbaseConnection.getAdmin()) {
            // 建表
            try {
                HTableDescriptor htdDb = admin.getTableDescriptor(TableName.valueOf(tableName));
            } catch (TableNotFoundException te) {
                HTableDescriptor htd = new HTableDescriptor(TableName.valueOf(tableName));
                HColumnDescriptor hcd = new HColumnDescriptor("family");
                hcd.setTimeToLive(184512); // 设置TTL过期时间4天 4 * 24 * 60 * 60
                htd.addFamily(hcd);
                admin.createTable(htd);
            }
        } catch (IOException e) {
            log.error("", e);
        }

        try {
//            Admin admin = hbaseConnection.getAdmin();
//            admin.disableTable(TableName.valueOf(toutiaoClickLong));
//            admin.deleteTable(TableName.valueOf(toutiaoClickLong));
//            HBaseUtils.initHadoopTable(toutiaoClickConnection, toutiaoClickLong, 60*DateTimeConstants.SECONDS_PER_DAY, true);
            HBaseUtils.initHadoopTable(toutiaoClickConnection, toutiaoClickLong, 60*DateTimeConstants.SECONDS_PER_DAY);
        } catch (IOException e) {
            log.error("", e);
        }
        return null;
    }

    ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("toHbaseClick",null,false);
    ExecutorService executorService =  new ThreadPoolExecutor(
            5
            , 100,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1200),
            threadFactory,
            new ThreadPollComponent.RejectedLogAbortPolicy("toHbaseActiveUser")
    );



    ThreadFactory threadFactory2 = ThreadUtil.newNamedThreadFactory("toHbaseClick2",null,false);
    ExecutorService executorService2 =  new ThreadPoolExecutor(
            40
            , 100,
            20,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(3000),
            threadFactory2,
            new ThreadPollComponent.RejectedLogAbortPolicy("toHbaseClick2")
    );
    @Autowired
    RtaHbaseService rtaHbaseService;
    public void saveHbaseClick(String product, String deviceId, String oaId, String mac, ToutiaoClick click) {
        executorService2.submit(()->{
            saveHbaseClick(product,deviceId,oaId,mac,click,tableName,hbaseConnection);
        });
        if(ocpcSwitcher.cklong){
            executorService.submit(()->{
                saveHbaseClick(product,deviceId,oaId,mac,click,toutiaoClickLong,toutiaoClickConnection);
                try {
                    rtaHbaseService.saveRtaClicks(DspType.getDspType(click.getDsp()),click);
                }catch (Exception e){
                    log.error("存储rtaclick错误",e);
                }
            });
        }
    }

    /**
     * 存储ks的另外一个caid
     * @param product
     */
    public void saveHbaseClick4Ks(String product, String caid, ToutiaoClick click) {
        executorService2.submit(()->{
            try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
                List<Put> putList = new ArrayList<>();
                String os = click.getOs();
                if (StringUtils.isNotBlank(caid) && StringUtils.equalsIgnoreCase("ios",os)) {
                    String key1 = RedisKeyConstants.getClickCaidKey(product,os,caid);
                    Put put = new Put(Bytes.toBytes(key1));
                    put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                    putList.add(put);
                }
                if(!putList.isEmpty()){
                    table.put(putList);
                }
            }catch (Exception e) {
                log.error("hbase add error ", e);
            }
        });
    }

    
    public void saveHbaseClick(String product, String deviceId, String oaId, String mac, ToutiaoClick click,String tableName,Connection hbaseDConnection) {

        if (!ocpcSwitcher.writeHbaseSwitch) {
            return;
        }
        try (Table table = hbaseDConnection.getTable(TableName.valueOf(tableName))) {
            List<Put> putList = new ArrayList<>();
            String os = click.getOs();
            String oaid2 = click.getOaid2();
            String idfa2 = click.getIdfa2();
            if (StringUtils.isNotBlank(deviceId) && !ConstCls.zeroMd5.equals(deviceId)) {
                String key1 = RedisKeyConstants.getClickDeviceKey(product, os, deviceId);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
//                table.put(put);
                putList.add(put);
            }
            if (StringUtils.isNotBlank(idfa2) && !ConstCls.zeroMd5.equals(idfa2)) {
                String key1 = RedisKeyConstants.getClickIdfa2Key(product, os, idfa2);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
//                table.put(put);
                putList.add(put);
            }
            if (StringUtils.isNotBlank(oaId)) {

                String key1 = RedisKeyConstants.getClickOaIdKey(product, os, oaId);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
//                table.put(put);
                putList.add(put);
            }
            if (StringUtils.isNotBlank(oaid2)) {

                String key1 = RedisKeyConstants.getClickOaId2Key(product, os, oaid2);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
//                table.put(put);
                putList.add(put);
            }
            if (StringUtils.isNotBlank(mac)) {
                String key1 = RedisKeyConstants.getClickMacKey(product, os, mac);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
//                table.put(put);
                putList.add(put);
            }
            if (StringUtils.isNotBlank(click.getAndroidId())) {
                String key1 = RedisKeyConstants.getClickAndroidIdKey(product, os, click.getAndroidId());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
//                table.put(put);
                putList.add(put);
            }
            if (StringUtils.isNotBlank(click.concatIpua())) {
                String key1 = RedisKeyConstants.getClickIpuaKey(product, os, click.concatIpua());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
//                table.put(put);
                putList.add(put);
            }


            if (StringUtils.isNotBlank(click.getOpenId())) {
                String key1 = RedisKeyConstants.getClickOpenKey(product, click.getOpenId());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if (StringUtils.isNotBlank(click.getCaid()) && StringUtils.equalsIgnoreCase("ios",os)) {
                String key1 = RedisKeyConstants.getClickCaidKey(product,os,click.getCaid());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if (StringUtils.isNotBlank(click.getCaid2()) && StringUtils.equalsIgnoreCase("ios",os)) {
                String key1 = RedisKeyConstants.getClickCaid2Key(product,os,click.getCaid2());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if (StringUtils.isNotBlank(click.concatIpuaMd())) {
                String key1 = RedisKeyConstants.getClickIAKey(product, os, click.concatIpuaMd());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }
            if (StringUtils.isNotBlank(click.concatIpModel()) && StringUtils.equalsIgnoreCase("ios",os)) {
                String key1 = RedisKeyConstants.getClickIMKey(product, os, click.concatIpModel());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if (StringUtils.isNotBlank(click.concatIp()) && StringUtils.equalsIgnoreCase("ios",os)) {
                String key1 = RedisKeyConstants.getClickIpKey(product, os, click.concatIp());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if (StringUtils.isNotBlank(click.getTrackId())) {
                String key1 = click.getTrackId();
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes("1"));
                putList.add(put);
            }

            if(putList.size()>0){
                table.put(putList);
            }
        } catch (Exception e) {
            log.error("hbase add error ", e);
        }
    }

    public ToutiaoClick getHbaseClickByDevice(UserEventReq request) {
        return getHbaseClickByDevice(request,tableName,hbaseConnection);
    }

    public ToutiaoClick getHbaseClickLongByDevice(UserEventReq request) {
        return getHbaseClickByDevice(request,toutiaoClickLong,toutiaoClickConnection);
    }

    public ToutiaoClick getHbaseClickByDevice(UserEventReq request,String tableName,Connection connection) {
        ToutiaoClick click = null;

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("hbaseGetClick product is null ! {}", JSONObject.toJSONString(request));
                return click;
            }

            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getOcpcDeviceId())
                    && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickDeviceKey(product, request.getOs(), request.getOcpcDeviceId())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.deive.name);
                    click.setGuiType(GuiyingType.deive.name);
                }
            }

            if (click == null && StringUtils.isNotBlank(request.getCaid()) && "ios".equals(request.getOs().toLowerCase())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(product,request.getOs(),request.getCaid())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    click.setGuiType(GuiyingType.caid.name);
                    request.setGuiType(GuiyingType.caid.name);
                }
            }
            if (click == null && StringUtils.isNotBlank(request.getCaid()) && "ios".equals(request.getOs().toLowerCase())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(product,request.getOs(),request.getCaid2())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    click.setGuiType(GuiyingType.caid.name);
                    request.setGuiType(GuiyingType.caid.name);
                }
            }

            /**
             * 按idfa_md5查询
             */
            if (click == null && StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(product, request.getOs(), request.getIdfa2())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.idfa.name);
                    click.setGuiType(GuiyingType.idfa.name);
                }
            }

            if(click == null && "ios".equals(request.getOs().toLowerCase())){
                String localCaid2022 = "";
                String localCaid2023 = "";
                String localCaid2025 = "";
                String caid2022 = "";
                String caid2025 = "";
                String idfaMap = "";
                // 根据caid取出映射关系
                Map<String,String> localCaidMap =  safeDataRedisService.getLocalCaidMap(request.getCaid());
                if (localCaidMap != null && !localCaidMap.isEmpty()) {
                    localCaid2022 = localCaidMap.get("20220111");
                    localCaid2023 = localCaidMap.get("20230330");
                    localCaid2025 = localCaidMap.get("20250325");
                }

                Map<String,String> caidMap = safeDataRedisService.getCaidMap(request.getCaid());
                if (caidMap != null && !caidMap.isEmpty()) {
                    caid2022 = caidMap.get("20220111");
                    caid2025 = caidMap.get("20250325");
                    idfaMap = caidMap.get("idfa");
                }

                if (StringUtils.isNotBlank(localCaid2022)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2022)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        click.setGuiType(GuiyingType.caid.name);
                        log.info("caidMapping映射 hbase归因 精准匹配成功 localcaid2022查归因成功 设备 {} dsp {} " + request.getProduct(), request.getOs(),click.getDsp());
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (click == null && StringUtils.isNotBlank(localCaid2023)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), localCaid2023)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        click.setGuiType(GuiyingType.caid.name);
                        log.info("caidMapping映射 hbase归因 精准匹配成功 localcaid2023查归因成功 设备 {} dsp {} " + request.getProduct(), request.getOs(),click.getDsp());
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (click == null && StringUtils.isNotBlank(localCaid2023)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2023)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        click.setGuiType(GuiyingType.caid.name);
                        log.info("caidMapping映射 hbase归因 精准匹配成功 localcaid2023查归因2成功 设备 {} dsp {} " + request.getProduct(), request.getOs(),click.getDsp());
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (click == null && StringUtils.isNotBlank(localCaid2025)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2025)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        click.setGuiType(GuiyingType.caid.name);
                        log.info("caidMapping映射 hbase归因 精准匹配成功 localcaid2025查归因2成功 设备 {} dsp {} " + request.getProduct(), request.getOs(),click.getDsp());
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (click == null && StringUtils.isNotBlank(caid2022)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid2022)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        click.setGuiType(GuiyingType.caid.name);
                        log.info("caidMapping映射 hbase归因 精准匹配成功 caid2022查归因成功 设备 {} dsp {} " + request.getProduct(), request.getOs(),click.getDsp());
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (click == null && StringUtils.isNotBlank(caid2025)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid2025)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        click.setGuiType(GuiyingType.caid.name);
                        log.info("caidMapping映射 hbase归因 精准匹配成功 caid2025查归因成功 设备 {} dsp {} " + request.getProduct(), request.getOs(),click.getDsp());
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (click == null && StringUtils.isNotBlank(idfaMap)) {
                    String[] idfaStr = idfaMap.split(",");
                    for (String idfa : idfaStr) {
                        if (click == null) {
                            Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), idfa)));
                            Result res = table.get(get);
                            String s = HbaseUtils.getCellValStr(res);
                            if (StringUtils.isNotBlank(s)) {
                                click = JSONObject.parseObject(s, ToutiaoClick.class);
                                click.setGuiType(GuiyingType.idfa.name);
                                log.info("caidMapping映射 hbase归因 精准匹配成功 idfaMap查归因成功 设备 {} dsp {} " + request.getProduct(), request.getOs(),click.getDsp());
                                request.setGuiType(GuiyingType.idfa.name);
                            }
                        }else {
                            break;
                        }
                    }

                }
            }

            /**
             * click不存在再查oaId
             */
            if (click == null && StringUtils.isNotBlank(request.getOaid())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaIdKey(product, request.getOs(), request.getOaid())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.oaid.name);
                    click.setGuiType(GuiyingType.oaid.name);
                }
            }
            if (click == null && StringUtils.isNotBlank(request.getOaid2())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaId2Key(product, request.getOs(), request.getOaid2())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.oaid.name);
                    click.setGuiType(GuiyingType.oaid.name);
                }
            }
            /**
             * 设备号和 oaId 查不到 再用mac地址查
             */
            if (click == null && StringUtils.isNotBlank(request.getMac())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickMacKey(product, request.getOs(), request.getMac())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.mac.name);
                    click.setGuiType(GuiyingType.mac.name);
                }
            }
            /**
             * androidId查
             */
            if (click == null && StringUtils.isNotBlank(request.getAndroidId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickAndroidIdKey(product, request.getOs(), request.getAndroidId())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    click.setGuiType(GuiyingType.androidId.name);
                    request.setGuiType(GuiyingType.androidId.name);
                }
            }

            if (click == null && StringUtils.isNotBlank(request.getOpenId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOpenKey(product,request.getOpenId())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.openid.name);
                    click.setGuiType(GuiyingType.openid.name);
                }
            }


            /**
             * 查ipua
             */
            if (click == null && StringUtils.isNotBlank(request.concatIpua())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIpuaKey(product, request.getOs(), request.concatIpua())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.ipua.name);
                    click.setGuiType(GuiyingType.ipua.name);
                }
            }

            if (click == null && StringUtils.isNotBlank(request.concatIpuaMd())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIAKey(product, request.getOs(), request.concatIpuaMd())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.iunod.name);
                    click.setGuiType(GuiyingType.iunod.name);
                }
            }


            if (click == null && StringUtils.isNotBlank(request.concatIpMd())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIMKey(product, request.getOs(), request.concatIpMd())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.ipmd.name);
                    click.setGuiType(GuiyingType.ipmd.name);
                }
            }


            if (click == null && StringUtils.isNotBlank(request.concatIp())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIpKey(product, request.getOs(), request.concatIp())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.ip.name);
                    click.setGuiType(GuiyingType.ip.name);
                }
            }

            if (click == null && "ToutiaoClick".equals(tableName)&&StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                String idfa = redisRelationshipService.getIdfaCaid(request.getCaid());
                if (StringUtils.isNotBlank(idfa)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(product, request.getOs(), idfa)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        log.info("通过映射 由未匹配到精准匹配成功 idfa 设备 {} ", request.getOs());
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.idfa.name);
                        click.setGuiType(GuiyingType.idfa.name);
                    }
                }
            }

            if (click != null) {
                log.info("hbaseGetClick success req={} ,rsp={}", JSONObject.toJSONString(request), JSONObject.toJSONString(click));
            }
        } catch (Exception e) {
            log.error("hbaseGetClick error ", e);
        }

        if (click != null && click.getCreateTime().after(new Date(System.currentTimeMillis() - (1000 * 86400 * 7)))) {
            return click;
        }else if (click != null) {
            log.info("旧版归因 查询点击时间超过7天 不使用 {}",JSONObject.toJSONString(click));
        }

        return click;
    }


    private boolean isAccurateMatch(UserEventReq request) {
        List<String> accurateIdList = new ArrayList<>();
        accurateIdList.add(request.getOaid());
        accurateIdList.add(request.getOaid2());
        accurateIdList.add(request.getAndroidId());
        accurateIdList.add(request.getAndroidMd5Id());
        accurateIdList.add(request.getCaid());
        accurateIdList.add(request.getCaid2());
        accurateIdList.add(request.getIdfa2());
        log.info("精准匹配列表accurateIdList {} ocpcDeviceId {}", JSONObject.toJSONString(accurateIdList),request.getOcpcDeviceId());
        return accurateIdList.contains(request.getOcpcDeviceId());
    }

    private String obscure2AccurateMatch(UserEventReq request, String product,Table table) throws IOException {
        String idfa2 = redisRelationshipService.getIdfaCaid(request.getCaid());
        if (StringUtils.isNotBlank(idfa2)) {
            Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(product, request.getOs(), idfa2)));
            Result res = table.get(get);
            String s = HbaseUtils.getCellValStr(res);
            if (StringUtils.isNotBlank(s)) {
                log.info("通过映射 由模糊到精准匹配成功 idfa 设备 {} ", request.getOs());
                return s;
            }
        }
        return null;
    }

    public ToutiaoClick getAccurateHbaseClickByDevice(UserEventReq request) {
        ToutiaoClick toutiaoClick = null;
        try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(toutiaoClickLong))) {
            if (StringUtils.isBlank(request.getProduct())) {
                log.warn("hbaseGetClick is null {}", JSONObject.toJSONString(request));
                return toutiaoClick;
            }

            //先按设备号查询，ios OcpcDeviceId 代表idfa，android OcpcDeviceId 代表imei，均为精准归因
            if (StringUtils.isNotBlank(request.getOcpcDeviceId())
                    && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickDeviceKey(request.getProduct(), request.getOs(), request.getOcpcDeviceId())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.deive.name);
                    toutiaoClick.setGuiType(GuiyingType.deive.name);
                }
            }

            if ("ios".equals(request.getOs().toLowerCase())) {

                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid())) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), request.getCaid())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid())) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), request.getCaid())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                // 根据caid映射取出caid2，并寻找点击
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid())) {
                    String caid2 = bpDispenseJedisClusterClient.get("caidMap:" + request.getCaid());
                    if (StringUtils.isNotBlank(caid2)) {
                        Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(),caid2)));
                        Result res = table.get(get);
                        String s = HbaseUtils.getCellValStr(res);
                        if (StringUtils.isNotBlank(s)) {
                            toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                            toutiaoClick.setGuiType(GuiyingType.caid.name);
                            request.setGuiType(GuiyingType.caid.name);
                        }else {
                            Get get2 = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(),caid2)));
                            Result res2 = table.get(get2);
                            String s2 = HbaseUtils.getCellValStr(res2);
                            if (StringUtils.isNotBlank(s2)) {
                                toutiaoClick = JSONObject.parseObject(s2, ToutiaoClick.class);
                                toutiaoClick.setGuiType(GuiyingType.caid.name);
                                request.setGuiType(GuiyingType.caid.name);
                            }
                        }
                    }
                }


                /**
                 * 按idfa_md5查询
                 */
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), request.getIdfa2())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.idfa.name);
                        toutiaoClick.setGuiType(GuiyingType.idfa.name);
                    }
                }

                String localCaid2022 = "";
                String localCaid2023 = "";
                String localCaid2025 = "";
                String caid2022 = "";
                String caid2025 = "";
                String idfaMap = "";
                String source = "";
                // 根据caid取出映射关系
                Map<String,String> localCaidMap =  safeDataRedisService.getLocalCaidMap(request.getCaid());
                if (localCaidMap != null && !localCaidMap.isEmpty()) {
                    localCaid2022 = localCaidMap.get("20220111");
                    localCaid2023 = localCaidMap.get("20230330");
                    localCaid2025 = localCaidMap.get("20250325");
                }

                Map<String,String> caidMap = safeDataRedisService.getCaidMap(request.getCaid());
                if (caidMap != null && !caidMap.isEmpty()) {
                    caid2022 = caidMap.get("20220111");
                    caid2025 = caidMap.get("20250325");
                    idfaMap = caidMap.get("idfa");
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2022)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2022)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2023)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), localCaid2023)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2023)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2023)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2025)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2025)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(caid2022)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid2022)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "caid_service";
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(caid2025)) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid2025)));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "caid_service";
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(idfaMap)) {
                    String[] idfaStr = idfaMap.split(",");
                    for (String idfa : idfaStr) {
                        if (toutiaoClick == null) {
                            Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), idfa)));
                            Result res = table.get(get);
                            String s = HbaseUtils.getCellValStr(res);
                            if (StringUtils.isNotBlank(s)) {
                                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                                toutiaoClick.setGuiType(GuiyingType.idfa.name);
                                source = "click_event";
                                request.setGuiType(GuiyingType.idfa.name);
                            }
                        }else {
                            break;
                        }
                    }

                }

                if (StringUtils.isNotBlank(source) && toutiaoClick != null) {
                    // 通过Kafka发送数据
                    CaidMappingGyData caidMappingGyData = buildCaidMappingGyData(source,toutiaoClick,idfaMap,request);
                    caidMappingGyData.setDataObtainTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    kafkaSender.sendCaidMappingGyData(JSONObject.toJSONString(caidMappingGyData));
                }

                // 若点击查询为null，则根据userId查询caidList
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getUserId()) && !"0".equals(request.getUserId())) {
                    // 查询userId 和 caidList的映射
                    List<String> caidListWithUserId = safeDataRedisService.getCaidListWithUserId(request.getUserId());
                    // 如果caidList的数量大于1 或 caidList只有一个且和请求获取caid不同 则查询点击
                    if (caidListWithUserId.size() > 1 || (caidListWithUserId.size() == 1 && caidListWithUserId.get(0).equals(request.getUserId()))) {
                        // 循环查询点击
                        for (String caid : caidListWithUserId) {
                            Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid)));
                            Result res = table.get(get);
                            String s = HbaseUtils.getCellValStr(res);
                            if (StringUtils.isNotBlank(s)) {
                                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                                request.setGuiType(GuiyingType.caidMaping.name);
                                toutiaoClick.setGuiType(GuiyingType.caidMaping.name);
                                break;
                            }
                        }
                    }
                }


                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                    String idfa = redisRelationshipService.getIdfaCaid(request.getCaid());
                    if (StringUtils.isNotBlank(idfa)) {
                        Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), idfa)));
                        Result res = table.get(get);
                        String s = HbaseUtils.getCellValStr(res);
                        if (StringUtils.isNotBlank(s)) {
                            toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.idfa.name);
                            toutiaoClick.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(request.getUserId()) && !"0".equals(request.getUserId())) {
                    // 查询userId 和 caidList的映射
                    List<String> caidListWithUserId = safeDataRedisService.getCaidListWithUserId(request.getUserId());
                    // 如果caidList的数量大于1 或 caidList只有一个且和请求获取caid不同 则查询点击
                    if (caidListWithUserId.size() > 1 || (caidListWithUserId.size() == 1 && caidListWithUserId.get(0).equals(request.getUserId()))) {
                        // 循环查询点击
                        for (String caid : caidListWithUserId) {
                            String idfa = redisRelationshipService.getIdfaCaid(caid);
                            if (StringUtils.isNotBlank(idfa)) {
                                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), idfa)));
                                Result res = table.get(get);
                                String s = HbaseUtils.getCellValStr(res);
                                if (StringUtils.isNotBlank(s)) {
                                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                                    request.setGuiType(GuiyingType.idfaMaping.name);
                                    toutiaoClick.setGuiType(GuiyingType.idfaMaping.name);
                                    break;
                                }
                            }
                        }
                    }
                }

            } else if ("android".equals(request.getOs().toLowerCase())) {
                if (StringUtils.isNotBlank(request.getOaid())) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaIdKey(request.getProduct(), request.getOs(), request.getOaid())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.oaid.name);
                        toutiaoClick.setGuiType(GuiyingType.oaid.name);
                    }
                }
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getOaid2())) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaId2Key(request.getProduct(), request.getOs(), request.getOaid2())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.oaid.name);
                        toutiaoClick.setGuiType(GuiyingType.oaid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(request.getAndroidId())) {
                    Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickAndroidIdKey(request.getProduct(), request.getOs(), request.getAndroidId())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.androidId.name);
                        request.setGuiType(GuiyingType.androidId.name);
                    }
                }
            }

        } catch (Exception e) {
            log.error("getAccurateHbaseClickByDevice error ", e);
        }
        if (toutiaoClick != null && toutiaoClick.getCreateTime().after(new Date(System.currentTimeMillis() - (1000 * 86400 * 7)))) {
            return toutiaoClick;
        } else if (toutiaoClick != null) {
            log.info("新版精准查询点击时间超过7天 不使用 {}", JSONObject.toJSONString(toutiaoClick));
        }
       return null;
    }

    private CaidMappingGyData buildCaidMappingGyData(String source, ToutiaoClick toutiaoClick,String idfa,UserEventReq userEventReq) {
        CaidMappingGyData caidMappingGyData = new CaidMappingGyData();
        caidMappingGyData.setOs(toutiaoClick.getOs());
        caidMappingGyData.setProduct(toutiaoClick.getProduct());
        caidMappingGyData.setUserId(userEventReq.getUserId());
        caidMappingGyData.setSourceDeviceId(userEventReq.getSourceDeviceId());
        caidMappingGyData.setCaid(userEventReq.getCaid());
        caidMappingGyData.setAndroidId(userEventReq.getAndroidId());
        caidMappingGyData.setOaid(userEventReq.getOaid());
        caidMappingGyData.setOaid2(userEventReq.getOaid2());
        caidMappingGyData.setAccountId(toutiaoClick.getAccountId());
        caidMappingGyData.setAccountName(toutiaoClick.getAccountName());
        caidMappingGyData.setDsp(toutiaoClick.getDsp());
        caidMappingGyData.setIdfa(idfa);
        caidMappingGyData.setMappingSource(source);
        return caidMappingGyData;
    }

    public ToutiaoClick getObscureHbaseClickByDevice(UserEventReq request) {
        //查询非精准归因。 根据deviceId、macId、ipua、openId、ipua(加密)、ip依次查询
        ToutiaoClick toutiaoClick = null;
        if (StringUtils.isBlank(request.getProduct())) {
            log.warn("obscure hbase product is null {}", JSONObject.toJSONString(request));
            return toutiaoClick;
        }
        try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(toutiaoClickLong))) {
            //根据macId查询
            if (toutiaoClick == null && StringUtils.isNotBlank(request.getMac())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickMacKey(request.getProduct(), request.getOs(), request.getMac())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.mac.name);
                    toutiaoClick.setGuiType(GuiyingType.mac.name);
                    log.info("新版归因 非精准匹配成功 mac 设备 {} ", request.getOs());
                }
            }

            //根据openId进行查询
            if (toutiaoClick == null && StringUtils.isNotBlank(request.getOpenId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOpenKey(request.getProduct(), request.getOpenId())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.openid.name);
                    toutiaoClick.setGuiType(GuiyingType.openid.name);
                    log.info("新版归因 非精准匹配成功 openId 设备 {} ", request.getOs());
                }
            }

            //根据ipua进行查询
            if (toutiaoClick == null && StringUtils.isNotBlank(request.concatIpua())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIpuaKey(request.getProduct(), request.getOs(), request.concatIpua())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.ipua.name);
                    toutiaoClick.setGuiType(GuiyingType.ipua.name);
                    log.info("新版归因 非精准匹配成功 ipua 设备 {} ", request.getOs());
                }
            }

            //根据iunod进行查询
            if (toutiaoClick == null && StringUtils.isNotBlank(request.concatIpuaMd())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIAKey(request.getProduct(), request.getOs(), request.concatIpuaMd())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.iunod.name);
                    toutiaoClick.setGuiType(GuiyingType.iunod.name);
                    log.info("新版归因 非精准匹配成功 iunod 设备 {} ", request.getOs());
                }
            }

            //根据ipmd进行查询
            if (toutiaoClick == null && StringUtils.isNotBlank(request.concatIpMd())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIMKey(request.getProduct(), request.getOs(), request.concatIpMd())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.ipmd.name);
                    toutiaoClick.setGuiType(GuiyingType.ipmd.name);
                    log.info("新版归因 非精准匹配成功 ipmd 设备 {} ", request.getOs());
                }
            }

            //根据IP进行查询
            if (toutiaoClick == null && StringUtils.isNotBlank(request.concatIp())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickIpKey(request.getProduct(), request.getOs(), request.concatIp())));
                Result res = table.get(get);
                String s = HbaseUtils.getCellValStr(res);
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.ip.name);
                    toutiaoClick.setGuiType(GuiyingType.ip.name);
                    log.info("新版归因 非精准匹配成功 ip 设备 {} ", request.getOs());
                }
            }

        } catch (Exception e) {
            log.error("getObscureHbaseClickByDevice error ", e);
        }
        if (toutiaoClick != null && toutiaoClick.getCreateTime().after(new Date(System.currentTimeMillis() - (1000 * 86400 * 7)))) {
            return toutiaoClick;
        }else if (toutiaoClick != null) {
            log.info("新版非精准查询点击时间超过7天 不使用 {}",JSONObject.toJSONString(toutiaoClick));
        }

        return toutiaoClick;
    }

    public List<ToutiaoClick> getToutiaoClickList(UserEventReq request) {
        List<ToutiaoClick> clickList = new ArrayList<>();
        if (StringUtils.isBlank(request.getProduct())) {
            log.warn("multiple click product is null {}", JSONObject.toJSONString(request));
            return clickList;
        }
        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {

            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getOcpcDeviceId())
                    && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickDeviceKey(request.getProduct(), request.getOs(), request.getOcpcDeviceId())));
                get.readAllVersions();
                Result res = table.get(get);
                List<String> listValStr = HbaseUtils.getListValStr(res);
                if (!listValStr.isEmpty()) {
                    for (String s : listValStr) {
                        ToutiaoClick toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.deive.name);
                        clickList.add(toutiaoClick);
                    }
                    request.setGuiType(GuiyingType.deive.name);
                }
            }
            /**
             * click不存在再查oaId
             */
            if (clickList.isEmpty() && StringUtils.isNotBlank(request.getOaid())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaIdKey(request.getProduct(), request.getOs(), request.getOaid())));
                get.readAllVersions();
                Result res = table.get(get);
                List<String> listValStr = HbaseUtils.getListValStr(res);
                if (!listValStr.isEmpty()) {
                    for (String s : listValStr) {
                        ToutiaoClick toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.oaid.name);
                        clickList.add(toutiaoClick);
                    }
                    request.setGuiType(GuiyingType.oaid.name);
                }
            }
            if (clickList.isEmpty() && StringUtils.isNotBlank(request.getOaid2())) {
                Get get = new Get(Bytes.toBytes(RedisKeyConstants.getClickOaId2Key(request.getProduct(), request.getOs(), request.getOaid2())));
                get.readAllVersions();
                Result res = table.get(get);
                List<String> listValStr = HbaseUtils.getListValStr(res);
                if (!listValStr.isEmpty()) {
                    for (String s : listValStr) {
                        ToutiaoClick toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        toutiaoClick.setGuiType(GuiyingType.oaid.name);
                        clickList.add(toutiaoClick);
                    }
                    request.setGuiType(GuiyingType.oaid.name);
                }
            }
        } catch (Exception e) {
            log.error("getObscureHbaseClickByDevice error ", e);
        }
        return clickList;
    }

    public String getTrackClickByHbase(ToutiaoClick sdkClick) {

        if (StringUtils.isBlank(sdkClick.getProduct()) || StringUtils.isBlank(sdkClick.getTrackId())) {
            log.warn("hbase查询时trackId为空 {}", JSONObject.toJSONString(sdkClick));
            return null;
        }
        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
            Get get = new Get(Bytes.toBytes(sdkClick.getTrackId()));
            Result res = table.get(get);
            return HbaseUtils.getCellValStr(res);
        } catch (Exception e) {
            log.error("getObscureHbaseClickByDevice error ", e);
        }
        return null;
    }
}
