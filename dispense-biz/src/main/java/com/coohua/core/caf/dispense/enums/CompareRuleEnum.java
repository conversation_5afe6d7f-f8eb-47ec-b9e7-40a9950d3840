package com.coohua.core.caf.dispense.enums;

import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.function.BiPredicate;

@AllArgsConstructor
public enum CompareRuleEnum {
	INT_EQUAL("整数相等", CompareRuleEnum::intEqual),
	INT_NOT_LESS_THAN("整数大于等于", CompareRuleEnum::intNotLessThan),
	DOUBLE_NOT_LESS_THAN("浮点数大于等于", CompareRuleEnum::doubleNotLessThan),
	;
	public String comment;
	public BiPredicate<String, String> checkRule;

	/**
	 *
	 * @param checkVal 待校验的值
	 * @param requireVal 要求的值
	 * @return
	 */
	public static boolean intEqual(String checkVal, String requireVal) {
		try {
			return Integer.valueOf(checkVal).equals(Integer.valueOf(requireVal));
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 *
	 * @param checkVal 待校验的值
	 * @param requireVal 要求的值
	 * @return
	 */
	public static boolean intNotLessThan(String checkVal, String requireVal) {
		try {
			return Integer.valueOf(checkVal) >= Integer.valueOf(requireVal);
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 *
	 * @param checkVal 待校验的值
	 * @param requireVal 要求的值
	 * @return
	 */
	public static boolean doubleNotLessThan(String checkVal, String requireVal) {
		try {
			return new BigDecimal(checkVal).compareTo(new BigDecimal(requireVal)) >= 0;
		} catch (Exception e) {
			return false;
		}
	}

}
