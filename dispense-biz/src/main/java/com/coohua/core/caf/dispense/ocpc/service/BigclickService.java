package com.coohua.core.caf.dispense.ocpc.service;

import com.coohua.core.caf.dispense.ocpc.entity.Tables;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcClickMapperExt;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import org.apache.commons.collections.map.HashedMap;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class BigclickService {
    @Value("#{${big.table.appid.map: {\"400\": 4,\"402\": 4,\"395\": 64,\"394\": 64}}}")
    public Map<Integer,Integer> bigAppMap;
    @Value("${big.table.doubleWrite:true}")
    private Boolean doubleWrite;

    @Autowired
    TablesService tablesService;

    public int getTableNum(Integer appId){
       Integer tableNum = bigAppMap.get(appId);
       return tableNum;
    }
    public boolean isDoubleWrite(Integer appId){
        return doubleWrite;
    }
    public boolean isReadDouble(Integer appId){
        if(bigAppMap.keySet().contains(appId)){
            return true;
        }else {
            return false;
        }
    }
    public boolean isCreateSubtables(Integer appId){
        return bigAppMap.keySet().contains(appId);
    }
    @Autowired
    OcpcClickMapperExt ocpcClickMapperExt;
    public String getTableEnd(Integer appId,String deviceId){
        if(bigAppMap.keySet().contains(appId)){
            int tid = getHashCk(appId,deviceId);
            String tableEnd = appId+"";
            if(tid>=0){
                String tempTableEnd = tableEnd+"_"+tid;
                Tables tables = tablesService.queryByTableName(tempTableEnd);
                if(tables!=null){
                    tableEnd=tempTableEnd;
                }
            }
            return tableEnd;
        }else {
            if(!ocpcClickMapperExt.existOcpcTable(appId + "")){
                ocpcClickMapperExt.createOcpcClick(appId + "");
            };
            return appId+"";
        }
    }
    @ApolloJsonValue("${big.table.appid.list:[400,407,402,456,395,394,485,476,480,484,492]}")
    public Set<Integer> dataAppList;
    public boolean isReadByDate(Integer appId){
        return dataAppList.contains(appId);
    }
    public boolean isDateQuery(Integer appId){
        return dataAppList.contains(appId);
    }
    private int queryDays = 2;
    private static Map<String,Boolean> isEMap = new HashedMap();
    private void isExits(String tend){
        Boolean isE = isEMap.get(tend);
        if(isE==null || !isE){
            isE = ocpcClickMapperExt.existOcpcTable(tend);
            if(!isE){
                ocpcClickMapperExt.createOcpcClick(tend);
            }
            isEMap.put(tend,isE);
        }
    }
    public List<String> getTableDateEnds(Integer appId, String deviceId){
        List<String> tableEndList = new ArrayList<>();
        if(dataAppList.contains(appId)){
            for(int i=0;i<queryDays;i++){
                String cdateStr = DateUtils.formatDateForYMDSTR((new Date(System.currentTimeMillis()-i* DateTimeConstants.MILLIS_PER_DAY)));
                String tend = appId+"_"+cdateStr;
                tableEndList.add(tend);
                isExits(tend);
            }
        }else {
            tableEndList.add(appId+"");
        }
        return tableEndList;
    }

    public String getTableSaveDateEnd(Integer appId, String deviceId){
        if(dataAppList.contains(appId)){
            String cdateStr = DateUtils.formatDateForYMDSTR(new Date());
            String tend = appId+"_"+cdateStr;
            if(!ocpcClickMapperExt.existOcpcTable(tend)){
                ocpcClickMapperExt.createOcpcClick(tend);
            };
            return tend;
        }else {
            return  appId+"";
        }
    }

    public String getTableEnd(Integer appId,int tableNum){
        return appId+"_"+tableNum;
    }
    public int getHashCk(Integer appId,String deviceId){
        if(deviceId==null){
            return 0;
        }
        if(deviceId==null){
            return 0;
        }else{
            return Math.abs(deviceId.hashCode()%bigAppMap.get(appId));
        }
    }
}
