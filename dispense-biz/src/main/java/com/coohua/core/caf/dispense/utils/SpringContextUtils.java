package com.coohua.core.caf.dispense.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * Created by pgq on 2021/8/19.
 */

@Configuration
@Component("SpringContextUtils")
public class SpringContextUtils implements ApplicationContextAware {

    /**
     * 上下文对象实例
     */
    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    /**
     * 获取applicationContext
     */
    public static ApplicationContext getContext() {
        return context;
    }

    /**
     * 通过name获取 Bean.
     */
    public static Object getBean(String name) {
        if (getContext() == null) {
            return null;
        }
        return getContext().getBean(name);
    }

    /**
     * 通过class获取Bean.
     */
    public static <T> T getBean(Class<T> clazz) {
        if (getContext() == null) {
            return null;
        }
        return getContext().getBean(clazz);
    }

    /**
     * 通过name,以及Clazz返回指定的Bean
     */
    public static <T> T getBean(String name, Class<T> clazz) {
        if (getContext() == null) {
            return null;
        }
        return getContext().getBean(name, clazz);
    }
}
