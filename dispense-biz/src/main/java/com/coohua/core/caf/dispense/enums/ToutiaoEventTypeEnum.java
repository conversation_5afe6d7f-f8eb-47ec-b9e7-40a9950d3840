package com.coohua.core.caf.dispense.enums;

import static com.coohua.core.caf.dispense.constant.BaseConstants.UnknownEventTypeV2;
import static com.coohua.core.caf.dispense.enums.ThirdAlertReportType.*;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/8/8 18:29
 */
public enum ToutiaoEventTypeEnum {
    //30天内第一次打开APP
    ACTIVATE_APP(0, "激活", "active"),
    //用户点击广告后注册成为APP的新用户
    REGISTER(1, "注册", "active_register", Extra),
    //用户完成付费
    PURCHASE(2, "购买", "active_pay"),
    //用户点击下单生成订单
    COMPLETE_ORDER(20, "下单", "in_app_order", Extra),
    //arpu次留
    ARPU0(-10001, "arpu次留", "arpu0", Extra),
    //用户将商品加入购物车
    ADD_TO_CART(22, "加入购物车", UnknownEventTypeV2),
    //用户激活后第二天打开APP的行为
    START_APP(6, "次日留存", "next_day_open"),
    //关键页面访问
    KEY_EVENT(25, "关键页面访问", "game_addiction"),
    //穿山甲LTV0
    LTV(259, "穿山甲LTV0", UnknownEventTypeV2, Ltv),
    //关键行为衍生事件 (实际是357-338的范围内均为衍生事件)
    KEY_ACTION_EXT_EVENT(357, "关键行为衍生事件1", "key_action1", ExtEvent),
    KEY_ACTION_EXT_EVENT2(356, "关键行为衍生事件2", "key_action2", ExtEvent),
    KEY_ACTION_EXT_EVENT3(355, "关键行为衍生事件3", "key_action3", ExtEvent),
    ;
    public Integer value;
    public String desc;
    /**
     * 头条回传v2版本 eventType 用字符串表示
     */
    public String typeV2;
    /**
     * 打点上报时的名称
     */
    public String reportName;


    ToutiaoEventTypeEnum(Integer value, String desc, String typeV2) {
        this(value, desc, typeV2, Default);
    }

    ToutiaoEventTypeEnum(Integer value, String desc, String typeV2, String reportName) {
        this.value = value;
        this.desc = desc;
        this.typeV2 = typeV2;
        this.reportName = reportName;
    }

    public static ToutiaoEventTypeEnum getStatus(Integer value) {
        if (value != null) {
            ToutiaoEventTypeEnum[] otypes = ToutiaoEventTypeEnum.values();
            for (ToutiaoEventTypeEnum memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }

    /**
     * 判断是否是关键行为衍生事件
     * @return
     */
    public boolean isKeyActionExtEvent() {
        return value != null && value >= 338 && value <= 357;
    }


    public static String getReportName(Integer eventType, boolean success) {
        return getReportName(DspType.TOUTIAO, eventType, success);
    }

    public static String getKuaishouReportName(Integer eventType, boolean success) {
        return getReportName(DspType.KUAISHOU, eventType, success);
    }

    private static String getReportName(DspType dspType, Integer eventType, boolean success) {
        ToutiaoEventTypeEnum eventTypeEnum = getStatus(eventType);
        if (eventTypeEnum == null) {
            return dspType.cnName + "未知事件回调" + (success ? "成功" : "失败");
        }
        return dspType.cnName + eventTypeEnum.reportName + (success ? "成功" : "失败");
    }
}
