package com.coohua.core.caf.dispense.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.stream.Collectors;

import static com.coohua.core.caf.dispense.enums.AccountActionTypeEnum.*;

public enum EventValueRuleEnum {

    RULE_1_2_4(
            WATCH_VIDEO, CompareRuleEnum.INT_NOT_LESS_THAN,
            ARPU_ONE_DAY, CompareRuleEnum.DOUBLE_NOT_LESS_THAN,
            GAME_ACTION, CompareRuleEnum.DOUBLE_NOT_LESS_THAN
    ),

    RULE_1_3_4(
            WATCH_VIDEO, CompareRuleEnum.INT_NOT_LESS_THAN,
            ECPM, CompareRuleEnum.INT_NOT_LESS_THAN,
            GAME_ACTION, CompareRuleEnum.DOUBLE_NOT_LESS_THAN
    ),

    RULE_2_4(
            ARPU_ONE_DAY, CompareRuleEnum.DOUBLE_NOT_LESS_THAN,
            GAME_ACTION, CompareRuleEnum.DOUBLE_NOT_LESS_THAN
    ),

    RULE_3_4(
            ECPM, CompareRuleEnum.INT_NOT_LESS_THAN,
            GAME_ACTION, CompareRuleEnum.DOUBLE_NOT_LESS_THAN
    ),

    ;
    public String typeKey;
    public HashMap<String, CompareRuleEnum> compareRules = new HashMap();

    EventValueRuleEnum(AccountActionTypeEnum type1, CompareRuleEnum rule1) {
        this.typeKey = type1.value.toString();
        this.compareRules.put(type1.getTypeKey(), rule1);
    }

    EventValueRuleEnum(AccountActionTypeEnum type1, CompareRuleEnum rule1, AccountActionTypeEnum type2, CompareRuleEnum rule2) {
        this.typeKey = Arrays.asList(type1.value, type2.value)
                .stream()
                .sorted()
                .map(k -> k.toString())
                .collect(Collectors.joining(","));
        this.compareRules.put(type1.getTypeKey(), rule1);
        this.compareRules.put(type2.getTypeKey(), rule2);
    }

    EventValueRuleEnum(AccountActionTypeEnum type1, CompareRuleEnum rule1, AccountActionTypeEnum type2, CompareRuleEnum rule2, AccountActionTypeEnum type3, CompareRuleEnum rule3) {
        this.typeKey = Arrays.asList(type1.value, type2.value, type3.value)
                .stream()
                .sorted()
                .map(k -> k.toString())
                .collect(Collectors.joining(","));
        this.compareRules.put(type1.getTypeKey(), rule1);
        this.compareRules.put(type2.getTypeKey(), rule2);
        this.compareRules.put(type3.getTypeKey(), rule3);
    }

    public static EventValueRuleEnum parse(String typeKey) {
        for (EventValueRuleEnum item : EventValueRuleEnum.values()) {
            if (item.typeKey.equals(typeKey)) {
                return item;
            }
        }
        return null;
    }

}
