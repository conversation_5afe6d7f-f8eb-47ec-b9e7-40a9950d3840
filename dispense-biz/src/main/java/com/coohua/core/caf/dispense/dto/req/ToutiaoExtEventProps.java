package com.coohua.core.caf.dispense.dto.req;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Accessors(chain = true)
public class ToutiaoExtEventProps {

    private Long is_ga_convert;
    private Long action_ts;
    private Long depth;
    private Long action_type1;
    private Long value1;
    private Long action_type2;
    private Long value2;
    private Long action_type3;
    private Long value3;
    private Integer pay_amount;

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
