package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CvrProductCountLog对象", description="cvr3.0的检查日志")
public class CvrProductCountLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "dsp")
    private String dsp;
    @ApiModelProperty(value = "系统, 同PhoneOsEnum")
    private Integer os;
    @ApiModelProperty(value = "打点")
    private String product;
    @ApiModelProperty(value = "产品中文")
    private String productName;
    @ApiModelProperty(value = "检查时间")
    private Date createTime;
    @ApiModelProperty(value = "激活数")
    private Integer activeCount;
    @ApiModelProperty(value = "计算回传数")
    private Integer keyCount;
    //如果CVR>mCVRa，则减少y台回传设备
    private Integer yCount;
    //如果CVR<CVRa/m，则增加z台回传设备
    private Integer zCount;
    //限制内可以回传的数量, 去掉此次要回传的设备
    private Integer limitKeyCount;
    private Double cvr;
    private Double cvrAvg;
    private Integer status;
    @ApiModelProperty(value = "其他信息")
    private String remark;
}
