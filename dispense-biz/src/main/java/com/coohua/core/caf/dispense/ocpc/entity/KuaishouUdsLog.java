package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 快手uds回传记录
 * </p>
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OcpcEvent对象", description="")
public class KuaishouUdsLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "应用ID")
    private Integer appId;

    @ApiModelProperty(value = "用户ID")
    @TableField("userId")
    private String userId;

    @ApiModelProperty(value = "原始deviceId")
    private String sourceDeviceId;

    @ApiModelProperty(value = "原始oaId")
    private String sourceOaid;

    private String product;

    private String os;

    private String ocpcDeviceId;

    private String oaid;

    @ApiModelProperty(value = "oaid加md5")
    private String oaid2;

    @ApiModelProperty(value = "idfa加md5")
    private String idfa2;

    @ApiModelProperty(value = "事件类型")
    private Integer eventType;

    @ApiModelProperty(value = "类型名称")
    private String eventTypeName;

    private Long toutiaoClickId;

    @ApiModelProperty(value = "clickId")
    private Long clickId;

    @ApiModelProperty(value = "accountId")
    private String accountId;

    @ApiModelProperty(value = "account名称")
    private String accountName;

    @ApiModelProperty(value = "dsp平台")
    private String dsp;

    private String reqUrl;
    private String reqData;
    private String res;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private String macId;

    @ApiModelProperty(value = "click_time")
    private Date clickTime;

    private String androidId;

    @ApiModelProperty(value = "目前用做记录关键行为回传触发时的值")
    private String remark;

    @ApiModelProperty(value = "回传到的快手广告主id")
    private Long udsAccountId;

}
