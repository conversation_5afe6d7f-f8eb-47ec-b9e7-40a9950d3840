package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.KuaishouExtApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.mapper.AuthKuaishouAdvertiserMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.*;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.motan.service.UserEventRpcService;
import com.coohua.core.caf.dispense.ocpc.entity.*;
import com.coohua.core.caf.dispense.ocpc.service.*;
import com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil;
import com.coohua.core.caf.dispense.utils.PairUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.constant.BaseConstants.*;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getEcpmLevel10;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getFixedUserEventReq;
import static com.coohua.core.caf.dispense.enums.AccountActionTypeEnum.WATCH_VIDEO;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.*;
import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 广告主 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthKuaishouAdvertiserService extends ServiceImpl<AuthKuaishouAdvertiserMapper, AuthKuaishouAdvertiser> implements IService<AuthKuaishouAdvertiser> {

    @Autowired
    private AuthKuaishouAppService authKuaishouAppService;

    private Map<Long, AuthKuaishouApp> accountActionMap = Maps.newHashMap();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();
    @Value("${kuaishou.event.switch:false}")
    private Boolean kuaishouEnventSwitch;

    @Resource
    private AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    ThirdExtEventService thirdExtEventService;
    @Autowired
    KuaishouExtApolloConfig extApolloConfig;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    MatchCrowdBroadService matchCrowdBroadService;

    public Map<Long, AuthKuaishouApp> getAcMap(){
        return accountActionMap;
    }
    //    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void initAccountActionConfig() {
        List<AuthKuaishouApp> list = authKuaishouAppService.lambdaQuery()
                .isNotNull(AuthKuaishouApp::getAdvertiserId)
                .list();

        accountActionMap = list
                .stream()
                // 新老规则必须至少存在一个，才能加到配置里
                .filter(authKuaishouApp -> StringUtils.isNotBlank(authKuaishouApp.getEventTypes()) && StringUtils.isNotBlank(authKuaishouApp.getEventValues())
                        || authKuaishouApp.getEventType() != null && authKuaishouApp.getEventNumber() != null)
                .collect(Collectors.toMap(AuthKuaishouApp::getAdvertiserId, Function.identity(), (a, b) -> a));

        Map<BigInteger, AuthKuaishouAdvertiser> advertiserMap = this.lambdaQuery().eq(AuthKuaishouAdvertiser::getDelFlag, DELFLAG.weishanchu.value)
                .list()
                .stream()
                .collect(Collectors.toMap(k -> k.getCustomerId(), k -> k, (oldVal, newVal) -> oldVal));

        for (AuthKuaishouApp authKuaishouApp : list) {
            AuthKuaishouAdvertiser one = advertiserMap.get(authKuaishouApp.getId());
            if (one != null && one.getProductName() != null) {
                authKuaishouApp.setAppName(one.getProductName());
            }
        }
        appMap = list.stream()
                .filter(a -> a.getAppName() != null)
                .collect(Collectors.groupingBy(AuthKuaishouApp::getAppName,
                        Collectors.collectingAndThen(Collectors.toList(), apps -> apps.stream()
                                .flatMap(this::convertAction)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()))));
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        if (kuaishouEnventSwitch) {
            return appMap;
        } else {
            return new HashMap<>();
        }
    }

    public List<AccountAction> queryActionConfig(String appName) {
        if (kuaishouEnventSwitch) {
            return appMap.getOrDefault(appName, Lists.newArrayList());
        } else {
            return Lists.newArrayList();
        }

    }

    public AuthKuaishouApp getByAccountId(String accountId) {
        try {
            return accountActionMap.get(Long.valueOf(accountId));
        } catch (Exception e) {
            log.error("获取快手账户异常 " + accountId, e);
            return null;
        }
    }
    @Autowired
    DeviceSundyService deviceSundyService;
    @Autowired
    PeopleDevice30NoRetainService peopleDevice30NoRetainService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    UserBidService userBidService;
    @Autowired
    ProductService productService;
    @Autowired
    UserEventRpcService userEventRpcService;
    @Autowired
    RetainOneDayPeopleService retainOneDayPeopleService;;

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        MatchCrowdBroadNew matchCrowdBroadNew = new MatchCrowdBroadNew();
        try {

            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();

            if (!kuaishouEnventSwitch) {
                return matchDefaultConfig;
            }
            //若无配置 仅当默认值时上报  快手广告变现账户不回传关键行为
            AuthKuaishouApp authKuaishouApp = accountActionMap.get(StringUtils.isNotBlank(toutiaoClick.getAccountId()) ? Long.parseLong(toutiaoClick.getAccountId()) : 0L);
            if (authKuaishouApp == null) {
                return matchDefaultConfig;
            }

            String eventTypes;
            String eventValues;
            //日志打印使用，用来比较eventValues是否改变
            String oldEventValues;
            //日志打印使用，记录来自哪个人群包
            EnumCrowdType crowdType = null;
            Integer maxAccumulatePeriod;

            // 是否是达人账户
            boolean isDaren = Objects.equals(authKuaishouApp.getAccountType(), 2);
            // 是否是流量助推流量 : 达人账户 且 creative_id = "2"
            boolean isLlzt = isDaren && Objects.equals(toutiaoClick.getCid(), "2");
            // 是否是直播流量 : 达人账户 且 creative_id != "1" && != "2"
            boolean isZhibo = isDaren && Objects.equals(toutiaoClick.getCid(), "live");
            // 是否是直播流量 : 达人账户 且 creative_id = 'video'
            boolean isShipin = isDaren && Objects.equals(toutiaoClick.getCid(), "video");

            if (isLlzt) {
                // 流量助推流量
                eventTypes = authKuaishouApp.getEventTypesLlzt();
                eventValues = authKuaishouApp.getEventValuesLlzt();
                oldEventValues = authKuaishouApp.getEventValuesLlzt();
                maxAccumulatePeriod = authKuaishouApp.getMaxAccumulatePeriodLlzt();
            } else if (isZhibo) {
                // 直播流量
                eventTypes = authKuaishouApp.getEventTypesZhibo();
                eventValues = authKuaishouApp.getEventValuesZhibo();
                oldEventValues = authKuaishouApp.getEventValuesZhibo();
                maxAccumulatePeriod = authKuaishouApp.getMaxAccumulatePeriodZhibo();
            } else if (isShipin) {
                eventTypes = authKuaishouApp.getEventTypes();
                eventValues = authKuaishouApp.getEventValues();
                oldEventValues = authKuaishouApp.getEventValues();
                maxAccumulatePeriod = authKuaishouApp.getMaxAccumulatePeriod();
            }else {
                // 非达人 或 达人自然量
                eventTypes = authKuaishouApp.getEventTypes();
                eventValues = authKuaishouApp.getEventValues();
                oldEventValues = authKuaishouApp.getEventValues();
                maxAccumulatePeriod = authKuaishouApp.getMaxAccumulatePeriod();
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            javafx.util.Pair<String, DeviceSundy>  pair = deviceSundyService.queryByDevice(userEventReq);
            //通过开关判断是否要匹配低质量人群
            javafx.util.Pair<String, PeopleDevice30NoRetain>  pair2 = new javafx.util.Pair<>(null,null);

            if (ocpcSwitcher.kuaishouCallBackTestSwitch) {
                //匹配低质量人群包
                    log.info("匹配低质量人，userEventReq="+JSONObject.toJSONString(userEventReq));
                pair2 = peopleDevice30NoRetainService.queryLowQualifyByDevice(userEventReq);
            }

            //查询TL 七日留存一日人群包
            javafx.util.Pair<String, Integer> retainOneDayPeoplePair = retainOneDayPeopleService.queryRetainOneDayPeopleByDevice(userEventReq);

            Integer retainOneDayPeople = retainOneDayPeoplePair.getValue();
            DeviceSundy deviceSundy = pair.getValue();
            PeopleDevice30NoRetain peopleDevice30NoRetain = pair2.getValue();
            log.info("match user deviceSundy = {} peopleDevice30NoRetain = {} retainOneDayPeople = {}", JSONObject.toJSONString(deviceSundy), JSONObject.toJSONString(peopleDevice30NoRetain), JSONObject.toJSONString(retainOneDayPeople));


            int sundyCpaType = 0; //周人群cpa
            int lowQualityCpaType = 0; //低质量人群cpa
            int retainOneDayCpaType = 0;
            if (deviceSundy != null){
                sundyCpaType = deviceSundy.getCpaType();
            }
            if (peopleDevice30NoRetain != null){
                lowQualityCpaType = peopleDevice30NoRetain.getCapType();
            }
            if (retainOneDayPeople != null){
                retainOneDayCpaType = retainOneDayPeople;
            }

            //如果retainOneDayCpaType最大则走bid出价回传
            if (retainOneDayCpaType != 0 && retainOneDayCpaType > lowQualityCpaType && retainOneDayCpaType > sundyCpaType) {
                //当前产品是否走cpa回传
                boolean cpaCallBack = ocpcSwitcher.callBackProductList.isEmpty() || ocpcSwitcher.callBackProductList.contains(userEventReq.getProduct());
                if (cpaCallBack) {
                    log.info("bidCallBackSwitcher {} sundyCpaType {} lowQualityCpaType{} firstCallback{}",ocpcSwitcher.bidCallBackSwitcher,sundyCpaType,lowQualityCpaType,firstTimeJudge.getAsBoolean());
                }

                //CpaType >=2的人需要判断 (收入-提醒) > cpa 回传逻辑
                if (ocpcSwitcher.bidCallBackSwitcher && cpaCallBack && retainOneDayCpaType >= 2 && firstTimeJudge.getAsBoolean() ){
                    if (cpaCanCallBack(userEventReq)){
                        log.info("kuaishou cpaType>=2 当前用户可以回传 userId {} product {}",userEventReq.getUserId(),userEventReq.getProduct());
                        //入库统计数据
                        matchCrowdBroadNew.setUserId(userEventReq.getUserId());
                        matchCrowdBroadNew.setProduct(userEventReq.getProduct());
                        matchCrowdBroadNew.setSource("kuaishou");
                        matchCrowdBroadNew.setCanCallback(0);
                        matchCrowdBroadNew.setOs(userEventReq.getOs());
                        matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                        return true;
                    }else {
                        log.info("kuaishou cpaType>=2 当前用户不能回传 userId {} product {}",userEventReq.getUserId(),userEventReq.getProduct());
                        return false;
                    }
                }
            }


            //判断谁的倍数高使用哪个人群
            Integer capType = deviceSundyService.compareMultiple(userEventReq.getProduct(), deviceSundy != null ? deviceSundy.getOs() : "", sundyCpaType, lowQualityCpaType);
            if(pair.getValue()!=null&&capType.equals(pair.getValue().getCpaType())){
                userEventReq.setUarpuGy(pair.getKey());
                String nweventValues = deviceSundyService.authAccountAppFl(userEventReq,pair.getValue(),eventTypes,eventValues,authKuaishouApp.getAdvertiserId()+"","kuaishou");
                log.info("kuaishou etypes赋值改变 "+eventValues+"->"+nweventValues + "命中周末人群包 用户id {} 产品名称 {} capType {}",userEventReq.getUserId(),userEventReq.getProduct(),capType);
                eventValues = nweventValues;
                crowdType = EnumCrowdType.SUNDAY_CROWD;
            }else if (pair2.getValue()!=null&&capType.equals(pair2.getValue().getCapType())){
                userEventReq.setUarpuGy(pair2.getKey());
                String nweventValues = deviceSundyService.authAccountAppFl(userEventReq,pair2.getValue(),eventTypes,eventValues,authKuaishouApp.getAdvertiserId()+"","kuaishou");
                log.info("kuaishou etypes2赋值改变 "+eventValues+"->"+nweventValues+ "命中低质量人群包 用户id {} 产品名称 {} capType {}",userEventReq.getUserId(),userEventReq.getProduct(),capType);
                eventValues = nweventValues;
                crowdType = EnumCrowdType.LOW_QUALITY_CROWD;
            }
            fillMatchCrowdBroad(userEventReq, matchCrowdBroadNew, crowdType, oldEventValues, eventValues);
            matchCrowdBroadNew.setCpaType(capType);
            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                if (matchNewRule(userEventReq, eventTypes,
                        eventValues, firstTimeJudge, "快手", authKuaishouApp.getAppName(),
                        authKuaishouApp.getAdvertiserId().toString(), maxAccumulatePeriod)) {
                    //记录日志
                    recordInfo(toutiaoClick, userEventReq, isLlzt, isZhibo, eventTypes, eventValues, maxAccumulatePeriod, oldEventValues,capType,crowdType);
                    matchCrowdBroadNew.setCanCallback(0);
                    matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                    return true;
                }else {
                    if (matchNewRule(userEventReq, eventTypes,
                            oldEventValues, firstTimeJudge, "快手", authKuaishouApp.getAppName(),
                            authKuaishouApp.getAdvertiserId().toString(), maxAccumulatePeriod) && capType > 0){
                            log.info("加倍未回传，且未加倍可以回传 {}->{} cpaType {} 人群包crowd {} 用户id {} 产品名称 {}", oldEventValues, eventValues,capType,crowdType == null?"":crowdType.name,userEventReq.getUserId(),userEventReq.getProduct());
                        matchCrowdBroadNew.setCanCallback(1);
                        matchCrowdBroadNew.setOriginalCanCallback(0);
                    }
                    matchCrowdBroadNew.setOriginalCanCallback(1);
                    matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                }
                return matchOldRule(userEventReq, authKuaishouApp);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                boolean result = matchNewRule(userEventReq, eventTypes,
                        eventValues, firstTimeJudge, "快手", authKuaishouApp.getAppName(),
                        authKuaishouApp.getAdvertiserId().toString(), maxAccumulatePeriod);
                if (result) {
                    //记录日志
                    recordInfo(toutiaoClick, userEventReq, isLlzt, isZhibo, eventTypes, eventValues, maxAccumulatePeriod, oldEventValues,capType,crowdType);
                    matchCrowdBroadNew.setCanCallback(0);
                }else {
                    matchCrowdBroadNew.setCanCallback(1);
                    matchCrowdBroadNew.setOriginalCanCallback(1);
                    if (matchNewRule(userEventReq, eventTypes,
                            oldEventValues, firstTimeJudge, "快手", authKuaishouApp.getAppName(),
                            authKuaishouApp.getAdvertiserId().toString(), maxAccumulatePeriod) && capType > 0){
                        log.info("加倍未回传，且未加倍可以回传 {}->{} cpaType {} 人群包crowd {} 用户id {} 产品名称 {}", oldEventValues, eventValues,capType,crowdType == null?"":crowdType.name,userEventReq.getUserId(),userEventReq.getProduct());
                        matchCrowdBroadNew.setOriginalCanCallback(0);
                    }
                }
                matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                return result;
            }
            // 旧版本配置
            if (userEventReq.getActionType() != null) {
                return matchOldRule(userEventReq, authKuaishouApp);
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }

    private Boolean cpaCanCallBack(UserEventReq userEventReq) {
        if (userEventReq.getActionValues() == null) {
            log.info("用户actionValues为空，不回传 {}",JSONObject.toJSONString(userEventReq));
            return false;
        }
        log.info("kuaishou用户信息 userEventReq {}",JSONObject.toJSONString(userEventReq));
        //查询用户的提现和出价
        Map userBidMap = userBidService.queryUserCpaBy(userEventReq.getUserId(), userEventReq.getProduct());
        if (userBidMap == null || userBidMap.get("withdraw") == null || userBidMap.get("bid") == null) {
            return false;
        }
        Double withdraw = (Double) userBidMap.get("withdraw");
        Double bid = (Double) userBidMap.get("bid");
        //根据产品名称查询appiId
        Product product = productService.getByName(userEventReq.getProduct());
        if (product == null) {
            return false;
        }
        //查询ecpm、pv
        String mapStr = userEventRpcService.queryUserECPMBatch(userEventReq.getOs(), product.getAppId(), Arrays.asList(Long.parseLong(userEventReq.getUserId())));
        log.info("ecpm、pv查询 mapStr {} userId {}",mapStr,userEventReq.getUserId());
        Map map = JSONObject.parseObject(mapStr, Map.class);
        if (map == null || map.get("ecpm") == null || map.get("pv") == null) {
            return false;
        }
        Double ecpm = (Double) map.get("ecpm");
        Double pv = (Double) map.get("pv");
        log.info("当前用户收入、提现、出价 userId={} product={} income={} withdraw={} bid={}",userEventReq.getUserId(),userEventReq.getProduct(),ecpm*pv,withdraw,bid);
        return (ecpm * pv - withdraw) / bid >= ocpcSwitcher.callBackMutiple;
    }

    private static void fillMatchCrowdBroad(UserEventReq userEventReq, MatchCrowdBroadNew matchCrowdBroadNew, EnumCrowdType crowdType, String oldEventValues, String eventValues) {
        matchCrowdBroadNew.setSource(crowdType == null ? "" : crowdType.name());
        matchCrowdBroadNew.setProduct(userEventReq.getProduct());
        matchCrowdBroadNew.setUserId(userEventReq.getUserId());
        matchCrowdBroadNew.setOldEventValues(oldEventValues);
        matchCrowdBroadNew.setNewEventValues(eventValues);
        matchCrowdBroadNew.setOs(userEventReq.getOs());
    }

    private static void recordInfo(ToutiaoClick toutiaoClick, UserEventReq userEventReq, boolean isLlzt, boolean isZhibo, String eventTypes, String eventValues, Integer maxAccumulatePeriod, String oldEventValues, Integer capType, EnumCrowdType crowdType) {
        if (isLlzt || isZhibo) {
            log.info("{} 快手达人配置满足回传要求 {} {} {} {} req: {} deviceId: {}", isLlzt ? "流量助推" : "直播", toutiaoClick.getAccountId(), eventTypes, eventValues, maxAccumulatePeriod, userEventReq.getActionValues(), toutiaoClick.getOcpcDeviceId());
        }
        if (eventValues != null && eventValues.equals(oldEventValues)) {
            log.info("回传要求提高且达到回传要求 {}->{} cpaType {} 人群包 {} 用户id {} 产品名称 {}", oldEventValues, eventValues, capType, crowdType == null ? "" : crowdType.name, userEventReq.getUserId(), userEventReq.getProduct());
        } else {
            log.info("回传要求未改动达到回传要求 {}->{} cpaType {} 人群包 {} 用户id {} 产品名称 {}", oldEventValues, eventValues, capType, crowdType == null ? "" : crowdType.name, userEventReq.getUserId(), userEventReq.getProduct());
        }
    }

    private static boolean matchOldRule(UserEventReq userEventReq, AuthKuaishouApp authKuaishouApp) {

        if (authKuaishouApp.getEventType() == null || authKuaishouApp.getEventNumber() == null) {
            return false;
        }

        if (userEventReq.getActionType() == null || StringUtils.isBlank(userEventReq.getActionNumber())) {
            return false;
        }

        log.info("开始匹配快手旧版规则 device {} report {} config {} 产品 {} 广告主 {}", userEventReq.getOcpcDeviceId(),
                userEventReq.getActionNumber(), authKuaishouApp.getEventNumber(), authKuaishouApp.getAppName(),
                authKuaishouApp.getAdvertiserId());

        // 类型为1且视频次数相等，则满足回传条件
        if (WATCH_VIDEO.value.equals(userEventReq.getActionType())
                && Integer.parseInt(userEventReq.getActionNumber()) == authKuaishouApp.getEventNumber()) {
            log.info("快手视频次数回传条件满足 device {} report {} config {} 产品 {} 广告主 {}", userEventReq.getOcpcDeviceId(),
                    userEventReq.getActionNumber(), authKuaishouApp.getEventNumber(), authKuaishouApp.getAppName(),
                    authKuaishouApp.getAdvertiserId());
            return true;
        }

        return false;
    }

    private Stream<AccountAction> convertAction(AuthKuaishouApp authKuaishouApp) {
        try {
            // 新旧配置均无
            if (authKuaishouApp.getEventType() == null && StringUtils.isBlank(authKuaishouApp.getEventTypes())) {
                return Stream.empty();
            }

            if (authKuaishouApp.getEventType() != null && authKuaishouApp.getEventNumber() != null) {
                AccountAction accountAction = new AccountAction();
                accountAction.setAccountId(new BigInteger(String.valueOf(authKuaishouApp.getAdvertiserId())));
                accountAction.setEventType(authKuaishouApp.getEventType());
                accountAction.setEventNumber(String.valueOf(authKuaishouApp.getEventNumber()));
                return Stream.of(accountAction);
            }

            // 新配置
            try {
            /*    // 是否是达人账户 达人配置有三种
                boolean isDaren = Objects.equals(authKuaishouApp.getAccountType(), 2);
                if (isDaren) {
                    return Stream.of(
                            buildEventStandardConsideringMultiEcpm(authKuaishouApp.getEventTypes(), authKuaishouApp.getEventValues(), new BigInteger(String.valueOf(authKuaishouApp.getAdvertiserId())), authKuaishouApp.getMaxAccumulatePeriod())
                            , buildEventStandardConsideringMultiEcpm(authKuaishouApp.getEventTypesLlzt(), authKuaishouApp.getEventValuesLlzt(), new BigInteger(String.valueOf(authKuaishouApp.getAdvertiserId())), authKuaishouApp.getMaxAccumulatePeriodLlzt())
                    ).flatMap(k-> k);
                }*/

                return buildEventStandardConsideringMultiEcpm(authKuaishouApp.getEventTypes(), authKuaishouApp.getEventValues(),
                        new BigInteger(String.valueOf(authKuaishouApp.getAdvertiserId())), authKuaishouApp.getMaxAccumulatePeriod());

            } catch (Exception e) {
                log.error("快手广告主配置解析出错 ", e);
                return Stream.empty();
            }

        }catch (Exception e){
            log.error("",e);
        }

        return Stream.empty();
    }

    public static Stream<AccountAction> buildEventStandardConsideringMultiEcpm(String eventTypesText, String eventValuesText,
                                                                               BigInteger advertiserId, Integer maxAccumulatePeriod) {

        String[] eventTypes = StringUtils.splitByWholeSeparator(eventTypesText, ",");
        String[] eventValues = StringUtils.splitByWholeSeparator(eventValuesText, ",");

        // 多arpu格式，走新逻辑
        if (OcpcNewCallbackRuleValidateUtil.isMultiArpu(eventTypesText, eventValuesText)) {
            return buildAccountActionForMultiArpu(advertiserId, eventTypes, eventValues);
        }

        int configNumber = StringUtils.countMatches(eventValues[0], MULTI_ECPM_SEPARATOR) + 1;

        List<Map<String, String>> configs = IntStream.range(0, configNumber)
                .mapToObj(index -> Maps.<String, String>newHashMap())
                .collect(toList());

        for (int index = 0; index < eventTypes.length; index++) {
            String[] eventValuePieces = StringUtils.splitByWholeSeparator(eventValues[index], MULTI_ECPM_SEPARATOR);

            for (int valueIndex = 0; valueIndex < configNumber; valueIndex++) {
                configs.get(valueIndex).put(eventTypes[index], eventValuePieces[valueIndex]);
            }
        }

        return configs.stream()
                .map(config -> new AccountAction(advertiserId, config, maxAccumulatePeriod));
    }

    private static Stream<AccountAction> buildAccountActionForMultiArpu(BigInteger advertiserId, String[] eventTypes, String[] eventValues) {

        // 多arpu 格式 : 1,2 -> 8/10/10,1.5/2.5/5,30/60/1440  (次数1/次数2/次数3,arpu1/arpu2/arpu3,累计时间1/累计时间2/累计时间3)
        // 以下为 8/10/10,1.5/2.5/5,30/60/1440 拆分后的值
        String[] nums = StringUtils.splitByWholeSeparator(eventValues[0], MULTI_ECPM_SEPARATOR);
        String[] arpus= StringUtils.splitByWholeSeparator(eventValues[1], MULTI_ECPM_SEPARATOR);
        String[] times= StringUtils.splitByWholeSeparator(eventValues[2], MULTI_ECPM_SEPARATOR);

        int configNumber = arpus.length;

        List<AccountAction> configs = IntStream.range(0, configNumber)
                .mapToObj(index -> new AccountAction(advertiserId))
                .collect(toList());

        for (int valueIndex = 0; valueIndex < configNumber; valueIndex++) {
            AccountAction accountActionIndex = configs.get(valueIndex);
            accountActionIndex.getEventConfigurations().put("1", nums[valueIndex]);
            accountActionIndex.getEventConfigurations().put("2", arpus[valueIndex]);
            accountActionIndex.setMaxAccumulatePeriod(Integer.valueOf(times[valueIndex]));
        }

        return configs.stream();

    }

    public void tryCallbackExtDefine(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        AuthKuaishouApp advertiser = getKuaishouAdvertiserForExtEvent(toutiaoClick, userEventReq);

        if (advertiser == null) {
            return;
        }

        ThirdAdvertiser thirdAdvertiser = new ThirdAdvertiser(advertiser);
        DspType dspType = thirdAdvertiser.getDsp();

        boolean locked = false;
        int eventTypeKey = ToutiaoEventTypeEnum.KEY_ACTION_EXT_EVENT.value;
        String lockKey = RedisKeyConstants.getReportLockKey(userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
        try {
            if (extApolloConfig.lockExtDefineAllEnable || extApolloConfig.lockExtDefineEnableProducts.contains(userEventReq.getProduct())) {
                // 要使用分布式锁保证回传顺序执行
                locked = ocpcEventService.tryGetDistributedLock(lockKey, "1", 10000);

                if (!locked) {
                    log.warn("快手衍生定义上报并发抢占资格失败 {} {} {}", userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
                    return;
                }

                if (NUM_ARPU_RULE.equals(advertiser.getEventTypes())) {
                    // 获取符合的关键行为衍生事件
                    List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, userEventReq);
                    if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                        OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                        List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_2(dspType, matchExtEvents, ocpcEvent ,
                                ()-> checkExtDefineCount(toutiaoClick, userEventReq, advertiser));
                        ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                    }
                } else if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())) {
                    // ecpm需要先判断是否符合账户的配置条件
                    Pair<Boolean, ActionTypes> actionTypesPair = checkExtDefineCount(toutiaoClick, userEventReq, advertiser);
                    if (actionTypesPair.getLeft()) {
                        ActionTypes ecpmLevel10 = getEcpmLevel10(advertiser.getEventValues());
                        UserEventReq fixedUserEventReq = getFixedUserEventReq(userEventReq, ecpmLevel10);
                        // 获取符合的关键行为衍生事件
                        List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, fixedUserEventReq);
                        if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                            OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                            List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_3(dspType, ecpmLevel10, matchExtEvents, ocpcEvent);
                            ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("衍生事件分布式锁抢占出现异常 ", e);
        } finally {
            if (locked) {
                ocpcEventService.unlockDistributedLock(lockKey);
            }
        }
    }

    private AuthKuaishouApp getKuaishouAdvertiserForExtEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return null;
            }

            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            if (dspType != DspType.KUAISHOU) {
                return null;
            }

            // 限制产品
            if (!extApolloConfig.extDefineEnableAll && !extApolloConfig.extDefineEnableProducts.contains(userEventReq.getProduct())) {
                return null;
            }

            AuthKuaishouApp advertiser = accountActionMap.get(Long.valueOf(toutiaoClick.getAccountId()));

            //若无配置 不上报
            if (advertiser == null || StringUtils.isAnyBlank(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // 非普通账户不回传 (达人账户不回传)
            boolean isNormalAccount = Objects.equals(advertiser.getAccountType(), 1);
            if (!isNormalAccount) {
                return null;
            }

            // 分时arpu账户 不上报
            if (isMultiArpu(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // 次数ecpm账户，若没有10这一档位时 不上报
            if (checkEcpmHasNotLevel10(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            Map<String, String> reportValues = analyzeNewValueToMap(userEventReq.getActionValues());
            String reqEventTypes = reportValues.keySet().stream().sorted(Comparator.comparing(Integer::valueOf)).collect(Collectors.joining(","));
            // 若请求的类型和当前账户的类型不一致，不上报
            if (!Objects.equals(reqEventTypes, advertiser.getEventTypes())) {
                return null;
            }

            // 限制适用类型
            if (!extApolloConfig.extDefineEnableEventTypes.contains(advertiser.getEventTypes())) {
                return null;
            }

            // 限制适用账户
            if (!extApolloConfig.extDefineEnableAllAccount && !extApolloConfig.extDefineEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                return null;
            }

            // 对于1,3的账户限制产品
            if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())
                    && !extApolloConfig.extDefineEnableAllFor_1_3
                    && !extApolloConfig.extDefineEnableProductsFor_1_3.contains(userEventReq.getProduct())) {
                return null;
            }

            return advertiser;

        } catch (Exception e) {
            log.error("getKuaishouAdvertiserForExtEvent error ", e);
        }
        return null;
    }

    /**
     * 检查衍生事件计数
     * @return
     */
    public Pair<Boolean, ActionTypes> checkExtDefineCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq, AuthKuaishouApp advertiser) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return PairUtil.ofFalse();
            }

            //非快手不回传衍生定义
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            if (dspType != DspType.KUAISHOU) {
                return PairUtil.ofFalse();
            }

            //若无配置 仅当默认值时上报
            if (advertiser == null) {
                return PairUtil.ofFalse();
            }

            // 限制适用类型
            if (!extApolloConfig.extDefineEnableEventTypes.contains(advertiser.getEventTypes())) {
                return PairUtil.ofFalse();
            }

            // 限制适用账户
            if (!extApolloConfig.extDefineEnableAllAccount && !extApolloConfig.extDefineEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                return PairUtil.ofFalse();
            }

            // 不需判断是否首次满足条件
            BooleanSupplier firstTimeJudge = () -> true;

            Pair<Boolean, ActionTypes> matchNewRuleWithResult = matchNewRuleWithResult(userEventReq, advertiser.getEventTypes(),
                    advertiser.getEventValues(), firstTimeJudge, "快手",
                    advertiser.getAppName(), advertiser.getAdvertiserId().toString(), advertiser.getMaxAccumulatePeriod());
            if (matchNewRuleWithResult.getLeft()) {
                return matchNewRuleWithResult;
            }

        } catch (Exception e) {
            log.error("checkExtEventCount error ", e);
        }
        return PairUtil.ofFalse();
    }
}
