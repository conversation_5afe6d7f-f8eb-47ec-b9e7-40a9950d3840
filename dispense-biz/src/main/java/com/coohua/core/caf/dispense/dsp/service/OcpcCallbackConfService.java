package com.coohua.core.caf.dispense.dsp.service;

import com.coohua.core.caf.dispense.dsp.entity.OcpcCallbackConf;
import com.coohua.core.caf.dispense.dsp.mapper.OcpcCallbackConfMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-08-10
*/
@Service
public class OcpcCallbackConfService extends ServiceImpl<OcpcCallbackConfMapper, OcpcCallbackConf> {

}
