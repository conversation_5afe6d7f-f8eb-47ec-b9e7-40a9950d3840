package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

import java.util.Date;

@Data
public class UploadAdvBehaviorReq {
    private String appId;
    private String deviceIdType;
    private String deviceId;
    private Long actionTime;
    private String actionType;
    private String customAction;
    private String productId;
    private String productClass;
    private String productParam;
    private String missType;
    private Long missTime;
    private String callBack;
}
