package com.coohua.core.caf.dispense.dsp.service.bytemin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.ocpc.entity.ByteMinconf;
import com.coohua.core.caf.dispense.ocpc.service.ByteMinconfService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicHeader;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class DouyinMinEcpmService {
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    ByteMinconfService byteMinconfService;
    public String getEpmUrl = "https://open.douyin.com/api/traffic/v1/rt_ecpm/query/";

    /**
     * {
     *     "err_msg": "",
     *     "err_no": 0,
     *     "log_id": "202405092052558667536760016D148AAA",
     *     "data": {
     *         "next_cursor": "6579612964",
     *         "records": [
     *             {
     *                 "event_time": "1715255208",
     *                 "id": "6578419349",
     *                 "mp_id": "tte77e91c3784f212e01",
     *                 "open_id": "_000uz5d-ONjSY61nEW8a5Rt4iqtHWvLPBiw",
     *                 "ad_type": "激励视频广告",
     *                 "cost": "1517"
     *             },
     *             {
     *                 "id": "6578715944",
     *                 "mp_id": "tte77e91c3784f212e01",
     *                 "open_id": "_000uz5d-ONjSY61nEW8a5Rt4iqtHWvLPBiw",
     *                 "ad_type": "激励视频广告",
     *                 "cost": "92",
     *                 "event_time": "1715255378"
     *             },
     *             {
     *                 "id": "6579033173",
     *                 "mp_id": "tte77e91c3784f212e01",
     *                 "open_id": "_000uz5d-ONjSY61nEW8a5Rt4iqtHWvLPBiw",
     *                 "ad_type": "激励视频广告",
     *                 "cost": "98",
     *                 "event_time": "1715255566"
     *             },
     *             {
     *                 "ad_type": "激励视频广告",
     *                 "cost": "44",
     *                 "event_time": "1715255740",
     *                 "id": "6579345864",
     *                 "mp_id": "tte77e91c3784f212e01",
     *                 "open_id": "_000uz5d-ONjSY61nEW8a5Rt4iqtHWvLPBiw"
     *             },
     *             {
     *                 "ad_type": "激励视频广告",
     *                 "cost": "52",
     *                 "event_time": "1715255896",
     *                 "id": "6579612964",
     *                 "mp_id": "tte77e91c3784f212e01",
     *                 "open_id": "_000uz5d-ONjSY61nEW8a5Rt4iqtHWvLPBiw"
     *             }
     *         ]
     *     }
     * }
     * @param userId
     * @param product
     * @param openId
     * @param ackToken
     * @param dateHour
     * @return
     */
    public List<ByteEcpm> getDouyinMinEcmp(String userId, String product, String openId, String ackToken, String dateHour, String cursor){
        List<ByteEcpm> dlist = new ArrayList();
        String reqUrl = getEpmUrl;
        CloseableHttpClient client = toutiaoCallService.getHttpClient();
        try {

            Header[] headers = new BasicHeader[]{
                new BasicHeader("content-type","application/json"),
                    new BasicHeader("access-token",ackToken),
            };
            /**
             * {
             *   "date_hour": "2023-11-01 23"
             * }
             * {
             *   "open_id": "xxxyyyzzz",
             *   "date_hour": "2023-11-01",
             *   "cursor": "100000",
             *   "page_size": 200
             * }
             */
            int page_size = 500;
            JSONObject body = new JSONObject()
                    .fluentPut("open_id", openId)
                    .fluentPut("date_hour", dateHour)
                    .fluentPut("cursor", cursor)
                    .fluentPut("page_size", page_size);
            int tryNum = 0;
            String rsp = toutiaoCallService.resendUrlPost(reqUrl,body.toJSONString(),client,headers);
            JSONObject jsonObject  = JSON.parseObject(rsp);
            JSONObject dataObj  = jsonObject.getJSONObject("data");
            Integer errNo = jsonObject.getInteger("err_no");
            while (errNo.equals(28001003) && tryNum < 3){
                //错误码为 28001003 重新请求
                log.info("重新请求ack失效 "+userId+" "+rsp+" "+ackToken );
                Thread.sleep(300);
                ByteMinconf byteMinconf = byteMinconfService.getByteMinconf(product);
                Header[] headers2 = new BasicHeader[]{
                        new BasicHeader("content-type","application/json"),
                        new BasicHeader("access-token",byteMinconf.getAcktoken()),
                };
                rsp = toutiaoCallService.resendUrlPost(reqUrl,body.toJSONString(),client,headers2);
                jsonObject  = JSON.parseObject(rsp);
                dataObj  = jsonObject.getJSONObject("data");
                errNo = jsonObject.getInteger("err_no");
                tryNum++;
            }
            log.info("请求ecpm "+userId+" reqUrl "+reqUrl+" "+rsp);
            JSONArray jsonArray = dataObj.getJSONArray("records");
            if(jsonArray!=null){
                for(int i=0;i<jsonArray.size();i++){
                    ByteEcpm  byteAdEcpm = new ByteEcpm();
                    JSONObject adJobj  =  jsonArray.getJSONObject(i);
                    Double cost = adJobj.getDouble("cost"); //十万分之一元
                    Date eventDate = new Date(Long.parseLong(adJobj.getString("event_time")) * 1000);
                    String id = adJobj.getString("id");
                    String openIdTr = adJobj.getString("open_id");

                    byteAdEcpm.setCost(cost);
                    byteAdEcpm.setEventDate(eventDate);
                    byteAdEcpm.setId(id);
                    byteAdEcpm.setOpenId(openIdTr);
                    byteAdEcpm.setUserId(userId);
                    byteAdEcpm.setProduct(product);
                    Date date = new Date();
                    byteAdEcpm.setCreateTime(date);
                    byteAdEcpm.setUpdateTime(date);
                    dlist.add(byteAdEcpm);
                }
                if(jsonArray.size() == page_size && dataObj.containsKey("next_cursor")) {
                    dlist.addAll(getDouyinMinEcmp(userId, product, openId, ackToken, dateHour, dataObj.getString("next_cursor")));
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
        return dlist;
    }

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    public String getAckTokenC(String client_key,String client_secret){
        String redisKey = client_key+"clientTk";
        String redisVal = bpDispenseJedisClusterClient.get(redisKey);
        if(StringUtils.isNotBlank(redisVal)){
            log.info("命中clientToken 缓存 "+redisVal);
            return redisVal;
        }
        String ackToken =  getAckToken(client_key,client_secret);
        if(StringUtils.isNotBlank(ackToken)){
            log.info("刷新clientToken 缓存 "+client_key+" "+redisVal);
            bpDispenseJedisClusterClient.setex(redisKey, DateTimeConstants.SECONDS_PER_HOUR*2,ackToken);
        }
        return ackToken;

    }
    public String reshAckTokenC(String client_key,String client_secret){
        String redisKey = client_key+"clientTk";
        String ackToken =   getAckToken(client_key,client_secret);
        if(StringUtils.isNotBlank(ackToken)){
            log.info("刷新clienttoken 缓存刷新 "+client_key);
            bpDispenseJedisClusterClient.setex(redisKey, DateTimeConstants.SECONDS_PER_HOUR*2,ackToken);
        }
        return ackToken;
    }

    private String getAckToken(String client_key,String client_secret){
        String ackUrl = "https://open.douyin.com/oauth/client_token/";
        CloseableHttpClient client = toutiaoCallService.getHttpClient();
        try {
            ackUrl = ackUrl+"?client_key="+client_key+"&secret="+client_secret+"&grant_type=client_credential";

            JSONObject body = new JSONObject()
                    .fluentPut("client_key", client_key)
                    .fluentPut("secret", client_secret)
                    .fluentPut("grant_type", "client_credential");

            String rsp = toutiaoCallService.resendUrlPost(ackUrl,body.toJSONString(),client);

            //{
            //  "err_no": 0,
            //  "err_tips": "success",
            //  "data": {
            //    "access_token": "0801121***********",
            //    "expires_in": 7200
            //  }
            //}
            log.info("请求clienttoken "+rsp+" reqUrl: "+ackUrl);
            JSONObject jsonObject  = JSON.parseObject(rsp);
            JSONObject dataObj  =jsonObject.getJSONObject("data");
            return dataObj.getString("access_token");
        }catch (Exception e){
            log.error("获取acktoken 错误 "+client_key,e);
        }finally {
            if(client!=null){
                try {
                    client.close();
                }catch (Exception e){
                    log.error("",e);
                }
            }
        }
        return null;
    }

}
