package com.coohua.core.caf.dispense.ocpc.mapper;

import com.coohua.core.caf.dispense.ocpc.entity.GuiStatistics;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface UserActiveMapper extends BaseMapper<UserActive> {
    @Select({
              "                 select *, zrl / total                                               "
            + "                 from (select product, channel, count(*) total,                      "
            + "                              sum(if(source = '自然量', 1, 0)) zrl                      "
            + "                 from `coo-ocpc`.user_active                                           "
            + "                 where create_time >= date_add(now(), interval -4 hour)                "
            + "                 group by product, channel) c order by total desc                    "
    })
    List<GuiStatistics> queryGuiStatistics();
}
