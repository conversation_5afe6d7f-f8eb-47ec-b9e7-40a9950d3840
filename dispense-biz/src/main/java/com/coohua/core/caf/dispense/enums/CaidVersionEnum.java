package com.coohua.core.caf.dispense.enums;

import lombok.Getter;

@Getter
public enum CaidVersionEnum {

    /**
     * Local CAID类型
     */
    CAID_LOCAL(1, "localCaid", "local"),

    /**
     * 2022年1月11日版本CAID
     */
    CAID_20220111(2, "caid20220111", "20220111"),

    /**
     * 2023年3月30日版本CAID
     */
    CAID_20230330(3, "caid20230330", "20230330"),

    /**
     * 2025年3月25日版本CAID
     */
    CAID_20250325(4, "caid20250325", "20250325");

    /**
     * CAID类型代码
     */
    private final Integer code;

    /**
     * 对应的Redis字段名
     */
    private final String fieldName;

    /**
     * Redis key前缀
     */
    private final String keyPrefix;

    CaidVersionEnum(Integer code, String fieldName, String keyPrefix) {
        this.code = code;
        this.fieldName = fieldName;
        this.keyPrefix = keyPrefix;
    }

    /**
     * 根据code获取枚举
     */
    public static CaidVersionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CaidVersionEnum version : values()) {
            if (version.getCode().equals(code)) {
                return version;
            }
        }
        return null;
    }

    /**
     * 根据字段名获取枚举
     */
    public static CaidVersionEnum getByKeyPrefix(String keyPrefix) {
        if (keyPrefix == null) {
            return null;
        }
        for (CaidVersionEnum version : values()) {
            if (version.getKeyPrefix().equals(keyPrefix)) {
                return version;
            }
        }
        return null;
    }
}
