package com.coohua.core.caf.dispense.ocpc.service;

import com.coohua.core.caf.dispense.ocpc.entity.Tables;
import com.coohua.core.caf.dispense.ocpc.mapper.InfomationSchemaMapper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-03-23
*/
@Configuration
@ConditionalOnClass(InfomationSchemaMapper.class)
public class TablesService {
    @Autowired
    InfomationSchemaMapper infomationSchemaMapper;
    public static Cache<String,Tables> cache = CacheBuilder.newBuilder().expireAfterWrite(30,TimeUnit.MINUTES).build();
    //    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ?")
    public void initCache(){
        List<Tables> tablesList = infomationSchemaMapper.queryAllClickTables();
        for(Tables tables : tablesList){
            String keyT = tables.getTableName().replace("ocpc_click_","");
            cache.put(keyT,tables);
        }
    }
    public Tables queryByTableName(String tableEnd){

        Tables tables = cache.getIfPresent(tableEnd);
        if(tables==null){
            List<Tables> tablesList = infomationSchemaMapper.queryTablesByEnd(tableEnd);
            if(tablesList.size()==1){
                cache.put(tableEnd,tablesList.get(0));
                return tablesList.get(0);
            }else{
                cache.put(tableEnd,new Tables());
            }
        }else if(tables.getTableName()==null){
            return null;
        }
        return tables;
    }

}
