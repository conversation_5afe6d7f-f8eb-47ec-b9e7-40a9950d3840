package com.coohua.core.caf.dispense.ocpc.service;

import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.hbase.HbaseLockUserActiveService;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class VivoLockService {
    @Autowired
    HbaseLockUserActiveService hbaseLockUserActiveService;
    @Autowired
    UserActiveService userActiveService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    OcpcLockinfoService ocpcLockinfoService;
    public String getVivoDsp(UserEventReq userEventReq){
        UserActive userActive = hbaseLockUserActiveService.queryDeviceActive(userEventReq);
        ToutiaoClick toutiaoClick = userActiveService.guiOcpc(userEventReq);
        Date userActTime  = new Date();
        if(userActive!=null){
            userActTime = userActive.getCreateTime();
        }
        String dsp = "nodsp";

        if(toutiaoClick!=null){
//            boolean isToFlg = isCheckChal(userEventReq.getPkgChannel());
//            if(isToFlg){
//                Long mins = (userActTime.getTime()-toutiaoClick.getCreateTime().getTime())/ DateTimeConstants.MILLIS_PER_MINUTE;
//                if(mins>=0 && mins < DateTimeConstants.MINUTES_PER_HOUR*ocpcSwitcher.clickHours){
//                    dsp = toutiaoClick.getDsp();
//                }else{
//                    log.info("vivo无效click "+userEventReq.getProduct()+" "+userEventReq.getPkgChannel()+" "+ DateUtils.formatDateForYMDSTR(userActTime)+":"+ DateUtils.formatDateForYMDSTR(toutiaoClick.getCreateTime()));
//                    dsp = "nodsp";
//
//                }
//                dsp = toutiaoClick.getDsp();
//            }else{
//                dsp = toutiaoClick.getDsp();
//            }
            dsp = toutiaoClick.getDsp();
        }else{
            long days = (System.currentTimeMillis()-userActTime.getTime())/DateTimeConstants.MILLIS_PER_DAY;
            if(days>20){
                dsp = "than";
                log.info("创建时间大于20直接放过 "+days+" "+userEventReq.getUserId()+" "+userEventReq.getOaid()+" "+userEventReq.getOs());
            }
        }
        ocpcLockinfoService.saveLockInfo(userEventReq,userActive,toutiaoClick,dsp,"vivodsp");
        return dsp;
    }
    public  boolean isVivoFlg(UserEventReq userEventReq){
        boolean isToFlg = StringUtils.isNotBlank(userEventReq.getPkgChannel()) && isCheckChal(userEventReq.getPkgChannel());
        return isToFlg;
    }

    public boolean isCheckChal(String channel){
        boolean isToFlg = false;
        if(StringUtils.isNotBlank(channel)){
            for(String confChal : ocpcSwitcher.saveChls){
                if(StringUtils.contains(channel,confChal)){
                    isToFlg = true;
                    break;
                }
            }
        }
        return isToFlg;
    }

}
