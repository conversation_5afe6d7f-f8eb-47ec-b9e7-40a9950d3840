package com.coohua.core.caf.dispense.ocpc.service;

import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEventCold;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEventCold2;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcEventColdMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-11-02
*/
@Service
@Slf4j
public class OcpcEventColdService extends ServiceImpl<OcpcEventColdMapper, OcpcEventCold> {
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @Resource(name = "ocpcTo8Eevent")
    ThreadPoolTaskExecutor ocpcTo8Eevent;
    @Autowired
    OcpcEventCold2Service ocpcEventCold2Service;
    public void insertColdEvent(UserEvent userEvent){
        try {
            ocpcTo8Eevent.execute(()->{
                OcpcEventCold2 ocpcEventCold = new OcpcEventCold2();
                BeanUtils.copyProperties(userEvent,ocpcEventCold);
                ocpcEventCold2Service.save(ocpcEventCold);
                log.info("插入cold event成功 "+ocpcEventCold.getOcpcDeviceId());
            });
        }catch (Exception e){
            log.error("",e);
        }
    }
}
