package com.coohua.core.caf.dispense.cache;

import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.PkgToProduct;
import com.coohua.core.caf.dispense.dsp.service.PkgToProductService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcPkgToProductService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;
import javax.xml.ws.Action;
import java.util.*;
import java.util.stream.Collectors;

import static com.coohua.core.caf.dispense.constant.BaseConstants.*;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/12/4 17:43
 */
@Component
@Slf4j
public class InnerPkgToProductCache {

    @Autowired
    private PkgToProductService pkgToProductService;

    public static Map<String, String> PKG_TO_PRODUCT_MAP = new HashMap();
    @Autowired
    OcpcPkgToProductService ocpcPkgToProductService;

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    /**
     *
     */
    //    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ?")
    public void initPkgToProductMap() {
        try {
            List<PkgToProduct> list = null;
            try {
                list = pkgToProductService.lambdaQuery().list();

            } catch (Exception e) {
                log.error("", e);
                list = ocpcPkgToProductService.lambdaQuery().list();
            }
            if (!CollectionUtils.isEmpty(list)) {
                PKG_TO_PRODUCT_MAP = list.stream().collect(Collectors.toMap(a -> a.getPkgName(), a -> a.getProduct(), (old, newVal) -> newVal));
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }

    @Scheduled(cron = "0 0/3 * * * ?")
    public void syncPkgToProductToOcpc() {

        try {

            boolean locked = tryGetDistributedLock(RedisKeyConstants.SYNC_PKG_TO_PRODUCT_TO_OCPC_KEY, "1", 10000);
            if (!locked) {
                log.warn("syncPkgToProductToOcpc并发抢占资格失败");
                return;
            }

            List<PkgToProduct> list = pkgToProductService.lambdaQuery().list();
            for (PkgToProduct pkgToProduct : list) {
                try {
                    ocpcPkgToProductService.saveOrUpdate(pkgToProduct);
                } catch (Exception e) {
                    log.error("syncPkgToProductToOcpc异常id:" + pkgToProduct.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error("syncPkgToProductToOcpc异常", e);
        }

    }

    /**
     * redis 分布式锁
     * @param lockKey  锁定Key
     * @param requestId 请求ID
     * @param millsExpireTime 过期毫秒数
     * @return
     */
    private boolean tryGetDistributedLock(String lockKey, String requestId, int millsExpireTime) {
        String result = bpDispenseJedisClusterClient.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, millsExpireTime);
        return LOCK_SUCCESS.equals(result);
    }
}
