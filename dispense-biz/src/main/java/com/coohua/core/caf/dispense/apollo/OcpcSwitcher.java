package com.coohua.core.caf.dispense.apollo;

import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.ocpc.entity.SdkConf;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Set;

@Configuration
@Slf4j
public class OcpcSwitcher {
    @Value("${ocpc.redis.read:true}")
    public boolean readRedisSwitch;

    @Value("${ocpc.redis.write:true}")
    public boolean writeRedisSwitch;


    @Value("${ocpc.ocpc.write:false}")
    public boolean writeOcpcSwitch;

    /**
     * ocpc-event表写入开关写入
     */
    @Value("${ocpc.event.write:true}")
    public boolean writeOcpcEventSwitch;

    @Value("${ocpc.event.sysWrite:false}")
    public boolean sysWriteOcpcEventSwitch;

    /**
     * toutiao_ltv表写入开关写入
     */
    @Value("${ocpc.toutiao.ltv.write:true}")
    public boolean writeToutiaoLtvSwitch;

    /**
     * kuaishou_ext_event 表写入开关写入
     */
    @Value("${ocpc.kuaishou.ext.write:true}")
    public boolean writeKuaishouExtSwitch;

    /**
     * tencent_ltv 表写入开关写入
     */
    @Value("${ocpc.tencent.ltv.write:true}")
    public boolean writeTencentLtvSwitch;

    @Value("${dsp.user.event.failed.write:true}")
    public boolean writeUserEventFailedSwitch;

    @Value("${ocpc.dsp.write:true}")
    public boolean writeDspSwitch;

    /** 需要写入 coo-dispense.toutiao_click 表的dsp范围 */
    @ApolloJsonValue("${ocpc.dsp.write.dsp:[]}")
    public Set<String> writeDspSwitchDsp;

    /** 需要写入 coo-dispense.toutiao_click 表的账户id范围 */
    @ApolloJsonValue("${ocpc.dsp.write.account:[]}")
    public Set<String> writeDspSwitchAccount;

    @Value("${ocpc.event.ks.ipua:false}")
    public boolean ksipua;

    @Value("${ocpc.rta.lognum:1000}")
    public Integer writeLogNUM;

    @Value("${ocpc.rta.isAllJ:false}")
    public Boolean isAllJ;

    @Value("${ocpc.rta.logopen:false}")
    public Boolean rtaLogOpen;
    @ApolloJsonValue("${ocpc.lock.caid:[]}")
    public Set<String> caids;

    @Value("${save.ksdr.to.redis.switcher:false}")
    public boolean saveKsdrSwithcer
            ;

    public boolean saveToutiaoClick(ToutiaoClick toutiaoClick) {
        if (!writeDspSwitch || toutiaoClick == null) {
            return false;
        }
        if (writeDspSwitchDsp.contains(toutiaoClick.getDsp())
                || writeDspSwitchAccount.contains(toutiaoClick.getAccountId())) {
            return true;
        }
        return false;
    }

    @Value("${ocpc.hbase.read:false}")
    public boolean readHbaseSwitch;


    @Value("${ocpc.hbase.ocpc.read:false}")
    public boolean readOcpcHbaseSwitch;
    @Value("${ocpc.bu.flag:true}")
    public boolean readOcpcYanchi;
    @Value("${ocpc.hbase.user.read:true}")
    public boolean readUserHbaseSwitch;

    @Value("${ocpc.user.active.readlong:false}")
    public boolean readClickLongFlag;

    @Value("${ocpc.hbase.write:true}")
    public boolean writeHbaseSwitch;

    @Value(value= "${httpclient.new.enable:false}")
    public Boolean toNewHttpClient;

    @ApolloJsonValue("${ocpc.log.switch:[1,2]}")
    public Set<Integer> ocpcLogSwitch;

    @Value("${ocpc.hbase.ocpc.xmd:false}")
    public boolean readHbaseXmd;


    @Value("${ocpc.baidu.channel.gy:true}")
    public boolean baiduGy;

    @Value("${ocpc.baidu.baiduJh:true}")
    public boolean baiduJh;

    @Value("${ocpc.save.lock:false}")
    public boolean saveLock;

    @ApolloJsonValue("${ocpc.baidu.cchannel:[]}")
    public Set<String> baiduClickChannel;
    /** gychannel 中配置老的渠道归因的账户和渠道包，现在百度默认点击归因 */
    @ApolloJsonValue("${ocpc.baidu.gychannel:[]}")
    public Set<String> baiduGyChannel;

    @ApolloJsonValue("${ocpc.baidu.kaccount:[]}")
    public Set<String> baiduKeyAccount;

    public boolean needLog(Integer type) {
        return CollectionUtils.isNotEmpty(ocpcLogSwitch) && ocpcLogSwitch.contains(type);
    }

    @Value("${ocpc.check.repeat.active.enable.all.product:false}")
    public boolean checkRepeatActiveEnableAllProduct;

    @ApolloJsonValue(value= "${ocpc.check.repeat.active.enable.products:[]}")
    public Set<String> checkRepeatActiveEnableProducts;

    @Value("${ocpc.baidu.cklong:false}")
    public boolean cklong;
    @Value("${ocpc.tencent.openid.log:true}")
    public boolean openIdLogFlag;
    @Value("${ocpc.redis.redisBuf:false}")
    public boolean redisBuf;


    @Value("${ocpc.ks.hbasegy:false}")
    public boolean ksHbaseGy;

    public boolean needCheckRepeatActive(String product) {
        return checkRepeatActiveEnableAllProduct || checkRepeatActiveEnableProducts.contains(product);
    }

    @ApolloJsonValue("${ocpc.toutiao.live.account:[]}")
    public Set<String> toutiaoLiveAccount;

    /**
     * 关键行为回传激活账户（用于短剧测试）
     */
    @ApolloJsonValue("${ocpc.toutiao.toActive.account:[]}")
    public Set<String> toutiaoToActiveAccount;

    @Value("${ocpc.log.open:false}")
    public boolean logOpen;


    /** 需要写入 coo-dispense.toutiao_click 表的账户id范围 */
    @ApolloJsonValue("${ocpc.rta.projects:[\"jygs\"]}")
    public Set<String> rtaProjectNames;
    @ApolloJsonValue("${ocpc.rta.accounts:[\"****************\"]}")
    public Set<String> rtaProjectAccounts;

    @ApolloJsonValue("${ocpc.lock.chls:[\"vivo\",\"mi\",\"oppo\"]}")
    public Set<String> saveChls;

    @Value("${ocpc.ov.chous:24}")
    public Integer clickHours;

    @ApolloJsonValue("${ocpc.vivo.projects:[\"nnyy\"]}")
    public Set<String> vivoLcProjectNames;


    @ApolloJsonValue("${ocpc.ip.lockcaid:[\"************\"]}")
    public Set<String> ipLockCaids;
    @ApolloJsonValue("${ocpc.ip.productscaid:[\"qzcyksy\"]}")
    public Set<String> productscaid;

    @ApolloJsonValue("${ocpc.ip.productdfcaid:[\"ywrs6\"]}")
    public Set<String> productDfscaid;

    @ApolloJsonValue("${ocpc.noev15:[\"kxsk\"]}")
    public Set<String> productVes;
    @ApolloJsonValue("${ocpc.caid2.products:[\"jyxbt\"]}")
    public Set<String> caid2Products;

    @ApolloJsonValue("${ocpc.sdk.product:[{\"product\":\"hjkbt\",\"packageName\":\"com.hainanyt.hjkbt\"}]}")
    public Set<SdkConf> ocpcSdkProductPkg;

    @Value("${ocpc.sdk.store:false}")
    public boolean ocpcSdkStore;
    @Value("${user.active.sdk:false}")
    public boolean userActiveSdk;


    @ApolloJsonValue("${ocpc.filter.sudproducts:[\"sgc\",\"qmxsg\",\"ttdls\",\"kldd\",\"wxkl\",\"wydfd\",\"sgdw\",\"qzmcahc\",\"qzmcahc2\",\"lsjyg\"]}")
    public Set<String> guilvArpuSet;
    @ApolloJsonValue("${ocpc.filter.sudBproducts:[\"klldsh\",\"qzfydxg\",\"jyqcz\",\"qlgq\",\"qzfbdzz\"]}")
    public Set<String> guilvBArpuSet;

    @Value("${user.openarpu.up:false}")
    public boolean isOpenUpArpu;


    @ApolloJsonValue("${ocpc.filter.suddays:[\"20240928\",\"20241004\",\"20241005\",\"20241006\"]}")
    public Set<String> sunDays;

    @Value("${ocpc.cvr.active.hour:6}")
    public Integer cvrActiveHour;
    /**
     * 快手排除低质量人群回调开关
     */
    @Value("${ocpc.kuaishou.low.quality.switch:false}")
    public boolean kuaishouCallBackTestSwitch;
    /**
     * 快手排除低质量人群回调开关
     */
    @Value("${ocpc.kuaishou.low.quality.flta1:1.1}")
    public double lowQualityAFlt1;
    /**
     * 快手排除低质量人群回调开关
     */
    @Value("${ocpc.kuaishou.low.quality.flta2:1.3}")
    public double lowQualityAFlt2;
    /**
     * 快手排除低质量人群回调开关
     */
    @Value("${ocpc.kuaishou.low.quality.flta3:1.5}")
    public double lowQualityAFlt3;
    /**
     * 快手排除低质量人群回调开关
     */
    @Value("${ocpc.kuaishou.low.quality.fltb1:1}")
    public double lowQualityBFlt1;    /**
     * 快手排除低质量人群回调开关
     */
    @Value("${ocpc.kuaishou.low.quality.fltb2:1.1}")
    public double lowQualityBFlt2;    /**
     * 快手排除低质量人群回调开关
     */
    @Value("${ocpc.kuaishou.low.quality.fltb3:1.3}")
    public double lowQualityBFlt3;
    /**
     * 可以回传的倍数
     */
    @Value("${ocpc.call.back.mutiple:1.0}")
    public double callBackMutiple;
    /**
     *  bid回传开关
     */
    @Value("${ocpc.call.back.switch:false}")
    public boolean bidCallBackSwitcher;

    @ApolloJsonValue("${ocpc.call.back.product.list:[]}")
    public Set<String> callBackProductList;

    @ApolloJsonValue("${juliang.sdk.product.map:{}}")
    public Map<Integer, String> juliangProductMap;

    /**
     * 控制归因匹配新老逻辑的开关
     */
    @Value("${ocpc.gy.switcher:false}")
    public boolean gySwitcher;
    /**
     * 走新版归因匹配的产品列表
     */
    @ApolloJsonValue("${ocpc.new.gy.product.list:[]}")
    public Set<String> gyProductList;

    @ApolloJsonValue("${ios.juliang.sdk.product.map:{}}")
    public Map<Integer, String> iosJuliangProductMap;

    /**
     * 默认归因开关
     */
    @Value("${ocpc.default.gy.switcher:false}")
    public boolean gyDefaultSwitcher;
    /**
     * 默认归因产品列表
     */
    @ApolloJsonValue("${ocpc.default.gy.product.list:[]}")
    public Set<String> gyDefaultProductList;
    /**
     * 默认归因开关
     */
    @Value("${ocpc.cvr.timing.open:true}")
    public boolean cvrTimingOpen;

    /**
     * 延迟返回caid产品列表
     */
    @ApolloJsonValue("${delay.caid.product.list:[]}")
    public Set<String> delayCaidProductList;

    /**
     * 延迟返回caid产品列表
     */
    @ApolloJsonValue("${retry.sdk.gy.product.list:[]}")
    public Set<String> retrySdkGyProductList;

    /**
     * 星图关键行为回传激活账户
     */
    @ApolloJsonValue("${toutiao.xingtu.call.back.active.list:[]}")
    public Set<String> toutiaoXingtuCallBackProductList;
}
