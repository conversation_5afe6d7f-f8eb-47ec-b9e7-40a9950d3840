package com.coohua.core.caf.dispense.dsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.CvrProduct;
import com.coohua.core.caf.dispense.dsp.mapper.CvrProductMapper;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CvrProductService extends ServiceImpl<CvrProductMapper, CvrProduct> implements IService<CvrProduct> {

    private static Map<String, CvrProduct> productMap = new HashMap<>();

    @PostConstruct
    @Scheduled(fixedDelay = 1000 * 60)
    public void reloadConfig() {
        List<CvrProduct> list = getAll();
        productMap = list.stream().collect(Collectors.toMap(o -> o.getDsp()+"_"+o.getProduct(), k->k, (oldVal, newVal)->oldVal));
    }

    public List<CvrProduct> getAll() {
        return lambdaQuery().eq(CvrProduct::getDelFlag, DELFLAG.weishanchu.value).list();
    }

    public CvrProduct getByProduct(String dsp, String product) {
        return productMap.get(dsp +"_"+product);
    }
}
