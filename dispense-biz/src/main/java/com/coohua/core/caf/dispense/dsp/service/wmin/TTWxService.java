package com.coohua.core.caf.dispense.dsp.service.wmin;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.dto.req.Props;
import com.coohua.core.caf.dispense.dto.req.TTWxReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.WxActiveReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Random;

@Component
@Slf4j
public class TTWxService {
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    RedisClickService redisClickService;

    //头条 微信小程序激活
    public void sendWxActive(WxActiveReq wxActiveReq){
        try {
            //url：https://clue.oceanengine.com/outer/wechat/applet/token/1743915176074243
            //token：5EC571E03469980C8ADDC3ACC65CC711
            String reqUrl = "https://clue.oceanengine.com/outer/wechat/applet/token/1756702469584903";
            String token = "162AC0033B61BEAE913EB5554EDFD5AA";
            //http://xxx.com/xxx?timestamp=___&nonce=___&signature=___
            String noticeStr = getRandInt()+"";
            String timeStmp = System.currentTimeMillis()/1000+"";
            boolean isActive = redisClickService.setOpenidActive(wxActiveReq.getProduct(),wxActiveReq.getOpenId());
            if(isActive){
                String sign = getSign(token,noticeStr,timeStmp);
                reqUrl = reqUrl+"?nonce="+noticeStr+"&timestamp="+timeStmp+"&signature="+sign;

                TTWxReq ttwxReq = new TTWxReq();
                ttwxReq.setClue_token(wxActiveReq.getClue_token());
                ttwxReq.setEvent_type("0");
                ttwxReq.setOpen_id(wxActiveReq.getOpenId());
                String reqJsn = JSON.toJSONString(ttwxReq);
                log.info("wx小游戏开始请求激活 "+reqJsn+" "+reqUrl);
                CloseableHttpClient client = toutiaoCallService.getHttpClient();
                String rsp = toutiaoCallService.resendUrlPost(reqUrl,reqJsn,client);
                //clueToken 存储在callBack中
                ToutiaoClick toutiaoClick = getTclick(wxActiveReq,wxActiveReq.getClue_token());
                UserEventReq userEventReq = getUevent(wxActiveReq);
                UserEvent userEvent = ocpcEventService.saveUserEvent(toutiaoClick, userEventReq, reqUrl, rsp);

                log.info("wx小游戏开始请求激活 "+reqJsn+" 响应为 "+rsp);
            }else{
                log.info("wx小游戏已经激活 直接忽略 "+JSON.toJSONString(wxActiveReq));
            }
        } catch (Exception e) {
            log.error("头条回传异常", e);
        }
    }


    public boolean sendWxEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent){
        try {
            //url：https://clue.oceanengine.com/outer/wechat/applet/token/1743915176074243
            //token：5EC571E03469980C8ADDC3ACC65CC711
            String reqUrl = "https://clue.oceanengine.com/outer/wechat/applet/token/1756702469584903";
            String token = "162AC0033B61BEAE913EB5554EDFD5AA";
            //http://xxx.com/xxx?timestamp=___&nonce=___&signature=___
            String noticeStr = getRandInt()+"";
            String timeStmp = System.currentTimeMillis()/1000+"";

            String sign = getSign(token,noticeStr,timeStmp);
            reqUrl = reqUrl+"?nonce="+noticeStr+"&timestamp="+timeStmp+"&signature="+sign;

            TTWxReq ttwxReq = new TTWxReq();
            ttwxReq.setClue_token(toutiaoClick.getCallbackUrl());
            ttwxReq.setEvent_type(userEvent.getEventType()+"");
            ttwxReq.setOpen_id(userEvent.getOpenId());
            ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEvent.getEventType());
            if(ToutiaoEventTypeEnum.PURCHASE.equals(toutiaoEventTypeEnum)){
                Props props = new Props();
                props.setPay_amount(userEventReq.getPayAmount());
                ttwxReq.setProps(props);
            }
            String reqJsn = JSON.toJSONString(ttwxReq);
            log.info("wx小游戏开始请求关键行为 "+reqJsn+" "+reqUrl);
            CloseableHttpClient client = toutiaoCallService.getHttpClient();
            String rsp = toutiaoCallService.resendUrlPost(reqUrl,reqJsn,client);
            Integer rstatus = JSON.parseObject(rsp).getInteger("status");
            if(!rstatus.equals(200)){
                log.error("wx小游戏开始请求关键行为失败 "+reqJsn+" 响应为 "+rsp);
                return false;
            }else{
                log.info("wx小游戏开始请求关键行为 "+reqJsn+" 响应为 "+rsp);
            }
            return true;
        } catch (Exception e) {
            log.error("头条回传异常", e);
        }
        return false;
    }

    public Integer getRandInt(){
        Random random = new Random();
        return random.nextInt(50000);
    }


    private ToutiaoClick getTclick(WxActiveReq wxActiveReq,String clueToken){
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(DspType.TTWX.value);
        toutiaoClick.setOpenId(wxActiveReq.getOpenId());
        toutiaoClick.setOs(wxActiveReq.getOs());
        toutiaoClick.setAccountId(wxActiveReq.getAdvertiser_id());
        toutiaoClick.setProduct(wxActiveReq.getProduct());
        toutiaoClick.setPid(wxActiveReq.getAd_id());
        toutiaoClick.setCid(wxActiveReq.getCreative_id());
        toutiaoClick.setCallbackUrl(clueToken);
        return toutiaoClick;
    }

    private UserEventReq getUevent(WxActiveReq wxActiveReq){
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setProduct(wxActiveReq.getProduct());
        userEventReq.setOs(wxActiveReq.getOs());
        userEventReq.setEventType(0);
        userEventReq.setUserId(wxActiveReq.getUserId());
        userEventReq.setOpenId(wxActiveReq.getOpenId());
        userEventReq.setAppId(wxActiveReq.getAppId());
        return userEventReq;
    }
    public static String getSign(String token,String randNotice,String timestmp){
        String[] arr = new String[]{token,randNotice,timestmp};
        Arrays.sort(arr);
        StringBuffer sn = new StringBuffer();
        for(String ddf : arr){
            sn.append(ddf);
        }

        String sign = DigestUtils.sha1Hex(sn.toString());
        return sign;
    }

    public static void main(String[] args){
//        假如token="D528C6F23C7D7"
//        nonce="234", timestamp="1646989046"，
//对排序后字符串进行sha1转换，结果为"a3c1ad351db9b88c1c5cb7a4cab977f8cd146524"
        System.out.println(getSign("D528C6F23C7D7","234","1646989046"));


        TTWxReq ttwxReq = new TTWxReq();
        ttwxReq.setClue_token("toutiaoClick.getCallbackUrl()");
        ttwxReq.setEvent_type("2");
        ttwxReq.setOpen_id("opid");
        ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(2);
        if(ToutiaoEventTypeEnum.PURCHASE.equals(toutiaoEventTypeEnum)){
            Props props = new Props();
            props.setPay_amount(50);
            ttwxReq.setProps(props);
        }

        System.out.println(JSON.toJSONString(ttwxReq));
    }
}
