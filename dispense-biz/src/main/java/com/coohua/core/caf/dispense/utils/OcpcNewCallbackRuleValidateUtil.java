package com.coohua.core.caf.dispense.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.ck.entity.ArpuPvReportRecord;
import com.coohua.core.caf.dispense.dsp.entity.ActionTypes;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.EventValueRuleEnum;
import com.coohua.core.caf.dispense.kafka.ArpuPvReportSender;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.coohua.core.caf.dispense.constant.BaseConstants.*;
import static com.coohua.core.caf.dispense.enums.AccountActionTypeEnum.*;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class OcpcNewCallbackRuleValidateUtil {

    public static boolean matchNewRule(UserEventReq userEventReq, String accountTypes, String typeValues,
                                       BooleanSupplier firstTimeJudge, String source, String productName,
                                       String advertiserId, Integer maxAccumulatePeriod) {

        return matchNewRuleWithResult(userEventReq, accountTypes, typeValues,
                firstTimeJudge, source, productName,
                advertiserId, maxAccumulatePeriod
        ).getLeft();
    }

    public static Pair<Boolean, ActionTypes> matchNewRuleWithResult(UserEventReq userEventReq, String accountTypes, String typeValues,
                                                                    BooleanSupplier firstTimeJudge, String source, String productName,
                                                                    String advertiserId, Integer maxAccumulatePeriod) {

        if (StringUtils.isBlank(accountTypes) || StringUtils.isBlank(userEventReq.getActionValues())) {
            return PairUtil.ofFalse();
        }

        Map<String, String> reportValues = analyzeNewValueToMap(userEventReq.getActionValues());

        if (reportValues.containsKey(GAME_ACTION.getTypeKey())) {
            return matchNewRuleWithResultV2(userEventReq, reportValues, accountTypes, typeValues,
                    firstTimeJudge, source, productName,
                    advertiserId, maxAccumulatePeriod
            );
        }

        Map<String, String> configurations = Maps.newHashMap();
        String[] eventTypes = StringUtils.splitByWholeSeparator(accountTypes, ",");
        String[] eventValues = StringUtils.splitByWholeSeparator(typeValues, ",");

        // 多arpu新规则校验
        if (isMultiArpu(accountTypes, typeValues)) {
            return matchMultiArpuRule(reportValues, eventValues, userEventReq, advertiserId, firstTimeJudge);
        }

        for (int index = 0; index < eventTypes.length; index++) {
            configurations.put(eventTypes[index], eventValues[index]);
        }

        boolean reportEcpm = reportValues.containsKey(ECPM.getTypeKey());

        // ecpm规则的视频次数用等于判断；其余规则依然是大于等于
        BiPredicate<Integer, Integer> watchVideoNumbersMatchFunction = reportEcpm ? Integer::equals
                : (report, configuration) -> report.compareTo(configuration) >= 0;

        String ruleName = reportEcpm ? ECPM.desc : reportValues.containsKey(ARPU_ONE_DAY.getTypeKey()) ? ARPU_ONE_DAY.desc : WATCH_VIDEO.desc;
//        log.info("开始匹配{}新版规则 类型 {} 系统 {} device {} 累积时间 {} 配置时间 {} 上报值 {} 配置值 {} 产品 {} 广告主 {}", source,
//                ruleName, userEventReq.getOs(), userEventReq.getOcpcDeviceId(), userEventReq.getAccumulateDuration(), maxAccumulatePeriod,
//                userEventReq.getActionValues(), JSON.toJSONString(configurations), productName, advertiserId);

        if (reportValues.size() != configurations.size()) {
            return PairUtil.ofFalse();
        }

        // 两个时间都非空，且上报的时间花费多于配置时，校验不通过
        if (userEventReq.getAccumulateDuration() != null && maxAccumulatePeriod != null
                && userEventReq.getAccumulateDuration() > maxAccumulatePeriod) {
            return PairUtil.ofFalse();
        }

        if (typeValues.contains(MULTI_ECPM_SEPARATOR)) {
            Pair<Boolean, ActionTypes> matchMultiEcpmRule = matchMultiEcpmRule(reportValues, configurations, advertiserId);
            if (!matchMultiEcpmRule.getLeft()) {
                return PairUtil.ofFalse();
            }
            if (firstTimeJudge.getAsBoolean()) {
//                log.info("{}首次进行新版规则回传 类型 {} device {} 累积时间 {} 配置时间 {} 上报值 {} 配置值 {} 产品 {} 广告主 {}", source,
//                        ruleName, userEventReq.getOcpcDeviceId(), userEventReq.getAccumulateDuration(), maxAccumulatePeriod,
//                        userEventReq.getActionValues(), JSON.toJSONString(configurations), productName, advertiserId);
                return matchMultiEcpmRule;
            }
            return PairUtil.ofFalse();
        }

        boolean watchVideoUpToSpecificTimes = matchSpecificRule(reportValues, configurations, WATCH_VIDEO.getTypeKey(),
                Integer::valueOf, watchVideoNumbersMatchFunction);

        boolean arpuUpToSpecificValue = matchSpecificRule(reportValues, configurations, ARPU_ONE_DAY.getTypeKey(),
                BigDecimal::new, (report, configuration) -> report.compareTo(configuration) >= 0);

        boolean ecpmUpToTargetValue = matchSpecificRule(reportValues, configurations, ECPM.getTypeKey(),
                Integer::valueOf, (report, configuration) -> report.compareTo(configuration) >= 0);

        // 统计arpu达到要求但是pv没有达到的数量
        if (arpuUpToSpecificValue && !watchVideoUpToSpecificTimes) {
            // 通过kafka向ck中写入数据
            ArpuPvReportRecord arpuPvReportRecord = buildSendData(userEventReq, source, reportValues, configurations);
            ArpuPvReportSender.sendArpuPvData(JSONObject.toJSONString(arpuPvReportRecord));
        }

        if (watchVideoUpToSpecificTimes && arpuUpToSpecificValue && ecpmUpToTargetValue) {
            if (firstTimeJudge.getAsBoolean()) {
                log.info("{}首次进行新版规则回传 类型 {} device {} 累积时间 {} 配置时间 {} 上报值 {} 配置值 {} 产品 {} 广告主 {}", source,
                        ruleName, userEventReq.getOcpcDeviceId(), userEventReq.getAccumulateDuration(), maxAccumulatePeriod,
                        userEventReq.getActionValues(), JSON.toJSONString(configurations), productName, advertiserId);
                // 通过kafka向ck中写入数据
                ArpuPvReportRecord arpuPvReportRecord = buildSendData(userEventReq, source, reportValues, configurations);
                ArpuPvReportSender.sendArpuPvData(JSONObject.toJSONString(arpuPvReportRecord));
                return PairUtil.ofTrue(ActionTypes.build(configurations));
            }
            return PairUtil.ofFalse();
        }

        return PairUtil.ofFalse();

    }

    private static ArpuPvReportRecord buildSendData(UserEventReq userEventReq, String source, Map<String, String> reportValues, Map<String, String> configurations) {
        ArpuPvReportRecord arpuPvReportRecord = new ArpuPvReportRecord();
        arpuPvReportRecord.setLogday(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        arpuPvReportRecord.setUserId(userEventReq.getUserId());
        arpuPvReportRecord.setProduct(userEventReq.getProduct());
        arpuPvReportRecord.setOs(userEventReq.getOs());
        arpuPvReportRecord.setDsp(source);
        arpuPvReportRecord.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
        arpuPvReportRecord.setSourceDeviceId(userEventReq.getSourceDeviceId());
        arpuPvReportRecord.setCaid(userEventReq.getCaid());
        arpuPvReportRecord.setOaid(userEventReq.getOaid());
        arpuPvReportRecord.setReportArpu(reportValues.get(ARPU_ONE_DAY.getTypeKey()));
        arpuPvReportRecord.setReportPv(reportValues.get(WATCH_VIDEO.getTypeKey()));
        arpuPvReportRecord.setConfigArpu(configurations.get(ARPU_ONE_DAY.getTypeKey()));
        arpuPvReportRecord.setConfigPv(configurations.get(WATCH_VIDEO.getTypeKey()));
        arpuPvReportRecord.setCreateTime(new Date());
        return arpuPvReportRecord;
    }

    public static Pair<Boolean, ActionTypes> matchNewRuleWithResultV2(UserEventReq userEventReq, Map<String, String> reportValues, String accountTypes, String typeValues,
                                                                      BooleanSupplier firstTimeJudge, String source, String productName,
                                                                      String advertiserId, Integer maxAccumulatePeriod) {

        if (StringUtils.isAnyBlank(accountTypes, userEventReq.getActionValues())) {
            return PairUtil.ofFalse();
        }

        // 两个时间都非空，且上报的时间花费多于配置时，校验不通过
        if (userEventReq.getAccumulateDuration() != null && maxAccumulatePeriod != null
                && userEventReq.getAccumulateDuration() > maxAccumulatePeriod) {
            return PairUtil.ofFalse();
        }

        Map<String, List<String>> configurations = analyzeAccountConfigs(accountTypes, typeValues);

        String reportType = reportValues.keySet().stream().sorted().collect(Collectors.joining(","));
        String configType = configurations.keySet().stream().sorted().collect(Collectors.joining(","));

        if (!Objects.equals(reportType, configType)) {
            return PairUtil.ofFalse();
        }

        // 配置通过/分隔后数量不同，则配置异常，校验不通过
        List<Integer> lengths = configurations.values().stream().map(k -> k.size()).distinct().collect(Collectors.toList());
        if (lengths.size() > 1) {
            return PairUtil.ofFalse();
        }
        int length = lengths.get(0);

        for (int index=0; index < length; index++) {
            // configuration = '/' 分割后的第index套配置
            Map<String, String> configuration = new HashMap<>();
            int finalIndex = index;
            configurations.entrySet().forEach(k-> configuration.put(k.getKey(), k.getValue().get(finalIndex)));

            // 每种上报的事件类型都有对应的校验规则
            EventValueRuleEnum eventValueRuleEnum = EventValueRuleEnum.parse(reportType);
            if (eventValueRuleEnum == null) {
                continue;
            }
            boolean allConditionMatch = reportValues.entrySet().stream()
                    .allMatch(reportEntry -> eventValueRuleEnum.compareRules.get(reportEntry.getKey())
                            .checkRule.test(reportEntry.getValue(), configuration.get(reportEntry.getKey())));
            if (!allConditionMatch) {
                continue;
            }
            // 全部条件满足时，判断是否是首次达成条件
            if (firstTimeJudge.getAsBoolean()) {
                log.info("{}首次进行新版规则回传 类型 {} device {} 累积时间 {} 配置时间 {} 上报值 {} 配置值 {} 产品 {} 广告主 {}", source,
                        reportType, userEventReq.getOcpcDeviceId(), userEventReq.getAccumulateDuration(), maxAccumulatePeriod,
                        userEventReq.getActionValues(), JSON.toJSONString(configurations), productName, advertiserId);
                return PairUtil.ofTrue(ActionTypes.build(configuration));
            } else {
                return PairUtil.ofFalse();
            }
        }
        return PairUtil.ofFalse();
    }

    private static Map<String, List<String>> analyzeAccountConfigs(String accountTypes, String typeValues) {
        Map<String, List<String>> configurations = Maps.newHashMap();
        String[] eventTypes = StringUtils.splitByWholeSeparator(accountTypes, ",");
        String[] eventValues = StringUtils.splitByWholeSeparator(typeValues, ",");

        for (int index = 0; index < eventTypes.length; index++) {
            configurations.put(eventTypes[index], Arrays.asList(StringUtils.splitByWholeSeparator(eventValues[index], MULTI_ECPM_SEPARATOR)));
        }
        return configurations;
    }

    public static Map<String, String> analyzeNewValueToMap(String actionValues) {

        return Arrays.stream(StringUtils.splitByWholeSeparator(actionValues, ","))
                .map(piece -> StringUtils.splitByWholeSeparator(piece, "-"))
                .collect(toMap(array -> array[0], array -> array[1], (oldValue, newValue) -> newValue));
    }

    /**
     * 是否满足特定种类的规则
     *
     * @param reportValues   上报的值集合
     * @param configurations 配置值集合
     * @param key            配置key
     * @param valueMapper    数值转换行为
     * @param matchFunction  匹配函数
     * @param <T>            数值类型
     * @return true-满足该规则
     */
    private static <T> boolean matchSpecificRule(Map<String, String> reportValues, Map<String, String> configurations,
                                                 String key, Function<String, T> valueMapper, BiPredicate<T, T> matchFunction) {

        String reportValue = reportValues.get(key);
        String configurationValue = configurations.get(key);

        if (reportValue == null && configurationValue == null) {
            return true;
        }

        if (reportValue != null && configurationValue != null) {

            T transformedReportValue = valueMapper.apply(reportValue);
            T transformedConfigurationValue = valueMapper.apply(configurationValue);

            return matchFunction.test(transformedReportValue, transformedConfigurationValue);
        }

        return false;
    }

    /**
     * 匹配多ecpm规则
     *
     * @param reportValues   报告值
     * @param configurations 配置值
     * @param advertiserId   广告主id
     * @return true-匹配
     */
    private static Pair<Boolean, ActionTypes> matchMultiEcpmRule(Map<String, String> reportValues, Map<String, String> configurations,
                                              String advertiserId) {

        String reportVideoNumber = reportValues.get(WATCH_VIDEO.getTypeKey());
        String reportEcpm = reportValues.get(ECPM.getTypeKey());

        if (StringUtils.isBlank(reportVideoNumber) || StringUtils.isBlank(reportEcpm)) {
            return PairUtil.ofFalse();
        }

        String[] videoConfigPieces = StringUtils.splitByWholeSeparator(configurations.get(WATCH_VIDEO.getTypeKey()), MULTI_ECPM_SEPARATOR);
        String[] ecpmConfigPieces = StringUtils.splitByWholeSeparator(configurations.get(ECPM.getTypeKey()), MULTI_ECPM_SEPARATOR);

        if (videoConfigPieces == null || ecpmConfigPieces == null || videoConfigPieces.length != ecpmConfigPieces.length) {
            log.warn("广告主多ecpm配置异常 {}", advertiserId);
            return PairUtil.ofFalse();
        }

        for (int index = 0; index < videoConfigPieces.length; index++) {

            if (!videoConfigPieces[index].equals(reportVideoNumber)) {
                continue;
            }

            if (Integer.parseInt(reportEcpm) >= Integer.parseInt(ecpmConfigPieces[index])) {
                return PairUtil.ofTrue(ActionTypes.build(ImmutableMap.of(WATCH_VIDEO.getTypeKey(), reportVideoNumber, ECPM.getTypeKey(), ecpmConfigPieces[index])));
            }
        }

        return PairUtil.ofFalse();
    }

    /**
     * 判断是否是多arpu规则
     */
    public static boolean isMultiArpu(String eventTypesText, String eventValuesText) {
        return NUM_ARPU_RULE.equals(eventTypesText) && StringUtils.splitByWholeSeparator(eventValuesText, ",").length >= 3;
    }

    /**
     * 判断是否是1,3账户且没有10这一档位
     */
    public static boolean checkEcpmHasNotLevel10(String eventTypes, String eventValues) {
        return NUM_ECPM_RULE.equals(eventTypes) && !Arrays.asList(eventValues.split(",")[0].split("/")).contains("10");
    }

    /**
     * 匹配多arpu规则
     *
     * @param reportValues   报告值
     * @param eventValues 配置值
     * @param userEventReq 上报的累计时间
     * @param advertiserId   广告主id
     * @param firstTimeJudge
     * @return true-匹配
     */
    private static Pair<Boolean, ActionTypes> matchMultiArpuRule(Map<String, String> reportValues, String[] eventValues,
                                                                 UserEventReq userEventReq, String advertiserId, BooleanSupplier firstTimeJudge) {
        Integer reportAccumulateDuration = userEventReq.getAccumulateDuration();
        log.info("多时间端匹配 "+advertiserId+" reportValues "+JSON.toJSONString(reportValues) + " userEventReq "+ JSON.toJSONString(userEventReq));
        String reportVideoNumber = reportValues.get(WATCH_VIDEO.getTypeKey());
        String reportArpu = reportValues.get(ARPU_ONE_DAY.getTypeKey());

        if (StringUtils.isBlank(reportVideoNumber) || StringUtils.isBlank(reportArpu)) {
            return PairUtil.ofFalse();
        }

        log.info("多arpu规则 系统 {} userId {} device {} 上报累积时间 {} 上报值 {} 配置值 {} 产品 {} 广告主 {}",
                userEventReq.getOs(), userEventReq.getUserId(), userEventReq.getOcpcDeviceId(), userEventReq.getAccumulateDuration(),
                userEventReq.getActionValues(), JSON.toJSONString(eventValues), userEventReq.getProduct(), advertiserId);

        // 多arpu 格式 : 1,2 -> 8/10/10,1.5/2.5/5,30/60/1440  (次数1/次数2/次数3,arpu1/arpu2/arpu3,累计时间1/累计时间2/累计时间3)
        // 看视频次数
        String[] videoNumConfigs = StringUtils.splitByWholeSeparator(eventValues[0], MULTI_ECPM_SEPARATOR);
        // arpu配置
        String[] arpuConfigPieces = StringUtils.splitByWholeSeparator(eventValues[1], MULTI_ECPM_SEPARATOR);
        // 累计时间配置
        String[] durationConfigPieces = StringUtils.splitByWholeSeparator(eventValues[2], MULTI_ECPM_SEPARATOR);

        if (videoNumConfigs == null || arpuConfigPieces == null || durationConfigPieces == null || arpuConfigPieces.length != durationConfigPieces.length) {
            log.warn("广告主多ecpm配置异常 {}", advertiserId);
            return PairUtil.ofFalse();
        }


        for (int index = 0; index < arpuConfigPieces.length; index++) {

            if (Integer.parseInt(reportVideoNumber) >= Integer.parseInt(videoNumConfigs[index])
                    && new BigDecimal(reportArpu).compareTo(new BigDecimal(arpuConfigPieces[index])) >= 0
                    && reportAccumulateDuration <= Integer.parseInt(durationConfigPieces[index])
            ) {
                if (firstTimeJudge.getAsBoolean()) {
//                System.out.println("多arpu 匹配 " + String.format("上报 %s %s %s 配置 %s %s %s", reportVideoNumber, reportArpu
//                , reportAccumulateDuration, videoNumConfigs[index], arpuConfigPieces[index], durationConfigPieces[index]));
                    log.info("多arpu规则 匹配成功 userId {} advertiserId {} {} {} 上报 {} {} {} 配置 {} {} {}",
                            userEventReq.getUserId(), advertiserId, userEventReq.getProduct(), userEventReq.getOcpcDeviceId(),
                            reportVideoNumber, reportArpu, reportAccumulateDuration, videoNumConfigs[index], arpuConfigPieces[index], durationConfigPieces[index]);
                    return PairUtil.ofTrue(ActionTypes.build(ImmutableMap.of(WATCH_VIDEO.getTypeKey(), reportVideoNumber,
                            ARPU_ONE_DAY.getTypeKey(), arpuConfigPieces[index])));
                }
                return PairUtil.ofFalse();
            }
        }
//        System.out.println("多arpu 不匹配");
        log.info("多arpu规则 无匹配条件 userId {} advertiserId {} {} {}",
                userEventReq.getUserId(), advertiserId, userEventReq.getProduct(), userEventReq.getOcpcDeviceId());
        return PairUtil.ofFalse();
    }

    public static void main(String[] args) {

        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setAccumulateDuration(40);
        userEventReq.setActionValues("1-10,2-2.5,4-30");

        BooleanSupplier supplier = () -> true;

        boolean b = matchNewRule(userEventReq, "4,1,2", "30/30,10/10,2.51/2.5", supplier, "source", "productName"
                , "123", null);

        System.out.println("最后结果 " + b);


    }
}
