package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserActive对象", description="")
public class UserActive implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value = "product")
    private String product;

    private String os;

    private String idfa;

    private String oaid;

    private String imei;

    private String mac;

    private String androidId;

    private String pkgid;

    private String channel;

    private String source;

    private String accountId;

    private Date createTime;

    private Date updateTime;

    private Integer appId;

    private String acDesc;

    private Date clickTime;

    private String gyType;

    private String ua;

    private String model;

    private String ip;

    private String oaid2;

    private String idfa2;

    private String cid;

    private String pid;

    private String gid;

    private String caid;

    private String ua2;

    private String clickId;
    private String mid;
}
