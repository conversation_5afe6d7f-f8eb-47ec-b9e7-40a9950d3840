package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/11/25 20:40
 * @Description: sigmob监控链接请求参数
 */
@Data
public class SigmobClickReq implements IClickReq{
    private String dsp;         // 平台
    private String product;      //产品
    private String account_id;    //广告主id（类似主体id的概念）


    private String clickId;     // 点击的唯一标识id，由Sigmob生成；格式为50位以内字符串
    private String idfaMd5;     // iOS设备的idfa md5；全部大写（等同于原值）后取32位小写md5摘要
    private String idfa1;       // IDFA原值MD5之后再转大写，取32位大写MD5摘要
    private String caid;        // 广协版本CAID的encode结果；上报结果可能含多个版本，归因时，任一版本命中即视为有效
    private String caid1;       // version= ********   的 广协caid
    private String caid2;       // version=********  的广协caid
    private String caidCon;     // CAID原值和版本号使用”_”拼接(若无版本号使用 0，多个使用”,”拼接)
    private String caidCon1;    // CAID原值MD5处理，需将MD5值和版本号使用”_”拼接（若无版本号使用0，多个使用”,”拼接
    private String imeiMd5;     // 安卓设备的imei（对双卡设备优先使用imei1，imei1为空，使用imei2）全部转小写（CDMA手机的imei含大写字母）后取32位小写md5摘要
    private String imei1;       // IMEI原值MD5之后再转大写，取32位MD5大写摘要
    private String orImeiMd5;   // 安卓设备的imei （对双卡设备优先使用imei1，imei1为空，使用imei2）原值，取32位小写md5摘要
    private String gaidMd5;     // android 设备的 google advertiser id的MD5值，
    private String oaidMd5;     // 安卓设备oaid全部转小写后取32位小写md5摘要
    private String orOaidMd5;   // 安卓设备oaid原值取32位小写md5摘要
    private String ipv4;        // 客户端ipv4 地址
    private String ipv6;        // 客户端ipv6 地址
    private String ua;          // user agent的urlencode替换结果
    private String timestamp;   // unix时间戳（秒）,"1609296499如果需要毫秒时间戳，请使用 _TIMESTAMP_000 "
    private String callback;    // 广告主转化目标达成后的回传通知地址，是一个encode后的url, "因为该url包含信息较多，整体长度超长，需要广告主侧使用可变长字符串varchar存储；归因阶段把callback直接访问即可（除了decode操作外，不要对callback url做任何改动，也不要拼接任何参数） "
    private String os;          // 用户设备操作系统,ios=1，android=2，若不同可自行定义
    private String model;       // 用户设备型号,iPhone10,6

    @Override
    public String getIp() {
        return ipv4;
    }

    @Override
    public void setIp(String ip) {
        this.ipv4=ip;
    }
}
