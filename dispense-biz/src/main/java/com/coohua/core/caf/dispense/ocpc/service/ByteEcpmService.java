package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.Product;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.mapper.ByteEcpmMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-09-25
*/
@Service
@Slf4j
public class ByteEcpmService extends ServiceImpl<ByteEcpmMapper, ByteEcpm> {

    public void saveAllEcpms(List<ByteEcpm> byteAdEcpms,OcpcEvent ocpcActiveEvent,boolean isLog){
        if(byteAdEcpms.size()>0){
            long ctime = System.currentTimeMillis();
            List<ByteEcpm> toSaveList = getNeedSaveList(byteAdEcpms);
            if (toSaveList != null && toSaveList.size() > 0) {
                if(ocpcActiveEvent!=null){
                    toSaveList.forEach(savEpm ->{
                        savEpm.setAccountNo(ocpcActiveEvent.getAccountId());
                        savEpm.setPlanId(ocpcActiveEvent.getPlanId());
                    });
                }
                saveOrUpdateBatch(toSaveList);
                String msg = "插入byteecpms 耗时 " + (System.currentTimeMillis() - ctime) + " 总共 " + byteAdEcpms.size() + " 需保存 " + toSaveList.size();
                if(isLog){
                    msg = "ecpm补充插入 "+msg;
                }
                log.info(msg);
            }
        }
    }

    public void delEcpms(String product, String openId, Date ecpmDate){
        List<ByteEcpm> byteEcpms = lambdaQuery().select(ByteEcpm::getId).eq(ByteEcpm::getProduct, product).eq(ByteEcpm::getOpenId, openId).eq(ByteEcpm::getEventDate,ecpmDate).list();
        if(byteEcpms.size()>0){
            List<String> byteEcpmsIds = byteEcpms.stream().map(ByteEcpm::getId).collect(Collectors.toList());
            removeByIds(byteEcpmsIds);
        }
    }

    public List<ByteEcpm> getNeedSaveList(List<ByteEcpm> byteAdEcpms){
        if(byteAdEcpms.size()>0){
            List<String> idList = byteAdEcpms.stream().map(a->a.getId()).collect(Collectors.toList());
            List<ByteEcpm> dbByteAdEcpms = lambdaQuery().in(ByteEcpm::getId, idList).list();

            Map<String,ByteEcpm> userMap2 =  dbByteAdEcpms.stream().collect(Collectors.toMap(ByteEcpm::getId, Function.identity(),(a1, a2)->a1));
            List<ByteEcpm> tosaveList = byteAdEcpms.stream().filter(byteEcpm -> !userMap2.keySet().contains(byteEcpm.getId())).collect(Collectors.toList());
            return tosaveList;
        }
        return null;
    }

    public List<ByteEcpm> getUserEcpmList(String product,String openId){
        List<ByteEcpm> ecpmList = lambdaQuery().eq(ByteEcpm::getProduct, product).eq(ByteEcpm::getOpenId, openId).list();
        return ecpmList;
    }

}
