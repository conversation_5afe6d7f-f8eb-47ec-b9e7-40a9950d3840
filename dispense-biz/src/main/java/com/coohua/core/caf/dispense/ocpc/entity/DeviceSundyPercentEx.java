package com.coohua.core.caf.dispense.ocpc.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;



@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class DeviceSundyPercentEx extends DeviceSundyPercent {

    private Integer count;

    public DeviceSundyPercentEx(DeviceSundyPercent p){
        this.setPercent(p.getPercent());
        this.setOs(p.getOs());
        this.setProduct(p.getProduct());

        int i = (int) (p.getPercent() / 0.5);
        if(i > 10){
            this.setCount(10);
        }else{
            this.setCount(i);
        }
    }
}
