package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 三方关键行为衍生事件配置表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ThirdExtEvent对象", description="三方关键行为衍生事件配置表")
public class ThirdExtEvent implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "dsp")
    private String dsp;

    @ApiModelProperty(value = "策略")
    private Long keyAction;

    @ApiModelProperty(value = "三方行为类型id")
    private Long toutiaoEventType;

    @ApiModelProperty(value = "行为类型")
    private String eventTypes;

    @ApiModelProperty(value = "行为数值")
    private String eventValues;

    @ApiModelProperty(value = "行为深度")
    private Long depth;

    @ApiModelProperty(value = "三方行为类型1")
    private Long actionType1;

    @ApiModelProperty(value = "三方行为数值1")
    private Long value1;

    private Long actionType2;

    private Long value2;

    private Long actionType3;

    private Long value3;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField(exist = false)
    private Long isGaConvert;


}
