package com.coohua.core.caf.dispense.ocpc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.ocpc.entity.MoveObj;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcClick;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk DateTime: 2020/10/22 15:11
 */
public interface OcpcClickMapperExt extends BaseMapper<OcpcClick> {

    /**
     * 创建新产品ocpcClick表
     * 
     * @param tableEnd
     * @return
     */
    @Update("create table if not exists  `ocpc_click_${tableEnd}` like ocpc_click")
    int createOcpcClick(@Param("tableEnd") String tableEnd);

    /**
     * 判断表是否存在
     * 
     * @param tableEnd
     * @return
     */
    @Select("select count(*) > 0 from `information_schema`.tables where table_name ='ocpc_click_${tableEnd}'")
    Boolean existOcpcTable(@Param("tableEnd") String tableEnd);

    /**
     * 根据ocpc_device_id
     * @param appId
     * @param eventType
     * @param os
     * @param ocpcDeviceId
     * @return
     */
    @Select({"<script>",
            "select  *  from   `ocpc_click_${tableEnd}`  where product = #{product} and os = #{os} and ocpc_device_id = #{ocpcDeviceId} ",
            // 次留 则查昨天的clickId
            "<if test='null != eventType and  6 == eventType'>", " and create_time &lt; date_format(now(),'%Y-%m-%d') ",
            "</if>", "order by id desc  limit #{limitPage}", "</script>"})
    List<ToutiaoClick> selectOcpcClickByDevice(@Param("tableEnd") String tableEnd, @Param("eventType") Integer eventType,
                                               @Param("product") String product, @Param("os") String os,
                                               @Param("ocpcDeviceId") String ocpcDeviceId,
                                               @Param("limitPage") Integer limitPage);


    @Select({"<script> select  *  from   `ocpc_click_${tableEnd}`  where id = #{clickId} </script>"})
    ToutiaoClick selectOcpcClickById(@Param("tableEnd") String tableEnd,  @Param("clickId") Long clickId);

    /**
     * oaId 查询click
     *
     * @param tableEnd
     * @param eventType
     * @param os
     * @param oaId
     * @return
     */
    @Select({"<script>",
            "select  *  from   `ocpc_click_${tableEnd}`  where product = #{product} and os = #{os} and oaid = #{oaId} ",
            // 次留 则查昨天的clickId
            "<if test='null != eventType and  6 == eventType'>", " and create_time &lt; date_format(now(),'%Y-%m-%d') ",
            "</if>", "order by id desc  limit #{limitPage}", "</script>"})
    List<ToutiaoClick> selectOcpcClickByOaId(@Param("tableEnd") String tableEnd, @Param("eventType") Integer eventType,
                                             @Param("product") String product, @Param("os") String os,
                                             @Param("oaId") String oaId,
                                             @Param("limitPage") Integer limitPage);

    /**
     * mac地址 查询click
     *
     * @param appId
     * @param eventType
     * @param os
     * @param mac
     * @param limitPage
     * @return
     */
    @Select({"<script>",
            "select  *  from   `ocpc_click_${tableEnd}`  where product = #{product} and os = #{os} and mac = #{mac} ",
            // 次留 则查昨天的clickId
            "<if test='null != eventType and  6 == eventType'>", " and create_time &lt; date_format(now(),'%Y-%m-%d')",
            "</if>", "order by id desc  limit #{limitPage}", "</script>"})
    List<ToutiaoClick> selectOcpcClickByMac(@Param("tableEnd") String tableEnd, @Param("eventType") Integer eventType,
                                            @Param("product") String product, @Param("os") String os, @Param("mac") String mac,
                                            @Param("limitPage") Integer limitPage);

    /**
     * AndroidId 查询click
     *
     * @param appId
     * @param eventType
     * @param os
     * @param mac
     * @param limitPage
     * @return
     */
    @Select({"<script>",
            "select  *  from   `ocpc_click_${tableEnd}`  where product = #{product} and os = #{os} and android_id = #{AndroidId} ",
            // 次留 则查昨天的clickId
            "<if test='null != eventType and  6 == eventType'>", " and create_time &lt; date_format(now(),'%Y-%m-%d')",
            "</if>", "order by id desc  limit #{limitPage}", "</script>"})
    List<ToutiaoClick> selectOcpcClickByAndroidId(@Param("tableEnd") String tableEnd, @Param("eventType") Integer eventType,
                                            @Param("product") String product, @Param("os") String os, @Param("AndroidId") String AndroidId,
                                            @Param("limitPage") Integer limitPage);

    /**
     * 根据appId和clickId查询click事件
     * 
     * @param clickId
     * @param appId
     * @return
     */
    @Select("select * from `ocpc_click_${tableEnd}` where id = #{clickId} limit 1")
    OcpcClick selectByIdAndProduct(@Param("clickId") Long clickId, @Param("tableEnd") String tableEnd);

    /**
     * 写入click事件
     * @param ocpcClick
     * @return
     */
    @Insert({"<script>",
              "INSERT INTO `ocpc_click_${tableEnd}`" +
               " ( `dsp`, `product`, `os`, `account_id`, `pkg_channel`, `ocpc_device_id`, `ts`, `account_name`, `callback_url`, `cid`, `gid`, `pid`, `oaid`, `aid_name`, `group_name`, `cid_name`, `activate_count`, `create_time`, `update_time`, `mac`, `android_id`) " +
                      "VALUES " +
             "( #{ocpcClick.dsp}, #{ocpcClick.product}, #{ocpcClick.os}, #{ocpcClick.accountId}, #{ocpcClick.pkgChannel}, #{ocpcClick.ocpcDeviceId}, #{ocpcClick.ts}, #{ocpcClick.accountName}, #{ocpcClick.callbackUrl}, #{ocpcClick.cid}, #{ocpcClick.gid}, #{ocpcClick.pid}, #{ocpcClick.oaid}, #{ocpcClick.aidName}, #{ocpcClick.groupName}, #{ocpcClick.cidName}, #{ocpcClick.activateCount}, #{ocpcClick.createTime}, #{ocpcClick.updateTime}, #{ocpcClick.mac}, #{ocpcClick.androidId});"      ,
            "</script>"})
    @Options(useGeneratedKeys = true, keyProperty = "ocpcClick.id")
    long insertIntoClick(@Param("ocpcClick") ToutiaoClick ocpcClick, @Param("tableEnd") String tableEnd);


    /**
     * dsp向ocpc同步click事件
     * @param ocpcClick
     * @return
     */
    @Insert({"<script>",
            "INSERT INTO `ocpc_click_${appId}`" +
                    " ( `dsp`, `product`, `os`, `account_id`, `pkg_channel`, `ocpc_device_id`, `ts`, `account_name`, `callback_url`, `cid`, `gid`, `pid`, `oaid`, `aid_name`, `group_name`, `cid_name`, `activate_count`, `create_time`, `update_time`, `mac`) " +
                    "VALUES " +
                    "( #{ocpcClick.dsp}, #{ocpcClick.product}, #{ocpcClick.os}, #{ocpcClick.accountId}, #{ocpcClick.pkgChannel}, #{ocpcClick.ocpcDeviceId}, #{ocpcClick.ts}, #{ocpcClick.accountName}, #{ocpcClick.callbackUrl}, #{ocpcClick.cid}, #{ocpcClick.gid}, #{ocpcClick.pid}, #{ocpcClick.oaid}, #{ocpcClick.aidName}, #{ocpcClick.groupName}, #{ocpcClick.cidName}, #{ocpcClick.activateCount}, #{ocpcClick.createTime}, #{ocpcClick.updateTime}, #{ocpcClick.mac});"      ,
            "</script>"})
    @Options(useGeneratedKeys = true,keyProperty = "ocpcClick.id")
    long dspInsertIntoClick(@Param("ocpcClick") OcpcClick ocpcClick, @Param("appId") Integer appId);

    /**
     * 获取所有的click表
     *
     * @return
     */
    @Select("select table_name from `information_schema`.tables where table_name like 'ocpc_click_%' and ${tableDateStr}")
    List<String> findClickOrderTables(@Param("tableDateStr")String tableDateStr);

    @Select("truncate table ${tableName};")
    List<String> truncateOrderTables(@Param("tableName")String tableName);


    /**
     * 查询要删除click
     * @param tableName
     * @param date
     * @return
     */
    @Select("select id from ${tableName} where create_time < #{date} limit #{limit}")
    List<Long> queryListForRemove(@Param("tableName") String tableName, @Param("date") Date date,@Param("limit") int limit);



    /**
     * 查询要删除click
     * @param tableName
     * @param date
     * @return
     */
    @Select("select max(id) as maxId,min(id) as minId,count(1) as dnum from ${tableName} where create_time < #{date} ")
    MoveObj queryMaxMin(@Param("tableName") String tableName, @Param("date") Date date);
    /**
     * 清除click事件
     * @param tableName
     * @param removeIdList
     * @return
     */
    @Update("<script> "+
            "delete from ${tableName} where id in " +
            "<foreach item='item' index='index' collection='removeIdList' open='(' separator=',' close=')'> " +
            "   #{item} " +
            "</foreach>" +
            " </script> ")
    int deleteClickList(@Param("tableName") String tableName, @Param("removeIdList") List<Long> removeIdList);

    @Update("delete from ${tableName} where id < #{maxId} and id > #{minId} and create_time < #{date}")
    Long deleteClickByMaxMin(@Param("tableName") String tableName,@Param("maxId") long maxId, @Param("minId") long minId, @Param("date") Date date);

    @Select("select id from ${tableName} where  create_time < #{date} limit #{limitNum}")
    List<Long> selectListIds(@Param("tableName") String tableName, @Param("date") Date date,@Param("limitNum") Integer limitNum);
}
