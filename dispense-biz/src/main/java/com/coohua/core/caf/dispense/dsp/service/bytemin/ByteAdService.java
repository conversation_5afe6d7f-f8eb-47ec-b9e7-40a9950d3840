package com.coohua.core.caf.dispense.dsp.service.bytemin;

import cn.hutool.core.date.DateUtil;
import com.coohua.core.caf.dispense.dto.req.ByteActiveReq;
import com.coohua.core.caf.dispense.hbase.HbaseByteEcpmService;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.ocpc.entity.ByteMinconf;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.ByteEcpmService;
import com.coohua.core.caf.dispense.ocpc.service.ByteMinconfService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ByteAdService {
    @Autowired
    ByteMinThirdService byteMinThirdService;
    @Autowired
    HbaseByteEcpmService hbaseByteEcpmService;
    @Autowired
    ByteEcpmService byteEcpmService;
    @Autowired
    ByteAppConfig byteAppConfig;
    @Autowired
    DouyinMinEcpmService douyinMinEcpmService;
    @Resource(name = "byteminEcpmPool")
    ThreadPoolTaskExecutor ocpcTo8Eevent;
    @Autowired
    ByteMinconfService byteMinconfService;
    public Pair<Integer,Double> getUserArpu(ByteActiveReq byteActiveReq, OcpcEvent ocpcActiveEvent,boolean isMinProm){
        long ctime = System.currentTimeMillis();
        //实时计算当前用户的arpu ecpm
        Pair<String,String>  pkye = byteAppConfig.getAppPair(byteActiveReq.getProduct());
        String hourStr = DateUtil.format(new Date(), "yyyy-MM-dd HH");

        List<ByteEcpm> byteAdEcpmList = new ArrayList<>();
        if(!isMinProm){
            byteAdEcpmList = byteMinThirdService.getEcmp(byteActiveReq.getUserId(),byteActiveReq.getProduct(),byteActiveReq.getOpenId(),pkye.getKey(),pkye.getValue(),hourStr);
        }else{
            ByteMinconf byteMinconf = byteMinconfService.getByteMinconf(byteActiveReq.getProduct());
            if(byteMinconf!=null){
                byteAdEcpmList = douyinMinEcpmService.getDouyinMinEcmp(byteActiveReq.getUserId(),byteActiveReq.getProduct(),byteActiveReq.getOpenId(),byteMinconf.getAcktoken(),hourStr, null);
            }else{
                log.error("获取doumin配置失败 "+byteActiveReq.getProduct());
            }
        }
        byteEcpmService.saveAllEcpms(byteAdEcpmList,ocpcActiveEvent,false);

        ocpcTo8Eevent.execute(new Runnable() {
            @Override
            public void run() {
                String dateStr = DateUtil.format(new Date(), "yyyy-MM-dd");
                cn.hutool.core.lang.Pair<Integer,List<ByteEcpm>> bytePair = byteMinThirdService.getDayEcmp(byteActiveReq.getUserId(),byteActiveReq.getProduct(),
                        byteActiveReq.getOpenId(),pkye.getKey(),pkye.getValue(),dateStr,1,500);

                List<ByteEcpm> dlist = bytePair.getValue();
                int szied = bytePair.getValue().size();
                int dnum = 1;
                if(bytePair.getKey()>500){
                    while (szied<=bytePair.getKey() && dnum<=50){
                        cn.hutool.core.lang.Pair<Integer,List<ByteEcpm>> bytePair2 = null;
                        try {
                           bytePair2 = byteMinThirdService.getDayEcmp(byteActiveReq.getUserId(),byteActiveReq.getProduct(),
                                    byteActiveReq.getOpenId(),pkye.getKey(),pkye.getValue(),dateStr,dnum,500);
                        }catch (Exception e){
                            log.error("",e);
                        }
                        dlist.addAll(bytePair2.getValue());
                        szied = szied + bytePair2.getValue().size();

                        log.info("byteecpm数据大于500 "+byteActiveReq.getUserId()+" "+byteActiveReq.getOpenId()+" "+byteActiveReq.getProduct()+" 第"+dnum+"拉取 总共"+szied);
                        dnum ++;
                    }
                }

                byteEcpmService.saveAllEcpms(bytePair.getValue(),ocpcActiveEvent,true);
            }
        });

        List<ByteEcpm>  byteEcpms = byteEcpmService.getUserEcpmList(byteActiveReq.getProduct(),byteActiveReq.getOpenId());
        int exposureNum = 0;
        Double arpus = 0d;
        for(ByteEcpm byteEcpm : byteEcpms){
            exposureNum = exposureNum +1;
            arpus = arpus + byteEcpm.getCost()/100000d;
        }
        Pair<Integer,Double> pairExposure = new Pair<>(exposureNum,arpus);
        log.info("bytemin拉取ecpm耗时 "+byteActiveReq.getOpenId() +" "+(System.currentTimeMillis()-ctime));
        //hbase合并
        return pairExposure;
    }

}
