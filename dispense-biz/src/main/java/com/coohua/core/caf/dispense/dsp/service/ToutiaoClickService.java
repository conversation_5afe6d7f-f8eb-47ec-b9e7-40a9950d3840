package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.mapper.ToutiaoClickMapper;
import com.coohua.core.caf.dispense.dto.req.IClickReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.service.OcpcClickService;
import com.coohua.core.caf.dispense.utils.ConstCls;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/7/6
 **/
@Service
@Slf4j
public class ToutiaoClickService  extends ServiceImpl<ToutiaoClickMapper, ToutiaoClick> {

	/**
	 * 是否打开次留判断开关
	 */
	@Value("${ciliu.date.enable:true}")
	public  boolean ciliuEnable;
	/**
	 * 默认最大5小时
	 */
    private static long maxClickTime = 5*60*60*1000L;
    @Autowired
    private UserEventPkgService eventPkgService;
    @Autowired
    private ProductCache productCache;
    @Autowired
    private OcpcClickService ocpcClickService;
	@Autowired
    OcpcSwitcher ocpcSwitcher;
	@Resource(name = "dspToutiaoClickWrite")
	ThreadPoolTaskExecutor dspToutiaoClickWriteExecutor;

    public boolean saveClick(ToutiaoClick toutiaoClick){
    	try {
			String osstr = toutiaoClick.getOs();
			if(StringUtils.isNotBlank(osstr) && osstr.contains(",")){
				String[] osAry =  osstr.split(",");
				for(String os:osAry){
					if(os.equalsIgnoreCase("android") || os.equalsIgnoreCase("ios")){
						log.warn("@@ 替换多平台os版本 "+osstr+"->"+os);
						toutiaoClick.setOs(os);
						break;
					}
				}
			}
		    productCache.checkClickProductName(toutiaoClick);
		    // TODO 产品是否走ocpc库链路，默认走dsp链路
		    if(ocpcSwitcher.saveToutiaoClick(toutiaoClick)){
				dspToutiaoClickWriteExecutor.execute(()-> save(toutiaoClick));
			}
		    return true;
	    }catch (Exception e){
    		log.error("dsp表toutiao_click写入异常",e);
		}
    	return false;
	}



    /**
	 * ocpc 查询click事件方法（除快手外）
	 * @param request
	 * @param isOne
	 * @return
	 */
    public List<ToutiaoClick> queryClick(UserEventReq request,boolean isOne) {
    	List<ToutiaoClick> dlist = new ArrayList<>();
    	try {
			boolean isCiliuFlag = ToutiaoEventTypeEnum.START_APP.value.equals(request.getEventType()) && ciliuEnable;
			Date nowDay = DateUtils.getNowDayDate();
			int limitNum = 20;
			if(isOne){
				limitNum = 1;
			}
			String pkgChannel = eventPkgService.queryPkgChannel(request.getPkgChannel());
			if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && StringUtils.isNotBlank(request.getOs()) && StringUtils.isNotBlank(request.getProduct())) {
				if (!ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
					dlist = this.lambdaQuery()
							.eq(ToutiaoClick::getProduct, request.getProduct())
							.eq(ToutiaoClick::getOs, request.getOs())
							.eq(ToutiaoClick::getOcpcDeviceId, request.getOcpcDeviceId())
							//                    .gt(ToutiaoClick::getTs,startTime)
							.eq(!UserEventPkgService.PKG_EMPTY.equals(pkgChannel),ToutiaoClick::getPkgChannel,pkgChannel)
							.le(isCiliuFlag,ToutiaoClick::getCreateTime,nowDay)
							.orderByDesc(ToutiaoClick::getCreateTime)
							.last("limit "+limitNum)
							.list();
				}
			}
			if (dlist.size() == 0 && StringUtils.isNotBlank(request.getOaid())&&request.getOaid().length()>6&& StringUtils.isNotBlank(request.getOs())&& StringUtils.isNotBlank(request.getProduct())) {// to 梳理
				dlist = this.lambdaQuery()
						.eq(ToutiaoClick::getProduct, request.getProduct())
						.eq(ToutiaoClick::getOs, request.getOs())
						.eq(ToutiaoClick::getOaid, request.getOaid())
						.eq(!UserEventPkgService.PKG_EMPTY.equals(pkgChannel),ToutiaoClick::getPkgChannel,pkgChannel)
//                    .gt(ToutiaoClick::getTs,startTime)
						.le(isCiliuFlag,ToutiaoClick::getCreateTime,nowDay)
						.orderByDesc(ToutiaoClick::getCreateTime)
						.last("limit "+limitNum)
						.list();
			}

			if(dlist.size() == 0&& StringUtils.isNotBlank(request.getMac())&&request.getMac().length()>6&& StringUtils.isNotBlank(request.getOs())&& StringUtils.isNotBlank(request.getProduct())){
				dlist = this.lambdaQuery()
						.eq(ToutiaoClick::getProduct, request.getProduct())
						.eq(ToutiaoClick::getOs, request.getOs())
						.eq(ToutiaoClick::getMac, request.getMac())
						.eq(!UserEventPkgService.PKG_EMPTY.equals(pkgChannel),ToutiaoClick::getPkgChannel,pkgChannel)
//                    .gt(ToutiaoClick::getTs, startTime)
						.le(isCiliuFlag,ToutiaoClick::getCreateTime,nowDay)
						.orderByDesc(ToutiaoClick::getCreateTime)
						.last("limit "+limitNum)
						.list();
			}
		}catch (Exception e){
			log.error("",e);
		}
		return dlist;
    }

    private ToutiaoClick isCiLiuClick(ToutiaoClick toutiaoClick, Date nowDay, boolean isCiliuFlag) {
        if (toutiaoClick == null) {
            return null;
        }
        // 非次留上报，不判断click创建时间
        if (!isCiliuFlag) {
            return toutiaoClick;
        }
        Date clickTime = toutiaoClick.getCreateTime();
        //次留只取昨天的点击事件
	    if(clickTime!=null && clickTime.getTime()<nowDay.getTime()){
        	return toutiaoClick;
        }
	    log.warn("次留click 匹配不一致 {}", JSONObject.toJSONString(toutiaoClick));
        return null;
    }


	public ToutiaoClick queryClickByDeviceId(String ocpcDeviceId, String os, String product) {
		ToutiaoClick toutiaoClick = null;
		long startTime = System.currentTimeMillis() - maxClickTime;

		if(StringUtils.isNotBlank(ocpcDeviceId)){
			toutiaoClick = this.lambdaQuery()
					.eq(ToutiaoClick::getOcpcDeviceId,ocpcDeviceId)
					.eq(ToutiaoClick::getProduct, product)
					.eq(ToutiaoClick::getOs, os)
					.gt(ToutiaoClick::getTs,startTime)
					.orderByDesc(ToutiaoClick::getCreateTime)
					.last("limit 1")
					.one();
		}

		return toutiaoClick;
	}


    public static void main(String[] args) {
        Date nowDayDate = DateUtils.getNowDayDate();
        System.out.println("nowDayDate = " + nowDayDate.getTime());
	    HashSet<String> set = new HashSet<>();
	    set.add("123");
	    set.add("456");
	    System.out.println("JSONObject.toJSONString(set) = " + JSONObject.toJSONString(set));
    }

	public void checkLengthAndSetUaMd5(DspType dspType, IClickReq req) {
		try {
			if (req.getIp() != null && req.getIp().length() > BaseConstants.IpMaxLength) {
				log.warn("{} 点击事件参数超出长度 ip {} {}", dspType.cnName, req.getIp().length(), req.getIp());
				req.setIp(StringUtils.substring(req.getIp(), 0, BaseConstants.IpMaxLength));
			}
			if (StringUtils.isNotBlank(req.getUa())) {
				req.setUa(MD5Utils.getMd5Sum(req.getUa()));
			}
			if (req.getModel() != null && req.getModel().length() > BaseConstants.ModelMaxLength) {
				log.warn("{} 点击事件参数超出长度 model {} {}", dspType.cnName, req.getModel().length(), req.getModel());
				req.setModel(StringUtils.substring(req.getModel(), 0, BaseConstants.ModelMaxLength));
			}
		} catch (Exception e) {
			log.warn(dspType.cnName + " 点击事件参数超出长度 校验异常", e);
		}
	}
}
