package com.coohua.core.caf.dispense.dsp.entity;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ExpousureEcpm implements Serializable {

    private static final long serialVersionUID = 1L;

    private String product;

    private String userId;

    private long csjEcpm = 0l;
    private long otherPlatEcpm = 0l;

    private long totalEcpm = 0l;

    private long addNum = 0l;

    private boolean stopOcpc;

    public boolean getStopOcpc() {
        return this.stopOcpc;
    }

}
