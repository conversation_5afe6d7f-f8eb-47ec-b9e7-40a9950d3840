package com.coohua.core.caf.dispense.ocpc.service;

import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dto.req.DeviceReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.OcpcSourceType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseLockUserActiveService;
import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
import com.coohua.core.caf.dispense.ocpc.entity.GuiStatistics;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.entity.UserActiveNew;
import com.coohua.core.caf.dispense.ocpc.mapper.UserActiveMapper;
import com.coohua.core.caf.dispense.ocpc.mapper.UserActiveNewMapper;
import com.coohua.core.caf.dispense.utils.ConstCls;
import com.coohua.core.caf.dispense.utils.EventDataCheck;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.Resource;
import javax.swing.table.TableRowSorter;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Service
@Slf4j
public class UserActiveService extends ServiceImpl<UserActiveMapper, UserActive> {
    @Value("#{${channel.source.map: {\"vivo\": \"vivo\",\"oppo\": \"oppo\",\"xiaomi\": \"xiaomi\",\"huawei\": \"huawei\",\"yingyongbao\": \"yingyongbao\",\"neilaxin\":\"neilaxin\",\"update\":\"neilaxin\",\"daoliu\":\"neilaxin\"}}}")
    public Map<String, String> channelSourceMap;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    UserActiveMapper userActiveMapper;
    @Autowired
    HbaseUserActiveService hbaseUserActiveService;
    @Autowired
    ProductCache productCache;
    @Autowired
    OceanengineSdkService oceanengineSdkService;
    @Autowired
    private UserActiveNewMapper userActiveNewMapper;

    public UserActive queryActive(String userId,String product){
        try{
            if(StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(product)){
                LambdaQueryChainWrapper<UserActive> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActive::getUserId,userId);
                lambdaQuery.eq(UserActive::getProduct,product);
                lambdaQuery.last(" limit 1 ");
                List<UserActive> userActiveList = lambdaQuery.list();
                if(userActiveList.size()>0){
                    return userActiveList.get(0);
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }
    public UserEventReq getURq(UserActive userActive) {
        if (StringUtils.isNotBlank(userActive.getOs())) {
            userActive.setOs(userActive.getOs().toLowerCase());
        }
        UserEventReq userEventReq = new UserEventReq();
        if (StringUtils.equalsAnyIgnoreCase("ios", userActive.getOs())) {
            userEventReq.setOcpcDeviceId(userActive.getIdfa());
        } else if (StringUtils.isNotBlank(userActive.getImei())) {
            userEventReq.setOcpcDeviceId(MD5Utils.getMd5Sum(userActive.getImei()));
        }
        userEventReq.setOaid(userActive.getOaid());
        if ((StringUtils.isBlank(userActive.getProduct()) || "null".equalsIgnoreCase(userActive.getProduct())) && userActive.getAppId() != null) {
            String pname = productCache.getPname(userActive.getAppId());
            userEventReq.setProduct(pname);
            userActive.setProduct(pname);
        } else {
            userEventReq.setProduct(userActive.getProduct());
        }
        userEventReq.setAppId(userActive.getAppId());
        userEventReq.setPkgChannel(userActive.getChannel());
        userEventReq.setOs(userActive.getOs());
        userEventReq.setMac(userActive.getMac());
        userEventReq.setUserId(userActive.getUserId() + "");
        userEventReq.setAndroidId(userActive.getAndroidId());
        userEventReq.setSouceType(OcpcSourceType.OCPC);
        if(StringUtils.isNotBlank(userActive.getUa())){
            try {
                userEventReq.setUa(MD5Utils.getMd5Ua(URLDecoder.decode(userActive.getUa(),"UTF-8")));
            }catch (Exception e){
                log.error("ua decode error ",e);
            }
        }
        userEventReq.setIp(userActive.getIp());
        userEventReq.setModel(userActive.getModel());
        userEventReq.setCaid(userActive.getCaid());
        userEventReq.setOaid2(userActive.getOaid2());
        if(StringUtils.equalsIgnoreCase("ios",userActive.getOs())){
            userEventReq.setIdfa2(userActive.getIdfa2());
            userEventReq.setOcpcDeviceId(userActive.getIdfa2());
//            userEventReq.setCaid();
        }

        EventDataCheck.replaceZto(userEventReq);
        return userEventReq;
    }


    public List<UserActive> deviceRegisters(DeviceReq deviceReq){
        List<UserActive> userActiveList = new ArrayList<>();
        if("ios".equalsIgnoreCase(deviceReq.getOs())){
            if(StringUtils.isNotBlank(deviceReq.getIdfa())){
                LambdaQueryChainWrapper<UserActive> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActive::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActive::getIdfa,deviceReq.getIdfa());
                userActiveList.addAll(list(lambdaQuery));
            }
        }else{
            if(StringUtils.isNotBlank(deviceReq.getImei())){
                LambdaQueryChainWrapper<UserActive> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActive::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActive::getImei,deviceReq.getImei());
                userActiveList.addAll(lambdaQuery.list());
            }

            if(StringUtils.isNotBlank(deviceReq.getOaid())){
                LambdaQueryChainWrapper<UserActive> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActive::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActive::getOaid,deviceReq.getOaid());
                List<UserActive>  oaIduserActiveList = lambdaQuery.list();
                if(oaIduserActiveList.size()>0){
                    userActiveList.addAll(oaIduserActiveList);
                }
            }


            if(StringUtils.isNotBlank(deviceReq.getAndroidId())){
                LambdaQueryChainWrapper<UserActive> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActive::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActive::getAndroidId,deviceReq.getAndroidId());
                List<UserActive>  oaIduserActiveList = lambdaQuery.list();
                if(oaIduserActiveList.size()>0){
                    userActiveList.addAll(oaIduserActiveList);
                }
            }

            if(StringUtils.isNotBlank(deviceReq.getMac())){
                LambdaQueryChainWrapper<UserActive> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActive::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActive::getMac,deviceReq.getMac());
                List<UserActive>  oaIduserActiveList = lambdaQuery.list();
                if(oaIduserActiveList.size()>0){
                    userActiveList.addAll(oaIduserActiveList);
                }
            }
        }
        return userActiveList;
    }

    private String getStoreChannel(UserActive userActive){
        if (StringUtils.isNotBlank(userActive.getChannel())) {
            AtomicReference<String> sourceName = new AtomicReference<>("");
            channelSourceMap.keySet().forEach(pkgName -> {
                if (userActive.getChannel().toLowerCase().contains(pkgName)) {
                    sourceName.set(channelSourceMap.get(pkgName));
                }
            });

            if (StringUtils.isNotBlank(sourceName.get())) {
                return sourceName.get();
            }
            if("ALIYUN_MAN_CHANNEL".equalsIgnoreCase(userActive.getChannel())){
                return "自然ALIYUN量";
            }
        }
        return null;
    }
    @Autowired
    HbaseLockUserActiveService hbaseLockUserActiveService;
    public ToutiaoClick guiOcpc(UserEventReq userEventReq){
        //复制一份实时归因逻辑
        ToutiaoClick toutiaoClick = hbaseClickService.getHbaseClickLongByDevice(userEventReq);//14天过期数据 存储
        return toutiaoClick;
    }

    public ToutiaoClick guiOcpc2(UserEventReq userEventReq){
        //复制一份实时归因逻辑
        ToutiaoClick toutiaoClick = hbaseClickService.getHbaseClickLongByDevice(userEventReq);
        return toutiaoClick;
    }

    public ToutiaoClick guiOcpcBySdk(UserEventReq userEventReq) {
        if (ocpcSwitcher.userActiveSdk && StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && StringUtils.isNotBlank(userEventReq.getSourceDeviceId())) {
            ToutiaoClick toutiaoClick = oceanengineSdkService.querySdkClick(userEventReq, true, true);
            log.info("user gui ocpc, userId:{}, req:{} res:{}", userEventReq.getUserId(), userEventReq, toutiaoClick);
            return toutiaoClick;
        }
        return null;
    }



    @Resource(name = "userActiveSave")
    ThreadPoolTaskExecutor poolTaskExecutor;
    @Autowired
    private RedissonClient redissonClient;
    public void activeUser(UserActive userActive, boolean isBuShu, String sourceDeviceId) {
        UserEventReq userEventReq = getURq(userActive);
        userEventReq.setSourceDeviceId(sourceDeviceId);

        if(ocpcSwitcher.baiduGy && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && userEventReq.getPkgChannel().startsWith("bd")
                && ocpcSwitcher.baiduGyChannel.contains(userEventReq.getPkgChannel())){
            log.info("uactivebaidu渠道替换product "+"bd"+userEventReq.getProduct()+"@"+userEventReq.getPkgChannel());
            userEventReq.setProduct("bd"+userEventReq.getProduct());
        }

        if (isBuShu || hbaseUserActiveService.queryByHbase(userEventReq.getUserId(), userEventReq.getProduct()) == null) {
            poolTaskExecutor.execute(() -> {
                String lockKey = "userActiveLock:" + userEventReq.getUserId() + ":" + userEventReq.getProduct() + ":" + userEventReq.getOs();
                RLock lock = redissonClient.getLock(lockKey);
                try {
                    if (lock.tryLock()) {
                        ToutiaoClick toutiaoClick = null;
                        //复制一份实时归因逻辑
                        if (StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
                            //andoidId 就是 ocpcSourceId 不用查2遍
                            toutiaoClick = oceanengineSdkService.querySdkClick(userEventReq, true, false);
                            //log.info("user active sdk, userId:{}, res:{}", userEventReq.getUserId(), toutiaoClick);
                        }
                        if (null == toutiaoClick) {
                            if (ocpcSwitcher.readClickLongFlag) {
                                toutiaoClick = hbaseClickService.getHbaseClickLongByDevice(userEventReq);//14天过期数据 存储
                            } else {
                                toutiaoClick = redisClickService.getClick(userEventReq, ocpcSwitcher.readUserHbaseSwitch);
                            }
                        }
                        userActive.setUa(null);
                        if (toutiaoClick != null) {
                            userActive.setAccountId(toutiaoClick.getAccountId());
                            userActive.setSource(toutiaoClick.getDsp());
                            userActive.setGyType(userEventReq.getGuiType());
                            userActive.setClickTime(toutiaoClick.getCreateTime());
                            userActive.setCid(toutiaoClick.getCid() + "");
                            userActive.setPid(toutiaoClick.getPid() + "");
                            userActive.setClickId(toutiaoClick.getClickId());
                            userActive.setGid(toutiaoClick.getGid() + "");
                            userActive.setMid(toutiaoClick.getMid());
                            userActive.setIdfa2(userActive.getIdfa2());
                            userActive.setIdfa(userActive.getIdfa());
                            userActive.setIp(userEventReq.getIp());
                            userActive.setModel(userEventReq.getModel());
                            userActive.setUa2(userEventReq.getUa());
                            if (isBuShu) {
                                userActive.setUpdateTime(new Date());
                                userActive.setAcDesc("回调延迟");
                                int updNum = userActiveMapper.updateById(userActive);
                                log.info("ocpc补偿激活成功 " + userActive.getUserId() + "@" + userActive.getProduct() + "" + updNum);
                            }
                        } else {
                            String storeChannel = getStoreChannel(userActive);
                            if (StringUtils.isBlank(storeChannel)) {
                                userActive.setSource("自然量");
                            } else {
                                userActive.setSource(storeChannel);
                            }
                        }

                        Date date = new Date();
                        userActive.setCreateTime(date);
                        userActive.setUpdateTime(date);

                        hbaseUserActiveService.saveUserActive(userActive);

                        if (!isBuShu) {
                            saveDb(userActive);
                        }
                    }
                } catch (Exception e) {
                    log.error("插入数据异常 ", e);
                } finally {
                    lock.unlock();
                }
            });
        } else {
            log.warn(userEventReq.getUserId() + "" + userEventReq.getProduct() + " 已经产生归因");
        }
    }

    private void saveDb(UserActive userActive) {
        UserActiveNew userActiveNew = new UserActiveNew();
        BeanUtils.copyProperties(userActive, userActiveNew);
        userActiveNewMapper.insert(userActiveNew);
        userActiveMapper.insert(userActive);
    }


    private Set<String> dset = new HashSet<>();

    {
        dset.add("neilaxin");
        dset.add("share");
        dset.add("yingyongbao");
    }

    public List<UserActive> queryZiranSourceActive(Date beforeDate, long limit, long offset, String product,boolean isNew) {
        LambdaQueryChainWrapper<UserActive> lambdaQueryChainWrapper = lambdaQuery().gt(UserActive::getCreateTime, beforeDate)
                .eq(UserActive::getSource, "自然量")
                .isNotNull(UserActive::getUserId)
                .isNotNull(UserActive::getAppId)
                .last(" limit " + offset + "," + limit);
        if(StringUtils.isNotBlank(product)){
            lambdaQueryChainWrapper.eq(UserActive::getProduct,product);
        }
        if(isNew){
            lambdaQueryChainWrapper.isNull(UserActive::getAcDesc);
        }else{
            lambdaQueryChainWrapper.ne(UserActive::getAcDesc,"参数不合法");
            lambdaQueryChainWrapper.orderByDesc(UserActive::getCreateTime);
        }
        List<UserActive> dlist = lambdaQueryChainWrapper.list();
        List<UserActive> fllist = dlist.stream().filter(userActive -> {
            if(StringUtils.isBlank(userActive.getChannel())){
                return true;
            }
            final boolean[] isFl = {false};
            dset.forEach(channelName -> {
                isFl[0] = isFl[0] || userActive.getChannel().contains(channelName);
            });
            return !isFl[0];
        }).collect(Collectors.toList());

        return fllist;
    }
    public List<UserActive> queryZiranSourceActive(Date beforeDate, long limit, long offset,boolean isNew) {
        return queryZiranSourceActive(beforeDate,limit,offset,null,isNew);
    }

    public long sumZiranCount(Date beforeDate, String product,boolean isNew) {
        LambdaQueryChainWrapper<UserActive> lambdaQueryChainWrapper = lambdaQuery().gt(UserActive::getCreateTime, beforeDate)
                .eq(UserActive::getSource, "自然量")
                .isNotNull(UserActive::getUserId)
                .isNotNull(UserActive::getAppId);
        if (StringUtils.isNotBlank(product)) {
            lambdaQueryChainWrapper.eq(UserActive::getProduct, product);
        }
        if(isNew){
            lambdaQueryChainWrapper.isNull(UserActive::getAcDesc);
        }else{
            lambdaQueryChainWrapper.ne(UserActive::getAcDesc,"参数不合法");
        }
        return lambdaQueryChainWrapper.count();
    }


    public long sumZiranCount(Date beforeDate) {
        return sumZiranCount(beforeDate, null,true);
    }


    public List<GuiStatistics> queryGuiStatistics() {
        return userActiveMapper.queryGuiStatistics();
    }

    public UserActive queryActiveByDevice(String deviceId, String product, String os) {
        if (StringUtils.isBlank(deviceId) || StringUtils.isBlank(product) || StringUtils.isBlank(os)) {
            return null;
        }
        LambdaQueryChainWrapper<UserActive> lambdaQuery = lambdaQuery();
        lambdaQuery.eq(UserActive::getProduct, product);
        lambdaQuery.eq(UserActive::getOs, os);
        if ("ios".equals(os)) {
            lambdaQuery.eq(UserActive::getCaid,deviceId);
        }else {
            lambdaQuery.eq(UserActive::getOaid,deviceId);
        }
        lambdaQuery.last(" limit 1 ");
        List<UserActive> list = lambdaQuery.list();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 核心业务逻辑同原注册方法@activeUser
     * 为mq使用
     * @param userActive
     */
    public void activeUserForMq(UserActive userActive) {
        // 保持原有逻辑
        setUserActiveMd5Info(userActive);


        UserEventReq userEventReq = getURq(userActive);
        userEventReq.setSourceDeviceId(userActive.getAndroidId());

        if(ocpcSwitcher.baiduGy && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && userEventReq.getPkgChannel().startsWith("bd")
                && ocpcSwitcher.baiduGyChannel.contains(userEventReq.getPkgChannel())){
            log.info("uactivebaidu渠道替换product "+"bd"+userEventReq.getProduct()+"@"+userEventReq.getPkgChannel());
            userEventReq.setProduct("bd"+userEventReq.getProduct());
        }


        UserActive existsInfo = hbaseUserActiveService.queryByHbase(userEventReq.getUserId(), userEventReq.getProduct());
        if (existsInfo != null) {
            log.info("active user already exists: {}", JSONObject.toJSONString(existsInfo));
            return;
        }

        // 原逻辑基础上去除线程异步
        ToutiaoClick toutiaoClick = null;
        //复制一份实时归因逻辑
        if (StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
            //andoidId 就是 ocpcSourceId 不用查2遍
            toutiaoClick = oceanengineSdkService.querySdkClick(userEventReq, true, false);
            //log.info("user active sdk, userId:{}, res:{}", userEventReq.getUserId(), toutiaoClick);
        }
        if (null == toutiaoClick) {
            if (ocpcSwitcher.readClickLongFlag) {
                toutiaoClick = hbaseClickService.getHbaseClickLongByDevice(userEventReq);//14天过期数据 存储
            } else {
                toutiaoClick = redisClickService.getClick(userEventReq, ocpcSwitcher.readUserHbaseSwitch);
            }
        }
        userActive.setUa(null);
        if (toutiaoClick != null) {
            userActive.setAccountId(toutiaoClick.getAccountId());
            userActive.setSource(toutiaoClick.getDsp());
            userActive.setGyType(userEventReq.getGuiType());
            userActive.setClickTime(toutiaoClick.getCreateTime());
            userActive.setCid(toutiaoClick.getCid() + "");
            userActive.setPid(toutiaoClick.getPid() + "");
            userActive.setClickId(toutiaoClick.getClickId());
            userActive.setGid(toutiaoClick.getGid() + "");
            userActive.setMid(toutiaoClick.getMid());
            userActive.setIdfa2(userActive.getIdfa2());
            userActive.setIdfa(userActive.getIdfa());
            userActive.setIp(userEventReq.getIp());
            userActive.setModel(userEventReq.getModel());
            userActive.setUa2(userEventReq.getUa());
        } else {
            String storeChannel = getStoreChannel(userActive);
            if (StringUtils.isBlank(storeChannel)) {
                userActive.setSource("自然量");
            } else {
                userActive.setSource(storeChannel);
            }
        }

        // 这里重复查一次，避免原方法写入慢导致mq重复刷hbase
        UserActive existsInfo1 = hbaseUserActiveService.queryByHbase(userEventReq.getUserId(), userEventReq.getProduct());
        if (existsInfo1 != null) {
            log.info("active user already exists: {}", JSONObject.toJSONString(existsInfo1));
            return;
        }

        Date date = new Date();
        userActive.setCreateTime(date);
        userActive.setUpdateTime(date);

        // 与原逻辑相比，暂去除写库逻辑，只留存储到HBase
        // 写库逻辑先保持在接口请求实现，后续考虑全切mq
        log.info("kafka active save hbase: {}", JSONObject.toJSONString(userActive));
        hbaseUserActiveService.saveUserActiveNoCatchError(userActive);
    }

    /**
     * 原@UserActiveController中的setMd5Info方法
     * @param userActive
     */
    public void setUserActiveMd5Info(UserActive userActive) {
        if(StringUtils.isNotBlank(userActive.getMac()) && MD5Utils.macSet.contains(userActive.getMac())){
            log.info("macmoren "+userActive.getMac()+" "+userActive.getUserId());
            userActive.setMac(null);
        }
        if (StringUtils.isNotBlank(userActive.getOaid()) && !ConstCls.emptyMd5.equals(userActive.getOaid()) && !ConstCls.zeroMd5.equals(userActive.getOaid())) {
            String oaid2 = MD5Utils.getMd5Sum(userActive.getOaid());
            userActive.setOaid2(oaid2);
        }
        if ("ios".equalsIgnoreCase(userActive.getOs())
                && StringUtils.isNotBlank(userActive.getIdfa())
                && userActive.getIdfa().length()>3
                && !userActive.getIdfa().startsWith("00000000-0000")
                && !userActive.getIdfa().startsWith("000000-0000")
                && !userActive.getIdfa().startsWith("0000-0000") ) {
            String idfa2 = MD5Utils.getMd5Sum(userActive.getIdfa());
            userActive.setIdfa2(idfa2);
        }
        userActive.setMac(MD5Utils.getMd5Mac(userActive.getMac()));
        userActive.setAndroidId(MD5Utils.getMd5AndroidId(userActive.getAndroidId()));
    }

}
