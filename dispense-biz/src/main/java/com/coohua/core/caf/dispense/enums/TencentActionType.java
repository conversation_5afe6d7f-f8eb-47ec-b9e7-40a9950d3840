package com.coohua.core.caf.dispense.enums;

public enum TencentActionType {
    ACTIVATE_APP(0,"ACTIVATE_APP","激活"),//	         30天内第一次打开APP
    REGISTER(1,"REGISTER","注册"),//	         用户点击广告后注册成为APP的新用户
    COMPLETE_ORDER(20,"COMPLETE_ORDER","下单"),//	           用户点击下单生成订单
    PURCHASE(2,"PURCHASE","购买"),//	         用户完成付费
    ADD_TO_CART(22,"ADD_TO_CART","加入购物车"),//	     用户将商品加入购物车
    START_APP(6,"START_APP","次日留存"),//	     用户激活后第二天打开APP的行为
    KEY_EVENT(25,"CUSTOM","关键行为"),//	     关键页面访问

    /** 衍生回传相关 start */
    WATCH_AND_ARPU(357, "衍生行为arpu", "衍生行为arpu", 1),
    WATCH_AND_ECPM(356, "衍生行为ecpm", "衍生行为ecpm", 2),
    /** 衍生回传相关 end */

    LTV(259,"AD_PURCHASE","变现LTV"),//	 用户广告变现价值

    ;
    public Integer value;
    public String name;
    public String desc;

    /** 衍生回传时的 key_type 字段*/
    public Integer extKeyType;

    TencentActionType(Integer value, String name,String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

    TencentActionType(Integer value, String name, String desc, Integer extKeyType) {
        this.value = value;
        this.name = name;
        this.desc = desc;
        this.extKeyType = extKeyType;

    }

    public static TencentActionType getStatus(Integer value) {
        if (value != null) {
            TencentActionType[] otypes = TencentActionType.values();
            for (TencentActionType memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
