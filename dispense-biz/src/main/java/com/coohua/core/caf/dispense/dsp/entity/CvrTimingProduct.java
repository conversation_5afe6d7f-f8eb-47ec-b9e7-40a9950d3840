package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <p>
 * cvr3产品
 * </p>
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="CvrTimingProduct", description="定时cvr产品")
public class CvrTimingProduct implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private BigInteger id;
    @ApiModelProperty(value = "数据打点产品名")
    private String product;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "小时数组")
    private String timingArray;

    @ApiModelProperty(value = "出价比例,转化率比例")
    private String rateArray;

    @ApiModelProperty(value = "检查间隔时间分钟")
    private Integer intervalMinute;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "修改人")
    private String updateName;

    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
