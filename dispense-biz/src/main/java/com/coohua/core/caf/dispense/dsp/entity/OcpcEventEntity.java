package com.coohua.core.caf.dispense.dsp.entity;

import com.coohua.core.caf.dispense.enums.DspType;
import lombok.Data;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/8/4 20:03
 */
@Data
public class OcpcEventEntity {

	private	String event;

	private String elementPage;

	private String elementName;

	private String elementUri;


	/**
	 * 设置平台
	 */
	private DspType dspType;
	/**
	 *是否是激活事件
	 */
	private boolean activeEvent;
	/**
	 * 是否是关键行为
	 */
	private boolean behaviorEvent;
	/**
	 * 是否是次留事件
	 */
	private boolean ciliuEvent;

}
