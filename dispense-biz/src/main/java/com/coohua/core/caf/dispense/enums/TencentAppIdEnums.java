package com.coohua.core.caf.dispense.enums;

public enum TencentAppIdEnums {
	Lucky<PERSON><PERSON><PERSON>(8, 1110114481),
	;

	public Integer value;
	public Integer tencentAppId;

	TencentAppIdEnums(Integer value, Integer tencentAppId) {
		this.value = value;
		this.tencentAppId = tencentAppId;
	}

	public static TencentAppIdEnums getTenceAppIdEnums(Integer value) {
		if (value != null) {
			TencentAppIdEnums[] otypes = TencentAppIdEnums.values();
			for (TencentAppIdEnums memberType : otypes) {
				if (value.equals(memberType.value)) {
					return memberType;
				}
			}
		}
		return null;
	}
}
