package com.coohua.core.caf.dispense.dsp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.XimalayaAdvertiser;
import com.coohua.core.caf.dispense.dsp.mapper.XimalayaAdvertiserMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.swing.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.coohua.core.caf.dispense.constant.BaseConstants.UNDERLINE;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.matchNewRule;

/**
 * 
 * <AUTHOR>
 * @date 2022/4/25 20:52
 */
@Service
public class XimalayaAdvertiserService extends ServiceImpl<XimalayaAdvertiserMapper, XimalayaAdvertiser> {

    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    public static Map<String,XimalayaAdvertiser> AccountMap = new HashMap<>();

    @Scheduled(cron = "0 1/3 * * * ?")
    public void reloadConfig(){
        try {
            List<XimalayaAdvertiser> ximalayaAdvertiserList = lambdaQuery().eq(XimalayaAdvertiser::getDelFlag,0).list();
            AccountMap = ximalayaAdvertiserList.stream().collect(Collectors.toMap(XimalayaAdvertiser::getAdvertiserId, Function.identity()));

        }catch (Exception e){
            log.error("",e);
        }
    }

    public XimalayaAdvertiser getByAccountId(String accountId){
        return AccountMap.get(accountId);
    }

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            String  accountId = toutiaoClick.getAccountId();
            XimalayaAdvertiser ximalayaAdvertiser = getByAccountId(accountId);
            if(ximalayaAdvertiser==null){
                log.error("ximalaya无配置，请配置 "+accountId+" ");
                return false;
            }
            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();


            // 若无配置或未配置行为参数 仅当默认值时上报
            if (ximalayaAdvertiser == null || StringUtils.isBlank(ximalayaAdvertiser.getEventTypes()) || StringUtils.isBlank(ximalayaAdvertiser.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, ximalayaAdvertiser.getEventTypes(),
                        ximalayaAdvertiser.getEventValues(), firstTimeJudge, "ximalaya", ximalayaAdvertiser.getProductName(),
                        ximalayaAdvertiser.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, ximalayaAdvertiser.getEventTypes(),
                        ximalayaAdvertiser.getEventValues(), firstTimeJudge, "ximalaya", ximalayaAdvertiser.getProductName(),
                        ximalayaAdvertiser.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }
}
