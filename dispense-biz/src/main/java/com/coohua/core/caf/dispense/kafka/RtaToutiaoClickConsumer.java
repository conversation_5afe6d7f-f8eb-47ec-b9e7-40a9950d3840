package com.coohua.core.caf.dispense.kafka;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.ck.entity.GroupToutiaoClickLocal;
import com.coohua.core.caf.dispense.ck.service.GroupToutiaoClickService;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.rta.entity.ToutiaoClickDistGroup;
import com.coohua.core.caf.dispense.rta.service.ToutiaoClickDistGroupService;
import com.coohua.core.caf.dispense.utils.ABShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;


@Component
@Slf4j
public class RtaToutiaoClickConsumer implements InitializingBean {
    static final int saveTnum = 12;
    private static final AtomicInteger threadId = new AtomicInteger();
    public static final ExecutorService POOL = Executors.newFixedThreadPool(saveTnum);
    private static final String topic = "rta_toutiao_click";
    public static ExecutorService executorService = new ThreadPoolExecutor(
            32,
            32,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(1000),
            r -> new Thread(r, "rta-work-" + threadId.getAndIncrement()),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    @Autowired
    private ToutiaoClickDistGroupService groupToutiaoClickService;

    public void subscribe() {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "172.16.200.5:9092,172.16.200.6:9092,172.16.200.7:9092");
        //默认值为30000ms，可根据自己业务场景调整此值，建议取值不要太小，防止在超时时间内没有发送心跳导致消费者再均衡。
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 20000);
//        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 3000);
        //每次poll的最大数量。
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        //注意该值不要改得太大，如果poll太多数据，而不能在下次poll之前消费完，则会触发一次负载均衡，产生卡顿。
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 20000);
//        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 2097152);//2M
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, 1024 * 2);//2M
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, 2097152);
//        props.put(ConsumerConfig.RECEIVE_BUFFER_CONFIG, 1048576 * 20);//2M
        props.put(ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG, 1000);
        //消息的反序列化方式。
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        //当前消费实例所属的Consumer Group，请在控制台创建后填写。
        //属于同一个Consumer Group的消费实例，会负载消费消息。
        props.put(ConsumerConfig.GROUP_ID_CONFIG, "group_rta_toutiao_click");

        for (int i = 0; i < saveTnum; i++) {
            POOL.submit(new Runnable() {
                @Override
                public void run() {
                    processConsume(props);
                }
            });
        }
    }


    private void processConsume(Properties props) {
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);
        //设置Consumer Group订阅的Topic，可订阅多个Topic。如果GROUP_ID_CONFIG相同，那建议订阅的Topic设置也相同。
        List<String> subscribedTopics = new ArrayList<>();
        //每个Topic需要先在控制台进行创建。
        subscribedTopics.add(topic);
        consumer.subscribe(subscribedTopics);
        List<ToutiaoClickDistGroup> recordList = new ArrayList<>();
        //循环消费消息。
        while (true) {
            try {

                ConsumerRecords<String, String> records = consumer.poll(1000);
                //必须在下次poll之前消费完这些数据, 且总耗时不得超过SESSION_TIMEOUT_MS_CONFIG 的值。
                //建议开一个单独的线程池来消费消息，然后异步返回结果。
                if (records.count() > 0) {
                    for (ConsumerRecord<String, String> record : records) {
                        String valueStr = record.value();
                        if (StringUtils.isNotBlank(valueStr)) {
                            // log.info("GroupToutiaoClickLocal拉取数据 {}",valueStr);
                            ToutiaoClickDistGroup toutiaoClick = JSONObject.parseObject(valueStr, ToutiaoClickDistGroup.class);
                            recordList.add(toutiaoClick);
                        }
                        if (recordList.size() > 5000) {
                            List<ToutiaoClickDistGroup> finalFlist = recordList;
                            // 异步去处理数据
                            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> saveDataToMysql(finalFlist), executorService);
//                            poolList.add(future);
//                            if (poolList.size() >= 32){
//                                poolList = new ArrayList<>();
//                                executorService.shutdown();
//                                executorService = new ThreadPoolExecutor(
//                                        32,
//                                        32,
//                                        0L,
//                                        TimeUnit.MILLISECONDS,
//                                        new LinkedBlockingQueue<>(1000),
//                                        r -> new Thread(r, "work-" + threadId.getAndIncrement()),
//                                        new ThreadPoolExecutor.CallerRunsPolicy()
//                                );
//                            }
                            recordList = new ArrayList<>();
                        }
                        consumer.commitSync();
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(recordList)) {
                        List<ToutiaoClickDistGroup> finalFlist = recordList;
                        // 异步去处理数据
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> saveDataToMysql(finalFlist), executorService);
                        recordList = new ArrayList<>();
                    }
                }

            } catch (Exception e) {
                try {
                    Thread.sleep(100);
                } catch (Throwable ignore) {
                    log.error("", ignore);
                }
                //更多报错信息，参见常见问题文档。
                log.error("", e);
            }
        }
    }

    private void saveDataToMysql(List<ToutiaoClickDistGroup> dataList) {
        for (ToutiaoClickDistGroup toutiaoClick : dataList) {
            // 计算groupId
            Integer groupId = null;
            if (StringUtils.equalsIgnoreCase("android", toutiaoClick.getOs()) && StringUtils.isNotBlank(toutiaoClick.getOaid())) {
                groupId = ABShardingUtil.shard("android" + toutiaoClick.getOaid());
            } else if (StringUtils.equalsIgnoreCase("ios", toutiaoClick.getOs()) && StringUtils.isNotBlank(toutiaoClick.getCaid())) {
                groupId = ABShardingUtil.shard("ios" + toutiaoClick.getCaid());
            }
            // 生成GroupToutiaoClick
            toutiaoClick.setGroupId(groupId);
            // log.info("GroupToutiaoClickLocal {}", valueStr);
        }
        // 入库
        // log.info("GroupToutiaoClickLocal保存ck成功");
        groupToutiaoClickService.saveBatch(dataList);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        new Thread(this::subscribe).start();
    }
}
