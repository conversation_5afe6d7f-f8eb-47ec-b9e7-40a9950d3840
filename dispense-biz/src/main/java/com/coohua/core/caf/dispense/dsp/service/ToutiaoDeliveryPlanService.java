package com.coohua.core.caf.dispense.dsp.service;

import com.coohua.core.caf.dispense.dsp.entity.ToutiaoDeliveryPlan;
import com.coohua.core.caf.dispense.dsp.mapper.ToutiaoDeliveryPlanMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    * 广告计划 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-10-02
*/
@Service
public class ToutiaoDeliveryPlanService extends ServiceImpl<ToutiaoDeliveryPlanMapper, ToutiaoDeliveryPlan> {
    public String getAccountId(String proid){
        List<ToutiaoDeliveryPlan> dlist = lambdaQuery().eq(ToutiaoDeliveryPlan::getAdId,proid).last(" limit 1 ").list();
        if(dlist.size()>0){
            return  ""+dlist.get(0).getAdvertiserId();
        }
        return null;
    }
}
