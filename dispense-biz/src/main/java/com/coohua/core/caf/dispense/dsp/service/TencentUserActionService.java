package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.apollo.TencentApolloConfig;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.TencentDeveloper;
import com.coohua.core.caf.dispense.dsp.entity.ThirdExtEvent;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dto.req.AddTecentActiveReq;
import com.coohua.core.caf.dispense.dto.req.AddTecentActiveReqV2;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.*;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.ocpc.service.TencentLtvService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;
import top.zrbcool.pepper.boot.httpclient.HttpBioClient;

import javax.annotation.Resource;
import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_URL_BODY;

@Service
@Slf4j
public class TencentUserActionService {
    @Autowired
    TencentDeveloperService tencentDeveloperService;

    @Autowired
    private AlertService alertService;

    @Autowired
    private OcpcEventService ocpcEventService;
    @Autowired
    private DspUserEventService dspEventService;

    @Autowired
    TencentApolloConfig apolloConfig;
    @Autowired
    TencentLtvService tencentLtvService;
    @Resource(name = "jobForTencentLtvCallback")
    ThreadPoolTaskExecutor jobForTencentLtvCallbackExecutor;
    @Autowired
    private HttpBioClient httpBioClient;

    public void createAction(UserEventReq userEventReq,ToutiaoClick toutiaoClick) {
        createAction(userEventReq.getAppId(),userEventReq.getEventType(),toutiaoClick);
    }
    public void createAction(int appId,int eventType,ToutiaoClick toutiaoClick) {
//        curl 'https://api.e.qq.com/v1.1/user_action_sets/add?access_token=<ACCESS_TOKEN>&timestamp=<TIMESTAMP>&nonce=<NONCE>' \
//        -H 'Content-Type: application/json' \
//        -d '{
//        "account_id": "<ACCOUNT_ID>",
//                "type": "WEB",
//                "name": "webuser_action_set",
//                "description": ""
//       }'
        String createActionUrl = "https://api.e.qq.com/v1.1/user_action_sets/add?";

        List<TencentDeveloper>  developers = tencentDeveloperService.getAllDevelops(appId);
        Map<String, Object> dmap = new HashMap<>();
        for(TencentDeveloper tencentDeveloper : developers){
            if(StringUtils.isEmpty(tencentDeveloper.getUserActionSetId()) && StringUtils.isNotBlank(tencentDeveloper.getAccessToken())){
                dmap.put("account_id", toutiaoClick.getAccountId());
                dmap.put("type", "ANDROID");
                TencentAppIdEnums tencentAppIdEnums = TencentAppIdEnums.getTenceAppIdEnums(appId);
                dmap.put("mobile_app_id", tencentAppIdEnums.tencentAppId);
                dmap.put("name",eventType);
                dmap.put("description", eventType);

                String reqUrl = createActionUrl+"access_token="+tencentDeveloper.getAccessToken()+"&timestamp="+(System.currentTimeMillis()/1000)+"&nonce="+getNonce();

                try {
                    String html = HttpClientUtil.post(HttpConfig.custom().timeout(3000).url(reqUrl).map(dmap));

                    JSONObject jobj =  JSON.parseObject(html);
                    int code = jobj.getInteger("code");
                    if(code==0){
                        String userActionSetId = jobj.getJSONObject("data").getString("user_action_set_id");

                        TencentDeveloper tencentDeveloperRecord = new TencentDeveloper();
                        tencentDeveloperRecord.setId(tencentDeveloper.getId());
                        tencentDeveloperRecord.setUserActionSetId(userActionSetId);

                        tencentDeveloperService.updateById(tencentDeveloperRecord);
                    }else{
                        log.error("{} 账户名：{}创建数据源失败",tencentDeveloper.getId(),tencentDeveloper.getLoginAccount());
                    }
                    log.info("创建数据源成功  返回为："+html);
                }catch (Exception e){
                    log.error("",e);
                }
            }

        }

    }

    public static String getNonce(){
        UUID uuid = UUID.randomUUID();
        return  uuid.toString().substring(0,12);
    }
    public boolean activeEvent(UserEventReq userEventReq,ToutiaoClick toutiaoClick, UserEvent userEvent) {
        log.info("tencent  activeEvent = {} ",JSON.toJSONString(userEventReq));
        try {
            //创建action
            createAction(userEventReq,toutiaoClick);
        } catch (Exception e) {
            log.error("创建 action 失败",  e);
        }

        try {

            String reqUrl = "https://api.e.qq.com/v1.1/user_actions/add";
            String accountId =  toutiaoClick.getAccountId();

            TencentActionType tencentActionType = TencentActionType.getStatus(userEventReq.getEventType());

            // 指定账户（微信视频号）
            if (tencentDeveloperService.isChannelAccount(accountId)) {
                if (Objects.equals(userEventReq.getEventType(), ToutiaoEventTypeEnum.KEY_EVENT.value)) {
                    tencentActionType = TencentActionType.ACTIVATE_APP;
                    log.info("广点通视频号账户 回传激活 {}", accountId);
                } else {
                    log.info("广点通视频号账户 不回传除关键行为外的事件 {}", accountId);
                    userEvent.setReqRsp("广点通视频号账户 不回传除关键行为外的事件");
                    return false;
                }
            }

            if (tencentDeveloperService.isKeyRegisterAccount(accountId) && Objects.equals(userEventReq.getEventType(), ToutiaoEventTypeEnum.KEY_EVENT.value)) {
                tencentActionType = TencentActionType.REGISTER;
                log.info("广点通账户 回传关键行为到注册 {}", accountId);
            }

            // 微信小游戏 激活回传到注册
            if(StringUtils.isNotBlank(userEventReq.getOpenId())){
                String wAppid = ProductCache.getWAppid(userEventReq.getOpenId(),userEventReq.getProduct());
                if (StringUtils.isBlank(userEventReq.getWAppId())) {
                    userEventReq.setWAppId(wAppid);
                }
                if(StringUtils.isNotBlank(wAppid) && tencentActionType == TencentActionType.ACTIVATE_APP){
                    tencentActionType = TencentActionType.REGISTER;
                }
            }

            if (!useV2Callback(toutiaoClick.getCallbackUrl())) {
                // 是否是关键行为
                boolean isKeyEvent = Objects.equals(userEventReq.getEventType(), ToutiaoEventTypeEnum.KEY_EVENT.value);
                // 广点通关键行为加白账户不回传注册和下单
                if (isKeyEvent && !tencentDeveloperService.isKeyActionWhiteAccount(accountId)) {
                    // 额外回传到注册
                    requestTencent(userEventReq, toutiaoClick, userEvent, reqUrl, accountId, TencentActionType.REGISTER);
                    // 额外回传到下单
                    requestTencent(userEventReq, toutiaoClick, userEvent, reqUrl, accountId, TencentActionType.COMPLETE_ORDER);
                }
                requestTencent(userEventReq, toutiaoClick, userEvent, reqUrl, accountId, tencentActionType);
            } else {
                requestTencentV2(userEventReq, toutiaoClick, userEvent, accountId, tencentActionType);
            }

        } catch (Exception e) {
            log.error("腾讯回调异常 " + JSON.toJSONString(userEventReq), e);
            return false;
        }
        return false;
    }

    /** 是否使用V2版本的回传方式 */
    public static boolean useV2Callback(String callbackUrl) {
        // 有callbackUrl时走新版回传方式
        boolean hasCallbackUrl = StringUtils.isNotBlank(callbackUrl);
        return hasCallbackUrl;
    }

    private void requestTencent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent, String reqUrl, String accountId, TencentActionType tencentActionType) throws Exception {
        TencentDeveloper tencentDeveloper = tencentDeveloperService.getByAccountId(accountId);
        AddTecentActiveReq tecentActive = AddTecentActiveReq.getTencentEventReq(tencentDeveloper.getUserActionSetId(), userEventReq.getClickId(),tencentDeveloper.getAdvertiserId(), tencentActionType.name,
                userEventReq.getOcpcDeviceId(), userEventReq.getOaid(), toutiaoClick.getOs(),userEventReq);


        reqUrl = reqUrl + "?access_token=" + tencentDeveloper.getAccessToken()+"&timestamp="+(System.currentTimeMillis()/1000)+"&nonce="+getNonce();
        String jsonStr = JSON.toJSONString(tecentActive);

        String html = HttpClientUtil.post(HttpConfig.custom().timeout(3000).url(reqUrl).json(jsonStr));

        log.info("腾讯行为类型  {} {} {} {}", accountId, tencentActionType.name, reqUrl, jsonStr);
        JSONObject jobj =  JSON.parseObject(html);
        if (jobj != null && Objects.equals(jobj.getInteger("code"), 0)) {
            log.info("腾讯回调成功2 {} {} {}" , accountId, tencentActionType.name ,html);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "腾讯回调成功");
        }else{
            log.info("腾讯回调失败2 {} {} {} , req {} reqBody {}" , accountId, tencentActionType.name ,html, reqUrl, jsonStr);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "腾讯回调失败");
        }
        if(userEvent !=null){
            userEvent.setReqRsp(html);
            userEvent.setReqUrl(reqUrl + DELIMITER_URL_BODY + jsonStr);
        }
    }

    private void requestTencentV2(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent, String accountId, TencentActionType tencentActionType) throws Exception {
        TencentActionType[] actionTypes;
        if (tencentActionType == TencentActionType.KEY_EVENT && !tencentDeveloperService.isKeyActionWhiteAccount(accountId)) {
            // 关键行为需要额外回传到注册和下单
            actionTypes = new TencentActionType[]{TencentActionType.KEY_EVENT, TencentActionType.REGISTER, TencentActionType.COMPLETE_ORDER};
        } else {
            actionTypes = new TencentActionType[]{tencentActionType};
        }
        AddTecentActiveReqV2 tencentActive = AddTecentActiveReqV2.getTencentEventReq(userEventReq, toutiaoClick.getOs(), actionTypes);
        // 点击时间大于2025-06-15 14:00:00 回传23版本caid
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 解析字符串
        Date date = sdf.parse("2025-06-15 14:30:00");
        if (toutiaoClick.getCreateTime().after(date) ) {
            tencentActive = AddTecentActiveReqV2.getTencentEventReq2(userEventReq, toutiaoClick.getOs(), actionTypes);
        }
        String reqUrl = toutiaoClick.getCallbackUrl();
        String jsonStr = JSON.toJSONString(tencentActive);

        String html = HttpClientUtil.post(HttpConfig.custom().timeout(3000).url(reqUrl).json(jsonStr));

        log.info("腾讯行为类型V2d  {} {} {} {}", accountId, tencentActionType.name, reqUrl, jsonStr);
        JSONObject jobj =  JSON.parseObject(html);
        if (jobj != null && Objects.equals(jobj.getInteger("code"), 0)) {
            log.info("腾讯回调成功V2d {} {} {}" , accountId, tencentActionType.name ,html);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "腾讯回调成功");
        }else{
            log.info("腾讯回调失败V2d {} {} {} , req {} reqBody {}" , accountId, tencentActionType.name ,html, reqUrl, jsonStr);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "腾讯回调失败");
        }
        if(userEvent !=null){
            userEvent.setReqRsp(html);
            userEvent.setReqUrl(reqUrl + DELIMITER_URL_BODY + jsonStr);
        }
    }

    /**
     * 腾讯关键行为衍生回传
     */
    public void callbackExtEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userExtEvent, ThirdExtEvent thirdExtEvent, long callbackSecond) {

        TencentActionType tencentActionType = TencentActionType.getStatus(userExtEvent.getEventType());
        String accountId =  toutiaoClick.getAccountId();

        AddTecentActiveReqV2 tencentActive = AddTecentActiveReqV2.getTencentExtEventReq(userEventReq, toutiaoClick.getOs(), thirdExtEvent, callbackSecond);

        String reqUrl = toutiaoClick.getCallbackUrl();
        String jsonStr = JSON.toJSONString(tencentActive);

        String html = null;
        try {
            html = HttpClientUtil.post(HttpConfig.custom().timeout(3000).url(reqUrl).json(jsonStr));

            log.info("腾讯衍生回传  {} {} {} {}", accountId, tencentActionType.name, reqUrl, jsonStr);
            JSONObject jobj =  JSON.parseObject(html);
            if (jobj != null && Objects.equals(jobj.getInteger("code"), 0)) {
                log.info("腾讯衍生回传 成功 {} {} {}" , accountId, tencentActionType.name ,html);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "腾讯衍生回调成功");
            }else{
                log.info("腾讯衍生回传 失败 {} {} {} , req {} reqBody {}" , accountId, tencentActionType.name ,html, reqUrl, jsonStr);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "腾讯衍生回调失败");
                if (StringUtils.isBlank(html)) {
                    html = "腾讯衍生回调失败 返回空";
                }
            }
        } catch (Exception e) {
            log.info("腾讯衍生回传 异常 {} {} {} , req {} reqBody {}" , accountId, tencentActionType.name ,html, reqUrl, jsonStr);
            log.info("腾讯衍生回传 异常 " + accountId , e);
        }
        if(userExtEvent !=null){
            userExtEvent.setReqRsp(html);
            userExtEvent.setReqUrl(reqUrl + DELIMITER_URL_BODY + jsonStr);
        }
    }

    /**
     * 广点通 回传用户广告变现价值 即每次看视频回传到三方
     * @param eventTime 看广告时间
     */
    public void callbackLtv(UserEventReq userEventReq, OcpcEvent ocpcEvent, String product, String price, Long eventTime) {

        if (!DspType.GUANGDIANTONG.value.equals(ocpcEvent.getDsp())) {
            return;
        }

        if (apolloConfig.illegalProduct(product)) {
            return;
        }

        if (apolloConfig.illegalAccount(ocpcEvent.getAccountId())) {
            return;
        }

        // 激活超过1天后的不回传
        if (eventTime > ocpcEvent.getCreateTime().getTime() + TimeUnit.DAYS.toMillis(1)) {
            return;
        }

        TencentActionType tencentActionType = TencentActionType.LTV;
        String accountId =  ocpcEvent.getAccountId();

        //  用户单次广告ecpm,  单位: 毫 (10000毫 = 1元)
        Integer ecpm = Integer.valueOf(price) * 10;
        AddTecentActiveReqV2 tencentReq = AddTecentActiveReqV2.getTencentLtvReq(userEventReq, ocpcEvent.getOs(), eventTime, ecpm);

        String reqUrl = ocpcEvent.getCallbackUrl();
        String jsonStr = JSON.toJSONString(tencentReq);
        String requestId = UUID.randomUUID().toString();

        jobForTencentLtvCallbackExecutor.execute(()-> {
            String html = null;
            try {
                URI uri = new URI(reqUrl);
                HttpPost httpPost = new HttpPost(uri);
                httpPost.addHeader("Content-Type", "application/json");
                httpPost.setEntity(new StringEntity(jsonStr));

                HttpResponse response = httpBioClient.execute(requestId, httpPost, 7000, TimeUnit.MILLISECONDS);

                if (response.getStatusLine().getStatusCode() == 200) {
                    html = EntityUtils.toString(response.getEntity(), "UTF-8");
                } else {
                    alertService.report(product, accountId, "腾讯LTV回传请求失败");
                }

                log.info("腾讯LTV回传  {} {} {} {}", accountId, tencentActionType.name, reqUrl, jsonStr);
                JSONObject jobj =  JSON.parseObject(html);
                if (jobj != null && Objects.equals(jobj.getInteger("code"), 0)) {
                    log.info("腾讯LTV回传 成功 {} {} {}" , accountId, tencentActionType.name ,html);

                    tencentLtvService.saveTencentLtv(userEventReq, ocpcEvent, reqUrl, jsonStr, html, ecpm, eventTime);

                    alertService.report(ocpcEvent.getProduct(), ocpcEvent.getAccountId(), "腾讯衍生回调成功");
                }else{
                    log.info("腾讯LTV回传 失败 {} {} {} , req {} reqBody {}" , accountId, tencentActionType.name ,html, reqUrl, jsonStr);
                    alertService.report(ocpcEvent.getProduct(), ocpcEvent.getAccountId(), "腾讯衍生回调失败");
                }
            } catch (Exception e) {
                log.info("腾讯LTV回传 异常 {} {} {} , req {} reqBody {}" , accountId, tencentActionType.name ,html, reqUrl, jsonStr);
                log.info("腾讯LTV回传 异常 " + accountId , e);
            }
        });

    }

    public static void main(String[] args) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 解析字符串
        Date date = sdf.parse("2025-06-15 14:00:00");
        System.out.println(date);
        System.out.println(date.getTime());
    }
}
