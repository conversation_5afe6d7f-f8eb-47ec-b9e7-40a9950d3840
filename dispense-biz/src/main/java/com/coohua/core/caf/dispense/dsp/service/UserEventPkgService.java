package com.coohua.core.caf.dispense.dsp.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;

/**
 * (。≖ˇェˇ≖。)
 *
 * 用户渠道包配置
 * 
 * @author: zkk DateTime: 2020/8/28 10:27
 */
@Service
@Slf4j
public class UserEventPkgService {

    @ApolloJsonValue("${pkg.channel.set:[\"123\",\"456\"]}")
    public HashSet<String> pkgChannelSet;

    public static final String PKG_EMPTY = "0";

    /**
     * 默认
     */
    @Value("${pkg.open.enable:false}")
    private boolean closeFlag;
    /**
     * 处理渠道包，
     * @param pkgChannel
     * @return
     */
    public String queryPkgChannel(String pkgChannel) {
        try {
        	//若是未配置渠道包，或者渠道包传入参数为空 则默认为0
            if (StringUtils.isEmpty(pkgChannel) || closeFlag || CollectionUtils.isEmpty(pkgChannelSet)) {
                return PKG_EMPTY;
            }
            //若是 pkgChannel前缀已配置 则增加渠道包归因标识
            for (String pkg : pkgChannelSet) {
                if(StringUtils.isNotBlank(pkg) && pkgChannel.contains(pkg)){
                    return pkg;
                }
            }
        } catch (Exception e) {
            log.error("ocpc上报 渠道包参数解析异常", e);
        }
        return PKG_EMPTY;
    }
}
