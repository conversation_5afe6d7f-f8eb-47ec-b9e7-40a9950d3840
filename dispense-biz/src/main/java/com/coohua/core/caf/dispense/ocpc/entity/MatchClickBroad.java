package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MatchClickBroad对象", description="精准匹配点击事件看板对象")
public class MatchClickBroad {
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "是否为精准匹配")
    private Integer accurateMatch;
    @ApiModelProperty(value = "能否匹配点击事件")
    private Integer canMatch;
    @ApiModelProperty(value = "匹配到事件的id类型")
    private String matchIdType;
    @ApiModelProperty(value = "设备")
    private String os;
    @ApiModelProperty(value = "映射id类型")
    private String mapIdType;
    @ApiModelProperty(value = "根据映射是否可以匹配到点击事件")
    private Integer canMatchByMap;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
