package com.coohua.core.caf.dispense.dsp.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.core.caf.dispense.dsp.entity.SigmobAccount;


/**
 * <AUTHOR>
 * @date 2024/11/26 11:13
 * @description : sigmobAccount的Mapper接口
 */

public interface SigmobAccountMapper extends BaseMapper<SigmobAccount> {

    /**
     * 获取腾讯视频号在投账户
     * @return
     */
    /*@Select("select distinct advertiser_id from ad_plan_config where dsp ='guangdiantong' and del_flag =0 and delivery_range_name ='微信视频号'")
    Set<String> getSigmobAccountByAppName();*/

}
