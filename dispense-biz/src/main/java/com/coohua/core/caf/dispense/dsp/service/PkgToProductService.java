package com.coohua.core.caf.dispense.dsp.service;

import com.coohua.core.caf.dispense.dsp.entity.PkgToProduct;
import com.coohua.core.caf.dispense.dsp.mapper.PkgToProductMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-12-04
*/
@Service
public class PkgToProductService extends ServiceImpl<PkgToProductMapper, PkgToProduct> {

}
