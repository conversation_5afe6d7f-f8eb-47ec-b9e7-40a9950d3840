package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.IqiyiEventType;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.StandardHttpRequestRetryHandler;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_MULTI_URL;

@Component
@Slf4j
public class IqiyiUserEventService {
    @Autowired
    private AlertService alertService;
    @Autowired
    IqiyiAdvertiserService iqiyiAdvertiserService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    RedisEventService redisEventService;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {
        IqiyiEventType eventType = IqiyiEventType.parse(userEventReq.getEventType());
        if (eventType == null) {
            log.error("爱奇艺回传 无法识别的用户事件 {} {} {}", userEventReq.getEventType(), JSON.toJSONString(userEventReq), JSON.toJSONString(toutiaoClick));
            return false;
        }
        requestIqiyi(userEventReq, toutiaoClick, userEvent, eventType, false);
        // 关键行为额外回传到首次付费
        /*if (eventType == IqiyiEventType.KeyEvent) {
            requestIqiyi(userEventReq, toutiaoClick, userEvent, IqiyiEventType.Pay, true);
        }*/
        return false;
    }

    /**
     *
     * @param isExtraCallback 是否是额外回传
     */
    public void requestIqiyi(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent, IqiyiEventType eventType, boolean isExtraCallback) {
        CloseableHttpClient client = getHttpClient();
        try {
            String reqUrl = toutiaoClick.getCallbackUrl() + "&event_type=" + eventType.iqiyiVal;

            String rspStr = resendUrl(reqUrl,client);
            JSONObject jobj = JSON.parseObject(rspStr);
            if(jobj!=null && Objects.equals(jobj.getInteger("status"), 200)){
                log.info("爱奇艺回调成功 {} {} {} {}",toutiaoClick.getAccountId(),toutiaoClick.getProduct(),reqUrl,rspStr);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "爱奇艺回调成功");
            }else{
                log.warn("爱奇艺回调失败 {} {} {} {}",toutiaoClick.getAccountId(),toutiaoClick.getProduct(),reqUrl,rspStr);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "爱奇艺回调失败");
            }

            if (userEvent != null) {
                userEvent.setReqUrl(!isExtraCallback ? reqUrl : userEvent.getReqUrl() + DELIMITER_MULTI_URL + reqUrl);
                userEvent.setReqRsp(!isExtraCallback ? rspStr : userEvent.getReqRsp() + DELIMITER_MULTI_URL + rspStr);
            }
        } catch (Exception e) {
            log.error("爱奇艺回传未知异常 " + JSON.toJSONString(userEventReq), e);
        } finally {
            try {
                client.close();
            }catch (Exception e){
                log.error("爱奇艺回传关闭httpclient异常",e);
            }
        }
    }

    private static CloseableHttpClient getHttpClient(){
        SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(
                SSLContexts.createDefault(),
                new String[] { "TLSv1.2"},
                null,
                SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).setRetryHandler(new StandardHttpRequestRetryHandler(2,false)).build();
        return httpclient;
    }

    public int retryNum=3;
    private String resendUrl(String reqUrl, CloseableHttpClient client) throws Exception{
        String html = null;
        for(int i=0;i<=retryNum;i++){
            try {
                HttpConfig httpConfig = HttpConfig.custom().timeout(2000);
                html = HttpClientUtil.get(httpConfig.url(reqUrl).client(client));
                if(i!=0){
                    log.info("爱奇艺回调重试成功  第"+i+"次"+html);
                }
                return html;
            }catch (HttpProcessException e){
                if(i==retryNum){
                    log.info("爱奇艺回调超时",e.getMessage());
                }
            }
        }
        return html;
    }
}
