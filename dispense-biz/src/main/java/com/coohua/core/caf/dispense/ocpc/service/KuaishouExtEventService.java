package com.coohua.core.caf.dispense.ocpc.service;

import  com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.KuaishouEventType;
import com.coohua.core.caf.dispense.ocpc.entity.KuaishouExtEvent;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.mapper.KuaishouExtEventMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class KuaishouExtEventService extends ServiceImpl<KuaishouExtEventMapper, KuaishouExtEvent> {

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Resource(name = "ocpcKuaishouExtEventPool")
    ThreadPoolTaskExecutor poolTaskExecutor;

    public KuaishouExtEvent saveKuaishouExt(UserEventReq userEventReq, OcpcEvent ocpcEvent, String reqUrl, String rspStr, KuaishouEventType eventType, Integer ecpm, Long eventTime) {
        try {
            KuaishouExtEvent extEvent = new KuaishouExtEvent();
            Date date = new Date();
            extEvent.setAppId(userEventReq.getAppId());
            extEvent.setCreateTime(date);
            extEvent.setUpdateTime(date);
            extEvent.setUserId(userEventReq.getUserId());
            extEvent.setProduct(userEventReq.getProduct());

            extEvent.setClickId(ocpcEvent.getId());
            extEvent.setAccountId(ocpcEvent.getAccountId());
            extEvent.setDsp(ocpcEvent.getDsp());
            extEvent.setCallbackUrl(ocpcEvent.getCallbackUrl());

            extEvent.setOaid(userEventReq.getOaid());
            extEvent.setSourceOaid(userEventReq.getOaid());
            extEvent.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
            extEvent.setSourceDeviceId(userEventReq.getSourceDeviceId());
            extEvent.setOs(userEventReq.getOs());
            extEvent.setReqUrl(reqUrl);
            extEvent.setReqRsp(rspStr);

            extEvent.setEventType(Integer.parseInt(eventType.name));
            extEvent.setEcpm(ecpm);
            extEvent.setEventTime(new Date(eventTime));


            try {
                asyncSave(extEvent);
//                save(extEvent);
            } catch (Exception e) {
                log.error("", e);
            }

            return extEvent;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    private void asyncSave(KuaishouExtEvent extEvent) {
        if (ocpcSwitcher.writeKuaishouExtSwitch) {
            poolTaskExecutor.execute(() -> {
                try {
                    save(extEvent);
                }catch (Exception e){
                    log.error("异步写入kuaishou_ext_event异常", e);
                }
            });
        }
    }


}
