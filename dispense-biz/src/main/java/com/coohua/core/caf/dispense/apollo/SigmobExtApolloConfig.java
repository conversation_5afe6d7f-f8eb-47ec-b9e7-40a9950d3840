package com.coohua.core.caf.dispense.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/11/26 17:35
 * @description : 关键行为衍生事件相关apollo配置
 */

@Configuration
@Slf4j
public class SigmobExtApolloConfig {

    /**
     * 关键行额外回传到注册和下单 是否对全部advertiserId生效
     */
    @Value("${ocpc.sigmob.extra.enable.all:false}")
    public boolean sigmobExtraEnableAll;

    /**
     * 关键行额外回传到注册和下单 生效的advertiserId列表
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.enable.advertisers:[1719291750329357]}")
    public Set<Long> sigmobExtraEnableAdvertiserIds;


    /**
     * 百度衍生回传 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.enable.products:[]}")
    public Set<String> extEnableProducts;

    /**
     * 衍生回传 适用的账户 eventTypes范围
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.enable.eventTypes:[\"1,2\",\"1,3\"]}")
    public Set<String> extEnableEventTypes;

    /**
     * 衍生回传 是否对全部账户生效
     */
    @Value("${ocpc.sigmob.extra.enable.account.all:false}")
    public boolean extEnableAllAccount;

    /**
     * 关键行额外回传到 arpu次留 是否对全部advertiserId生效
     */
    @Value("${ocpc.sigmob.extra.arpu0.enable.all:false}")
    public boolean sigmobExtraArpu0EnableAll;

    /**
     * 衍生回传 生效的账户id列表
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.accounts:[]}")
    public Set<String> extEnableAccounts;

    /**
     * 全部使用分布式锁保证 衍生回传 不重复
     */
    @Value("${lock.sigmob.extra.all.enable:true}")
    public boolean lockExtDefineAllEnable;

    /**
     * 启用分布式锁保证 衍生回传 不重复的产品打点名称集合
     */
    @ApolloJsonValue("${lock.sigmob.extra.enable.products:[]}")
    public Set<String> lockExtDefineEnableProducts;

    /**
     * 衍生回传 (1,3类型) 是否对全部产品生效
     */
    @Value("${ocpc.sigmob.extra.1_3.enable.all:true}")
    public boolean extEnableAllFor_1_3;

    /**
     * 衍生回传 (1,3类型) 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.1_3.enable.products:[]}")
    public Set<String> extEnableProductsFor_1_3;

    /**
     * 关键行额外回传到 arpu次留 生效的advertiserId列表
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.arpu0.enable.advertisers:[]}")
    public Set<Long> sigmobExtraArpu0EnableAdvertiserIds;

    @Value("${ocpc.sigmob.extra.define.enable.account.all:false}")
    public boolean extDefineEnableAllAccount;

    /**
     * 衍生定义回传 生效的账户id列表
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.define.accounts:[]}")
    public Set<String> extDefineEnableAccounts;

    /**
     * 衍生定义回传 适用的账户 eventTypes范围
     */
    @ApolloJsonValue("${ocpc.sigmob.extra.define.enable.eventTypes:[\"1,2\",\"1,3\"]}")
    public Set<String> extDefineEnableEventTypes;




}
