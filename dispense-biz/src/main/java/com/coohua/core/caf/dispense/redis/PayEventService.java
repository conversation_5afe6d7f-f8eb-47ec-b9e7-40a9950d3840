package com.coohua.core.caf.dispense.redis;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.service.AuthKuaishouAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.TencentDeveloperService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.AccountEventTypeEnum;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.Date;

@Component
@Slf4j
public class PayEventService {
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    OcpcEventService ocpcEventService;
    public void payKeyEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq){
        if(userEventReq.getEventType()==ToutiaoEventTypeEnum.PURCHASE.value){
            userEventReq.setEventType(ToutiaoEventTypeEnum.KEY_EVENT.value);
            //关键行为只回传2天
            Date ctime = toutiaoClick.getCreateTime();

            //复原金额
            if(toutiaoClick!=null && StringUtils.equalsIgnoreCase(toutiaoClick.getDsp(),DspType.GUANGDIANTONG.name)){
                if(StringUtils.isNotBlank(userEventReq.getPayOrderNo()) && userEventReq.getPayAmount()!=null){
                    if(StringUtils.endsWith(userEventReq.getPayOrderNo(),"_od")){
                        //ios
                        Integer payAmount = userEventReq.getPayAmount();
                        int payAllAmount = DoubleUtil.divideDouble(userEventReq.getPayAmount()*1.0d,0.997d).intValue();
                        userEventReq.setPayAmount(payAllAmount);
                        log.info("wx小游戏广点通复原金额 ios "+payAmount+"->"+payAllAmount);
                    }
                }else if(userEventReq.getPayAmount()!=null){
                    int payAllAmount = DoubleUtil.divideDouble(userEventReq.getPayAmount()*1.0d,0.6d).intValue();
                    userEventReq.setPayAmount(payAllAmount);
                    Integer payAmount = userEventReq.getPayAmount();
                    log.info("wx小游戏广点通复原金额 "+payAmount+"->"+payAllAmount);
                }
            }
            if(ctime!=null){
                Long days = (System.currentTimeMillis()-ctime.getTime())/ DateTimeConstants.MILLIS_PER_DAY;
                if (isPayEventCall(toutiaoClick, userEventReq) && days<=2) {
                    boolean isFReq = authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);
                    if(isFReq){
                        boolean isAct = ocpcEventService.guiyingDspClick(toutiaoClick, userEventReq, true);
                        log.info("附加关键行为 "+ JSON.toJSONString(userEventReq));
                    }else{
                        log.info("支付重复回调 "+JSON.toJSONString(userEventReq));
                    }
                }
            }
            userEventReq.setEventType(ToutiaoEventTypeEnum.PURCHASE.value);
        }
    }
    @Autowired
    AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    @Autowired
    TencentDeveloperService tencentDeveloperService;
    /**
     * 是否满足付费回传条件
     * @param toutiaoClick
     * @param userEventReq
     * @return
     */
    public boolean isPayEventCall(ToutiaoClick toutiaoClick, UserEventReq userEventReq){
        Pair<String,String> eventTypesValPair = getEventTypesVals(toutiaoClick);
        if(StringUtils.isNotBlank(eventTypesValPair.getKey())
                && StringUtils.isNotBlank(eventTypesValPair.getValue())
                && isPayAccount(eventTypesValPair.getKey())){
            Integer amountJE = Integer.parseInt(eventTypesValPair.getValue());
            if(userEventReq.getPayAmount()!=null && userEventReq.getPayAmount()>0 && userEventReq.getPayAmount()>=amountJE){
                log.info("支付回传符合条件 "+userEventReq.getPayAmount()+" 配置 "+amountJE+" "+toutiaoClick.getAccountId()+" "+userEventReq.getUserId());
                return true;
            }
        }
        return false;
    }


    private Pair<String,String> getEventTypesVals(ToutiaoClick toutiaoClick){
        DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
        String eventTypes = "";
        String eventVals = "";
        if(DspType.TOUTIAO.equals(dspType) || DspType.TTWX.equals(dspType) || DspType.TTWMINDR.equals(dspType) ) {
            //快手需要灵性判断
            AuthToutiaoAdvertiser advertiser = authToutiaoAdvertiserService.getAccountActionMap().get(new BigInteger(toutiaoClick.getAccountId()));
            eventTypes = advertiser.getEventTypes();
            eventVals = advertiser.getEventValues();
        }else if(DspType.KUAISHOU.equals(dspType)) {
            AuthKuaishouApp authKuaishouApp = authKuaishouAdvertiserService.getAcMap().get(Long.valueOf(toutiaoClick.getAccountId()));
            eventTypes = authKuaishouApp.getEventTypes();
            eventVals = authKuaishouApp.getEventValues();
        }else if(DspType.GUANGDIANTONG.equals(dspType)){
            TencentDeveloper tencentDeveloper = tencentDeveloperService.getByAccountId(toutiaoClick.getAccountId());
            eventTypes = tencentDeveloper.getEventTypes();
            eventVals = tencentDeveloper.getEventValues();
        }
        Pair<String,String> dpair = new Pair<>(eventTypes,eventVals);
        return dpair;
    }
    public void setPayKeyEventRemark(ToutiaoClick toutiaoClick, UserEventReq userEventReq, UserEvent userEvent){
        if(isPayAccount(toutiaoClick)){
            String remark = userEvent.getRemark()==null?"":userEvent.getRemark();
            userEvent.setRemark(remark+" pay:"+userEventReq.getPayAmount());
        }
    }
    public boolean isPayAccount(ToutiaoClick toutiaoClick){
        Pair<String,String> eventTypesValPair = getEventTypesVals(toutiaoClick);
        boolean isPac = isPayAccount(eventTypesValPair.getKey());
        return isPac;
    }
    public boolean isPayAccount(String eventTypes){
        if(StringUtils.isNotBlank(eventTypes)) {
            AccountEventTypeEnum accountEventTypeEnum = AccountEventTypeEnum.getType(eventTypes);
            if (AccountEventTypeEnum.pay.equals(accountEventTypeEnum)) {
                return true;
            }
        }
        return false;
    }
}
