package com.coohua.core.caf.dispense.ocpc.service;

import com.coohua.core.caf.dispense.ocpc.entity.Tables;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcTableMapper;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class OcpcTableService {
    @Autowired
    OcpcTableMapper ocpcTableMapper;

    public static   String lockInfo = "ocpc_lockinfo";

    public void createIfNot(String tablePre) {
        try {
            ocpcTableMapper.createOrderUnionTable(tablePre);
        } catch (Exception e) {
            log.warn("",e);
        }
    }
    /**
     *
     * @param tablePre gpt_chat_info
     */
    public void renameTable(String tablePre) {
        String dateStr = DateUtils.formatDateForYMDSTR(new Date());
        List<Tables> tbs = ocpcTableMapper.queryTablesByEnd(tablePre,dateStr);
        if(tbs.size()==0){
            try {
                ocpcTableMapper.renameTable(tablePre,dateStr);
                log.info("agentck rename " + tablePre+dateStr + " 成功");
            } catch (Exception e) {
                log.warn("",e);
            }
            try {
                ocpcTableMapper.createOrderUnionTable(tablePre);
                log.info("agentck create " + tablePre+dateStr + " 成功");
            } catch (Exception e) {
                log.warn("",e);
            }
        }
        try {
            String delDateStr = DateUtils.formatDateForYMDSTR(new Date(System.currentTimeMillis() - 4 * DateTimeConstants.MILLIS_PER_DAY));
            List<Tables>  delTabs = ocpcTableMapper.queryTablesByEnd(tablePre,delDateStr);
            if(delTabs.size()>0){
                ocpcTableMapper.truncateOrderUnionTable(tablePre,delDateStr);
                ocpcTableMapper.dropOrderUnionTable(tablePre,delDateStr);
                log.info("agentck 删除表 " + delDateStr + " 成功");
            }
        } catch (Exception e) {
            log.warn("",e);
        }
    }
}
