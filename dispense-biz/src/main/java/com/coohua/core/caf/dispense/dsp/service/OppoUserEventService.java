package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.dsp.entity.OppoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dto.req.OppoAciveReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.Header;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.GeneralSecurityException;
import java.security.Key;
import java.util.Date;
import java.util.Set;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_URL_BODY;

@Component
@Slf4j
public class OppoUserEventService {
    @Autowired
    AlertService alertService;
    @Autowired
    OppoAdvertiserService oppoAdvertiserService;
    @Autowired
    RedisEventService redisEventService;

    public String oppoEncode(String data) throws GeneralSecurityException {
        if (data == null) {
            return null;
        }
        return encode(data.getBytes(), base64Key);
    }

    private static String encode(byte[] data, String base64Key) throws GeneralSecurityException {
        final Key dataKey = new SecretKeySpec(Base64.decodeBase64(base64Key), "AES");

        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, dataKey);
        byte[] encryptData = cipher.doFinal(data);

        return Base64.encodeBase64String(encryptData).replaceAll("\r", "").replaceAll("\n", "");
    }

    @Value("${ocpc.oppo.base64Key:\"XGAXicVG5GMBsx5bueOe4w==\"}")
    private static String base64Key = "XGAXicVG5GMBsx5bueOe4w==";
    @Value("${ocpc.oppo.salt:\"e0u6fnlag06lc3pl\"}")
    private static String salt = "e0u6fnlag06lc3pl";
    private String oppoActiveUrl = "https://api.ads.heytapmobi.com/api/uploadActiveData";

    @ApolloJsonValue("${ocpc.oppo.active.accounts:[\"**********\"]}")
    public Set<String> activeAccountSet;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {
        Date date = new Date();
        try {
            /**
             * 转化数据类型：1、激活，2、注册，4、次留， 8、自定义目标
             */
            ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEventReq.getEventType());
            int dataType = getOppoType(toutiaoEventTypeEnum);

            OppoAdvertiser oppoAdvertiser = oppoAdvertiserService.getByAdId(toutiaoClick.getAccountId());
            if(oppoAdvertiser==null){
                log.error("oppo无对应账户配置 " + toutiaoClick.getAccountId());
                userEvent.setReqRsp("oppo无对应账户配置");
                return false;
            }

            if(activeAccountSet.contains(oppoAdvertiser.getAdvertiserId())){
                log.info("oppo加白账户 "+oppoAdvertiser.getAdvertiserId());
                if(ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)){
                    dataType = 1;
                }else{
                    log.info("加白账户只回传 "+oppoAdvertiser.getAdvertiserId()+" @"+ JSON.toJSONString(userEventReq));
                    userEvent.setReqRsp("oppo 加白账户只回传 active ");
                    return false;
                }
            }else if(dataType < 0){
                log.info("oppo非关键行为不回传 {} {} {} ", toutiaoClick.getAccountId(), userEventReq.getProduct(), userEventReq.getEventType());
                userEvent.setReqRsp("oppo非关键行为不回传");
                return false;
            }

            OppoAciveReq oppoAciveReq = new OppoAciveReq();
            oppoAciveReq.setAdId(Long.parseLong(toutiaoClick.getPid()));
            oppoAciveReq.setAppType(1);//应用类别：1应用 2游戏 3快应用 0其他，默认1应用
            oppoAciveReq.setAscribeType(1);//归因类型：1：广告主归因，0：OPPO归因（默认或者不填即为0），2：助攻归因
            oppoAciveReq.setChannel(1);//渠道：1、OPPO，2、一加，0、其他
            if (ToutiaoEventTypeEnum.KEY_EVENT.equals(toutiaoEventTypeEnum)) {
//                oppoAciveReq.setCustomType();//自定义目标类型：dataType填了8之后补充，枚举值与客户沟通后补充
            }
            oppoAciveReq.setDataType(dataType);

            // 需要判断激活事件是通过哪个字段归因到的
            DeviceType deviceType = redisEventService.checkActiveEventFrom(userEventReq);

            if (deviceType == DeviceType.device) {
                oppoAciveReq.setImei(oppoEncode(userEventReq.getSourceDeviceId()));
                oppoAciveReq.setType(0);//Imei原始加密类型   1： md5加密 0： 无加密（默认为0） 如果传oaid，type值填0
            } else if (deviceType == DeviceType.oaid) {
                oppoAciveReq.setOuId(oppoEncode(userEventReq.getOaid()));
                oppoAciveReq.setType(0);
            } else {
                log.info("oppo回传未匹配到imei或oaid {} {} {} ", toutiaoClick.getAccountId(), JSON.toJSONString(toutiaoClick), JSON.toJSONString(userEventReq));
                userEvent.setReqRsp("oppo回传未匹配到imei或oaid");
                return false;
            }
//            if(StringUtils.isNotBlank(userEventReq.getOaid())){
//                oppoAciveReq.setOuId(encode(userEventReq.getOaid().getBytes(),base64Key));
//            }
            oppoAciveReq.setPkg(oppoAdvertiser.getPkg());
            oppoAciveReq.setTimestamp(date.getTime());

            String rspStr = sendOppo(oppoAciveReq, userEventReq.getProduct(), toutiaoClick.getAccountId());

            if (userEvent != null) {
                userEvent.setReqUrl(oppoActiveUrl + DELIMITER_URL_BODY + JSON.toJSONString(oppoAciveReq));
                userEvent.setReqRsp(rspStr);
            }
        } catch (Exception e) {
            log.error("oppo userEvent unknown error. userEventReq: {}, toutiaoClick: {}", userEventReq, toutiaoClick, e);
            return false;
        }
        return false;
    }

    public String sendOppo(OppoAciveReq oppoAciveReq, String product, String accountId) {
        try {
            String requestJson = JSON.toJSONString(oppoAciveReq);
            String md5SignStr = requestJson + oppoAciveReq.getTimestamp() + salt;
            String headSign = MD5Utils.getMd5Sum(md5SignStr);

            Header[] headers = new Header[3];
            headers[0] = new BasicHeader("signature", headSign);
            headers[1] = new BasicHeader("timestamp", oppoAciveReq.getTimestamp() + "");
            headers[2] = new BasicHeader("Content-Type", "application/json;charset=UTF-8");
            CloseableHttpClient httpClient = getHttpClient();
            HttpConfig config = HttpConfig.custom()
                    .headers(headers)
                    .url(oppoActiveUrl)
                    .encoding("utf-8")
                    .client(httpClient).json(requestJson);

            String rspStr = HttpClientUtil.post(config);
            log.info("OPPO回调结果 {} {} ,res: {} ,sign: {} ,ts: {} ,body: {}", accountId, product, rspStr, headSign, oppoAciveReq.getTimestamp(), requestJson);

            JSONObject jobj = JSON.parseObject(rspStr);
            int code = jobj.getInteger("ret");
            if (code == 0) {
                alertService.report(product, accountId, "oppo回调成功");
            } else {
                alertService.report(product, accountId, "oppo回调失败");
            }
            return rspStr;
        } catch (Exception e) {
            log.error("oppo userEvent unknown error. oppoAciveReq: {}", oppoAciveReq, e);
            return null;
        }
    }

    private static CloseableHttpClient getHttpClient() {
        CloseableHttpClient httpclient = HttpClients.custom().build();
        return httpclient;
    }

    private int getOppoType(ToutiaoEventTypeEnum toutiaoEventTypeEnum) {
        /**
         * 转化数据类型：1、激活，2、注册，4、次留， 8、自定义目标
         */
        switch (toutiaoEventTypeEnum) {
            case ACTIVATE_APP:
                return -1;
            case REGISTER:
                return -2;
            case START_APP:
                return -3;
            case KEY_EVENT:
                return 1;
        }
        return -1;
    }

}
