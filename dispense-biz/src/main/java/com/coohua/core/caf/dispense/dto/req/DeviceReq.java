package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

@Data
public class DeviceReq {
    private String userId;
    private String product;//xiaoxiaole
    private String os;//android || ios
    private String imei;//imei 原值
    private String mac;//mac md5 macSoc.replaceAll(":","").toUpperCase()
    private String androidId;//安卓id原值的md5，32位
    private String oaid;// and头条roid 拿的

    private String idfa;// idfa原值
}
