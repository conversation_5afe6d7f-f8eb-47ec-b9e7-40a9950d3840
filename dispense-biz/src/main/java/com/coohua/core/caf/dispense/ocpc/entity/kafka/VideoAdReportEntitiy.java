package com.coohua.core.caf.dispense.ocpc.entity.kafka;

import lombok.Data;
import lombok.experimental.Accessors;

import java.sql.Date;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @since 2021/2/9
 */
@Data
@Accessors(chain = true)
public class VideoAdReportEntitiy {
    private Integer appId;
    // 广告类型
    private Integer adType;
    // 平台
    private String dsp;
    // 价格 即ECPM值
    private String price;
    private Date ckLogDay;
    private Timestamp callTime;
    private Long timestamp;
    private String deviceId;
    private String userId;
    private String os;
    private String channel;
    private String ad_id;
    private String uri;
    private String imei;
    private String oaid;
    private String product;

}
