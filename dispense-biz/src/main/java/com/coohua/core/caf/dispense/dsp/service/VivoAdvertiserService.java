package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.dsp.entity.AccountAction;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.VivoAdvertiser;
import com.coohua.core.caf.dispense.dsp.mapper.VivoAdvertiserMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.matchNewRule;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-11-09
*/
@Service
@Slf4j
public class VivoAdvertiserService extends ServiceImpl<VivoAdvertiserMapper, VivoAdvertiser> {
    private static Map<String,VivoAdvertiser> advertiserIdVivoMap = new HashMap<>();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();

    @Scheduled(cron = "10 0/3 * * * ?")
    public void startInitVivoAdvertiser(){
        try {
            List<VivoAdvertiser> vivoAdvertiserList = lambdaQuery()
                    .eq(SystemInfo.isNotTest(), VivoAdvertiser::getDelFlag,0)
                    .isNotNull(VivoAdvertiser::getAdvertiserId)
                    .list();
            advertiserIdVivoMap = vivoAdvertiserList.stream().collect(Collectors.toMap(VivoAdvertiser::getAdvertiserId, Function.identity(), (oldVal,newVal)->oldVal));

            appMap = vivoAdvertiserList.stream()
                    .filter(a -> a.getProductName() != null && ProductCache.productEn2CnMap.get(a.getProductName()) != null)
                    .collect(Collectors.groupingBy(a-> ProductCache.productEn2CnMap.get(a.getProductName()),
                            Collectors.collectingAndThen(Collectors.toList(), apps -> apps.stream()
                                    .flatMap(this::convertAction)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList()))));
        }catch (Exception e){
            log.error("vivo加载配置异常",e);
        }
    }
    public VivoAdvertiser queryByAdvertiserId(String advertiserId){
        return advertiserIdVivoMap.get(advertiserId);
    }
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();

            VivoAdvertiser vivoAdvertiser = queryByAdvertiserId(toutiaoClick.getAccountId());

            // 若无配置或未配置行为参数 仅当默认值时上报
            if (vivoAdvertiser == null || StringUtils.isBlank(vivoAdvertiser.getEventTypes()) || StringUtils.isBlank(vivoAdvertiser.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, vivoAdvertiser.getEventTypes(),
                        vivoAdvertiser.getEventValues(), firstTimeJudge, "VIVO", vivoAdvertiser.getProductName(),
                        vivoAdvertiser.getClientId(), DateTimeConstants.MINUTES_PER_DAY);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, vivoAdvertiser.getEventTypes(),
                        vivoAdvertiser.getEventValues(), firstTimeJudge, "VIVO", vivoAdvertiser.getProductName(),
                        vivoAdvertiser.getClientId(), DateTimeConstants.MINUTES_PER_DAY);
            }
        } catch (Exception e) {
            log.error("vivoCheckActionCount error", e);
        }

        return false;
    }

    private Stream<AccountAction> convertAction(VivoAdvertiser advertiser) {
        try {
            // 无配置
            if (StringUtils.isBlank(advertiser.getEventTypes())) {
                return null;
            }

            if (StringUtils.isBlank(advertiser.getEventValues()) || StringUtils.isBlank(advertiser.getEventTypes())) {
                return Stream.empty();
            }

            return AuthKuaishouAdvertiserService.buildEventStandardConsideringMultiEcpm(advertiser.getEventTypes(),
                    advertiser.getEventValues(), new BigInteger("0"),
                    null);

        }catch (Exception e){
            log.error("vivo广告主配置信息解析出错 ",e);
        }

        return null;
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        return appMap;
    }


    public void updAckToken(String clientId, String state, String code){
        LambdaQueryWrapper<VivoAdvertiser> objectQueryWrapper = new QueryWrapper<VivoAdvertiser>().lambda();
        objectQueryWrapper.eq(VivoAdvertiser::getClientId,clientId)
                .eq(VivoAdvertiser::getDelFlag, 0);
        VivoAdvertiser vivoAdvertiser = getOne(objectQueryWrapper);
        if(vivoAdvertiser==null){
            log.error("vivo "+clientId+" 为空 请及时配置");
            throw new RuntimeException("vivo "+clientId+" 为空 请及时配置");
        }


        String rspStr = getToken(clientId,vivoAdvertiser.getSecret(),code);
        if(StringUtils.isNotBlank(rspStr)){
            JSONObject job = JSON.parseObject(rspStr);
            if(job.getInteger("code")==0){
                JSONObject dataObj = job.getJSONObject("data");
                String accessToken = dataObj.getString("access_token");
                String refreshToken = dataObj.getString("refresh_token");
                Long tokenDate = dataObj.getLong("token_date");//ms
                vivoAdvertiser.setAccessToken(accessToken);
                vivoAdvertiser.setRefreshToken(refreshToken);
                vivoAdvertiser.setRefreshTokenDate(new Date(tokenDate));
                updateById(vivoAdvertiser);
                log.info("更新ackToken成功 "+clientId);

            }else{
                log.error("授权失败 client:{}, code:{}, state:{}, result:{}", clientId, code, state, rspStr);
            }
        }else{
            log.error("授权失败返回空, client:{}, state:{}, code:{}", clientId, state, code);
        }
    }
    public String getToken(String clientId,String secret,String code)  {
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Request request = new Request.Builder().url("https://marketing-api.vivo.com.cn/openapi/v1/oauth2/token?client_id="+clientId+"&client_secret="+secret+"&grant_type=code&code="+code)
                    .method("GET", null)
                    .build();
            Response response = client.newCall(request).execute();
            return response.body().string();
        }catch (Exception e){
            log.error("",e);

        }
        return null;
    }
}
