package com.coohua.core.caf.dispense.dsp.service;

import com.coohua.core.caf.dispense.dsp.entity.Product;
import com.coohua.core.caf.dispense.dsp.mapper.ProductMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-08-01
*/
@Service
@Slf4j
public class ProductService extends ServiceImpl<ProductMapper, Product> {

    @Autowired
    SafeDataRedisService safeDataRedisService;

    private static Map<String, Product> productCnMap = new HashMap<>();
    private static Map<String, Product> productNameMap = new HashMap<>();
    private static Map<Integer, Product> productAppIdMap = new HashMap<>();

    @PostConstruct
    @Scheduled(fixedDelay = 1000 * 60)
    public void reloadConfig() {
        List<Product> list = lambdaQuery().eq(Product::getDelFlag, DELFLAG.weishanchu.value).list();

        productCnMap = list.stream().collect(Collectors.toMap(k-> k.getRemark(), k->k, (oldVal, newVal)->oldVal));
        productNameMap = list.stream().collect(Collectors.toMap(k-> k.getName(), k->k, (oldVal, newVal)->oldVal));
        productAppIdMap = list.stream().collect(Collectors.toMap(Product::getAppId, k->k, (oldVal, newVal)->oldVal));
    }

    public Product getByCnName(String productCn) {
        return productCnMap.get(productCn);
    }

    //查询所有的产品name
    public Set<String> getProductNameSet() {
       return productCnMap.values().stream().map(Product::getName).collect(Collectors.toSet());
    }

    public Product getByName(String name) {
        return productNameMap.get(name);
    }

    public Product getByAppId(Integer appId) {
        return productAppIdMap.get(appId);
    }
}
