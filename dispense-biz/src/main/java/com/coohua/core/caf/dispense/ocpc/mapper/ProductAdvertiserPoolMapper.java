package com.coohua.core.caf.dispense.ocpc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.core.caf.dispense.ocpc.entity.ProductAdvertiserPool;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface ProductAdvertiserPoolMapper extends BaseMapper<ProductAdvertiserPool> {

    @Select("select dsp, product, account_id, sum(key_count) key_count from daily_advertiser_account where data_date >= #{dayStart} group by dsp, product, account_id having key_count > 0")
    List<ProductAdvertiserPool> queryKeyCountList(@Param("dayStart") Date dayStart);
}
