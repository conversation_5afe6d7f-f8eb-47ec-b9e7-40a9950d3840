package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ByteEcpm对象", description="")
public class ByteEcpm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    private String product;

    private String userId;

    private Double cost;

    private Date eventDate;

    private String openId;

    private String dsp;

    private String accountNo;

    private String planId;

    private Date createTime;

    private Date updateTime;


}
