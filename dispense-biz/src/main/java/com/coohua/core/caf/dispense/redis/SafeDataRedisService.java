package com.coohua.core.caf.dispense.redis;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import redis.clients.jedis.JedisCluster;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SafeDataRedisService {

    @Autowired
    @Qualifier("safe-dataJedisClusterClient")
    private JedisClusterClient safeDataJedisClusterClient;

    /**
     * 根据用户id和产品获取用户出价、提现
     * @param userId
     * @param product
     * @return
     */
    public Map getBidMap(String userId,String product){
        String key = "userBid:"+product+"-"+userId;
        String valueStr = safeDataJedisClusterClient.get(key);
        log.info("valueStr {} key {}",valueStr,key);
        if (StringUtils.isBlank(valueStr)) {
            return null;
        }
        log.info("用户提现出价查询结果 {}",valueStr);
        return JSONObject.parseObject(valueStr, Map.class);
    }

    /**
     *  获取180天到30天，7日内只有一次留存的用户
     * @param deviceId
     * @return
     */
    public String getRetainOneDayPeople(String deviceId){
        String key = "retainOneDayPeople:"+deviceId;
        return safeDataJedisClusterClient.get(key);
    }

    public void setProductGroup(String key, String productGroup) {
        safeDataJedisClusterClient.set(key, productGroup);
    }

    /**
     *  获取userId对应的caidList
     * @param userId
     * @return
     */
    public List<String> getCaidListWithUserId(String userId){
        String key = "userIdCaidMapping:"+userId;
        String caidListStr = safeDataJedisClusterClient.get(key);
        if (StringUtils.isBlank(caidListStr)) {
            log.info("caidList {} key {}", "empty", key);
            return new ArrayList<>();
        }else {
            List<String> list = JSONObject.parseObject(caidListStr, List.class);
            log.info("caidList {} key {}", list, key);
            return list;
        }
    }

    public void incrementCount(String key){
        safeDataJedisClusterClient.incr(key);
    }

    public String getAndroidIdIdfv(String userId,String product,String os){
        String key = "user:id:map:"+userId+":"+os+":"+product;
        return safeDataJedisClusterClient.get(key);
    }

    public Map<String, String> getLocalCaidMap(String caid) {
        if (StringUtils.isBlank(caid)) {
            return null;
        }
        Map<String,String> map = safeDataJedisClusterClient.hgetAll("local:" + caid);
        if (map != null && !map.isEmpty()) {
            return map;
        }
        return null;
    }

    public Map<String, String> getCaidMap(String caid) {
        if (StringUtils.isBlank(caid)) {
            return null;
        }
        Map<String,String> map = safeDataJedisClusterClient.hgetAll("20230330:" + caid);
        if (map != null && !map.isEmpty()) {
            return map;
        }
        return null;
    }
}
