package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.service.ProductService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;

import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
@Slf4j
public class RetainOneDayPeopleService {
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @Autowired
    ProductService productService;

    @Autowired
    SafeDataRedisService safeDataRedisService;


    public Pair<String, Integer> queryRetainOneDayPeopleByDevice(UserEventReq userEventReq) {
        return queryRetainOneDayPeopleDevice(userEventReq.getProduct(), userEventReq.getOs(), userEventReq.getPkgChannel(), userEventReq.getOaid(), userEventReq.getCaid(), userEventReq.getOcpcDeviceId(), userEventReq.getSourceDeviceId());
    }


    private Pair<String, Integer> queryRetainOneDayPeopleDevice(String product, String os, String pkgChannel, String oaid, String caid, String ocpcDeviceId, String sourceDeviceId) {
        Set<String> productNameSet = productService.getProductNameSet();

        boolean isCt = ocpcSwitcher.guilvArpuSet.contains(product)
                || ocpcSwitcher.guilvBArpuSet.contains(product)
                || productNameSet.contains(product);
        if (isCt) {
            log.info("一日留存用户开始进入提升arpu流程 " + product + " " + pkgChannel);
            Integer retainOneDayPeople = null;
            String remark = "";
            String numberPattern = "-?\\d+(\\.\\d+)?";
            if (StringUtils.equalsIgnoreCase("ios", os)) {
                if (StringUtils.isNotBlank(caid)) {
                    String retainOneDayPeopleStr = safeDataRedisService.getRetainOneDayPeople(caid);
                    if (StringUtils.isNotBlank(retainOneDayPeopleStr)) {
                        retainOneDayPeople = retainOneDayPeopleStr.matches(numberPattern) ? Integer.parseInt(retainOneDayPeopleStr) : 0;
                        remark = "caid";
                    }
                }
            } else if (StringUtils.equalsIgnoreCase("android", os)) {
                if (StringUtils.isNotBlank(oaid)) {
                    String retainOneDayPeopleStr = safeDataRedisService.getRetainOneDayPeople(oaid);
                    if (StringUtils.isNotBlank(retainOneDayPeopleStr)) {
                        retainOneDayPeople = retainOneDayPeopleStr.matches(numberPattern) ? Integer.parseInt(retainOneDayPeopleStr) : 0;
                        remark = "oaid";
                    }
                }
            }

            if (retainOneDayPeople == null && StringUtils.isNotBlank(ocpcDeviceId)) {
                String retainOneDayPeopleStr = safeDataRedisService.getRetainOneDayPeople(ocpcDeviceId);
                if (StringUtils.isNotBlank(retainOneDayPeopleStr)) {
                    retainOneDayPeople = retainOneDayPeopleStr.matches(numberPattern) ? Integer.parseInt(retainOneDayPeopleStr) : 0;
                    remark = "ocpcDeviceId";
                }
            }

            if (retainOneDayPeople == null && StringUtils.isNotBlank(sourceDeviceId)) {
                String retainOneDayPeopleStr = safeDataRedisService.getRetainOneDayPeople(sourceDeviceId);
                if (StringUtils.isNotBlank(retainOneDayPeopleStr)) {
                    retainOneDayPeople = retainOneDayPeopleStr.matches(numberPattern) ? Integer.parseInt(retainOneDayPeopleStr) : 0;
                    remark = "sourceDeviceId";
                }
            }

            return new Pair<>(remark, retainOneDayPeople);
        }
        return new Pair<>(null, null);
    }
}
