package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.ExtEventApolloConfig;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.mapper.ThirdExtEventMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hbase.thirdparty.com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.constant.BaseConstants.NUM_ARPU_RULE;
import static com.coohua.core.caf.dispense.constant.BaseConstants.NUM_ECPM_RULE;
import static com.coohua.core.caf.dispense.enums.AccountActionTypeEnum.*;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.matchNewRule;

/**
 * <p>
 * 头条关键行为衍生事件配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class ThirdExtEventService extends ServiceImpl<ThirdExtEventMapper, ThirdExtEvent> implements IService<ThirdExtEvent> {

    @Autowired
    ExtEventApolloConfig extEventApolloConfig;

    private ConcurrentMap<ThirdExtEventGroupBy, List<ThirdExtEvent>> configFromDB = new ConcurrentHashMap<>();

    public static final String EVENT_TYPE_1_3 = "1,3";

    // 不区分渠道
    private List<AccountAction> actionTypeConfigs = Lists.newArrayList();

    @Scheduled(cron = "*/30 * * * * ?")
    public void reloadConfigs() {
        List<ThirdExtEvent> all = lambdaQuery()
                .eq(ThirdExtEvent::getDelFlag, DELFLAG.weishanchu.value)
                .list();

        if (CollectionUtils.isEmpty(all)) {
            return;
        }

        ConcurrentMap<ThirdExtEventGroupBy, List<ThirdExtEvent>> newConfig = all
                .stream()
                // 深度按正序排序
                .sorted(Comparator.comparing(ThirdExtEvent::getDepth))
                .collect(Collectors.groupingByConcurrent(k -> new ThirdExtEventGroupBy(k)));

        configFromDB = newConfig;

        // 行为arpu的需要忽略次数，所以要将每一条行为arpu的配置，次数从5至10复制一遍
        List<ThirdExtEvent> duplicate_1_2_configs = duplicate_1_2_Configs(all);
        actionTypeConfigs = duplicate_1_2_configs.stream().flatMap(this::convert2AccountAction)
                .filter(distinctByKey(k-> JSON.toJSONString(k.getEventConfigurations())))
                .collect(Collectors.toList());
    }

    private List<ThirdExtEvent> duplicate_1_2_Configs(List<ThirdExtEvent> all) {
        List<ThirdExtEvent> result = new ArrayList<>();
        for (ThirdExtEvent event : all) {
            if (NUM_ARPU_RULE.equals(event.getEventTypes())) {
                String arpu = StringUtils.substringAfter(event.getEventValues(), ",");
                for (int i = 5; i <= 10; i++) {
                    ThirdExtEvent copy = new ThirdExtEvent();
                    BeanUtils.copyProperties(event, copy);
                    copy.setEventValues(i+ "," + arpu);
                    result.add(copy);
                }
            } else {
                result.add(event);
            }
        }
        return result;
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        Map<String, List<AccountAction>> res = new HashMap<>();
        try {
            for (Map.Entry<String, String> entry : ProductCache.productCn2EnMap.entrySet()) {
                String productEn = entry.getValue();
                if (StringUtils.isNotBlank(productEn)) {
                    if (extEventApolloConfig.extEventEnableAll || extEventApolloConfig.extEventEnableProducts.contains(productEn)) {
                        res.put(entry.getKey(), actionTypeConfigs);
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成全产品衍生事件配置列表异常", e);
        }
        return res;
    }

    public List<AccountAction> queryActionConfig(String appName) {
        try {
            String productEn = ProductCache.productCn2EnMap.get(appName);
            if (StringUtils.isNotBlank(productEn)) {
                if (extEventApolloConfig.extEventEnableAll || extEventApolloConfig.extEventEnableProducts.contains(productEn)) {
                    return actionTypeConfigs;
                }
            }
        } catch (Exception e) {
            log.error("获取衍生事件配置列表异常 " + appName, e);
        }
        return new ArrayList<>();
    }

    public List<ThirdExtEvent> getAllExtEventConfigs(DspType dsp, String eventTypes) {
        List<ThirdExtEvent> cache = configFromDB.getOrDefault(new ThirdExtEventGroupBy(dsp, eventTypes), new ArrayList<>());
        // 深度copy一份，因为后续会有修改ToutiaoExtEvent的内容
        return cache.stream().map(k-> {
            ThirdExtEvent copy = new ThirdExtEvent();
            BeanUtils.copyProperties(k, copy);
            return copy;
        }).collect(Collectors.toList());
    }

    /**
     * 获取所有需要回传的衍生事件
     */
//    public List<ToutiaoExtEvent> getMatchExtEventList(ActionTypes actionTypes, UserEventReq userEventReq) {
//
//        // 获取所有低于 用户当前达成行为数值的
//        List<ToutiaoExtEvent> allLowerExtEventList = getAllLowerExtEventList(actionTypes.getEventTypes(), userEventReq);
//
//        // 获取首条高于 账户中配置值的，如果没有高于的，取深度最大的那一条
//        ToutiaoExtEvent firstHigherExtEvent = getFirstHigherExtEvent(actionTypes);
//
//        // 过滤深度低于 firstHigherExtEvent 的 (根据策划需求不需回传)
//        allLowerExtEventList = allLowerExtEventList.stream().filter(k-> k.getDepth() >= firstHigherExtEvent.getDepth()).collect(Collectors.toList());
//
//        allLowerExtEventList.add(firstHigherExtEvent);
//        // 过滤深度相同的
//        allLowerExtEventList = allLowerExtEventList.stream()
//                .filter(distinctByKey(ToutiaoExtEvent::getDepth))
//                .collect(Collectors.toList());
//
//        return allLowerExtEventList;
//    }

    /**
     * 获取所有低于 userEventReq 配置值的
     */
//    public List<ToutiaoExtEvent> getAllLowerExtEventList(String eventTypes, UserEventReq userEventReq) {
//
//        Map<String, String> reportValues = OcpcNewCallbackRuleValidateUtil.analyzeNewValueToMap(userEventReq.getActionValues());
//
//        if (eventTypes == null || !extEventApolloConfig.extEventEnableEventTypes.contains(eventTypes)) {
//            return null;
//        }
//
//        boolean reportEcpm = EVENT_TYPE_1_3.equals(eventTypes);
//
//        List<ToutiaoExtEvent> extEventList = configFromDB.get(eventTypes);
//
//        if (CollectionUtils.isEmpty(extEventList)) {
//            return null;
//        }
//
//        List<ToutiaoExtEvent> res = new ArrayList<>();
//
//        for (ToutiaoExtEvent toutiaoExtEvent : extEventList) {
//
//            Map<String, String> configurations = convertToMap(toutiaoExtEvent);
//
//            // ecpm规则的视频次数用等于判断；其余规则依然是大于等于
//            BiPredicate<Integer, Integer> watchVideoNumbersMatchFunction = reportEcpm ? Integer::equals
//                    : (report, configuration) -> report.compareTo(configuration) >= 0;
//
//            boolean watchVideoUpToSpecificTimes = matchSpecificRule(reportValues, configurations, WATCH_VIDEO.getTypeKey(),
//                    Integer::valueOf, watchVideoNumbersMatchFunction);
//
//            boolean arpuUpToSpecificValue = matchSpecificRule(reportValues, configurations, ARPU_ONE_DAY.getTypeKey(),
//                    BigDecimal::new, (report, configuration) -> report.compareTo(configuration) >= 0);
//
//            boolean ecpmUpToTargetValue = matchSpecificRule(reportValues, configurations, ECPM.getTypeKey(),
//                    Integer::valueOf, (report, configuration) -> report.compareTo(configuration) >= 0);
//
//            if (watchVideoUpToSpecificTimes && arpuUpToSpecificValue && ecpmUpToTargetValue) {
//                ToutiaoExtEvent copy = new ToutiaoExtEvent();
//                BeanUtils.copyProperties(toutiaoExtEvent, copy);
//                res.add(copy);
//            }
//        }
//
//        return res;
//    }

    /**
     * 获取首条高于 actionTypes 配置值的(向上取整)
     */
    public ThirdExtEvent getFirstHigherExtEvent(DspType dsp, ActionTypes actionTypes) {

        if (actionTypes == null || !extEventApolloConfig.extEventEnableEventTypes.contains(actionTypes.getEventTypes())) {
            return null;
        }

        // 已按深度正序排序
        List<ThirdExtEvent> sortedList = getAllExtEventConfigs(dsp, actionTypes.getEventTypes());

        if (CollectionUtils.isEmpty(sortedList)) {
            return null;
        }

        Map<String, String> reportValues = actionTypes.getMap();

        if (dsp.equals(DspType.TOUTIAO) && NUM_ARPU_RULE.equals(actionTypes.getEventTypes())) {
            //当头条账户配置存在非0.5倍数 等不规则arpu配置值，当用户达到关键行为后，要向下取整
            for (int i = sortedList.size() -1; i >= 0; i--) {
                ThirdExtEvent thirdExtEvent = sortedList.get(i);

                Map<String, String> configurations = convertToMap(thirdExtEvent);

                    boolean arpuUpToSpecificValue = matchSpecificRule(reportValues, configurations, ARPU_ONE_DAY.getTypeKey(),
                            BigDecimal::new, (report, configuration) -> report.compareTo(configuration) >= 0);

                    if (arpuUpToSpecificValue) {
                        return thirdExtEvent;
                    }
            }

        }else {
            // 深度正序遍历
            for (int i = 0; i < sortedList.size(); i++) {
                ThirdExtEvent thirdExtEvent = sortedList.get(i);

                Map<String, String> configurations = convertToMap(thirdExtEvent);

                if (NUM_ARPU_RULE.equals(actionTypes.getEventTypes())) {
                    // 1,2 改为无需比较视频次数
//                boolean watchVideoUpToSpecificTimes = matchSpecificRule(reportValues, configurations, WATCH_VIDEO.getTypeKey(),
//                        Integer::valueOf, (report, configuration) -> report.compareTo(configuration) >= 0);

                    boolean arpuUpToSpecificValue = matchSpecificRule(reportValues, configurations, ARPU_ONE_DAY.getTypeKey(),
                            BigDecimal::new, (report, configuration) -> report.compareTo(configuration) <= 0);

                    if (arpuUpToSpecificValue) {
                        return thirdExtEvent;
                    }
                } else if (NUM_ECPM_RULE.equals(actionTypes.getEventTypes())) {
                    // 看视频次数等于
                    boolean watchVideoUpToSpecificTimes = matchSpecificRule(reportValues, configurations, WATCH_VIDEO.getTypeKey(),
                            Integer::valueOf, (report, configuration) -> report.compareTo(configuration) == 0);

                    boolean ecpmUpToTargetValue = matchSpecificRule(reportValues, configurations, ECPM.getTypeKey(),
                            BigDecimal::new, (report, configuration) -> report.compareTo(configuration) <= 0);

                    if (watchVideoUpToSpecificTimes && ecpmUpToTargetValue) {
                        return thirdExtEvent;
                    }
                }
            }
        }
        // 不存在大于账户配置的时，取最后一个深度最高的
        return sortedList.get(sortedList.size() - 1);
    }


    /**
     * 获取首条低于 actionTypes 配置值的(向下取整)
     */
    public ThirdExtEvent getFirstLowerExtEvent(DspType dsp, ActionTypes actionTypes) {

        if (actionTypes == null || !extEventApolloConfig.extEventEnableEventTypes.contains(actionTypes.getEventTypes())) {
            return null;
        }

        // 已按深度正序排序
        List<ThirdExtEvent> sortedList = getAllExtEventConfigs(dsp, actionTypes.getEventTypes());

        if (CollectionUtils.isEmpty(sortedList)) {
            return null;
        }

        Map<String, String> reportValues = actionTypes.getMap();

        // 深度倒序遍历
        for (int i = sortedList.size() - 1; i >= 0; i--) {
            ThirdExtEvent thirdExtEvent = sortedList.get(i);

            Map<String, String> configurations = convertToMap(thirdExtEvent);

            if (NUM_ARPU_RULE.equals(actionTypes.getEventTypes())) {
                // 1,2 改为无需比较视频次数
//                boolean watchVideoUpToSpecificTimes = matchSpecificRule(reportValues, configurations, WATCH_VIDEO.getTypeKey(),
//                        Integer::valueOf, (report, configuration) -> report.compareTo(configuration) >= 0);

                boolean arpuUpToSpecificValue = matchSpecificRule(reportValues, configurations, ARPU_ONE_DAY.getTypeKey(),
                        BigDecimal::new, (report, configuration) -> report.compareTo(configuration) >= 0);

                if (arpuUpToSpecificValue) {
                    return thirdExtEvent;
                }
            } else if (NUM_ECPM_RULE.equals(actionTypes.getEventTypes())) {
                // 看视频次数等于
                boolean watchVideoUpToSpecificTimes = matchSpecificRule(reportValues, configurations, WATCH_VIDEO.getTypeKey(),
                        Integer::valueOf, (report, configuration) -> report.compareTo(configuration) == 0);

                boolean ecpmUpToTargetValue = matchSpecificRule(reportValues, configurations, ECPM.getTypeKey(),
                        BigDecimal::new, (report, configuration) -> report.compareTo(configuration) >= 0);

                if (watchVideoUpToSpecificTimes && ecpmUpToTargetValue) {
                    return thirdExtEvent;
                }
            }
        }

        // 不存在小于账户配置的时，取第一个深度最低的
        return sortedList.get(0);
    }


    private Map<String, String> convertToMap(ThirdExtEvent thirdExtEvent) {
        Map<String, String> configurations = Maps.newHashMap();
        String[] eventTypes = StringUtils.splitByWholeSeparator(thirdExtEvent.getEventTypes(), ",");
        String[] eventValues = StringUtils.splitByWholeSeparator(thirdExtEvent.getEventValues(), ",");
        for (int index = 0; index < eventTypes.length; index++) {
            configurations.put(eventTypes[index], eventValues[index]);
        }
        return configurations;
    }

    /**
     * 是否满足特定种类的规则
     *
     * @param reportValues   上报的值集合
     * @param configurations 配置值集合
     * @param key            配置key
     * @param valueMapper    数值转换行为
     * @param matchFunction  匹配函数
     * @param <T>            数值类型
     * @return true-满足该规则
     */
    private static <T> boolean matchSpecificRule(Map<String, String> reportValues, Map<String, String> configurations,
                                                 String key, Function<String, T> valueMapper, BiPredicate<T, T> matchFunction) {

        String reportValue = reportValues.get(key);
        String configurationValue = configurations.get(key);

        if (reportValue == null && configurationValue == null) {
            return true;
        }

        if (reportValue != null && configurationValue != null) {

            T transformedReportValue = valueMapper.apply(reportValue);
            T transformedConfigurationValue = valueMapper.apply(configurationValue);

            return matchFunction.test(transformedReportValue, transformedConfigurationValue);
        }

        return false;
    }

    private Stream<AccountAction> convert2AccountAction(ThirdExtEvent extEvent) {
        try {
            // 无配置
            if (StringUtils.isBlank(extEvent.getEventTypes())) {
                return null;
            }

            if (StringUtils.isBlank(extEvent.getEventValues()) || StringUtils.isBlank(extEvent.getEventTypes())) {
                return Stream.empty();
            }

            return AuthKuaishouAdvertiserService.buildEventStandardConsideringMultiEcpm(extEvent.getEventTypes(),
                    extEvent.getEventValues(), null,
                    null);

        } catch (Exception e) {
            log.error("头条衍生事件配置信息解析出错 " + extEvent.getId(), e);
        }

        return null;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return object -> seen.putIfAbsent(keyExtractor.apply(object), Boolean.TRUE) == null;
    }

    /**
     * 获取所有匹配的衍生事件列表
     * @return
     */
    public List<ThirdExtEvent> getAllMatchExtEvent(ThirdAdvertiser advertiser, UserEventReq userEventReq) {
        try {

            // 不需判断是否首次满足条件
            BooleanSupplier firstTimeJudge = () -> true;

            // 已按深度正序排序
            List<ThirdExtEvent> allExtEvents = getAllExtEventConfigs(advertiser.getDsp(), advertiser.getEventTypes());

            // 对于1,2账户，衍生事件的次数条件改为和账户的次数条件一致
            if (NUM_ARPU_RULE.equals(advertiser.getEventTypes())) {
                allExtEvents.forEach(k-> k.setEventValues(genEventValues(k.getEventValues(), advertiser.getEventValues())));
            }

            List<ThirdExtEvent> matchExtEvents = new ArrayList<>();

            for (ThirdExtEvent extEvent : allExtEvents) {
                if (matchNewRule(userEventReq, extEvent.getEventTypes(),
                        extEvent.getEventValues(), firstTimeJudge, advertiser.getDsp().cnName,
                        advertiser.getProduct(), advertiser.getAdvertiserId(), advertiser.getMaxAccumulatePeriod())) {
                    matchExtEvents.add(extEvent);
                } else {
                    // 已按深度正序排序的情况下，首条不满足的之后，那些深度更大的无需继续判断
                    break;
                }
            }

            return matchExtEvents;
        } catch (Exception e) {
            log.error("getAllMatchExtEvent error ", e);
        }
        return null;
    }

    private static String genEventValues(String extValues, String accountValues) {
        return StringUtils.substringBefore(accountValues, ",") + "," + StringUtils.substringAfter(extValues, ",");
    }

    /**
     * 获取需要回传的衍生事件
     */
    public List<ThirdExtEvent> getNeedCallbackExtEventFor_1_2(DspType dsp, List<ThirdExtEvent> extEventList, OcpcEvent ocpcEvent, Supplier<Pair<Boolean, ActionTypes>> checkExtEventCount) {

        boolean alreadyCallbackIsGaConvert = false;

        if (ocpcEvent != null && !CollectionUtils.isEmpty(ocpcEvent.getExtEvents())) {
            // 过滤掉已回传过的衍生事件
            extEventList = filterExtEvent(extEventList, ocpcEvent.getExtEvents());
            alreadyCallbackIsGaConvert = ocpcEvent.getExtEvents().stream().anyMatch(k->Objects.equals(k.getIsGaConvert(), 1L));
        }

        if (CollectionUtils.isEmpty(extEventList)) {
            return new ArrayList<>();
        }

        // 若尚未回传 is_ga_convert = 1 的
        if (!alreadyCallbackIsGaConvert) {
            // 判断是否符合账户的配置条件
            Pair<Boolean, ActionTypes> actionTypesPair = checkExtEventCount.get();

            // 如果符合账户的配置条件
            if (actionTypesPair.getLeft()) {
                // 获取首条低于 账户中配置值的，如果没有高于的，取深度最大的那一条，作为回传衍生事件时is_ga_convert=1的那一条
                ThirdExtEvent firstHigherExtEvent = getFirstHigherExtEvent(dsp, actionTypesPair.getRight());
                // 深度相同的那一条 is_ga_convert 设为1
                extEventList.stream()
                        .filter(k -> Objects.equals(k.getDepth(), firstHigherExtEvent.getDepth()))
                        .findFirst().ifPresent(k-> k.setIsGaConvert(1L));
            }
        }

        return extEventList;
    }

    public List<ThirdExtEvent> getNeedCallbackExtEventFor_1_3(DspType dsp, ActionTypes ecpmLevel10, List<ThirdExtEvent> extEventList, OcpcEvent ocpcEvent) {

        boolean alreadyCallbackIsGaConvert = false;

        if (ocpcEvent != null && !CollectionUtils.isEmpty(ocpcEvent.getExtEvents())) {
            // 过滤掉已回传过的衍生事件
            extEventList = filterExtEvent(extEventList, ocpcEvent.getExtEvents());
            alreadyCallbackIsGaConvert = ocpcEvent.getExtEvents().stream().anyMatch(k->Objects.equals(k.getIsGaConvert(), 1L));
        }

        if (CollectionUtils.isEmpty(extEventList)) {
            return new ArrayList<>();
        }

        // 若尚未回传 is_ga_convert = 1 的
        if (!alreadyCallbackIsGaConvert) {
            // 获取首条高于 账户中配置值的，如果没有高于的，取深度最大的那一条，作为回传衍生事件时is_ga_convert=1的那一条
            ThirdExtEvent firstHigherExtEvent = getFirstHigherExtEvent(dsp, ecpmLevel10);
            // 深度相同的那一条 is_ga_convert 设为1
            ThirdExtEvent isGa1Depth = extEventList.stream()
                    .filter(k -> Objects.equals(k.getDepth(), firstHigherExtEvent.getDepth()))
                    .findFirst().orElse(null);
            if (isGa1Depth != null) {
                isGa1Depth.setIsGaConvert(1L);
            } else {
                // 未满足is_ga=1的档位时，由于已达成关键行为，也回传对应的is_ga=1的档位
                firstHigherExtEvent.setIsGaConvert(1L);
                extEventList.add(firstHigherExtEvent);
            }
        }

        return extEventList;
    }

    private List<ThirdExtEvent> filterExtEvent(List<ThirdExtEvent> extEventList, List<ThirdExtEvent> alreadyCallbackExtEvents) {
        if (CollectionUtils.isEmpty(alreadyCallbackExtEvents)) {
            return extEventList;
        }

        List<Long> depths = alreadyCallbackExtEvents.stream().map(k -> k.getDepth()).collect(Collectors.toList());

        return extEventList.stream().filter(k-> !depths.contains(k.getDepth())).collect(Collectors.toList());
    }

    public static ActionTypes getEcpmLevel10(String eventValues) {
        String[] numAndEcpm = eventValues.split(",");
        List<String> nums = Arrays.asList(numAndEcpm[0].split("/"));
        List<String> ecpms = Arrays.asList(numAndEcpm[1].split("/"));
        int index = nums.indexOf("10");
        Map<String, String> level10 = new HashMap<>();
        level10.put("1", "10");
        level10.put("3", ecpms.get(index));
        return ActionTypes.build(level10);
    }

    public static UserEventReq getFixedUserEventReq(UserEventReq userEventReq, ActionTypes ecpmLevel10) {
        UserEventReq fixed = new UserEventReq();
        BeanUtils.copyProperties(userEventReq, fixed);
        // 当上传的次数小于10次时，按10次算
        /**
         2、ecpm账户只回传次数带有10次的账户，组合形式均按10次处理
         a）is_ga=1均按账户配置的10次对应的ecpm为基准
         b）实际值小于10次且满足账户配置时把实际值当作账户配置的10次及对应ecpm值传衍生事件的is_ga=1及is_ga=1以下的配置
         c）实际值大于10次且满足账户配置时仅把实际值的次数当作10，回传衍生事件
         */
        String actionValues = fixed.getActionValues();
        // 举例: 假设ecpmLevel10 为 10,300  1-8,3-500 需要修正为 1-10,3-500  1-11,3-200 修正为 1-10,3-300
        String[] numAndEcpm = actionValues.split(",");
        Integer num = Integer.parseInt(numAndEcpm[0].split("-")[1]);

        // 等于10时不需要修改
        if (num < 10) {
            // 小于10时，需要将次数替换为10
            fixed.setActionValues("1-10," + numAndEcpm[1]);
        } else if (num > 10) {
            // 大于时，需要将ecpm替换为 ecpmLevel10 里的配置值
            fixed.setActionValues("1-10,3-" + ecpmLevel10.getMap().get("3"));
        }
        return fixed;
    }

}
