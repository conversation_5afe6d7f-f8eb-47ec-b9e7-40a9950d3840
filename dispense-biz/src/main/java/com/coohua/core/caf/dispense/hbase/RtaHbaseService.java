package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.utils.ConstCls;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class RtaHbaseService {
    @Resource
    private Connection toutiaoClickConnection;
    private String rtaTTCkName = "rta_tt_click";
    private String rtaKsCkName = "rta_ks_click";
    private String rtaTXCkName = "rta_tx_click";
    private String rtaOtCkName = "rta_ot_click";

//    @PostConstruct
    public HTableDescriptor createOcpcTable() {
        createHTbale(rtaTTCkName);
        createHTbale(rtaKsCkName);
        createHTbale(rtaTXCkName);
        createHTbale(rtaOtCkName);
        return null;
    }

    private void createHTbale(String tableNmae){
        try (Admin admin = toutiaoClickConnection.getAdmin()) {
            // 建表
            try {
                HTableDescriptor htdDb = admin.getTableDescriptor(TableName.valueOf(tableNmae));
            } catch (TableNotFoundException te) {
                HTableDescriptor htd = new HTableDescriptor(TableName.valueOf(tableNmae));
                HColumnDescriptor hcd = new HColumnDescriptor("family");
                hcd.setTimeToLive(4 * 24 * 60 * 60); // 设置TTL过期时间4天 4 * 24 * 60 * 60
                htd.addFamily(hcd);
                admin.createTable(htd);
            }
        } catch (IOException e) {
            log.error("", e);
        }
    }
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    public void saveRtaClicks(DspType dspType,ToutiaoClick click){
        if (!ocpcSwitcher.writeHbaseSwitch) {
            return;
        }
        String product = click.getProduct();
        if(ocpcSwitcher.rtaProjectNames.contains(click.getProduct()) && ocpcSwitcher.rtaProjectAccounts.contains(click.getAccountId())){
            log.info("存储rtaclick优先归因 "+click.getProduct()+" "+click.getAccountId()+" "+click.getOaid()+" "+click.getOcpcDeviceId());
        }else{
            return;
        }
        String tableName = getTableName(dspType);

        String keyPrefix = dspType.name+"@";
        try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(tableName))) {
            List<Put> putList = new ArrayList<>();
            String os = click.getOs();
            String oaid2 = click.getOaid2();
            String idfa2 = click.getIdfa2();

            String deviceId = click.getOcpcDeviceId();

            if (StringUtils.isNotBlank(deviceId) && !ConstCls.zeroMd5.equals(deviceId)) {
                String key1 = keyPrefix+RedisKeyConstants.getClickDeviceKey(product, os, deviceId);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }
            if (StringUtils.isNotBlank(idfa2) && !ConstCls.zeroMd5.equals(idfa2)) {
                String key1 = keyPrefix+RedisKeyConstants.getClickIdfa2Key(product, os, idfa2);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }
            if (StringUtils.isNotBlank(click.getOaid())) {
                String key1 = keyPrefix+RedisKeyConstants.getClickOaIdKey(product, os, click.getOaid());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }
            if (StringUtils.isNotBlank(oaid2)) {

                String key1 = keyPrefix+RedisKeyConstants.getClickOaId2Key(product, os, oaid2);
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if (StringUtils.isNotBlank(click.getCaid()) && StringUtils.equalsIgnoreCase("ios",os)) {
                String key1 = keyPrefix+RedisKeyConstants.getClickCaidKey(product,os,click.getCaid());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if (StringUtils.isNotBlank(click.getCaid2()) && StringUtils.equalsIgnoreCase("ios",os)) {
                String key1 = keyPrefix+RedisKeyConstants.getClickCaid2Key(product,os,click.getCaid2());
                Put put = new Put(Bytes.toBytes(key1));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), Bytes.toBytes(JSONObject.toJSONString(click)));
                putList.add(put);
            }

            if(putList.size()>0){
                table.put(putList);
            }
        } catch (Exception e) {
            log.error("hbase add error ", e);
        }
    }

    private String getTableName(DspType dspType){
        String tableName = rtaOtCkName;
        if(DspType.TOUTIAO.equals(dspType)){
            tableName = rtaTTCkName;
        }else if(DspType.GUANGDIANTONG.equals(dspType)){
            tableName = rtaTXCkName;
        }else if(DspType.KUAISHOU.equals(dspType)){
            tableName = rtaKsCkName;
        }
        return tableName;
    }

    public ToutiaoClick getHbaseClickByDevice(DspType dspType ,UserEventReq request) {
        String keyPrefix = dspType+"@";
        String tableName = getTableName(dspType);
        ToutiaoClick click = null;
        try (Table table = toutiaoClickConnection.getTable(TableName.valueOf(tableName))) {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("hbaseGetClick product is null ! {}", JSONObject.toJSONString(request));
                return click;
            }

            if(StringUtils.equalsIgnoreCase(request.getOs(),"ios")){
                if (click == null && StringUtils.isNotBlank(request.getCaid()) && "ios".equals(request.getOs().toLowerCase())) {
                    Get get = new Get(Bytes.toBytes(keyPrefix+RedisKeyConstants.getClickCaidKey(product,request.getOs(),request.getCaid())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        log.info("caid归因成功 " + product);
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }
                if (click == null && StringUtils.isNotBlank(request.getCaid2())) {
                    Get get = new Get(Bytes.toBytes(keyPrefix+RedisKeyConstants.getClickCaid2Key(product,request.getOs(),request.getCaid2())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        log.info("caid2归因成功 " + product);
                        request.setGuiType(GuiyingType.caid.name);
                    }
                }
                /**
                 * 按idfa_md5查询
                 */
                if (click == null && StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                    Get get = new Get(Bytes.toBytes(keyPrefix+RedisKeyConstants.getClickIdfa2Key(product, request.getOs(), request.getIdfa2())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.idfa.name);
                    }
                }
            }else if(StringUtils.equalsIgnoreCase(request.getOs(),"android")){
                /**
                 * click不存在再查oaId
                 */
                if (click == null && StringUtils.isNotBlank(request.getOaid())) {
                    Get get = new Get(Bytes.toBytes(keyPrefix+RedisKeyConstants.getClickOaIdKey(product, request.getOs(), request.getOaid())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.oaid.name);
                    }

                }
                if (click == null && StringUtils.isNotBlank(request.getOaid2())) {
                    Get get = new Get(Bytes.toBytes(keyPrefix+RedisKeyConstants.getClickOaId2Key(product, request.getOs(), request.getOaid2())));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.oaid.name);
                    }
                }

                if (click == null && StringUtils.isNotBlank(request.getOcpcDeviceId())) {
                    String key1 = keyPrefix+RedisKeyConstants.getClickDeviceKey(product, request.getOs(), request.getOcpcDeviceId());
                    Get get = new Get(Bytes.toBytes(key1));
                    Result res = table.get(get);
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        click = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.oaid.name);
                    }
                }

            }

            if (click != null) {
                log.info("hbaseGetClick success req={} ,rsp={}", JSONObject.toJSONString(request), JSONObject.toJSONString(click));
            }
        } catch (Exception e) {
            log.error("hbaseGetClick error ", e);
        }
        return click;
    }

}
