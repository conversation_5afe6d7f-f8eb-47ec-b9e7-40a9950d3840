package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.TencentLtv;
import com.coohua.core.caf.dispense.ocpc.mapper.TencentLtvMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TencentLtvService extends ServiceImpl<TencentLtvMapper, TencentLtv> {

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Resource(name = "ocpcTencentLtvPool")
    ThreadPoolTaskExecutor poolTaskExecutor;

    public TencentLtv saveTencentLtv(UserEventReq userEventReq, OcpcEvent ocpcEvent, String reqUrl, String reqBody, String rspStr, Integer ecpm, Long eventTime) {
        try {
            TencentLtv extEvent = new TencentLtv();
            Date date = new Date();
            extEvent.setAppId(userEventReq.getAppId());
            extEvent.setCreateTime(date);
            extEvent.setUpdateTime(date);
            extEvent.setUserId(userEventReq.getUserId());
            extEvent.setProduct(userEventReq.getProduct());

            extEvent.setClickId(ocpcEvent.getId());
            extEvent.setAccountId(ocpcEvent.getAccountId());
            extEvent.setCallbackUrl(ocpcEvent.getCallbackUrl());

            extEvent.setOaid(userEventReq.getOaid());
            extEvent.setSourceOaid(userEventReq.getOaid());
            extEvent.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
            extEvent.setSourceDeviceId(userEventReq.getSourceDeviceId());
            extEvent.setOs(userEventReq.getOs());
            extEvent.setReqUrl(reqUrl);
            extEvent.setReqBody(reqBody);
            extEvent.setReqRsp(rspStr);

            extEvent.setEcpm(ecpm);
            extEvent.setEventTime(new Date(eventTime));


            try {
                asyncSave(extEvent);
//                save(extEvent);
            } catch (Exception e) {
                log.error("", e);
            }

            return extEvent;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    private void asyncSave(TencentLtv tencentLtv) {
        if (ocpcSwitcher.writeTencentLtvSwitch) {
            poolTaskExecutor.execute(() -> {
                try {
                    save(tencentLtv);
                }catch (Exception e){
                    log.error("异步写入tencent_ltv异常", e);
                }
            });
        }
    }


}
