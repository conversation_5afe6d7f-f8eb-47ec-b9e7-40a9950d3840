package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

@Data
public class TencentClickReqV2 implements IClickReq {
    private String dsp;
    private String product;
    private String os;
    private String account_id;//账户ID

    private String click_id;//(点击ID)
    private Long click_time;//(点击时间)
    private String muid;//(设备ID)
    private String oaid;//(移动终端补充设备标识)
    private String hash_oaid;//(移动终端补充设备标识)
    private String mac;//(mac地址)
    private String android_id;//(安卓ID)
    private String ip;//(IP地址)
    private String user_agent;//(用户代理)
    private String model;//(手机型号)
    private String appid;//(应用ID)
    private String campaign_id;//(推广计划ID)
    private String adgroup_id;//(广告ID)
    private String creative_id;//(素材ID)
    private String agency_id;//(代理商ID)
    private String process_time;//(请求时间)
    private String adgroup_name;//(广告名称)
    private String click_sku_id;//(点击的SKUID)
    private String product_type;//(商品类型)
    private String request_id;//(请求ID)
    private String callback;//(请求ID)
    private String ipv6;//(IPV6地址)
    private String wechat_openid;

    private String caid;
    private String caid2;
    private String element_info; //__ELEMENT_INFO__	素材信息	广告中被曝光的所有素材 [{"id":"123"},{"id":"456"},{"id":"789"}]
    @Override
    public String getUa() {
        return user_agent;
    }

    @Override
    public void setUa(String ua) {
        this.user_agent = ua;
    }
}
