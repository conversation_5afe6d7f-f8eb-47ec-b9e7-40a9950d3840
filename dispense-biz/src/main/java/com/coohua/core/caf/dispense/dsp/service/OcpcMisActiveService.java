package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.OcpcMisActive;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.mapper.OcpcMisActiveMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.WxActiveReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Service
@Slf4j
public class OcpcMisActiveService extends ServiceImpl<OcpcMisActiveMapper, OcpcMisActive> {
    @Resource(name = "ocpcTo8Eevent")
    ThreadPoolTaskExecutor ocpcTo8Eevent;

    public void saveOcpcMisActive(WxActiveReq wxActiveReq){
        ocpcTo8Eevent.execute(()->{
            try {
                OcpcMisActive ocpcMisActive = new OcpcMisActive();
                Date date = new Date();
                ocpcMisActive.setCreateTime(date);
                ocpcMisActive.setUpdateTime(date);
                ocpcMisActive.setProduct(wxActiveReq.getProduct());
                ocpcMisActive.setOs(wxActiveReq.getOs());
                ocpcMisActive.setUserId(wxActiveReq.getUserId());
                ocpcMisActive.setOaid(wxActiveReq.getRequestId());
                ocpcMisActive.setDsp("dyop");

                ocpcMisActive.setGid(wxActiveReq.getAdvertiser_id());
                ocpcMisActive.setCid(wxActiveReq.getCreative_id());
                ocpcMisActive.setMac(wxActiveReq.getClue_token());
                ocpcMisActive.setCaid(wxActiveReq.getRequestId());
                ocpcMisActive.setIp(wxActiveReq.getRequestId());
                ocpcMisActive.setClickTime(date);
                save(ocpcMisActive);

                log.info("wx买量插入归因数据完成 "+ocpcMisActive.getUserId()+" ");
            }catch (Exception e){
                log.error("插入未归因数据",e);
            }
        });
    }
    public void saveOcpcMisActive(UserEventReq userEventReq, ToutiaoClick toutiaoClick){
        ocpcTo8Eevent.execute(()->{
            try {
                OcpcMisActive ocpcMisActive = new OcpcMisActive();
                Date date = new Date();
                ocpcMisActive.setChannel(userEventReq.getPkgChannel());
                ocpcMisActive.setCreateTime(date);
                ocpcMisActive.setUpdateTime(date);
                ocpcMisActive.setDeviceId(userEventReq.getOcpcDeviceId());
                ocpcMisActive.setMac(userEventReq.getMac());
                ocpcMisActive.setProduct(userEventReq.getProduct());
                ocpcMisActive.setOs(userEventReq.getOs());
                ocpcMisActive.setUserId(userEventReq.getUserId());
                ocpcMisActive.setCaid(userEventReq.getCaid());
                ocpcMisActive.setIp(userEventReq.getIp());
                ocpcMisActive.setOaid(userEventReq.getOaid());
                if(toutiaoClick!=null){
                    ocpcMisActive.setClickTime(toutiaoClick.getCreateTime());
                    ocpcMisActive.setDsp(toutiaoClick.getDsp());
                    ocpcMisActive.setCid(toutiaoClick.getCid());
                    ocpcMisActive.setGid(toutiaoClick.getGid());
                    ocpcMisActive.setPid(toutiaoClick.getPid());
                }else{
                    ocpcMisActive.setDsp("no");
                }
                save(ocpcMisActive);

                log.info("插入未归因到的数据 "+userEventReq.getProduct()+" "+userEventReq.getPkgChannel()+
                        " "+userEventReq.getUserId()+" "+userEventReq.getOs()+" "+userEventReq.getOaid()+"");
            }catch (Exception e){
                log.error("插入未归因数据 {}", JSONObject.toJSONString(userEventReq),e);
            }
        });
    }
}
