package com.coohua.core.caf.dispense.dto.req;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Accessors(chain = true)
public class KuaishouExtDefineProps {

    private Long is_convert;
    private Long depth;
    private Long key_action_category1;
    private Long key_action_threshold1;
    private Long key_action_category2;
    private Long key_action_threshold2;
    private Long key_action_category3;
    private Long key_action_threshold3;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
