package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dto.req.ToutiaoExtEventProps;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.StandardHttpRequestRetryHandler;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.httpclient.HttpBioClient;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_URL_BODY;

@Component
@Slf4j
public class ToutiaoCallService {
    @Autowired
    private AlertService alertService;
    @Autowired
    private HttpBioClient httpBioClient;
    @Autowired
    ToutiaoClickService toutiaoClickService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    public static final String ToutiaoCallbackUrlV2 = "https://analytics.oceanengine.com/api/v2/conversion";
    public static final String ToutiaoCallbackUrl_XGLT = "https://ad.oceanengine.com/track/activate/?callback=";

    public void touTiaoActive(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {

        //报今日点击时间的次留
        logForCiLiu(userEventReq, toutiaoClick, userEvent);

        boolean isDrChangeOnce = false;
        //指定账户关键行为回传激活
        if (ocpcSwitcher.toutiaoToActiveAccount.contains(toutiaoClick.getAccountId()) ) {
            if (ToutiaoEventTypeEnum.ACTIVATE_APP.value.equals(userEventReq.getEventType())) {
                log.info("头条ocpc指定账户 不回传除关键行为外的事件" + toutiaoClick.getAccountId());
                userEvent.setReqRsp("头条ocpc指定账户 不回传除关键行为外的事件");
                return;
            } else if (ToutiaoEventTypeEnum.KEY_EVENT.value.equals(userEventReq.getEventType())) {
                userEventReq.setEventType(ToutiaoEventTypeEnum.ACTIVATE_APP.value);
                isDrChangeOnce = true;
                log.info("头条ocpc指定账户 关键行为回传到激活 {}", toutiaoClick.getAccountId());
            }
        }

        // 头条回传v2版本，点击事件回传的callback是加密字符串而不是链接
        if (isToutiaoCallbackV2(toutiaoClick)) {
            ToutiaoExtEventProps toutiaoExtEventProps = null;
            if(ToutiaoEventTypeEnum.PURCHASE.value.equals(userEventReq.getEventType()) && userEventReq.getPayAmount()!=null && userEventReq.getPayAmount()>0){
                log.info("ocpc支付 头条2.0 "+JSON.toJSONString(userEventReq));
                toutiaoExtEventProps = new ToutiaoExtEventProps();
                toutiaoExtEventProps.setPay_amount(userEventReq.getPayAmount());
            }
            AuthToutiaoAdvertiser advertiser = authToutiaoAdvertiserService.getAdvertiser(toutiaoClick.getAccountId());
            boolean isXGLTAccount = advertiser != null && Objects.equals(advertiser.getAccountType(), 3);
            if(isXGLTAccount) {
                if (toutiaoClick != null && StringUtils.isNotBlank(toutiaoClick.getCallbackUrl())) {
                    try {
                        if (!Objects.equals(advertiser.getDelFlag(), 0)) {
                            log.info("头条回传星广联投账户 已关闭该账户回传 {}", toutiaoClick.getAccountId());
                            userEvent.setReqRsp("头条回传星广联投账户 已关闭该账户回传");
                            return;
                        }
                        //event_type=0表示激活，event_type=1表示注册，event_type=6，表示次留；event_type=2表示付费
                        if (!Objects.equals(userEventReq.getEventType(), 0)
                                && !Objects.equals(userEventReq.getEventType(), 1)
                                && !Objects.equals(userEventReq.getEventType(), 6)
                                && !Objects.equals(userEventReq.getEventType(), 2)) {
                            log.info("头条回传星广联投账户 仅回传0,1,6,2 {} {}", toutiaoClick.getAccountId(), userEventReq.getEventType());
                            userEvent.setReqRsp("头条回传星广联投账户 仅回传0,1,6,2");
                            return;
                        }
                        String reqUrl = ToutiaoCallbackUrl_XGLT + toutiaoClick.getCallbackUrl() + "&event_type=" + userEventReq.getEventType();
                        if ("ios".equals(userEventReq.getOs())) {
                            reqUrl = reqUrl + "&idfv=" + userEventReq.getIdfv();
                        } else if ("android".equals(userEventReq.getOs())) {
                            reqUrl = reqUrl + "&android_id=" + (StringUtils.isNotBlank(userEventReq.getOriginalAndroidId()) ? userEventReq.getOriginalAndroidId() : userEventReq.getSourceDeviceId());
                        }
                        log.info("头条回传星广联投账户 {} 回传类型 {}", toutiaoClick.getAccountId(), userEventReq.getEventType());
                        touTiaoActive(userEventReq, toutiaoClick, userEvent, reqUrl);
                    } catch (Exception e) {
                        log.error("头条回调生成url异常", e);
                    }
                }else {
                    alertService.report(userEventReq.getProduct(), "0", "匹配不到点击事件");
                    if(toutiaoClick != null && StringUtils.isBlank(toutiaoClick.getCallbackUrl())){
                        log.warn("toutiaoClickV2 星广联投 callbackurl "+JSON.toJSONString(toutiaoClick));
                    }
                }
            }else {
                touTiaoActiveV2(userEventReq, toutiaoClick, userEvent, toutiaoExtEventProps);
            }
            return;
        }

        String reqUrl = null;
        try {
            if (toutiaoClick != null && StringUtils.isNotBlank(toutiaoClick.getCallbackUrl())) {

                int eventType = userEventReq.getEventType();

                if (ocpcSwitcher.toutiaoLiveAccount.contains(toutiaoClick.getAccountId()) && ToutiaoEventTypeEnum.KEY_EVENT.value.equals(userEventReq.getEventType())) {
                    eventType = ToutiaoEventTypeEnum.PURCHASE.value;
                    log.info("头条回传直播账户 关键行为回传到付费 {}", toutiaoClick.getAccountId());
                }

                // 若是头条达人账户，仅将关键行为回传到激活上
                AuthToutiaoAdvertiser advertiser = authToutiaoAdvertiserService.getAdvertiser(toutiaoClick.getAccountId());
                boolean isDaRenAccount = advertiser != null && Objects.equals(advertiser.getAccountType(), 2);
                if (isDaRenAccount) {
                    if (!Objects.equals(advertiser.getDelFlag(), 0)) {
                        log.info("头条回传达人账户 已关闭该账户回传 {}", toutiaoClick.getAccountId());
                        userEvent.setReqRsp("头条回传达人账户 已关闭该账户回传");
                        return;
                    }
                    Date date = DateUtils.parse("2022-12-26 10:00:00",DateUtils.PATTERN_YHMS);
                    Date date2 = DateUtils.parse("2025-06-18 10:30:00",DateUtils.PATTERN_YHMS);
                    Set<String> toutiaoXingtuCallBackProductList = ocpcSwitcher.toutiaoXingtuCallBackProductList;
                    if(advertiser.getCreateTime().after(date) && advertiser.getCreateTime().before(date2)
                            && !toutiaoXingtuCallBackProductList.contains(advertiser.getAdvertiserId().toString())
                            && (isDrChangeOnce || ToutiaoEventTypeEnum.KEY_EVENT.value.equals(userEventReq.getEventType()))){
                        if (isDrChangeOnce) {
                            eventType = ToutiaoEventTypeEnum.ACTIVATE_APP.value;
                            log.info("头条达人账户 配置特殊" + advertiser.getAdvertiserId() + " 回传ACTIVATE_APP");
                        } else {
                            eventType = ToutiaoEventTypeEnum.PURCHASE.value;
                            log.info("头条达人账户 " + advertiser.getAdvertiserId() + " 回传PURCHASE");
                        }
                    } else if (ToutiaoEventTypeEnum.KEY_EVENT.value.equals(userEventReq.getEventType())) {
                        eventType = ToutiaoEventTypeEnum.ACTIVATE_APP.value;
                    } else {
                        log.info("头条回传达人账户 不回传除关键行为外的事件 {}", toutiaoClick.getAccountId());
                        userEvent.setReqRsp("头条回传达人账户 不回传除关键行为外的事件");
                        return;
                    }
                }

                // 不必在乎返回值
                reqUrl = toutiaoClick.getCallbackUrl() + "&event_type=" + eventType + "&conv_time=" + (System.currentTimeMillis() / 1000);
                if(userEventReq.getPayAmount()!=null && userEventReq.getPayAmount()>0 && ToutiaoEventTypeEnum.PURCHASE.value==userEventReq.getEventType()){
                    log.info("ocpc支付 头条1.0 "+JSON.toJSONString(userEventReq));
                    String payDetail = URLEncoder.encode("{\"pay_amount\":"+userEventReq.getPayAmount()+"}");
                    reqUrl = reqUrl+"&props="+payDetail;
                }
            }
        } catch (Exception e) {
            log.error("头条回调生成url异常", e);
        }

        touTiaoActive(userEventReq, toutiaoClick, userEvent, reqUrl);
    }

    private void touTiaoActive(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent, String reqUrl) {

        try {
            URI uri = null;
            HttpGet httpGet = null;
            HttpResponse response = null;
            String respContent = null;

            if (toutiaoClick != null && StringUtils.isNotBlank(toutiaoClick.getCallbackUrl())) {
                // 不必在乎返回值
//                reqUrl = toutiaoClick.getCallbackUrl() + "&event_type=" + userEventReq.getEventType()+"&conv_time="+(System.currentTimeMillis()/1000);
                uri = new URI(reqUrl);
                httpGet = new HttpGet(uri);
                String requestId = userEventReq.getUserId() + userEventReq.getProduct() + userEventReq.getEventType() + System.currentTimeMillis();

                if(userEventReq.getEventType()==0){
                    log.info("账户"+toutiaoClick.getAccountId()+" 回调头条 产品为"+toutiaoClick.getProduct()+" 设备 "+toutiaoClick.getOcpcDeviceId());
                }

                if(ocpcSwitcher.toNewHttpClient){
                    respContent = sendToutiaoByClient(reqUrl,toutiaoClick, userEventReq.getEventType());
                }else{

                    response = httpBioClient.execute(requestId, httpGet, 7000, TimeUnit.MILLISECONDS);
                    if (response.getStatusLine().getStatusCode() == 200) {
                        respContent = EntityUtils.toString(response.getEntity(), "UTF-8");
                        if(userEventReq.getEventType()==0){
                            log.info("回调成功 账户"+toutiaoClick.getAccountId()+" 回调头条 产品为"+toutiaoClick.getProduct()+" 设备 "+toutiaoClick.getOcpcDeviceId());
                        }
                        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), ToutiaoEventTypeEnum.getReportName(userEvent.getEventType(), true));
                    } else {
                        alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), ToutiaoEventTypeEnum.getReportName(userEvent.getEventType(), false));
                        respContent = "请求失败，code = " + response.getStatusLine().getStatusCode();
                    }
                }
                log.info("[touTiaoActive userEventReq = {} ] 回调 {}  结果为 {} advertiserId {}", JSONObject.toJSONString(userEventReq) ,reqUrl, respContent,toutiaoClick.getAccountId());

            } else {
                alertService.report(userEventReq.getProduct(), "0", "匹配不到点击事件");
                if(toutiaoClick != null && StringUtils.isBlank(toutiaoClick.getCallbackUrl())){
                    log.warn("toutiaoClick callbackurl "+JSON.toJSONString(toutiaoClick));
                }
            }
            if(userEvent!=null){
                userEvent.setReqRsp(respContent);
                userEvent.setReqUrl(reqUrl);
            }
        } catch (Exception e) {
            log.error("头条回传异常 reqUrl: " + reqUrl, e);
        }
    }

    public static CloseableHttpClient getHttpClient(){
        SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(
                SSLContexts.createDefault(),
                new String[] { "TLSv1.2"},
                null,
                SSLConnectionSocketFactory.getDefaultHostnameVerifier());
        CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).setRetryHandler(new StandardHttpRequestRetryHandler(2,false)).build();
        return httpclient;
    }
    @Value("${ocpc.toutiao.retryNum:3}")
    public int retryNum=3;
    public String resendUrl(String reqUrl, CloseableHttpClient client) throws Exception{
        String html = null;
        for(int i=0;i<=retryNum;i++){
            try {
                HttpConfig httpConfig = HttpConfig.custom().timeout(2000);
                html = HttpClientUtil.get(httpConfig.url(reqUrl).client(client));
                if(i!=0){
                    log.info("头条回调重试成功  第"+i+"次"+html);
                }
                return html;
            }catch (HttpProcessException e){
                if(i==retryNum){
                    log.info("头条回调超时",e.getMessage());
                }
            }
        }
        return html;
    }

    public String resendUrlPost(String reqUrl,String postJson,CloseableHttpClient client){
        String html = null;
        for(int i=0;i<=retryNum;i++){
            try {
                HttpConfig httpConfig = HttpConfig.custom().timeout(2000);
                if(Objects.equals("true", System.getProperty("https.skip"))) {
                    SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(
                            SSLContexts.createDefault(),
                            new String[]{"TLSv1.2"},
                            null,
                            SSLConnectionSocketFactory.getDefaultHostnameVerifier());
                    CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).setRetryHandler(new StandardHttpRequestRetryHandler(2, false)).build();
                    httpConfig.client(httpclient);
                }
                html = HttpClientUtil.post(httpConfig.json(postJson).timeout(15000).url(reqUrl));

                if(i!=0){
                    log.info("头条回调重试成功  第"+i+"次"+html);
                }
                return html;
            }catch (HttpProcessException e){
                if(i==retryNum){
                    log.info("头条回调超时",e.getMessage());
                }
            }
        }
        return html;
    }


    public String resendUrlPost(String reqUrl,String postJson,CloseableHttpClient client, Header[] headers){
        String html = null;
        for(int i=0;i<=retryNum;i++){
            try {
                HttpConfig httpConfig = HttpConfig.custom().timeout(2000);
                if(Objects.equals("true", System.getProperty("https.skip"))) {
                    SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(
                            SSLContexts.createDefault(),
                            new String[]{"TLSv1.2"},
                            null,
                            SSLConnectionSocketFactory.getDefaultHostnameVerifier());
                    CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).setRetryHandler(new StandardHttpRequestRetryHandler(2, false)).build();
                    httpConfig.client(httpclient);
                }
                html = HttpClientUtil.post(httpConfig.json(postJson).timeout(15000).headers(headers).url(reqUrl));

                if(i!=0){
                    log.info("头条回调重试成功  第"+i+"次"+html);
                }
                return html;
            }catch (HttpProcessException e){
                if(i==retryNum){
                    log.info("头条回调超时",e.getMessage());
                }
            }
        }
        return html;
    }

    private String sendToutiaoByClient(String reqUrl, ToutiaoClick toutiaoClick, Integer eventType){
        String reStr = "请求失败，code = " ;
        String html = null;
        CloseableHttpClient client = getHttpClient();
        try {
            html = resendUrl(reqUrl,client);
            JSONObject jobj = JSON.parseObject(html);
            if(jobj!=null && jobj.getInteger("code")!=null && jobj.getInteger("code")==0){
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), ToutiaoEventTypeEnum.getReportName(eventType, true));
                log.info("头条回调成功 "+toutiaoClick.getAccountId()+" "+toutiaoClick.getProduct());
            }else{
                log.warn(toutiaoClick.getProduct()+" 头条回调失败 "+"  requrl:"+reqUrl+" 返回为："+html);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), ToutiaoEventTypeEnum.getReportName(eventType, false));
            }
            return html;
        }catch (Exception e){
            log.error(reqUrl+" 头条回传异常响应为 "+html,e);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "头条回调失败");
            reStr = reStr + e.getMessage();
        }finally {
            try {
                client.close();
            }catch (Exception e){
                log.error("",e);
            }
        }
        return reStr;
    }

    /**
     * 头条关键行为衍生事件回传
     * @param userExtEvent 用户关键行为
     */
    public void touTiaoExtEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userExtEvent, ThirdExtEvent thirdExtEvent, long callbackTime) {

        if (isToutiaoCallbackV2(toutiaoClick)) {
            touTiaoActiveV2(userEventReq, toutiaoClick, userExtEvent, genProps(thirdExtEvent, callbackTime));
            return;
        }

        String reqUrl = null;
        try {
            if (toutiaoClick != null && StringUtils.isNotBlank(toutiaoClick.getCallbackUrl())) {
                reqUrl = toutiaoClick.getCallbackUrl() + "&event_type=" + userExtEvent.getEventType() + "&props=" + genPropsEncode(thirdExtEvent, callbackTime);
            }
        } catch (Exception e) {
            log.error("头条衍生事件回调生成url异常", e);
        }

        touTiaoActive(userEventReq, toutiaoClick, userExtEvent, reqUrl);
    }

    private static String genPropsEncode(ThirdExtEvent thirdExtEvent, long callbackTime) {
        String props = genProps(thirdExtEvent, callbackTime).toString();
        try {
            return URLEncoder.encode(props, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            return props;
        }
    }

    private static ToutiaoExtEventProps genProps(ThirdExtEvent thirdExtEvent, long callbackTime) {
        return new ToutiaoExtEventProps()
                .setIs_ga_convert(ObjectUtils.defaultIfNull(thirdExtEvent.getIsGaConvert(), 0L))
                .setDepth(thirdExtEvent.getDepth())
                .setAction_ts(callbackTime)
                .setAction_type1(thirdExtEvent.getActionType1())
                .setValue1(thirdExtEvent.getValue1())
                .setAction_type2(thirdExtEvent.getActionType2())
                .setValue2(thirdExtEvent.getValue2())
                .setAction_type3(thirdExtEvent.getActionType3())
                .setValue3(thirdExtEvent.getValue3());
    }

    /**
     * 回传穿山甲LTV0数据
     */
    public void touTiaoLtv(UserEventReq userEventReq, OcpcEvent ocpcEvent, UserEvent userEvent, BigDecimal finalUserArpu) {

        String reqUrl = null;
        try {
            if (ocpcEvent != null && StringUtils.isNotBlank(ocpcEvent.getCallbackUrl())) {
                reqUrl = ocpcEvent.getCallbackUrl() + "&event_type=" + userEventReq.getEventType() + "&conv_time=" + (System.currentTimeMillis() / 1000)
                + "&props=" + genLtvProps(finalUserArpu);
            }
        } catch (Exception e) {
            log.error("头条回调生成url异常", e);
        }

        ToutiaoClick toutiaoClick = RedisEventService.convertEventToClick(ocpcEvent);

        touTiaoActive(userEventReq, toutiaoClick, userEvent, reqUrl);
    }

    private String genLtvProps(BigDecimal finalUserArpu) {
        String props = new JSONObject()
                .fluentPut("ug_event_val", finalUserArpu.intValue())
                .toJSONString();
        try {
            return URLEncoder.encode(props, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            return props;
        }
    }

    /**
     * 添加次留log
     */
    private void logForCiLiu(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {
        //报今日点击时间的次留
        try {
            if (ToutiaoEventTypeEnum.START_APP.value.equals(userEvent.getEventType()) && toutiaoClick.getCreateTime().getTime() > DateUtils.getNowDayDate().getTime()) {
                log.warn("product=[{}]-click=[{}] 今日上报次留", userEventReq.getProduct(), toutiaoClick.getId());
            }
        } catch (Exception e) {
            log.error("头条记录次留异常 " + JSON.toJSONString(userEventReq), e);
        }
    }

    /**
     * 判断是否是头条回传v2版本，点击事件回传的callback是加密字符串而不是链接
     */
    public static boolean isToutiaoCallbackV2(ToutiaoClick toutiaoClick) {
        if (toutiaoClick != null
                && toutiaoClick.getCallbackUrl() != null
                && !toutiaoClick.getCallbackUrl().toLowerCase().startsWith("http://")
                && !toutiaoClick.getCallbackUrl().toLowerCase().startsWith("https://")
        ) {
            return true;
        }
        return false;
    }

    /**
     * 头条回传v2
     */
    private void touTiaoActiveV2(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent, ToutiaoExtEventProps props) {
        String reqUrl = ToutiaoCallbackUrlV2;
        String reqBody = null;
        String respContent = null;
        try {
            if (toutiaoClick != null && StringUtils.isNotBlank(toutiaoClick.getCallbackUrl())) {
                int eventType = userEventReq.getEventType();
                if(eventType == 0){
                    log.info("账户回调头条v2激活 {} 产品 {} 设备 {}", toutiaoClick.getAccountId(), toutiaoClick.getProduct(), toutiaoClick.getOcpcDeviceId());
                }
                if (ocpcSwitcher.toutiaoLiveAccount.contains(toutiaoClick.getAccountId()) && ToutiaoEventTypeEnum.KEY_EVENT.value.equals(userEventReq.getEventType())) {
                    eventType = ToutiaoEventTypeEnum.PURCHASE.value;
                    log.info("头条回传直播账户 关键行为回传到付费V2 {}", toutiaoClick.getAccountId());
                }
                reqBody = genBodyV2(eventType, toutiaoClick, props,userEventReq);

                respContent = HttpClientUtil.post(HttpConfig.custom().timeout(3000).url(reqUrl).json(reqBody));

                log.info("账户回调头条v2结果 {} req: {} body: {}  结果为 {}", JSONObject.toJSONString(userEventReq), reqUrl, reqBody, respContent);

                JSONObject jobj = JSON.parseObject(respContent);
                if(jobj!=null && Objects.equals(jobj.getInteger("code"), 0)){
                    log.info("头条回调成功v2 {} {}", toutiaoClick.getAccountId(), toutiaoClick.getProduct());
                    alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), ToutiaoEventTypeEnum.getReportName(userEventReq.getEventType(), true));
                }else{
                    log.warn("头条回调失败v2 {} {}, {} body: {} 返回: {}", toutiaoClick.getAccountId(), toutiaoClick.getProduct(), reqUrl, reqBody, respContent);
                    alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), ToutiaoEventTypeEnum.getReportName(userEventReq.getEventType(), false));
                }

            } else {
                alertService.report(userEventReq.getProduct(), "0", "匹配不到点击事件");
                if(toutiaoClick != null && StringUtils.isBlank(toutiaoClick.getCallbackUrl())){
                    log.warn("toutiaoClickV2 callbackurl "+JSON.toJSONString(toutiaoClick));
                }
            }
            if(userEvent!=null){
                userEvent.setReqRsp(respContent);
                userEvent.setReqUrl(reqUrl + DELIMITER_URL_BODY + reqBody);
            }
        } catch (Exception e) {
            log.error(String.format("头条回传异常v2 %s reqUrl: %s reqBody: %s 返回: %s", JSONObject.toJSONString(userEventReq), reqUrl, reqBody, respContent), e);
        }
    }

    /**
     * 生成头条回传v2版本的 body
     demo:
    {
        "event_type": "game_addiction",
        "context": {
            "ad": {
                "callback": "*********_nogDGIe6gIq1jZADIPjMwPC0jdgBKAAwDDgBQiIyMDIyMDIxODE0NDIyODAxMDIxMDEwNTAxNzEyNEFFNTIwSAFQAIgBAJABApgBAA=="
            }
        },
        "timestamp": 1645166013433
    }
     */
    private static String genBodyV2(int eventType, ToutiaoClick toutiaoClick, ToutiaoExtEventProps props,UserEventReq userEventReq) {
        ToutiaoEventTypeEnum eventTypeEnum = ToutiaoEventTypeEnum.getStatus(eventType);
        JSONObject body = new JSONObject();
        if ("ios".equals(userEventReq.getOs())) {
            body.fluentPut("event_type", eventTypeEnum.typeV2)
                    .fluentPut("context", new JSONObject()
                            .fluentPut("ad", new JSONObject()
                                    .fluentPut("callback", toutiaoClick.getCallbackUrl()))
                            .fluentPut("device", new JSONObject()
                                    .fluentPut("platform", userEventReq.getOs())
                                    .fluentPut("idfv", userEventReq.getIdfv()))
                            .fluentPut("timestamp", System.currentTimeMillis()));
        } else if ("android".equals(userEventReq.getOs())) {
            body.fluentPut("event_type", eventTypeEnum.typeV2)
                    .fluentPut("context", new JSONObject()
                            .fluentPut("ad", new JSONObject()
                                    .fluentPut("callback", toutiaoClick.getCallbackUrl()))
                            .fluentPut("device", new JSONObject()
                                    .fluentPut("platform", userEventReq.getOs())
                                    .fluentPut("android_id", StringUtils.isNotBlank(userEventReq.getOriginalAndroidId()) ? userEventReq.getOriginalAndroidId() : userEventReq.getSourceDeviceId()))
                            .fluentPut("timestamp", System.currentTimeMillis()));
        }

        // v2链路的衍生回传需要传此字段的参数
        if (props != null) {
            body.fluentPut("properties", props);
        }

        return body.toJSONString();
    }

}
