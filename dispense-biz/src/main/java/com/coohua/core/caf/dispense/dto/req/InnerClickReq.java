package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/12/3 10:54
 */
@Data
public class InnerClickReq {
	/**
	 * 来源渠道
	 *1 内拉新 ：INNER_OLD_PULL
	 * 2 自家视频：INNER_VIDEO
	 * 3 内部引流：INNER_DRAINAGE
	 */
	private String dsp;
	/**
	 * 来源产品打点名称
	 */
	private String sourceProduct;


	/**
	 * 目标产品打点名称
	 */
	private String product;
	/**
	 * 系统
	 * ios
	 * android
	 */
	private String os;
	/**
	 * 设备id md5后
	 */
	private String ocpc_device_id;

	/**
	 * mac地址
	 */
	private String mac;
	/**
	 * oaId
	 */
	private String oaid; // Android Q及更高版本的设备号

	/**
	 * 时间戳
	 */
	private String ts;

}
