package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SdkProductPkg", description="")
public class SdkProductPkg {


    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "数据打点产品名")
    private Integer appId;

    @ApiModelProperty(value = "设备")
    private String os;

    @ApiModelProperty(value = "产品打点名")
    private String product;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "项目组名称")
    private String pkgName;

    @ApiModelProperty(value = "删除标志")
    private Integer delFlag;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
