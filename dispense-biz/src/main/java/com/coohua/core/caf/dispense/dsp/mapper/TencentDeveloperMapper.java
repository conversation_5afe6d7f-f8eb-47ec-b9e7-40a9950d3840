package com.coohua.core.caf.dispense.dsp.mapper;

import com.coohua.core.caf.dispense.dsp.entity.TencentDeveloper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.Set;

/**
 * <p>
 * 广告title Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-03
 */
public interface TencentDeveloperMapper extends BaseMapper<TencentDeveloper> {

    /**
     * 获取腾讯视频号在投账户
     * @return
     */
    @Select("select distinct advertiser_id from ad_plan_config where dsp ='guangdiantong' and del_flag =0 and delivery_range_name ='微信视频号'")
    Set<String> getChannelAccount();

}
