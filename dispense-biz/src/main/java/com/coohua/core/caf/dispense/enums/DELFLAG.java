package com.coohua.core.caf.dispense.enums;

public enum DELFLAG {
    /** 1: 删除 */
    shanchu(-1,"删除"),
    /** 1: 未删除 */
    weishanchu(0,"未删除"),
    ;

    public Integer value;
    public String name;

    DELFLAG(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static DELFLAG getType(Integer value) {
    DELFLAG[] DELFLAGList = DELFLAG.values();
        for (DELFLAG DELFLAG : DELFLAGList) {
            if (DELFLAG.value.equals(value)) {
                return DELFLAG;
            }
        }
       return  null;
    }
}
