package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ProductAdvertiserPool对象", description="cvr3.0的账户")
public class ProductAdvertiserPool implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "dsp")
    private String dsp;
    @ApiModelProperty(value = "product")
    private String product;
    @ApiModelProperty(value = "product中文名")
    private String productName;
    @ApiModelProperty(value = "账户id")
    private String accountId;
    @ApiModelProperty(value = "关键行为到达数")
    private Integer keyCount;
    @ApiModelProperty(value = "同步账户表里的数据")
    private String eventValues;
    @ApiModelProperty(value = "cvr3.0状态 1=可用 0=不可用")
    private Integer cvrStatus;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
