package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ocpc.entity.MatchClickBroad;
import com.coohua.core.caf.dispense.ocpc.mapper.MatchClickBroadMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
@Service
@Slf4j
public class MatchClickBroadService extends ServiceImpl<MatchClickBroadMapper, MatchClickBroad> {

}
