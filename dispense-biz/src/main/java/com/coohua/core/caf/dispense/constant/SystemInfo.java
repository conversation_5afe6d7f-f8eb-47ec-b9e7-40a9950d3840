package com.coohua.core.caf.dispense.constant;

public class SystemInfo {

    public static SystemType SYSTEM_TYPE = SystemType.Win;

    public static boolean TEST_SWITCH = true;

    enum SystemType {
        Linux,
        Win,
        Mac
    }

    static  {
        if (System.getProperty("os.name").toLowerCase().contains("win")) {
            SYSTEM_TYPE = SystemType.Win;
        }else if (System.getProperty("os.name").toLowerCase().contains("mac os")){
            SYSTEM_TYPE = SystemType.Mac;
        } else {
            SYSTEM_TYPE = SystemType.Linux;
        }
    }

    public static boolean isWin() {
        return SYSTEM_TYPE == SystemType.Win;
    }

    public static boolean isMac() {
        return SYSTEM_TYPE == SystemType.Mac;
    }

    public static boolean isLinux() {
        return SYSTEM_TYPE == SystemType.Linux;
    }

    /**
     * 是本地测试
     */
    public static boolean isTest() {
        return (isWin() || isMac()) && TEST_SWITCH;
    }

    /**
     * 不是本地测试
     */
    public static boolean isNotTest() {
        return !isTest();
    }


    public static void main(String[] args) {
        System.out.println(System.getProperty("os.name").toLowerCase());
    }

}
