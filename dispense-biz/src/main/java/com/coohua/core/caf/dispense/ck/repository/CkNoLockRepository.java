package com.coohua.core.caf.dispense.ck.repository;

import com.coohua.core.caf.dispense.ck.entity.GroupToutiaoClickLocal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import ru.yandex.clickhouse.ClickHouseDataSource;
import ru.yandex.clickhouse.settings.ClickHouseProperties;

import javax.annotation.PostConstruct;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class CkNoLockRepository {
    private static final String USER = "default";
    private static final String PASSWORD = "Phw7a7A4";
    private static final String URL = "*********************************";
    public static final String INSERT_SQL = "INSERT INTO ods.group_toutiao_click_local (logday, id, group_id, account_id, product, os, dsp, caid, oaid, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    private static ClickHouseDataSource dataSource;
    private final int BATCH_SIZE = 5000;

    public void batchSave(List<GroupToutiaoClickLocal> finalDomainList) throws SQLException {
        long ctime = System.currentTimeMillis();
        int batchCounter = 0;
        Connection connection = getConnection();
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(INSERT_SQL);
            for (GroupToutiaoClickLocal eventDomain : finalDomainList) {
                addToBatch(eventDomain, connection,ps);
                batchCounter++;
            }
            long begin = System.currentTimeMillis();
            ps.executeBatch();
            log.info("保存数据成功! batchSize:{}, cost:{}mms 总共{}", batchCounter, System.currentTimeMillis() - begin, System.currentTimeMillis() - ctime);
        }catch (Exception e){
            log.error("",e);
        }finally {
            if(ps!=null){
                ps.close();
            }
            if(connection!=null){
                connection.close();
            }
        }
    }

    public void addToBatch(GroupToutiaoClickLocal eventDomain, Connection connection, PreparedStatement ps) throws SQLException {
        try {
            int colIndex = 1;
            ps.setString(colIndex++, eventDomain.getLogday());
            ps.setLong(colIndex++, eventDomain.getId());
            ps.setInt(colIndex++, eventDomain.getGroupId());
            ps.setString(colIndex++, eventDomain.getAccountId());
            ps.setString(colIndex++, eventDomain.getProduct());
            ps.setString(colIndex++, eventDomain.getOs());
            ps.setString(colIndex++, eventDomain.getDsp());
            ps.setString(colIndex++, eventDomain.getCaid());
            ps.setString(colIndex++, eventDomain.getOaid());
            ps.setTimestamp(colIndex++, new Timestamp(eventDomain.getCreateTime().getTime()));
//            ps.setTimestamp(colIndex++, new Timestamp(eventDomain.getCreateTime().getTime()));
            ps.addBatch();
        }catch (Exception e){
            log.error("",e);
        }
    }

    private Connection getConnection(){
        try {
            if(dataSource==null){
                synchronized (this.getClass()){
                    if(dataSource==null){
                        Class.forName("ru.yandex.clickhouse.ClickHouseDriver");
                        ClickHouseProperties properties = new ClickHouseProperties();
                        properties.setUser(USER);
                        properties.setPassword(PASSWORD);
                        dataSource = new ClickHouseDataSource(URL, properties);
                    }
                }
            }
            Connection conn= dataSource.getConnection();
            return  conn;
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }


}
