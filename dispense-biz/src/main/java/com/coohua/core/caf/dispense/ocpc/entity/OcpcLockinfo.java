package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OcpcLockinfo对象", description="")
public class OcpcLockinfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    @TableField("userId")
    private String userId;

    @ApiModelProperty(value = "原始deviceId")
    private String sourceDeviceId;

    @ApiModelProperty(value = "原始oaId")
    private String sourceOaid;

    private String product;

    private String os;

    private String ocpcDeviceId;

    private String oaid;

    @ApiModelProperty(value = "事件类型")
    private Integer eventType;

    @ApiModelProperty(value = "类型名称")
    private String eventTypeName;

    @ApiModelProperty(value = "accountId")
    private String accountId;

    @ApiModelProperty(value = "dsp平台")
    private String dsp;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private String macId;

    @ApiModelProperty(value = "click_time")
    private Date clickTime;

    @ApiModelProperty(value = "安卓id")
    private String androidId;

    private String remark;

    @ApiModelProperty(value = "oaid加md5")
    private String oaid2;

    @ApiModelProperty(value = "idfa加md5")
    private String idfa2;

    @ApiModelProperty(value = "创意ID")
    private String creativeId;

    @ApiModelProperty(value = "计划ID")
    private String planId;

    @ApiModelProperty(value = "dsp子类型")
    private String dspSubType;

    @ApiModelProperty(value = "广告组ID")
    private String groupId;

    @ApiModelProperty(value = "用户终端的公共IP地址")
    private String ip;

    @ApiModelProperty(value = "用户代理(User Agent)")
    private String ua;

    @ApiModelProperty(value = "手机型号")
    private String model;

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "wappid")
    private String wAppId;

    private String caid;

    private String gyType;


}
