package com.coohua.core.caf.dispense.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 关键行为衍生事件相关apollo配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class KuaishouExtApolloConfig {

    /**
     * 快手衍生过程 回传 是否对全部产品生效
     */
    @Value("${ocpc.kuaishou.ext.enable.all.product:false}")
    public boolean extProcessEnableAll;

    /**
     * 快手衍生过程 回传 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.kuaishou.ext.enable.products:[]}")
    public Set<String> extProcessEnableProducts;

    /**
     * 快手衍生过程 回传 是否对全部账户生效
     */
    @Value("${ocpc.kuaishou.ext.enable.all.accounts:false}")
    public boolean extProcessEnableAllAccounts;

    /**
     * 快手衍生过程 回传 生效的账户列表
     */
    @ApolloJsonValue("${ocpc.kuaishou.ext.enable.accounts:[]}")
    public Set<String> extProcessEnableAccounts;

    /**
     * 快手IAA衍生过程 回传 是否对全部产品生效
     */
    @Value("${ocpc.kuaishou.IAA.enable.all.product:false}")
    public boolean kuaishouIAAProcessEnableAll;

    /**
     * 快手IAA衍生过程 回传 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.kuaishou.IAA.enable.products:[]}")
    public Set<String> kuaishouIAAProcessEnableProducts;

    /**
     * 快手IAA衍生过程 回传 是否对全部账户生效
     */
    @Value("${ocpc.kuaishou.IAA.enable.all.accounts:false}")
    public boolean kuaishouIAAProcessEnableAllAccounts;

    /**
     * 快手衍生过程 回传 生效的账户列表
     */
    @ApolloJsonValue("${ocpc.kuaishou.IAA.enable.accounts:[]}")
    public Set<String> kuaishouIAAProcessEnableAccounts;

    /**
     * 快手判断是否是无需生效的产品
     * @return 若该产品无需生效 返回true
     */
    public boolean illegalProduct(String product) {
        return !extProcessEnableAll && !extProcessEnableProducts.contains(product);
    }

    /**
     * 快手判断是否是无需生效的账户
     * @return 若该账户无需生效 返回true
     */
    public boolean illegalAccount(String accountId) {
        return !extProcessEnableAllAccounts && !extProcessEnableAccounts.contains(accountId);
    }

    /**
     * 快手判断是否是无需生效的产品
     * @return 若该产品无需生效 返回true
     */
    public boolean illegalIAAProduct(String product) {
        return !kuaishouIAAProcessEnableAll && (kuaishouIAAProcessEnableProducts.contains(product) || kuaishouIAAProcessEnableProducts.isEmpty());
    }

    /**
     * 快手判断是否是无需生效的账户
     * @return 若该账户无需生效 返回true
     */
    public boolean illegalIAAAccount(String accountId) {
        return !kuaishouIAAProcessEnableAllAccounts && (kuaishouIAAProcessEnableAccounts.contains(accountId) || kuaishouIAAProcessEnableAccounts.isEmpty());
    }


    /**
     * 快手衍生定义回传 是否对全部产品生效
     */
    @Value("${ocpc.kuaishou.ext.define.enable.all:false}")
    public boolean extDefineEnableAll;

    /**
     * 快手衍生定义回传 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.kuaishou.ext.define.enable.products:[]}")
    public Set<String> extDefineEnableProducts;

    /**
     * 全部使用分布式锁保证 衍生定义回传 不重复
     */
    @Value("${lock.kuaishou.ext.define.all.enable:true}")
    public boolean lockExtDefineAllEnable;

    /**
     * 启用分布式锁保证 衍生定义回传 不重复的产品打点名称集合
     */
    @ApolloJsonValue("${lock.kuaishou.ext.define.enable.products:[]}")
    public Set<String> lockExtDefineEnableProducts;

    /**
     * 衍生定义回传 适用的账户 eventTypes范围
     */
    @ApolloJsonValue("${ocpc.kuaishou.ext.define.enable.eventTypes:[\"1,2\",\"1,3\"]}")
    public Set<String> extDefineEnableEventTypes;

    /**
     * 衍生定义回传 是否对全部账户生效
     */
    @Value("${ocpc.kuaishou.ext.define.enable.account.all:false}")
    public boolean extDefineEnableAllAccount;

    /**
     * 衍生定义回传 生效的账户id列表
     */
    @ApolloJsonValue("${ocpc.kuaishou.ext.define.accounts:[]}")
    public Set<String> extDefineEnableAccounts;

    /**
     * 衍生定义回传 (1,3类型) 是否对全部产品生效
     */
    @Value("${ocpc.kuaishou.ext.define.1_3.enable.all:false}")
    public boolean extDefineEnableAllFor_1_3;

    /**
     * 衍生定义回传 (1,3类型) 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.kuaishou.ext.define.1_3.enable.products:[]}")
    public Set<String> extDefineEnableProductsFor_1_3;


}
