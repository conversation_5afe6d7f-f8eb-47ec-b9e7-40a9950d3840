package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/11/26 11:14
 * @description : SigmobAccount对象
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "SigmobAccount对象", description = "sigmob产品及行为配置表")
public class SigmobAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "广告主id")
    private String advertiserId;

    @ApiModelProperty(value = "广告主全称")
    private String companyName;

    @ApiModelProperty(value = "产品Id")
    private String appId;

    @ApiModelProperty(value = "产品名称")
    private String appName;

    @ApiModelProperty(value = "数据打点产品名")
    private String appProductName;

    @ApiModelProperty(value = "投放类型，android/ios")
    private String appType;

    @ApiModelProperty(value = "删除标识;0:未删除 , 1:已删除")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "更新人")
    private String updateName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "关键行为回传配置")
    private String eventTypes;

    @ApiModelProperty(value = "关键行为回传数值")
    private String eventValues;

}
