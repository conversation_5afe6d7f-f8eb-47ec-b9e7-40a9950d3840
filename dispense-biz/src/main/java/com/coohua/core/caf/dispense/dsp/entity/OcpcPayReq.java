package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OcpcPayReq对象", description="")
public class OcpcPayReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String product;

    private String os;

    private String userId;

    private String openId;

    private Integer payAmount;

    private Integer allAmount;

    private Integer eventType;

    private String dsp;

    private Date clickTime;

    private String payOrderNo;

    private String pid;

    private String oaid;

    private String imei;

    private String deviceId;

    private String mac;

    private String ip;

    private String cid;

    private String caid;

    private String gid;

    private String channel;

    private Date createTime;

    private Date updateTime;


}
