package com.coohua.core.caf.dispense.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 常量 apollo配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ConstApolloConfig {

    /**
     * 合法arpu的最大值
     */
    @Value("${ocpc.legal.arpu.max:1200}")
    public int legalArpuMax;

    /**
     * 合法ecpm的最大值
     */
    @Value("${ocpc.legal.ecpm.max:3000}")
    public int legalEcpmMax;

    /**
     * 腾讯需要打印日志的测试账户id
     */
    @ApolloJsonValue("${ocpc.tencent.log.test.accounts:[]}")
    public Set<String> tencentLogTestAccounts;

    /**
     * ip+ua保存 是否对全部产品生效
     */
    @Value("${ocpc.save.ip.ua.enable.all.product:false}")
    public boolean saveIpUaEnableAllProduct;

    /**
     * ip+ua归因的有效时间 (单位：分钟) 默认3小时内以ip+ua的归因有效
     */
    @Value("${ocpc.gui.ip.ua.valid.minute:180}")
    public int guiIpuaValidMinute;

}
