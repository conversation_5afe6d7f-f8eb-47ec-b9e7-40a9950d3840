package com.coohua.core.caf.dispense.dsp.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthKuaishouApp;
import com.coohua.core.caf.dispense.dsp.mapper.AuthKuaishouAppMapper;
import com.coohua.core.caf.dispense.ocpc.entity.ProductAdvertiserPool;
import com.coohua.core.caf.dispense.utils.IterateUtils;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <p>
 * 快手app授权信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
public class AuthKuaishouAppService extends ServiceImpl<AuthKuaishouAppMapper, AuthKuaishouApp> implements IService<AuthKuaishouApp> {

    public void setEventValues(List<ProductAdvertiserPool> advertiserPools) {
        IterateUtils.iterateByStepSize(advertiserPools, 5000, data -> {
            List<AuthKuaishouApp> list = this.lambdaQuery().select(AuthKuaishouApp::getAdvertiserId, AuthKuaishouApp::getEventValues)
                    .in(AuthKuaishouApp::getAdvertiserId, data.stream().map(o -> Long.parseLong(o.getAccountId())).collect(toList()))
                    .eq(AuthKuaishouApp::getEventTypes, BaseConstants.NUM_ARPU_RULE)
                    .eq(AuthKuaishouApp::getAccountType, 1)
                    .list();
            Map<String, String> eventValues = list.stream().collect(toMap(o -> o.getAdvertiserId().toString(), AuthKuaishouApp::getEventValues, (o1, o2) -> o1));
            data.forEach(o -> o.setEventValues(eventValues.get(o.getAccountId())));
        });
    }

    public AuthKuaishouApp getAuthKuaishouAppByAdvertiserId(String advertiserId) {
        QueryWrapper<AuthKuaishouApp> authKuaishouAppQueryWrapper = new QueryWrapper<>();
        authKuaishouAppQueryWrapper.eq("advertiser_id", advertiserId);
        return getOne(authKuaishouAppQueryWrapper);
    }
}
