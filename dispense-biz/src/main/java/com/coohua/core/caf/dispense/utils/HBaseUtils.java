package com.coohua.core.caf.dispense.utils;

import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.constant.HBaseParameterEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 */
@Slf4j
public class HBaseUtils {

    /**
     * 本工程唯一指定splitKey
     */
    private static final byte[][] ONLY_SPLIT_KEYS = {Bytes.toBytes("0"), Bytes.toBytes("1"), Bytes.toBytes("2"),
            Bytes.toBytes("3"), Bytes.toBytes("4"), Bytes.toBytes("5"), Bytes.toBytes("6"),
            Bytes.toBytes("7"), Bytes.toBytes("8"), Bytes.toBytes("9")};

    /**
     * rowKey前缀数组
     */
    private static final String[] ROW_KEY_PREFIX_ARRAY = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
    public static void initHadoopTable(Connection connection, String tableName, int dataTtl) throws IOException {

        try (Admin admin = connection.getAdmin()) {
            // 建表
            try {
                admin.getDescriptor(TableName.valueOf(tableName));
            } catch (TableNotFoundException te) {

                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(HBaseParameterEnum.FAMILY.getBinaryContent())
                        .setTimeToLive(dataTtl)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", tableName, e);
            throw e;
        }
    }
    /**
     * 初始化hadoop表
     *
     * @param connection 连接
     * @param tableName  表名
     * @param dataTtl    数据存续时长
     * @throws IOException 可能的异常
     */
    public static void initHadoopTable(Connection connection, String tableName, int dataTtl, boolean rebuild) throws IOException {

        try (Admin admin = connection.getAdmin()) {

            // 建表
            try {

                if (rebuild) {

                    ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                            .newBuilder(HBaseParameterEnum.FAMILY.getBinaryContent())
                            .setTimeToLive(dataTtl)
                            .setCompressionType(Compression.Algorithm.ZSTD)
                            .setDataBlockEncoding(DataBlockEncoding.DIFF)
                            .build();

                    admin.modifyColumnFamily(TableName.valueOf(tableName), familyDescriptor);

                } else {
                    admin.getDescriptor(TableName.valueOf(tableName));
                }
            } catch (TableNotFoundException te) {

                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(HBaseParameterEnum.FAMILY.getBinaryContent())
                        .setTimeToLive(dataTtl)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", tableName, e);
            throw e;
        }
    }

    /**
     * 将数据存入Hadoop的指定表
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     行key
     * @param content    列值
     * @return 存储Hadoop结果
     */
    public static boolean saveToHadoop(Connection connection, String tableName, String rowKey, byte[] content) {

        try {
            initHadoopTable(connection, tableName, Integer.MAX_VALUE, false);
        } catch (Exception e) {
            log.error("HBase表创建失败 ", e);
            return false;
        }

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            String processedRowKey = hashRowKeyByLastCharacter(rowKey);

            Put put = new Put(Bytes.toBytes(processedRowKey));
            put.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent(),
                    content);

            table.put(put);

            return true;
        } catch (Exception e) {
            log.error("Hadoop表存储失败 :", e);
            return false;
        }
    }

    /**
     * 将数据存入Hadoop的指定表
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rows    列值
     * @return 存储Hadoop结果
     */
    public static boolean batchSaveToHadoop(Connection connection, String tableName, Map<String, byte[]> rows) {

        List<Put> saveRequest = rows.entrySet().stream()
                .map(entry -> {
                    String processedRowKey = hashRowKeyByLastCharacter(entry.getKey());
                    Put put = new Put(Bytes.toBytes(processedRowKey));
                    put.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent(),
                            entry.getValue());
                    return put;
                }).collect(toList());

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            table.put(saveRequest);

            return true;
        } catch (Exception e) {
            log.error("Hadoop表存储失败 :", e);
            return false;
        }
    }

    /**
     * 指定表名从Hadoop中检索特定数据
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     行key
     * @return 数据检索结果
     */
    public static String searchDataFromHadoop(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            String processedRowKey = hashRowKeyByLastCharacter(rowKey);

            Get get = new Get(Bytes.toBytes(processedRowKey));

            Result result = table.get(get);
            byte[] rbyts = result.getValue(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent());
            return result == null ? null : Bytes.toString(rbyts);
        } catch (Exception e) {
            log.error("Hadoop表检索失败:", e);
            return null;
        }
    }

    /**
     * 根据rowKey最后一位来散列rowKey：根据ASCII码加上0-9前缀
     *
     * @param rowKey rowKey
     * @return 散列后的rowKey
     */
    public static String hashRowKeyByLastCharacter(String rowKey) {

        char lastCharacter = rowKey.charAt(rowKey.length() - 1);

        int prefixIndex;

        if (lastCharacter > BaseConstants.CHAR_NINE || lastCharacter < BaseConstants.CHAR_ZERO) {
            prefixIndex = lastCharacter % 10;
        } else {
            prefixIndex = lastCharacter - BaseConstants.CHAR_ZERO;
        }

        return ROW_KEY_PREFIX_ARRAY[prefixIndex] + rowKey;
    }

}
