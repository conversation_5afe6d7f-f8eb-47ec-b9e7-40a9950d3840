package com.coohua.core.caf.dispense.cache;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.Product;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.dsp.service.AlertService;
import com.coohua.core.caf.dispense.dsp.service.ProductService;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.ocpc.service.OcpcClickService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.coohua.core.caf.dispense.constant.BaseConstants.*;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/9/27 10:48
 */
@Slf4j
@Component
public class ProductCache {
    public static Set<String> productNameSet;
    @Autowired
    private ProductService productService;

    @Value("${check.product.name:true}")
    private Boolean checkProductNameEnable;

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    @Autowired
    private AlertService alertService;
    public static List<Product> PRODUCT_LIST = Collections.EMPTY_LIST;
    /**
     * key:产品打点product
     * value : appId
     */
    private static Map<String, Integer> productAppMap = new HashMap<>();
    public static Map<Integer, String> appProductMap = new HashMap<>();
    /** 产品中文名 -> 产品英文打点名称 映射表*/
    public static Map<String, String> productCn2EnMap = new HashMap<>();
    /** 产品英文打点名称 -> 产品中文名 映射表*/
    public static Map<String, String> productEn2CnMap = new HashMap<>();

    public static Map<String, Product> en2ProductMap = new HashMap<>();
    @Autowired
    OcpcProductService ocpcProductService;

    /**
     * 3 分钟更新一下pkgId映射
     */
    //    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ?")
    public void initProductNameCache() {
        try {
            PRODUCT_LIST = productService.lambdaQuery().eq(Product::getDelFlag, DELFLAG.weishanchu.value).list();
        } catch (Exception e) {
            log.error("", e);
            PRODUCT_LIST = ocpcProductService.lambdaQuery().eq(Product::getDelFlag, DELFLAG.weishanchu.value).list();
        }
        if (CollectionUtils.isEmpty(PRODUCT_LIST)) {
            log.error("产品配置为空");
            return;
        }
        productNameSet = PRODUCT_LIST.stream().map(product -> product.getName()).collect(Collectors.toSet());
        productAppMap = PRODUCT_LIST.stream()
                .collect(Collectors.toMap(Product::getName, Product::getAppId, (oldValue, newValue) -> newValue));

        appProductMap = PRODUCT_LIST.stream()
                .collect(Collectors.toMap(Product::getAppId, Product::getName, (oldValue, newValue) -> oldValue));
        productNameSet.add("wangpaixiaoxiaole");
        productAppMap.put("wangpaixiaoxiaole", 10);

        en2ProductMap = PRODUCT_LIST.stream()
                .collect(Collectors.toMap(Product::getName, Function.identity(), (oldValue, newValue) -> oldValue));

        productCn2EnMap = PRODUCT_LIST.stream()
                .filter(k-> StringUtils.isNoneBlank(k.getName(), k.getRemark()))
                .collect(Collectors.toMap(Product::getRemark, Product::getName, (oldValue, newValue) -> oldValue));
        productEn2CnMap = PRODUCT_LIST.stream()
                .filter(k-> StringUtils.isNoneBlank(k.getName(), k.getRemark()))
                .collect(Collectors.toMap(Product::getName, Product::getRemark, (oldValue, newValue) -> oldValue));
        log.info("initProductNameCache success");
    }

    public String getPname(Integer appid){
        return appProductMap.get(appid);
    }

    public static String getWAppid(String openId,String productName){
        if(StringUtils.isNotBlank(openId)){

            Product product = ProductCache.en2ProductMap.get(productName);
            String gameAid = product.getWgameAppId();
            if(StringUtils.isBlank(gameAid)){
                log.warn("ocpc 微信小游戏"+productName+" id 未配置");
            }else{
                return gameAid;
            }
        }
        return null;
    }
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    public boolean checkClickProductName(ToutiaoClick toutiaoClick) {
        try {
            String productName = toutiaoClick.getProduct();
            if (!checkProductNameEnable) {
                return true;
            }
            if (productNameSet != null && StringUtils.isNotBlank(productName) && productNameSet.contains(productName)) {
                return true;
            }
            if (productName.startsWith(OcpcClickService.wbappStartStr)) {
                return true;
            }

            if(ocpcSwitcher.baiduGy && productName.startsWith("bd")){
                return true;
            }
            // 内拉新
            if (Objects.equals(toutiaoClick.getDsp(), DspType.INNER_OLD_PULL_NEW.value)) {
                alertService.report(productName, "0", "内拉新事件产品名不存在");
                log.error("内拉新事件产品名不存在或者为空 {} {}", productName, JSON.toJSONString(toutiaoClick));
            } else {
                alertService.report(productName, "0", "点击事件产品名不存在");
                log.error("点击事件产品名不存在或者为空 {} {}", productName, JSON.toJSONString(toutiaoClick));
            }
            return false;
        } catch (Exception e) {
            log.error("检查点击事件产品名异常");
        }
        return true;
    }

    public boolean checkEventProductName(UserEventReq userEventReq) {
        try {
            String productName = userEventReq.getProduct();
            if (!checkProductNameEnable) {
                return true;
            }
            if (productNameSet != null && StringUtils.isNotBlank(productName) && productNameSet.contains(productName)) {
                return true;
            }
            alertService.report(productName, "0", "上报事件产品名不存在");
            log.error("上报事件产品名不存在 {} {}", productName, JSON.toJSONString(userEventReq));
            return false;
        } catch (Exception e) {
            log.error("检查上报事件产品名异常");
        }
        return true;
    }

    @Scheduled(cron = "0 0/3 * * * ?")
    public void syncProductToProduct() {

        try {
            boolean locked = tryGetDistributedLock(RedisKeyConstants.SYNC_PRODUCT_TO_OCPC_KEY, "1", 10000);
            if (!locked) {
                log.warn("syncProductToProduct并发抢占资格失败");
                return;
            }

            List<Product> list = productService.lambdaQuery().eq(Product::getDelFlag, DELFLAG.weishanchu.value).list();
            for (Product product : list) {
                try {
                    ocpcProductService.saveOrUpdate(product);
                } catch (Exception e) {
                    log.error("syncProductToProduct异常id:" + product.getId(), e);
                }
            }
        } catch (Exception e) {
            log.error("syncProductToProduct异常", e);
        }

    }

    /**
     * redis 分布式锁
     * @param lockKey  锁定Key
     * @param requestId 请求ID
     * @param millsExpireTime 过期毫秒数
     * @return
     */
    private boolean tryGetDistributedLock(String lockKey, String requestId, int millsExpireTime) {
        String result = bpDispenseJedisClusterClient.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, millsExpireTime);
        return LOCK_SUCCESS.equals(result);
    }

    public Map<String, String> getProductEn2CnMap(Collection<String> productCns) {
        return PRODUCT_LIST
                .stream()
                .filter(k-> productCns.contains(k.getRemark()))
                .collect(Collectors.toMap(k->k.getName(), k-> k.getRemark(), (oldValue, newValue) -> oldValue));
    }

    /**
     * 根据产品英文打点名称获取appId
     * @param enName
     * @return
     */
    public static Integer getAppId(String enName) {
        return productAppMap.get(enName);
    }

}
