package com.coohua.core.caf.dispense.ck.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;
import java.sql.SQLException;

@Slf4j
@Configuration
@MapperScan(basePackages = {"com.coohua.core.caf.dispense.ck.mapper"}, sqlSessionFactoryRef = "clickHouseSqlSessionFactory11")
public class Click12DataSourceConfig {


    @Value("default")
    private String userName;

    @Value("Phw7a7A4")
    private String passWord;

    @Value("*************************************")
    private String url;

    @Value("ru.yandex.clickhouse.ClickHouseDriver")
    private String driverClass;

    @Value("200")
    private Integer maxActive;

    @Value("2000")
    private Integer maxWait;

    @Value("2")
    private Integer initialSize;

    @Value("false")
    private Boolean testWhileIdle;



    @Bean(name = "datasourceClickHouse11")
    public DataSource dataSource() throws SQLException{
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setUsername(userName);
        druidDataSource.setPassword(passWord);
        druidDataSource.setUrl(url);
        druidDataSource.setDriverClassName(driverClass);
        druidDataSource.setMaxActive(maxActive);
        // 配置从连接池获取连接等待超时的时间
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setTestWhileIdle(testWhileIdle);
        // 打开后，增强timeBetweenEvictionRunsMillis的周期性连接检查，minIdle内的空闲连接，每次检查强制验证连接有效性. 参考：https://github.com/alibaba/druid/wiki/KeepAlive_cn
        druidDataSource.init();
        return druidDataSource;
    }

    @Bean(name="clickHouseSqlSessionFactory11")
    @ConditionalOnBean(name = "datasourceClickHouse11")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("datasourceClickHouse11") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisSqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        mybatisSqlSessionFactoryBean.setDataSource(dataSource);
        mybatisSqlSessionFactoryBean.setConfigLocation(new ClassPathResource("config/mybatis_config.xml"));
        mybatisSqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        return mybatisSqlSessionFactoryBean.getObject();
    }

}
