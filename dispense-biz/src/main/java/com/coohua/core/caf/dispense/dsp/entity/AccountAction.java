package com.coohua.core.caf.dispense.dsp.entity;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class AccountAction {
    private BigInteger accountId;
    private Integer eventType;
    private String eventNumber;

    /**
     * 数据累积的最大时间
     */
    private Integer maxAccumulatePeriod;

    /**
     * 新版事件回传标准定义：多个事件标准之间为逻辑“与”的关系
     * key为"1"时，代表观看视频次数配置，value为相应的值，整数
     * key为"2"时，代表24小时arpu，value为相应的值，小数
     * 多个条件均满足时，触发回传
     */
    private Map<String, String> eventConfigurations;

    public AccountAction(BigInteger accountId) {
        this.accountId = accountId;
        this.eventConfigurations = new HashMap<>();
    }

    public AccountAction(BigInteger accountId, Map<String, String> eventConfigurations, Integer maxAccumulatePeriod) {
        this.accountId = accountId;
        this.eventConfigurations = eventConfigurations;
        this.maxAccumulatePeriod = maxAccumulatePeriod;
    }
    public AccountAction(BigInteger accountId,Map<String, String> eventConfigurations) {
        this.accountId = accountId;
        this.eventConfigurations = eventConfigurations;
    }


    /**
     * 去重方式
     * @return
     */
    public static String getDistinctKey(AccountAction action) {
        // 有新版配置时，根据 新版配置加上累计时间 去重
        if (action != null && action.getEventConfigurations() != null && action.getEventConfigurations().size() > 0) {
            return JSON.toJSONString(action.getEventConfigurations()) + action.maxAccumulatePeriod;
        } else {
            return JSON.toJSONString(action);
        }
    }
}
