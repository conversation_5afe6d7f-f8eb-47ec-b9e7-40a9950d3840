package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.AuthKuaishouApp;
import com.coohua.core.caf.dispense.dsp.entity.WeekendUpArpu;
import com.coohua.core.caf.dispense.dsp.entity.WeekendUpArpuGroup;
import com.coohua.core.caf.dispense.dsp.mapper.WeekendUpArpuGroupMapper;
import com.coohua.core.caf.dispense.dsp.mapper.WeekendUpArpuMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.DeviceSundy;
import com.coohua.core.caf.dispense.ocpc.entity.PeopleDevice30NoRetain;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.entity.DeviceSundyPercent;
import com.coohua.core.caf.dispense.ocpc.entity.DeviceSundyPercentEx;
import com.coohua.core.caf.dispense.ocpc.mapper.DeviceSundyMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ocpc.mapper.DeviceSundyPercentMapper;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import com.google.common.collect.Sets;
import io.swagger.models.auth.In;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-09-20
*/
@Service
@Slf4j
public class DeviceSundyService extends ServiceImpl<DeviceSundyMapper, DeviceSundy> {
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @Autowired
    WeekendUpArpuGroupMapper weekendUpArpuGroupMapper;

    @Autowired
    DeviceSundyPercentMapper deviceSundyPercentMapper;

    @Autowired
    WeekendUpArpuMapper weekendUpArpuMapper;

    public static Map<String,List<WeekendUpArpu>> cacheProductEx = new HashMap<>();
    public static Map<String,DeviceSundyPercentEx> cachePercentEx = new HashMap<>();

    @PostConstruct
    @Scheduled(cron = "0 0/5 * * * ? ")
    public void refresh(){
        refreshProduct();
        refreshDeviceSundyPercent();
    }

    private void refreshProduct() {
        QueryWrapper<WeekendUpArpu> params = new QueryWrapper<>();
        params.lambda().eq(WeekendUpArpu::getStatue, 1)
                .eq(WeekendUpArpu::getDelFlag, 0);

        List<WeekendUpArpu> temp = weekendUpArpuMapper.selectList(params);
        Map<String,List<WeekendUpArpu>> tempMap = new HashMap<>();

        for(WeekendUpArpu param : temp){
            List<WeekendUpArpu> tempList = tempMap.getOrDefault(param.getProduct(), new ArrayList<>());
            tempList.add(param);
            tempMap.put(param.getProduct(), tempList);
        }

        cacheProductEx = tempMap;
    }

    private void refreshDeviceSundyPercent() {
        QueryWrapper<DeviceSundyPercent> query = new QueryWrapper<>();
//        query.lambda().ge(DeviceSundyPercent::getPercent, 0.5);
        List<DeviceSundyPercent> deviceSundyPercents = deviceSundyPercentMapper.selectList(query);

        Map<String,DeviceSundyPercentEx> tempMap = new HashMap<>();
        for(DeviceSundyPercent temp : deviceSundyPercents){
            try {
                DeviceSundyPercentEx tempP = new DeviceSundyPercentEx(temp);
                tempMap.put(temp.getProduct() + temp.getOs(), tempP);
            } catch (Exception e) {
                log.error("refreshDeviceSundyPercent temp : {} ", temp, e);
            }
        }

        cachePercentEx = tempMap;
    }

//    @PostConstruct
    public void test(){
        refresh();
        DeviceSundy d = new DeviceSundy();
        d.setCpaType(3);
        d.setOs("android");
        getArpuFlt("sgc", d);
    }

    public double getArpuFlt(String product,DeviceSundy deviceSundy){
        double flt = 1d;
        try{
            List<WeekendUpArpu> weekendUpArpus = cacheProductEx.get(product);
            if(null == weekendUpArpus) {
                return flt;
            }
            DeviceSundyPercentEx deviceSundyPercent = cachePercentEx.get(product + deviceSundy.getOs());
            int co = 0;
            if(deviceSundyPercent != null && deviceSundyPercent.getCount() != null){
                co = getMultipleCount(deviceSundyPercent,deviceSundy.getCpaType());
                if (co > 10 || co == 0){
                    co = deviceSundyPercent.getCount();
                }
            }

            log.info("getArpuFlt product : {} weekendUpArpus : {}", product, weekendUpArpus);
            WeekendUpArpu weekendUpArpu = null;
            for(WeekendUpArpu single: weekendUpArpus){
                if(StringUtils.equals(single.getOs(), "all")){
                    weekendUpArpu = single;
                    break;
                }else if(StringUtils.equals(single.getOs(),deviceSundy.getOs())){
                    weekendUpArpu = single;
                    break;
                }
            }
            if(weekendUpArpu != null){
                WeekendUpArpuGroup weekendUpArpuGroup = weekendUpArpuGroupMapper.selectById(weekendUpArpu.getStrategyId());
                log.info("getArpuFlt deviceSundy : {} weekendUpArpuGroup : {} deviceSundyPercent : {}", deviceSundy, weekendUpArpuGroup, deviceSundyPercent);

                if(deviceSundy != null){
                    if(deviceSundy.getCpaType() == 1){
                        flt = weekendUpArpuGroup.getBase1() + weekendUpArpuGroup.getInc1() * co;
                    }else if(deviceSundy.getCpaType() == 2){
                        flt = weekendUpArpuGroup.getBase2() + weekendUpArpuGroup.getInc2() * co;
                    }else if(deviceSundy.getCpaType() >= 3){
                        flt = weekendUpArpuGroup.getBase3() + weekendUpArpuGroup.getInc3() * co;
                    }
                    log.info("getArpuFlt flt : {} deviceSundy.getCpaType() : {} weekendUpArpuGroup : {} deviceSundyPercent : {}", flt, deviceSundy.getCpaType() < 3 ? deviceSundy.getCpaType() : "3+", weekendUpArpuGroup, deviceSundyPercent);
                }
            }

        }catch (Exception e){
            log.error("getArpuFlt  product : {} deviceSundy : {} cacheProductEx : {} cachePercentEx : {} ", product, deviceSundy, cacheProductEx, cachePercentEx, e);
        }
        return flt;
    }

    private int getMultipleCount(DeviceSundyPercentEx deviceSundyPercent, Integer capType) {
        int count = 0;
        if (deviceSundyPercent.getPercent() == null){
            return count;
        }
        if (capType == 1){
            count = (int) (deviceSundyPercent.getPercent()/0.1);
        }else if (capType == 2){
            count = (int) (deviceSundyPercent.getPercent()/0.2);
        }else if (capType == 3){
            count = (int) (deviceSundyPercent.getPercent()/0.5);
        }
        if (count > 10){
            count = 10;
        }
        return count;
    }

    /**
     * 选择周末人群包和低质量人群包中倍数较高的人群
     * @param product
     * @param os
     * @param capType1
     * @param capType2
     * @return
     */
    public Integer compareMultiple(String product, String os,Integer capType1, Integer capType2){
        if (capType1 == 0 && capType2 == 0){
            return 0;
        } else if (capType1 > 0 && capType1.equals(capType2)){
            return capType1;
        } else if (capType1 == 0 && capType2 > 0){
            return capType2;
        }

        double flt = 1d;
        double flt2 = 1d;
        try{
            List<WeekendUpArpu> weekendUpArpus = cacheProductEx.get(product);
            if (weekendUpArpus == null){
                return capType1;
            }
            DeviceSundyPercentEx deviceSundyPercent = cachePercentEx.get(product + os);
            int co = 0;
            if(deviceSundyPercent != null && deviceSundyPercent.getCount() != null){
                co = getMultipleCount(deviceSundyPercent,capType1);
                if (co > 10 || co == 0){
                    co = deviceSundyPercent.getCount();
                }
            }
            log.info("getArpuFlt product : {} weekendUpArpus : {}", product, weekendUpArpus);
            WeekendUpArpu weekendUpArpu = null;
            for(WeekendUpArpu single: weekendUpArpus){
                if(StringUtils.equals(single.getOs(), "all")){
                    weekendUpArpu = single;
                    break;
                }else if(StringUtils.equals(single.getOs(),os)){
                    weekendUpArpu = single;
                    break;
                }
            }
            if(weekendUpArpu != null){
                WeekendUpArpuGroup weekendUpArpuGroup = weekendUpArpuGroupMapper.selectById(weekendUpArpu.getStrategyId());
                if (weekendUpArpuGroup == null){
                    return capType1;
                }
                if (capType1 == 1){
                    flt = weekendUpArpuGroup.getBase1() + weekendUpArpuGroup.getInc1() * co;
                }else if(capType1 == 2){
                    flt = weekendUpArpuGroup.getBase2() + weekendUpArpuGroup.getInc2() * co;
                }else if(capType1 >= 3){
                    flt = weekendUpArpuGroup.getBase3() + weekendUpArpuGroup.getInc3() * co;
                }
            }
            //判断低质量人群走a组还是b组
            if (ocpcSwitcher.guilvArpuSet.contains(product)){
                flt2 = getAFlt2(capType2);
            }else {
                flt2 = getBFlt2(capType2);
            }

        }catch (Exception e){
            log.error("比较倍数异常compareMultiple product : {}  ", product, e);
            return capType1;
        }
        // 只记录周末俩天的数据
        if (isWeekEnd(new Date(),ocpcSwitcher.sunDays)){
            log.info("当前周末人群包及快手人群包加倍倍数记录 flt {} flt2 {} capType1{} capType2{}", flt, flt2, capType1, capType2);
        }
        return flt >= flt2 ? capType1 : capType2;
    }

    private double getBFlt2(Integer capType2) {
        double flt = 1d;
        if (capType2 == 1){
            flt = ocpcSwitcher.lowQualityBFlt1;
        }else if(capType2 == 2){
            flt = ocpcSwitcher.lowQualityBFlt2;
        }else if(capType2 >= 3){
            flt = ocpcSwitcher.lowQualityBFlt3;
        }
        return flt;
    }

    private double getAFlt2(Integer capType2) {
        double flt = 1d;
        if (capType2 == 1){
            flt = ocpcSwitcher.lowQualityAFlt1;
        }else if(capType2 == 2){
            flt = ocpcSwitcher.lowQualityAFlt2;
        }else if(capType2 >= 3){
            flt = ocpcSwitcher.lowQualityAFlt3;
        }
        return flt;
    }


    /**
     *
     * @param deviceSundy
     * @param eventTypes 1,2
     * @param eventValues 8,3.8
     * @return
     */
    public String authAccountAppFl( UserEventReq userEventReq,DeviceSundy deviceSundy,String eventTypes,String eventValues,String accountNo,String dsp){
        String eventValuesNw = eventValues;
        String rmk = "";
        if(StringUtils.equalsIgnoreCase(eventTypes,"1,2")){
            double dfl = getArpuFlt(userEventReq.getProduct(),deviceSundy);
            eventValuesNw = getEventValuesNw(userEventReq, eventValues, accountNo, dsp, dfl, eventValuesNw);
        }
        return eventValuesNw;
    }

    /**
     * 重载authAccountAppFl方法，为30天为留存用户使用
     * @param userEventReq
     * @param peopleDevice30NoRetain
     * @param eventTypes
     * @param eventValues
     * @param accountNo
     * @param dsp
     * @return
     */

    public String authAccountAppFl( UserEventReq userEventReq,PeopleDevice30NoRetain peopleDevice30NoRetain,String eventTypes,String eventValues,String accountNo,String dsp){
        String eventValuesNw = eventValues;
        String rmk = "";
        if(StringUtils.equalsIgnoreCase(eventTypes,"1,2")){
            double dfl = getArpuFlt4NoRetain(userEventReq.getProduct(),peopleDevice30NoRetain.getCapType(),peopleDevice30NoRetain.getOs());
            eventValuesNw = getEventValuesNw(userEventReq, eventValues, accountNo, dsp, dfl, eventValuesNw);
        }
        return eventValuesNw;
    }

    public double getArpuFlt4NoRetain(String product, Integer capType, String os) {
            double flt = 1d;
            //判断低质量人群走a组还是b组
            if (ocpcSwitcher.guilvArpuSet.contains(product)){
                flt = getAFlt2(capType);
            }else {
                flt = getBFlt2(capType);
            }
            return flt;
    }

    private static String getEventValuesNw(UserEventReq userEventReq, String eventValues, String accountNo, String dsp, double dfl, String eventValuesNw) {
        String rmk;
        if(dfl >1){
            String[] ests = eventValues.split(",");
            if(ests.length==2){
                Double tihuanqianArpu = Double.parseDouble(ests[1]);
                Double arpumk = DoubleUtil.multiplicationDouble(tihuanqianArpu, dfl);

                eventValuesNw = ests[0]+","+arpumk;
                rmk = userEventReq.getUserId()+" "+ userEventReq.getProduct()+" xin2替换eventtype "+arpumk+"="+tihuanqianArpu+"*"+ dfl +" oldevents:"+ eventValues +" newEvents:"+ eventValuesNw +" userReqEvents:"+ userEventReq.getActionValues()+"  acc:"+ accountNo +" "+ dsp
                ;

                try {
                    String[] ureqApu = userEventReq.getActionValues().split(",");
                    if(ureqApu !=null && ureqApu.length==2 && ureqApu[0].split("-").length==2&& ureqApu[1].split("-").length==2){
                        Double uarpu = Double.parseDouble(ureqApu[0].split("-")[1]);
                        Double upv = Double.parseDouble(ureqApu[1].split("-")[1]);

                        if(uarpu>=arpumk && upv>= Double.parseDouble(ests[0])){
                            log.info("neng替换 "+uarpu+","+upv+" "+rmk);
                        }
                    }else{
                        log.info("解析数据出错 "+ userEventReq.getActionValues()+" "+rmk);
                    }
                }catch (Exception e){
                    log.error("neng替换错误 ",e);
                }
                userEventReq.setUarpuRmk(rmk);
                log.info(rmk);
            }
        }
        return eventValuesNw;
    }

    private static boolean isWeekEnd(Date date,Set<String> sunSet){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);

        String dateStr = DateUtils.formatDateForYMDSTR(date);
        if(sunSet.contains(dateStr)){
            return true;
        }
        return false;
    }


    public static void main(String[] args){
        Date date = DateUtils.parse("2024-09-28",DateUtils.PATTERN_YMD);
        Date date1 = DateUtils.parse("2024-10-04",DateUtils.PATTERN_YMD);
        Date date2 = DateUtils.parse("2024-10-05",DateUtils.PATTERN_YMD);
        Date date3 = DateUtils.parse("2024-10-07",DateUtils.PATTERN_YMD);
        //20240928","20241004","20241005","20241006
        Set<String> dset = Sets.newHashSet("20240928","20241004","20241005","20241006");
        System.out.println(isWeekEnd(date,dset));
        System.out.println(isWeekEnd(date1,dset));
        System.out.println(isWeekEnd(date2,dset));
        System.out.println(isWeekEnd(date3,dset));
    }
    public Pair<String,DeviceSundy> queryByDevice(UserEventReq userEventReq){
        return queryByDeviceByProductDevice(userEventReq.getProduct(), userEventReq.getOs(), userEventReq.getPkgChannel(), userEventReq.getOaid(), userEventReq.getCaid(), userEventReq.getOcpcDeviceId(),userEventReq.getSourceDeviceId());
    }
    public Pair<String,DeviceSundy> queryByDeviceByProductDevice(String product, String os, String pkgChannel, String oaid, String caid, String ocpcDeviceId,String sourceDeviceId){

        boolean isCt = ocpcSwitcher.guilvArpuSet.contains(product)
                ||  ocpcSwitcher.guilvBArpuSet.contains(product)
                || (cacheProductEx != null && cacheProductEx.containsKey(product));
        /*if(StringUtils.equalsIgnoreCase(userEventReq.getProduct(),"jyqcz")){
            isCt = StringUtils.equalsIgnoreCase("android",userEventReq.getProduct());
        }*/
        if(isCt && isWeekEnd(new Date(),ocpcSwitcher.sunDays)){

            String remark = "";

            LambdaQueryChainWrapper<DeviceSundy> lambdaQuery = lambdaQuery();
            lambdaQuery.eq(DeviceSundy::getOs,os);

            DeviceSundy deviceSundy = null;
            if(StringUtils.equalsIgnoreCase("ios",os)){
                if(StringUtils.isNotBlank(caid)){
                    lambdaQuery.eq(DeviceSundy::getCaid,caid);
                    lambdaQuery.last(" limit 1 ");
                    List<DeviceSundy> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "caid";
                    }
                }
            }else if(StringUtils.equalsIgnoreCase("android",os)){
                if(StringUtils.isNotBlank(oaid)){
                    lambdaQuery.eq(DeviceSundy::getOaid,oaid);
                    lambdaQuery.last(" limit 1 ");
                    List<DeviceSundy> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "oaid";
                    }
                }

                if(deviceSundy==null && StringUtils.isNotBlank(oaid)){
                    lambdaQuery.eq(DeviceSundy::getDeviceId,oaid);
                    lambdaQuery.last(" limit 1 ");
                    List<DeviceSundy> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "oaid";
                    }
                }

                if(deviceSundy==null && StringUtils.isNotBlank(sourceDeviceId)){
                    lambdaQuery.eq(DeviceSundy::getDeviceId,sourceDeviceId);
                    lambdaQuery.last(" limit 1 ");
                    List<DeviceSundy> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "sourceDeviceId";
                    }
                }

                if(deviceSundy==null && StringUtils.isNotBlank(sourceDeviceId)){
                    lambdaQuery.eq(DeviceSundy::getImei,sourceDeviceId);
                    lambdaQuery.last(" limit 1 ");
                    List<DeviceSundy> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "getImei";
                    }
                }

                if(deviceSundy==null && StringUtils.isNotBlank(ocpcDeviceId)){
                    lambdaQuery.eq(DeviceSundy::getDeviceId,ocpcDeviceId);
                    lambdaQuery.last(" limit 1 ");
                    List<DeviceSundy> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "ocpcDeviceId";
                    }
                }

                if(deviceSundy==null && StringUtils.isNotBlank(ocpcDeviceId)){
                    lambdaQuery.eq(DeviceSundy::getImei,ocpcDeviceId);
                    lambdaQuery.last(" limit 1 ");
                    List<DeviceSundy> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "getImei";
                    }
                }
            }
            if (deviceSundy != null) {
                log.info("成功匹配到周末人群包 deviceSunday {} {}", JSONObject.toJSONString(deviceSundy),remark);
            }
            return new Pair<>(remark,deviceSundy);
        }

        return new Pair<>(null,null);
    }



}
