package com.coohua.core.caf.dispense.enums;

public enum BaiduEventType {
    // 30天内第一次打开APP
    ACTIVATE_APP(0, "activate", "激活"),
    // 用户激活后第二天打开APP的行为
    START_APP(6, "retain_1day", "次日留存"),

    KEY_ACTION(25, "key_action", "关键行为"),
    // 付费（成单）
    ORDERS(-999, "orders", "付费"),
    // 商品下单成功,
    EC_BUY(-999, "ec_buy", "商品下单"),
    // 百度信息流回传到注册
    KEY_EVENT_FEED(25, "register", DspType.BAIDUFEED, "注册"),
    // 百度搜索回传到深度使用
    KEY_EVENT_SEM(25, "highvalue_customer", DspType.BAIDUSEM, "深度使用"),
    USER_DEFINED(25, "user_defined", DspType.BAIDUSEM, "自定义行为"),

    WATCH_AND_ARPU(357, "derived_event", "衍生行为arpu"),

    WATCH_AND_ECPM(356, "derived_event", "衍生行为ecpm"),

    ;
    public Integer value;
    public String desc;
    public DspType dspType;
    public String name;

    BaiduEventType(Integer value, String name, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

    BaiduEventType(Integer value, String name, DspType dspType, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
        this.dspType = dspType;
    }

    public static BaiduEventType getStatus(Integer value, DspType dspType) {
        if (value != null) {
            BaiduEventType[] otypes = BaiduEventType.values();
            for (BaiduEventType memberType : otypes) {
                if ((memberType.dspType == null || memberType.dspType == dspType) && value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
