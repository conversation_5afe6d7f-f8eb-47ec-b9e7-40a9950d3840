package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.CvrProduct;
import com.coohua.core.caf.dispense.ocpc.entity.CvrProductCountLog;
import com.coohua.core.caf.dispense.ocpc.mapper.CvrProductCountLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CvrProductCountLogService extends ServiceImpl<CvrProductCountLogMapper, CvrProductCountLog> {

    public CvrProductCountLog queryLastLogByCvr(CvrProduct cvrProduct) {
        return this.lambdaQuery().eq(CvrProductCountLog::getDsp, cvrProduct.getDsp()).eq(CvrProductCountLog::getProduct, cvrProduct.getProduct()).orderByDesc(CvrProductCountLog::getCreateTime).last("limit 1").one();
    }

    public void saveQueryRes(CvrProduct cvrProduct, Integer status, Double cvrAvg, String remark, Integer activeCount, Integer limitKeyCount) {
        saveQueryRes(cvrProduct, status, cvrAvg, remark, activeCount, limitKeyCount, 0, 0, 0);
    }

    public void saveQueryRes(CvrProduct cvrProduct, Integer status, Double cvrAvg, String remark, Integer activeCount, Integer limitKeyCount, Integer keyCount) {
        saveQueryRes(cvrProduct, status, cvrAvg, remark, activeCount, limitKeyCount, keyCount, 0, 0);
    }
    public void saveQueryRes(CvrProduct cvrProduct, Integer status, Double cvrAvg, String remark, Integer activeCount, Integer  limitKeyCount, Integer keyCount, Integer yCount, Integer zCount) {
        CvrProductCountLog countLog = new CvrProductCountLog();
        try {
            BeanUtils.copyProperties(cvrProduct, countLog);
            countLog.setCreateTime(new Date());
            countLog.setActiveCount(activeCount);
            countLog.setKeyCount(keyCount);
            countLog.setYCount(yCount);
            countLog.setZCount(zCount);
            countLog.setLimitKeyCount(limitKeyCount);
            countLog.setRemark(remark);
            countLog.setCvrAvg(cvrAvg);
            countLog.setCvr(0 == activeCount ? 0D : (keyCount * 1D / activeCount));
            countLog.setStatus(status);
            save(countLog);
        }catch (Exception ex) {
            log.error("saveCvrCountQueryRes err, data:{}", JSONObject.toJSONString(countLog), ex);
        }
    }
}
