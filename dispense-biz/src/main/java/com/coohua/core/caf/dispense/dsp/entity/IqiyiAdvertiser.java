package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value="IqiyiAdvertiser对象", description="")
public class IqiyiAdvertiser implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String product;

    private String productName;

    @ApiModelProperty(value = "账户id")
    private String advertiserId;

    @ApiModelProperty(value = "账户名称")
    private String advertiserName;

    private String eventTypes;

    private String eventValues;

    @ApiModelProperty(value = "数据累积期限，单位为分钟")
    private Integer maxAccumulatePeriod;

    private Integer delFlag;

    private Date createTime;

    private Date updateTime;

}
