package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.mapper.AdvertiserAliMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.coohua.core.caf.dispense.constant.BaseConstants.UNDERLINE;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.matchNewRule;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-03-20
*/
@Service
@Slf4j
public class AdvertiserAliService extends ServiceImpl<AdvertiserAliMapper, AdvertiserAli> {

    @Autowired
    XiaomiActionService xiaomiActionService;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    public static Map<String, AdvertiserAli> AccountMap = new HashMap<>();

    /** <账户id_eventType, XiaomiAction> */
    public static Map<String, XiaomiAction> ActionMap = new HashMap<>();

    @Scheduled(cron = "0 1/3 * * * ?")
    public void reloadConfig(){
        try {
            List<AdvertiserAli> xiaomiAdvertiserList = lambdaQuery().eq(AdvertiserAli::getDelFlag,0).list();
            AccountMap = xiaomiAdvertiserList.stream().collect(Collectors.toMap(AdvertiserAli::getAdvertiserId, Function.identity()));

            List<XiaomiAction> xiaomiActionList = xiaomiActionService.lambdaQuery().eq(XiaomiAction::getDelFlag,0).list();
            ActionMap = xiaomiActionList.stream().collect(Collectors.toMap(k-> k.getAdvertiserId() + UNDERLINE + k.getEventType(), Function.identity()));
        }catch (Exception e){
            log.error("",e);
        }
    }

    public AdvertiserAli getByAccountId(String accountId){
        return AccountMap.get(accountId);
    }

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {
            log.info("ali开始校验配置  "+ JSON.toJSONString(userEventReq)+" "+JSON.toJSONString(toutiaoClick));
            String  accountId = toutiaoClick.getAccountId();
            AdvertiserAli advertiserAli = getByAccountId(accountId);
            if(advertiserAli==null){
                log.error("ali无配置，请配置 "+accountId+" ");
                return false;
            }
            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();


            // 若无配置或未配置行为参数 仅当默认值时上报
            if (advertiserAli == null || StringUtils.isBlank(advertiserAli.getEventTypes()) || StringUtils.isBlank(advertiserAli.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, advertiserAli.getEventTypes(),
                        advertiserAli.getEventValues(), firstTimeJudge, "ali", advertiserAli.getProductName(),
                        advertiserAli.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, advertiserAli.getEventTypes(),
                        advertiserAli.getEventValues(), firstTimeJudge, "ali", advertiserAli.getProductName(),
                        advertiserAli.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }
}
