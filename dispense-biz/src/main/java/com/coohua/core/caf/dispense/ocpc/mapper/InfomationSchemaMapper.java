package com.coohua.core.caf.dispense.ocpc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcClick;
import com.coohua.core.caf.dispense.ocpc.entity.Tables;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface InfomationSchemaMapper  extends BaseMapper<OcpcClick> {
    @Select("select * from `information_schema`.tables where table_name ='ocpc_click_${tableEnd}'")
    List<Tables> queryTablesByEnd(@Param("tableEnd") String tableEnd);

    @Select("select * from `information_schema`.tables where table_name like 'ocpc_click_%'")
    List<Tables> queryAllClickTables();
}
