package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CvrTimingProductCountLog对象", description="cvr3.0的检查日志")
public class CvrTimingProductCountLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "检查时间")
    private Date logTime;
    @ApiModelProperty(value = "打点")
    private String product;
    @ApiModelProperty(value = "产品中文")
    private String productName;
    @ApiModelProperty(value = "昨天出价")
    private Double yesterdayPrice;
    @ApiModelProperty(value = "今天出价")
    private Double todayPrice;
    @ApiModelProperty(value = "昨天cvr")
    private Double yesterdayCvr;
    @ApiModelProperty(value = "今天cvr")
    private Double todayCvr;
    @ApiModelProperty(value = "拉线cvr")
    private Double limitCvr;
    @ApiModelProperty(value = "其他信息")
    private String remark;
    @ApiModelProperty(value = "状态 -1=不在时间范围 0=出价未生效 1=cvr比例未生效 2=生效")
    private Integer status;
    @ApiModelProperty(value = "时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
