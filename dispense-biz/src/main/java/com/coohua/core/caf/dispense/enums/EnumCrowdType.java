package com.coohua.core.caf.dispense.enums;

public enum EnumCrowdType {

    SUNDAY_CROWD(0,"周末人群包"),
    LOW_QUALITY_CROWD(1,"低质量人群包");

    public Integer value;
    public String name;

    EnumCrowdType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static EnumCrowdType getType(Integer value) {
        EnumCrowdType[] CrowdTypeList = EnumCrowdType.values();
        for (EnumCrowdType crowdType : CrowdTypeList) {
            if (crowdType.value.equals(value)) {
                return crowdType;
            }
        }
        return  null;
    }
}
