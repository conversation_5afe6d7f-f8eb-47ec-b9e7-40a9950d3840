package com.coohua.core.caf.dispense.dsp.service;

import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.KuaishouClick;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.entity.UserEventAct;
import com.coohua.core.caf.dispense.dsp.mapper.KuaishouClickMapper;
import com.coohua.core.caf.dispense.dsp.mapper.ToutiaoClickMapper;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventMapper;
import com.coohua.core.caf.dispense.enums.DspType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/10/30 15:17
 */
@Service
@Slf4j
public class DspUserEventService {
	@Resource(name = "ocpcToDspEevent")
	ThreadPoolTaskExecutor poolTaskExecutor;
	@Autowired
	private KuaishouClickMapper kuaishouClickMapper;
	@Autowired
	private ToutiaoClickMapper toutiaoClickMapper;
	@Autowired
	private UserEventMapper userEventMapper;
	@Autowired
	OcpcSwitcher ocpcSwitcher;
	@Autowired
	UserEventActService userEventActService;

	/**
	 * 双写 toDsp
	 *
	 * 同时冗余写入clickId
	 * @param toutiaoClick
	 */
	public void ocpcInsertEvent(ToutiaoClick toutiaoClick, UserEvent userEvent) {
		if (ocpcSwitcher.writeDspSwitch) {
			poolTaskExecutor.execute(() -> {
				try {
					if(DspType.KUAISHOU.name.equals(toutiaoClick.getDsp())){
						KuaishouClick kuaishouClick = new KuaishouClick();
						BeanUtils.copyProperties(toutiaoClick, kuaishouClick);
						kuaishouClick.setId(null);
						kuaishouClickMapper.insert(kuaishouClick);
						UserEvent newUserEvent = new UserEvent();
						BeanUtils.copyProperties(userEvent,newUserEvent);
						newUserEvent.setId(null);
						newUserEvent.setClickId(kuaishouClick.getId());

						//get ideaday
						newUserEvent.setPlanId(kuaishouClick.getGid());
						newUserEvent.setGroupId(kuaishouClick.getPid());
						newUserEvent.setCreativeId(kuaishouClick.getCid());
						newUserEvent.setMid(kuaishouClick.getMid());
						userEventMapper.insert(newUserEvent);
						try {
							if(newUserEvent.getEventType()==0){
								UserEventAct userEventAct = new UserEventAct();
								BeanUtils.copyProperties(newUserEvent,userEventAct);
								userEventActService.save(userEventAct);
							}
						}catch (Exception e){
							log.error("插入act ",e);
						}
						return;
					}
					ToutiaoClick newToutiaoClick = new ToutiaoClick();
					BeanUtils.copyProperties(toutiaoClick, newToutiaoClick);
					if(newToutiaoClick.getCallbackUrl()!=null && newToutiaoClick.getCallbackUrl().length()>1024){
						newToutiaoClick.setCallbackUrl(null);
					}
					newToutiaoClick.setId(null);
					toutiaoClickMapper.insert(newToutiaoClick);
					UserEvent newUserEvent = new UserEvent();
					Long id = newToutiaoClick.getId();
					BeanUtils.copyProperties(userEvent,newUserEvent);
					newUserEvent.setId(null);
					newUserEvent.setClickId(newToutiaoClick.getId());

					newUserEvent.setPlanId(toutiaoClick.getPid()+"");
					newUserEvent.setCreativeId(toutiaoClick.getCid());
					newUserEvent.setGroupId(toutiaoClick.getGid());
					newUserEvent.setMid(toutiaoClick.getMid());

					if(DspType.TOUTIAO.name.equals(toutiaoClick.getDsp())){
						newUserEvent.setUnionSite(toutiaoClick.getGroupName());
					}

					if(DspType.BAIDUFEED.name.equals(toutiaoClick.getDsp())){
						if(newUserEvent.getProduct().startsWith("bd")){
							newUserEvent.setProduct(newUserEvent.getProduct().replace("bd",""));
						}
					}
					userEventMapper.insert(newUserEvent);

					try {
						if(newUserEvent.getEventType()==0){
							UserEventAct userEventAct = new UserEventAct();
							BeanUtils.copyProperties(newUserEvent,userEventAct);
							userEventActService.save(userEventAct);
						}
					}catch (Exception e){
						log.error("插入act2 ",e);
					}

				}catch (Exception e){
					log.error("双写ocpcEvent成功异常", e);
				}
			});
		}
	}
}
