package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <p>
 * 快手app授权信息
 * </p>
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="AuthKuaishouApp对象", description="快手app授权信息")
public class AuthKuaishouApp implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private BigInteger id;

    @ApiModelProperty(value = "应用名")
    private String appName;

    @ApiModelProperty(value = "申请应用后快手返回的app_id")
    private String appId;

    @ApiModelProperty(value = "申请应用后快手返回的secret")
    private String secret;

    @ApiModelProperty(value = "公司主体")
    private String companyName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "state区分渠道")
    private String state;

    @ApiModelProperty(value = "回调地址")
    private String redirectUri;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private Integer createId;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Integer updateId;

    @ApiModelProperty(value = "更新人")
    private String updateName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "auth_code")
    private String authCode;

    @ApiModelProperty(value = "的有效期是1天。当access_token超时后，可以使用refresh_token进行刷新")
    private String accessToken;

    @ApiModelProperty(value = "有效期是30天。当refresh_token失效的后，需要通过获取token，得到全新access_token和refresh_token。")
    private String refreshToken;

    @ApiModelProperty(value = "广告主ID")
    private Long advertiserId;

    @ApiModelProperty(value = "授权地址")
    private String authUrl;

    @ApiModelProperty(value = "开发者id")
    private String developerId;

    @ApiModelProperty(value = "登录账号")
    private String accountName;

    @ApiModelProperty(value = "登录密码")
    private String accountPassword;

    private Integer eventType;

    private Integer eventNumber;

    private String eventTypes;

    private String eventValues;

    /**
     * 数据累积周期
     */
    private Integer maxAccumulatePeriod;

    /**
     * 1普通账户 2达人账户
     */
    private Integer accountType;

    /**
     * 回传要求类型 - 流量助推
     */
    private String eventTypesLlzt;

    /**
     * 回传要求数值 - 流量助推
     */

    private String eventValuesLlzt;

    /**
     * 回传数据累积周期 - 流量助推
     */
    private Integer maxAccumulatePeriodLlzt;

    /**
     * 回传要求类型 - 直播
     */
    private String eventTypesZhibo;

    /**
     * 回传要求数值 - 直播
     */
    private String eventValuesZhibo;

    /**
     * 数据累积周期 - 直播
     */
    private Integer maxAccumulatePeriodZhibo;
}
