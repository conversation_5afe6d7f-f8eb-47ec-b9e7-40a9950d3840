package com.coohua.core.caf.dispense.dsp.service.bytemin;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthToutiaoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoDeliveryPlanService;
import com.coohua.core.caf.dispense.dto.req.ByteActiveReq;
import com.coohua.core.caf.dispense.dto.req.Props;
import com.coohua.core.caf.dispense.dto.req.TTWxReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.math.BigInteger;
import java.util.*;

@Component
@Slf4j
public class ByteMiniService {
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    RedisEventService redisEventService;
    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    ByteMinThirdService byteMinThirdService;
    @Autowired
    ByteAdService byteAdService;
    @Autowired
    ToutiaoDeliveryPlanService toutiaoDeliveryPlanService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @ApolloJsonValue("${ocpc.byte.dsendUids:[]}")
    public Set<String> dsendUids;
    //https://bytedance.feishu.cn/docx/CWF8di9wFo2hqFxOPHhcydjEnZf 文档
    public void sendByteEvent(ByteActiveReq byteActiveReq,boolean isMinProm){

        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setUserId(byteActiveReq.getUserId());
        userEventReq.setOs(ByteActiveReq.getOs());
        userEventReq.setAppId(byteActiveReq.getAppId());
        userEventReq.setProduct(byteActiveReq.getProduct());
        OcpcEvent ocpcActiveEvent = redisEventService.getActiveEvent(userEventReq);
        OcpcEvent ocpcKeyEvent = redisEventService.queryKeyEvent(userEventReq);

        //首先拉取ecpm值
        Pair<Integer,Double>  adPair = byteAdService.getUserArpu(byteActiveReq,ocpcActiveEvent,isMinProm);

        if(ocpcActiveEvent!=null){
            if(StringUtils.isNotBlank(ocpcActiveEvent.getCallbackUrl())
                    && !StringUtils.equalsIgnoreCase("undefined",ocpcActiveEvent.getCallbackUrl())
                    && !StringUtils.equalsIgnoreCase("__CLICKID__",ocpcActiveEvent.getCallbackUrl())){
                boolean keyFlg = ocpcKeyEvent==null;
                log.info("bytemin获取到uid激活evetn为 "+JSON.toJSONString(ocpcActiveEvent));
                if(keyFlg){
                    Date createDate = ocpcActiveEvent.getCreateTime();
                    long hours = DateUtil.between(createDate,new Date(), DateUnit.HOUR);
                    if(hours>24){
                        log.info("当前用户已经超过24H "+byteActiveReq.getUserId());
                        return;
                    }

                    ToutiaoClick toutiaoClick = redisEventService.convertEventToClick(ocpcActiveEvent);
                    toutiaoClick.setClickId(ocpcActiveEvent.getCallbackUrl());
                    if(StringUtils.equalsIgnoreCase("undefined",toutiaoClick.getAccountId())){
                        String accountId = toutiaoDeliveryPlanService.getAccountId(ocpcActiveEvent.getPlanId());
                        toutiaoClick.setAccountId(accountId);
                        log.info("bytemin替换account "+ocpcActiveEvent.getPlanId()+"@"+accountId);
                    }
                    BigInteger accountId = new BigInteger(toutiaoClick.getAccountId());
                    boolean isManju = isManzuArpu(byteActiveReq,accountId,adPair);
                    if(isManju){
                        UserEventReq userEventReq2 = getUevent(byteActiveReq,ToutiaoEventTypeEnum.KEY_EVENT.value);
                        userEventReq2.setClickId(ocpcActiveEvent.getCallbackUrl());
                        byteActiveReq.setClickid(ocpcActiveEvent.getCallbackUrl());

                        boolean isActiveEvent = ocpcSwitcher.toutiaoToActiveAccount.contains(byteActiveReq.getAccountId());

                        Pair<String,String>  sendPair = sendByteEvt(byteActiveReq, isActiveEvent ? ToutiaoEventTypeEnum.ACTIVATE_APP : ToutiaoEventTypeEnum.KEY_EVENT);
                        //userEventReq.getActionValues() + ";" + userEventReq.getAccumulateDuration()
                        userEventReq2.setActionValues(adPair.getKey()+"-"+adPair.getValue());
                        UserEvent userEvent = ocpcEventService.saveUserEvent(toutiaoClick, userEventReq2, "", sendPair.getValue());
                        if(isActiveEvent) {
                            log.info("byte用户 " + byteActiveReq.getUserId() + " 回传关键行为成功");
                        }else {
                            log.info("byte用户 " + byteActiveReq.getUserId() + " 回传关键行为假激活成功");
                        }
                    }
                }else{
                    log.info("byte用户 "+byteActiveReq.getUserId()+" 已经回传关键行为");
                }
            }else{
                log.info("byte核心行为 "+byteActiveReq.getUserId()+" 非投放用户 "+ JSON.toJSONString(byteActiveReq));
            }
        }

    }
    public boolean isManzuArpu(ByteActiveReq byteActiveReq,BigInteger accountId,Pair<Integer,Double>  adPair){
        AuthToutiaoAdvertiser authToutiaoAdvertiser = authToutiaoAdvertiserService.getAccountActionMap().get(accountId);
        if(authToutiaoAdvertiser!=null){
            if(BaseConstants.NUM_ARPU_RULE.equalsIgnoreCase(authToutiaoAdvertiser.getEventTypes())){
                //一期只支持 次数+arpu
                String evals = authToutiaoAdvertiser.getEventValues();
                if(StringUtils.isNotBlank(evals)){
                    Integer cishu = Integer.parseInt(evals.split(",")[0]);
                    Double arpu = Double.parseDouble(evals.split(",")[1]);
                    //实时计算当前用户的arpu ecpm
                    if(adPair.getKey()>=cishu && adPair.getValue()>=arpu){
                        log.info("符合byte回传条件 "+byteActiveReq.getUserId()+"  次数为 "+JSON.toJSONString(adPair));
                        return true;
                    }else{
                        log.info("不符合byte回传条件 "+byteActiveReq.getUserId()+"  次数为 "+JSON.toJSONString(adPair));
                    }
                }else{
                    log.warn("bytemin账户配置错误 为空 "+evals+" "+accountId);
                }
            }else if(BaseConstants.NUM_ECPM_RULE.equalsIgnoreCase(authToutiaoAdvertiser.getEventTypes())){
                String evals = authToutiaoAdvertiser.getEventValues();
                if(StringUtils.isNotBlank(evals)) {
                    Integer cishu = Integer.parseInt(evals.split(",")[0]);
                    Double ecpm = Double.parseDouble(evals.split(",")[1]);

                    Double junEcpm = DoubleUtil.divideDouble(adPair.getValue()*1000d,cishu*1d);
                    if(adPair.getKey()>=cishu && junEcpm >= ecpm){
                        log.info("符合byte回传条件 "+byteActiveReq.getUserId()+" junEcpm="+junEcpm+"  ecpm均次数为 "+JSON.toJSONString(adPair));
                        return true;
                    }

                }else{
                    log.warn("bytemin账户ecpm配置错误 为空 "+evals+" "+accountId);
                }
            }else{
                log.warn("bytemin账户配置错误 只支持 "+BaseConstants.NUM_ARPU_RULE+" "+accountId);
            }
        }else{
            log.warn("bytemin账户为空 "+accountId);
        }
        return false;
    }


    public void sendByteAct(ByteActiveReq byteActiveReq){
        boolean isActive = setFirstOpenidActive(byteActiveReq.getProduct(),byteActiveReq.getOpenId(),byteActiveReq.getUserId());

        if(isActive || dsendUids.contains(byteActiveReq.getUserId())){
            //指定账户关键行为回传激活
            if (ocpcSwitcher.toutiaoToActiveAccount.contains(byteActiveReq.getAccountId()) ) {
                String before = byteActiveReq.getClickid();
                //防止 click_id_t 太长入不了库
                for(String click : before.split(",")) {
                    if(StringUtils.isNotBlank(click)) {
                        byteActiveReq.setClickid(click);
                        break;
                    }
                }
                //clueToken 存储在callBack中
                ToutiaoClick toutiaoClick = getTclick(byteActiveReq);
                UserEventReq userEventReq = getUevent(byteActiveReq, ToutiaoEventTypeEnum.ACTIVATE_APP.value);
                UserEvent userEvent = ocpcEventService.saveUserEvent(toutiaoClick, userEventReq, "", "头条ocpc指定账户 不回传除关键行为外的事件");
                return;
            }
            Pair<String, String> pair = sendByteEvt(byteActiveReq, ToutiaoEventTypeEnum.ACTIVATE_APP);
            //存入有效的clickId 都存 click_id_t 太长入不了库
            byteActiveReq.setClickid(pair.getKey());
            //clueToken 存储在callBack中
            ToutiaoClick toutiaoClick = getTclick(byteActiveReq);
            UserEventReq userEventReq = getUevent(byteActiveReq, ToutiaoEventTypeEnum.ACTIVATE_APP.value);
            UserEvent userEvent = ocpcEventService.saveUserEvent(toutiaoClick, userEventReq, "", pair.getValue());
            log.info("byte用户 " + byteActiveReq.getUserId() + " 回传激活行为成功");
        }else{
            UserEventReq userEventReq = getUevent(byteActiveReq,ToutiaoEventTypeEnum.ACTIVATE_APP.value);
            OcpcEvent ocpcActiveEvent = redisEventService.getActiveEvent(userEventReq);
            if(ocpcActiveEvent!=null){
                Date cstart = DateUtils.getStartOfDate(ocpcActiveEvent.getCreateTime());

                Date ecurDate = DateUtils.getStartOfDate(new Date());
                long days = DateUtil.between(cstart,ecurDate, DateUnit.DAY);
                userEventReq.setEventType(ToutiaoEventTypeEnum.START_APP.value);
                boolean isStp = setStartUp(byteActiveReq.getProduct(),byteActiveReq.getUserId());
                if(days>0 && isStp){
                    log.info("byte用户 "+byteActiveReq.getUserId()+" 开始次留事件");
                    Pair<String,String>  pair = sendByteEvt(byteActiveReq,ToutiaoEventTypeEnum.START_APP);
                    //clueToken 存储在callBack中
                    ToutiaoClick toutiaoClick = getTclick(byteActiveReq);

                    OcpcEvent ocpcEvent = new OcpcEvent();
                    ocpcEvent.setUserId(byteActiveReq.getUserId());
                    ocpcEvent.setCreateTime(new Date());
                    ocpcEvent.setUpdateTime(new Date());
                    ocpcEvent.setAccountId(toutiaoClick.getAccountId());
                    ocpcEvent.setReqUrl("");
                    ocpcEvent.setReqRsp(pair.getValue());
                    ocpcEvent.setProduct(byteActiveReq.getProduct());
                    ocpcEvent.setOs(ByteActiveReq.getOs());
                    ocpcEvent.setEventType(ToutiaoEventTypeEnum.START_APP.value);
                    ocpcEvent.setEventTypeName(ToutiaoEventTypeEnum.START_APP.desc);
                    ocpcEventService.save(ocpcEvent);
                    log.info("byte用户 "+byteActiveReq.getUserId()+" 次留行为成功");
                }
            }else{
                log.info("byte用户 "+byteActiveReq.getUserId()+" 自然量直接忽略");
            }
        }
    }
    //头条 微信小程序激活
    public Pair<String,String> sendByteEvt(ByteActiveReq byteActiveReq,ToutiaoEventTypeEnum toutiaoEventTypeEnum){
        CloseableHttpClient client = null;
        String reqUrl = "";
        String rsp = "";
        try {
            if(StringUtils.isNotBlank(byteActiveReq.getClickid())){
                for(String click : byteActiveReq.getClickid().split(",")) {
                    JSONObject body = new JSONObject()
                            .fluentPut("event_type", toutiaoEventTypeEnum.typeV2)
                            .fluentPut("context", new JSONObject()
                                    .fluentPut("ad", new JSONObject()
                                            .fluentPut("callback", click)))
                            .fluentPut("timestamp", System.currentTimeMillis());

                    log.info("byte小游戏开始请求  " + ToutiaoCallService.ToutiaoCallbackUrlV2 + " " + body.toJSONString());
                    client = toutiaoCallService.getHttpClient();
                    rsp = toutiaoCallService.resendUrlPost(ToutiaoCallService.ToutiaoCallbackUrlV2, body.toJSONString(), client);
                    reqUrl = click;

                    log.info("byte小游戏 开始请求" + toutiaoEventTypeEnum.desc + " " + byteActiveReq.getUserId() + " " + body.toJSONString() + " 响应为 " + rsp);
                    if(StringUtils.isNotBlank(rsp) && rsp.contains("\"code\":0")) {
                        break;
                    }
                }
            }else{
                log.info("byte小游戏 已经激活 直接忽略 "+byteActiveReq.getUserId()+" "+JSON.toJSONString(byteActiveReq));
            }
        } catch (Exception e) {
            log.error("头条回传异常", e);
        }finally {
            if(client!=null){
                try {
                    client.close();
                }catch (Exception e){
                    log.error("httpclient 关闭错误 ",e);
                }
            }
        }
        return new Pair<>(reqUrl,rsp);
    }


    public String getStBkey(String product,String userId){
        return "byte:ST:"+product+":"+userId;
    }

    public boolean setStartUp(String product,String userId){
        if(StringUtils.isNotBlank(product) &&  StringUtils.isNotBlank(userId)){
            String bkey = getStBkey(product,userId);
            String s = bpDispenseJedisClusterClient.get(bkey);

            if(StringUtils.isNotBlank(s)){
                return false;
            }else{
                bpDispenseJedisClusterClient.setex(bkey, DateTimeConstants.SECONDS_PER_DAY * 2 * 365, "1");
                return true;
            }
        }
        return true;
    }
    public String getBkey(String product,String openId,String userId){
        return "byte:"+product+":"+openId+":"+userId;
    }
    public boolean setFirstOpenidActive(String product,String openId,String userId){
        if(StringUtils.isNotBlank(product) &&  StringUtils.isNotBlank(openId) &&  StringUtils.isNotBlank(userId)){
            String bkey = getBkey(product,openId,userId);
            String s = bpDispenseJedisClusterClient.get(bkey);

            if(StringUtils.isNotBlank(s)){
                return false;
            }else{
                bpDispenseJedisClusterClient.setex(bkey, DateTimeConstants.SECONDS_PER_DAY * 2 * 365, "1");
                return true;
            }
        }
        return false;
    }

    public boolean isActive(UserEventReq userEventReq) {
        //小程序激活
        boolean isByteChannel = StringUtils.isNotBlank(userEventReq.getPkgChannel()) && Objects.equals(DspType.BYTEMIN.value, userEventReq.getPkgChannel());
        if(isByteChannel && StringUtils.isNotBlank(userEventReq.getProduct()) &&  StringUtils.isNotBlank(userEventReq.getOpenId()) &&  StringUtils.isNotBlank(userEventReq.getUserId())){
            boolean isExists = bpDispenseJedisClusterClient.exists(getBkey(userEventReq.getProduct(),userEventReq.getOpenId(),userEventReq.getUserId()));
            if(isExists ){
                OcpcEvent ocpcActiveEvent = redisEventService.getActiveEvent(userEventReq);
                return null != ocpcActiveEvent && StringUtils.isNotBlank(ocpcActiveEvent.getCallbackUrl())
                        && !StringUtils.equalsIgnoreCase("undefined",ocpcActiveEvent.getCallbackUrl())
                        && !StringUtils.equalsIgnoreCase("__CLICKID__",ocpcActiveEvent.getCallbackUrl());
            }
        }
        return false;
    }


    public Integer getRandInt(){
        Random random = new Random();
        return random.nextInt(50000);
    }


    private ToutiaoClick getTclick(ByteActiveReq byteActiveReq){
        //https://ocpc-api.shinet.cn/dispense/toutiao/click?
        //dsp=toutiao&product=cnaxx&os=android&account_id=****************&
        //ocpc_device_id=__IMEI__&ts=__TS__&callback_url=__CALLBACK_PARAM__&
        //cid=__MID3__&gid=__PROJECT_ID__&pid=__PROMOTION_ID__&oaid=__OAID__&
        //union_site=__UNION_SITE__&androidId=__ANDROIDID__&mac=__MAC__&
        //oaid2=__OAID_MD5__&ip=__IP__&ua=__UA__&model=__MODEL__&
        //clickId=__REQUEST_ID__
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(DspType.BYTEMIN.value);
        toutiaoClick.setOpenId(byteActiveReq.getOpenId());
        toutiaoClick.setAccountId(byteActiveReq.getAccountId());
        toutiaoClick.setOs(byteActiveReq.getOs());
        toutiaoClick.setProduct(byteActiveReq.getProduct());
        toutiaoClick.setPid(byteActiveReq.getPromotionid());
        toutiaoClick.setClickId(byteActiveReq.getClickid());
        toutiaoClick.setCallbackUrl(byteActiveReq.getClickid());
        toutiaoClick.setCreateTime(new Date());
        return toutiaoClick;
    }

    private UserEventReq getUevent(ByteActiveReq byteActiveReq,Integer eventType){
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setProduct(byteActiveReq.getProduct());
        userEventReq.setOs(byteActiveReq.getOs());
        userEventReq.setEventType(eventType);
        userEventReq.setUserId(byteActiveReq.getUserId());
        userEventReq.setOpenId(byteActiveReq.getOpenId());
        userEventReq.setAppId(byteActiveReq.getAppId());
        return userEventReq;
    }
    public static String getSign(String token,String randNotice,String timestmp){
        String[] arr = new String[]{token,randNotice,timestmp};
        Arrays.sort(arr);
        StringBuffer sn = new StringBuffer();
        for(String ddf : arr){
            sn.append(ddf);
        }

        String sign = DigestUtils.sha1Hex(sn.toString());
        return sign;
    }

    public static void main(String[] args){
//        假如token="D528C6F23C7D7"
//        nonce="234", timestamp="1646989046"，
//对排序后字符串进行sha1转换，结果为"a3c1ad351db9b88c1c5cb7a4cab977f8cd146524"
        System.out.println(getSign("D528C6F23C7D7","234","1646989046"));


        TTWxReq ttwxReq = new TTWxReq();
        ttwxReq.setClue_token("toutiaoClick.getCallbackUrl()");
        ttwxReq.setEvent_type("2");
        ttwxReq.setOpen_id("opid");
        ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(2);
        if(ToutiaoEventTypeEnum.PURCHASE.equals(toutiaoEventTypeEnum)){
            Props props = new Props();
            props.setPay_amount(50);
            ttwxReq.setProps(props);
        }

        System.out.println(JSON.toJSONString(ttwxReq));
    }
}
