package com.coohua.core.caf.dispense.ocpc.entity;

import lombok.Data;

import java.util.Date;

@Data
public class UserEcpmOcpcCheckLog {

    private Long id;
    private String dsp;
    private String productName;
    private String name;
    private String accountId;
    private Date createTime;
    private Integer natureActiveCount;
    private Integer natureKeyCount;
    private Integer natureNotCallback;
    private Integer natureQueryCallback;
    private Integer hourActiveCount;
    private Integer hourKeyCount;
    private Integer hourNotCallback;
    private Integer hourQueryCallback;
}
