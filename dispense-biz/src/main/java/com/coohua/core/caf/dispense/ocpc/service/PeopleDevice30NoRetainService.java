package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.WeekendUpArpu;
import com.coohua.core.caf.dispense.dsp.mapper.WeekendUpArpuGroupMapper;
import com.coohua.core.caf.dispense.dsp.mapper.WeekendUpArpuMapper;
import com.coohua.core.caf.dispense.dsp.service.ProductService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.DeviceSundy;
import com.coohua.core.caf.dispense.ocpc.entity.PeopleDevice30NoRetain;
import com.coohua.core.caf.dispense.ocpc.mapper.DeviceSundyMapper;
import com.coohua.core.caf.dispense.ocpc.mapper.PeopleDevice30NoRetainMapper;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

@Service
@Slf4j
public class PeopleDevice30NoRetainService  extends ServiceImpl<PeopleDevice30NoRetainMapper, PeopleDevice30NoRetain> {
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @Autowired
    WeekendUpArpuGroupMapper weekendUpArpuGroupMapper;

    @Autowired
    WeekendUpArpuMapper weekendUpArpuMapper;

    @Autowired
    ProductService productService;


    public static Map<String,List<WeekendUpArpu>> cacheProductEx = new HashMap<>();

    @PostConstruct
    @Scheduled(cron = "0 0/5 * * * ? ")
    public void refresh(){
        QueryWrapper<WeekendUpArpu> params = new QueryWrapper<>();
        params.lambda().eq(WeekendUpArpu::getStatue, 1)
                .eq(WeekendUpArpu::getDelFlag, 0);

        List<WeekendUpArpu> temp = weekendUpArpuMapper.selectList(params);
        Map<String,List<WeekendUpArpu>> tempMap = new HashMap<>();

        for(WeekendUpArpu param : temp){
            List<WeekendUpArpu> tempList = tempMap.getOrDefault(param.getProduct(), new ArrayList<>());
            tempList.add(param);
            tempMap.put(param.getProduct(), tempList);
        }

        cacheProductEx = tempMap;
    }

    public Pair<String, PeopleDevice30NoRetain> queryLowQualifyByDevice(UserEventReq userEventReq) {
        return queryLowQualifyByProductDevice(userEventReq.getProduct(), userEventReq.getOs(), userEventReq.getPkgChannel(), userEventReq.getOaid(), userEventReq.getCaid(), userEventReq.getOcpcDeviceId());
    }

    public Pair<String, PeopleDevice30NoRetain> queryLowQualifyByProductDevice(String product, String os, String pkgChannel, String oaid, String caid, String ocpcDeviceId) {
        Set<String> productNameSet = productService.getProductNameSet();
        // log.info("productNameSet:{}", JSONObject.toJSONString(productNameSet));
        boolean isCt = ocpcSwitcher.guilvArpuSet.contains(product)
                ||  ocpcSwitcher.guilvBArpuSet.contains(product)
                || productNameSet.contains(product);
        if(isCt){
            String remark = "";

            LambdaQueryChainWrapper<PeopleDevice30NoRetain> lambdaQuery = lambdaQuery();

            PeopleDevice30NoRetain deviceSundy = null;
            if(StringUtils.equalsIgnoreCase("ios",os)){
                if(StringUtils.isNotBlank(caid)){
                    lambdaQuery.eq(PeopleDevice30NoRetain::getCaid,caid);
                    lambdaQuery.last(" limit 1 ");
                    List<PeopleDevice30NoRetain> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "caid";
                    }
                }
            }else if(StringUtils.equalsIgnoreCase("android",os)){
                if(StringUtils.isNotBlank(oaid)){
                    lambdaQuery.eq(PeopleDevice30NoRetain::getOaid,oaid);
                    lambdaQuery.last(" limit 1 ");
                    List<PeopleDevice30NoRetain> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "oaid";
                    }
                }

                if(deviceSundy==null && StringUtils.isNotBlank(ocpcDeviceId)){
                    lambdaQuery.eq(PeopleDevice30NoRetain::getDeviceId,ocpcDeviceId);
                    lambdaQuery.last(" limit 1 ");
                    List<PeopleDevice30NoRetain> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "ocpcDeviceId";
                    }
                }

                if(deviceSundy==null && StringUtils.isNotBlank(ocpcDeviceId)){
                    lambdaQuery.eq(PeopleDevice30NoRetain::getImei,ocpcDeviceId);
                    lambdaQuery.last(" limit 1 ");
                    List<PeopleDevice30NoRetain> deviceSundyList = lambdaQuery.list();
                    if(deviceSundyList!=null && deviceSundyList.size()>0){
                        deviceSundy = deviceSundyList.get(0);
                        remark = "getImei";
                    }
                }
            }
            return new Pair<>(remark,deviceSundy);
        }

        return new Pair<>(null,null);
    }
}
