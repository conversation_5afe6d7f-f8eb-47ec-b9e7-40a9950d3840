package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

@Component
@Slf4j
public class HbaseRtaService {
    @Resource
    private Connection hbaseConnection;
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @Resource(name = "ocpcToHbaseActive")
    ThreadPoolTaskExecutor poolTaskExecutor;

    private String rtaKeyEventTname = "rtaKeyEventT";

//    @PostConstruct
    public HTableDescriptor createOcpcTable() {
        try (Admin admin = hbaseConnection.getAdmin()) {
            // 建表
            try {
                HTableDescriptor htdDb = admin.getTableDescriptor(TableName.valueOf(rtaKeyEventTname));
                return htdDb;
            } catch (TableNotFoundException te) {
                HTableDescriptor htd = new HTableDescriptor(TableName.valueOf(rtaKeyEventTname));
                HColumnDescriptor hcd = new HColumnDescriptor("family");
                hcd.setTimeToLive(30* DateTimeConstants.SECONDS_PER_DAY); // 设置TTL过期时间4天 2 * 24 * 60 * 60
                htd.addFamily(hcd);
                admin.createTable(htd);
                return htd;
            }
        } catch (IOException e) {
            log.error("", e);
        }
        return null;
    }

    public OcpcEvent getUserKeyEvent(String product,String os,String userId){
        String userKey = getUserKeyEvt(product,os,userId);

        try (Table table = hbaseConnection.getTable(TableName.valueOf(rtaKeyEventTname))) {

            Get get = new Get(Bytes.toBytes(userKey));
            Result result = table.get(get);
            String content = HbaseUtils.getCellValStr(result);
            if (StringUtils.isNotBlank(content)) {
                OcpcEvent userEvent = JSON.parseObject(content,OcpcEvent.class);
                return userEvent;
            }
        } catch (Exception e) {
            log.error("hbase查下异常 ", e);
        }
        return null;
    }

    public void saveUserKeyEvent(OcpcEvent userEvent){
        poolTaskExecutor.submit(()->{
            String eventJson = JSON.toJSONString(userEvent);
            byte[] eventBytes = Bytes.toBytes(eventJson);

            String userKey = getUserKeyEvt(userEvent.getProduct(),userEvent.getOs(),userEvent.getUserId());

            try (Table table = hbaseConnection.getTable(TableName.valueOf(rtaKeyEventTname))) {
                Put put = new Put(Bytes.toBytes(userKey));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), eventBytes);
                table.put(put);
            } catch (Exception e) {
                log.error("存储关键行为事件至hbase出现异常 ", e);
            }
        });
    }

    private String getUserKeyEvt(String product,String os,String userId){
        String uske = product+"@"+os+"@"+userId;
        return uske;
    }
}
