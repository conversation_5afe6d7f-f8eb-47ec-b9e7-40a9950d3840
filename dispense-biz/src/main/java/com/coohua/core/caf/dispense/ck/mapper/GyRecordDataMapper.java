package com.coohua.core.caf.dispense.ck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.core.caf.dispense.ck.entity.GyRecordDataLocal;
import com.coohua.core.caf.dispense.dsp.entity.OcpcPayReq;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

public interface GyRecordDataMapper extends BaseMapper<GyRecordDataLocal> {

    @Insert({
            "<script>",
            "INSERT INTO `ods`.`gy_record_data_local` (logday,user_id, product, os,oaid,android_id,caid,idfa,gy_type) VALUES",
            "<foreach collection='gyRecordDataList' item='gyRecordData' separator=','>",
            "(#{gyRecordData.logday},#{gyRecordData.userId}, #{gyRecordData.product}, #{gyRecordData.os},#{gyRecordData.oaid},#{gyRecordData.androidId},#{gyRecordData.caid},#{gyRecordData.idfa},#{gyRecordData.gyType})",
            "</foreach>",
            "</script>"
    })
    void insertBatch(@Param("gyRecordDataList") List<GyRecordDataLocal> gyRecordDataList);

}
