package com.coohua.core.caf.dispense.dto.req;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.enums.TencentActionType;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
/**
 * 请求广点通 事件组装
 */
@Data
public class AddTecentActiveReq {
    private String account_id;
    private String user_action_set_id;
    private List<TencentAction> actions;

    //DOC TO https://developers.e.qq.com/docs/guide/user_actions/convertion_app#dc_dataadd_gdt
    public static AddTecentActiveReq getTencentEventReq(String user_action_set_id,
                                                        String clickId,String accountId,
                                                        String action_type,String imei,
                                                        String oaid,
                                                        String os,
                                                        UserEventReq userEventReq
                                                        ){
        AddTecentActiveReq tecentActive = new AddTecentActiveReq();

        tecentActive.setAccount_id(accountId);

        List<TencentAction> tencentActionList = new ArrayList<>();

        TencentAction tencentAction = new TencentAction();

        tencentAction.setAction_time(((new Date()).getTime())/1000);
        //
        if(TencentActionType.START_APP.name.equals(action_type)) {
	        JSONObject jsonObject = new JSONObject();
	        jsonObject.put("length_of_stay",1);
	        tencentAction.setAction_param(jsonObject);
        }

        if(TencentActionType.PURCHASE.name.equals(action_type)&&userEventReq.getPayAmount()!=null&&userEventReq.getPayAmount()>0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value",userEventReq.getPayAmount());
            tencentAction.setAction_param(jsonObject);
            log.info("ocpc支付 腾讯1.0  "+jsonObject.toJSONString());
        }

        // 自定义关键行为 需额外传字段
        if(TencentActionType.KEY_EVENT.name.equals(action_type)) {
            tencentAction.setCustom_action("UV_CORE_ACTION");
        }

        tencentAction.setAction_type(action_type);
        JSONObject jobj = new JSONObject();
        jobj.put("click_id",clickId);
        tencentAction.setTrace(jobj);

        boolean isIos = os!=null && "ios".equals(os);

        TecentUserE tecentUserE = new TecentUserE();

        if (!isIos) {
            if(StringUtils.isNotBlank(oaid) && !"null".equals(oaid)){
                tecentUserE.setOaid(oaid);
            }
        }

        if (StringUtils.isNotBlank(imei)&& !"null".equals(imei)) {
            if(isIos){
                // 包含-时认为是idfa原值
                if (imei.contains("-") && imei.length() == 36) {
                    imei = MD5Utils.getMd5Sum(imei);
                }
                // ios 传 idfa 参数
                tecentUserE.setHash_idfa(imei);
            } else {
                // android 传 imei 参数
                tecentUserE.setHash_imei(imei);
            }
        }


        if(StringUtils.isNotBlank(userEventReq.getOpenId())){
            String wAppid = ProductCache.getWAppid(userEventReq.getOpenId(),userEventReq.getProduct());
            if(StringUtils.isNotBlank(wAppid)){
                tecentUserE.setWechat_openid(userEventReq.getOpenId());
                tecentUserE.setWechat_app_id(wAppid);
            }
        }
        tencentAction.setUser_id(tecentUserE);
        tencentActionList.add(tencentAction);

        tecentActive.setActions(tencentActionList);
        tecentActive.setUser_action_set_id(user_action_set_id);


        return tecentActive;
    }
}

/**
 *
 * {
 * “account_id”: “<your_account_id>“,
 * “user_action_set_id”: “<your_user_action_set_id>“,
 * “actions”: [
     * {
     * “action_time”: <action_timestamp>,
         * “user_id”: {
             * “hash_imei”: “<MD5_hash_imei>“,
             * “hash_phone”: “<MD5_hash_phone_number>“, //非必填, 电话号码直接MD5编码
             * “hash_android_id”: “<MD5_hash_android_id>“, //非必填, 对 android_id 进行MD5编码
             * “hash_mac”:”<MD5_hash_mac>” //非必填, mac 地址去掉‘:’ 后保持大写进行MD5编码
             * "wechat_app_id":"", //微信小游戏上报必填 且必须通过授权
             *         "wechat_openid":"", //微信小游戏上报必填
             *                                “oaid”: “<oaid>”   //Android选填，推荐使用
         * },
     * “action_type”: “ACTIVATE_APP”,
     * “trace”: {
     * “click_id”: “<CLICK_ID>” //非必填，推荐使用方案一的时候填入
     * }
     * }
     * ]
 * }
 *
 *
 *
 */
@Data
class TencentAction{
    private long action_time;
    private TecentUserE user_id;
    private String action_type;
    private String custom_action;
	private JSONObject action_param;
    private JSONObject trace;
}
@Data
class TecentUserE{
    private String hash_imei;
    private String hash_phone;
    private String hash_mac;
    private String oaid;

    private String hash_idfa;
    private String wechat_app_id;
    private String wechat_openid;
}