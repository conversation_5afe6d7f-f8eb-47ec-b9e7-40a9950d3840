package com.coohua.core.caf.dispense.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 关键行为衍生事件相关apollo配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ToutiaoLtvApolloConfig {

    /**
     * 头条ltv回传 是否对全部产品生效
     */
    @Value("${ocpc.toutiao.ltv.enable.all:false}")
    public boolean toutiaoLtvEnableAll;

    /**
     * 头条ltv回传 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.toutiao.ltv.enable.products:[]}")
    public Set<String> toutiaoLtvEnableProducts;

    /**
     * 头条判断是否是无需生效的产品
     * @return 若该产品无需生效 返回true
     */
    public boolean illegalProduct(String product) {
        return !toutiaoLtvEnableAll && !toutiaoLtvEnableProducts.contains(product);
    }


}
