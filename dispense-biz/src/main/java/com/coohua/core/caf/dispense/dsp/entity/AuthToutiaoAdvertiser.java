package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <p>
 * 广告主
 * </p>
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="AuthToutiaoAdvertiser对象", description="广告主")
public class AuthToutiaoAdvertiser implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private BigInteger id;

    @ApiModelProperty(value = "关联客户中心表ID ")
    private BigInteger customerId;

    @ApiModelProperty(value = "广告主ID（自动生成）")
    private BigInteger advertiserId;

    @ApiModelProperty(value = "名称")
    private String advertiserName;

    @ApiModelProperty(value = "产品表ID（运营选择）")
    private BigInteger productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "代理商ID")
    private String agentId;

    @ApiModelProperty(value = "代理商名称")
    private String agentName;

    @ApiModelProperty(value = "投放类型")
    private String putType;

    @ApiModelProperty(value = "花费")
    private Double costMoney;

    @ApiModelProperty(value = "分组ID")
    private Integer groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private Integer createId;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Integer updateId;

    @ApiModelProperty(value = "更新人")
    private String updateName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private Integer eventType;

    private Integer eventNumber;

    /**
     * 事件回传阈值：通配版；eventType > 1时适用
     */
    private String eventValue;

    private String eventTypes;

    private String eventValues;

    /**
     * 数据累积周期
     */
    private Integer maxAccumulatePeriod;

    /**
     * 是否回传ltv数值 1:回传 0:不回传
     */
    private Integer callbackLtv;

    /**
     * 1普通账户 2达人账户
     */
    private Integer accountType;
}
