package com.coohua.core.caf.dispense.component;

import com.pepper.metrics.integration.thread.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/7/21 15:00
 */
@Component
@Slf4j
public class ThreadPollComponent {

	/**
	 * 定时线程池
	 * @return
	 */
	@Bean(name = "SchedulerThreadPool")
	public ThreadPoolTaskScheduler schedulerThreadPool() {
		ThreadPoolTaskScheduler pool = new ThreadPoolTaskScheduler();
		pool.setPoolSize(10);
		pool.setThreadNamePrefix("cacheRefreshThread-");
		// 等待任务完成再销毁线程池(让任务先关闭)
		pool.setWaitForTasksToCompleteOnShutdown(true);
		pool.setAwaitTerminationSeconds(20);
		pool.setRemoveOnCancelPolicy(true);
		pool.setRejectedExecutionHandler(new RejectedLogAbortPolicy("SchedulerThreadPool"));
		pool.initialize();
		log.info("cacheRefreshThreadPool init complete");
		return pool;
	}

	/**
	 * dsp.toutiao_click 写入click事件线程池
	 * @return
	 */
	@Bean("dspToutiaoClickWrite")
	public ThreadPoolTaskExecutor dspToutiaoClickWritePoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("dspToutiaoClickWrite");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("dspToutiaoClickWrite"));
		return taskExecutor;
	}


	/**
	 * dsp向ocpc 写入click线程池
	 * @return
	 */
	@Bean("ocpcToDspClick")
	public ThreadPoolTaskExecutor ocpcToDspClickWritePoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("ocpcToDspClick");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcToDspClick"));
		return taskExecutor;
	}
	/**
	 * dsp向ocpc 写入click线程池
	 * @return
	 */
	@Bean("dspToOcpcEevent")
	public ThreadPoolTaskExecutor dspToOcpcEeventWritePoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("dspToOcpcEevent");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("dspToOcpcEevent"));
		return taskExecutor;
	}

	/**
	 * ocpc向dsp 写入event线程池
	 * @return
	 */
	@Bean("ocpcToDspEevent")
	public ThreadPoolTaskExecutor ocpcToDspEeventWritePoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(5);
		taskExecutor.setMaxPoolSize(10);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(10);
		taskExecutor.setThreadNamePrefix("ocpcToDspEevent");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcToDspEevent"));
		return taskExecutor;
	}

	/**
	 * ocpc 写入toutiao_ltv数据线程池
	 * @return
	 */
	@Bean("ocpcToutiaoLtvPool")
	public ThreadPoolTaskExecutor ocpcToutiaoLtvPool() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("ocpcToutiaoLtvPool");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcToutiaoLtvPool"));
		return taskExecutor;
	}

	/**
	 * ocpc 写入toutiao_ltv数据线程池
	 * @return
	 */
	@Bean("ocpcKuaishouExtEventPool")
	public ThreadPoolTaskExecutor ocpcKuaishouExtEventPool() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("ocpcKuaishouExtEventPool");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcKuaishouExtEventPool"));
		return taskExecutor;
	}

	/**
	 * ocpc 写入tencent_ltv数据线程池
	 * @return
	 */
	@Bean("ocpcTencentLtvPool")
	public ThreadPoolTaskExecutor ocpcTencentLtvPool() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("ocpcTencentLtvPool");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcTencentLtvPool"));
		return taskExecutor;
	}


	@Bean("ocpcTo8Eevent")
	public ThreadPoolTaskExecutor ocpcTo8Eevent() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(10);
		taskExecutor.setMaxPoolSize(50);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("ocpcTo8Eevent");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcTo8Eevent"));
		return taskExecutor;
	}


	@Bean("byteminEcpmPool")
	public ThreadPoolTaskExecutor byteminEcpmPool() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(10);
		taskExecutor.setMaxPoolSize(50);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("byteminEcpmPool");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("byteminEcpmPool"));
		return taskExecutor;
	}

	@Bean("ocpcToRedisEevent")
	public ThreadPoolTaskExecutor ocpcToRedisEeventWritePoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("ocpcToDspEevent");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcToDspEevent"));
		return taskExecutor;
	}

	@Bean("ocpcToHbaseActive")
	public ThreadPoolTaskExecutor ocpcToHbaseActive() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("ocpcToHbaseActive");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("ocpcToHbaseActive"));
		return taskExecutor;
	}

	/**
	 * dispense-job ToutiaoLtvCallbackService 头条回传
	 */
	@Bean("jobForToutiaoLtvCallback")
	public ThreadPoolTaskExecutor jobForToutiaoLtvCallbackPoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(200);
		taskExecutor.setMaxPoolSize(400);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(5000);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("jobForToutiaoLtvCallback");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("jobForToutiaoLtvCallback"));
		return taskExecutor;
	}

	/**
	 * dispense-job ToutiaoLtvCallbackService 快手回传
	 */
	@Bean("jobForKuaishouExtProcessCallback")
	public ThreadPoolTaskExecutor jobForKuaishouExtProcessCallbackPoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(200);
		taskExecutor.setMaxPoolSize(400);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(5000);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("jobForKuaishouExtProcessCallback");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("jobForKuaishouExtProcessCallback"));
		return taskExecutor;
	}

	/**
	 * dispense-job ToutiaoLtvCallbackService 腾讯回传
	 */
	@Bean("jobForTencentLtvCallback")
	public ThreadPoolTaskExecutor jobForTencentLtvCallbackPoll() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(200);
		taskExecutor.setMaxPoolSize(400);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(5000);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("jobForTencentLtvCallback");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("jobForTencentLtvCallback"));
		return taskExecutor;
	}


	@Bean("minEcpmToDb")
	public ThreadPoolTaskExecutor minEcpmToDb() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(3);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("minEcpmToDb");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("dspToutiaoClickWrite"));
		return taskExecutor;
	}

	/**
	 * 拒绝策略 默认丢弃，打印日志
	 */
	public static class RejectedLogAbortPolicy  implements RejectedExecutionHandler {
		String name;
		public RejectedLogAbortPolicy(String name) {
			this.name = name;
		}

		@Override
		public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
			String msg = String.format(name + " pool is EXHAUSTED!" +
							" Pool Size: %d (active: %d, core: %d, max: %d, largest: %d), Task: %d (completed: %d)," +
							" Executor status:(isShutdown:%s, isTerminated:%s, isTerminating:%s)" ,
					e.getPoolSize(), e.getActiveCount(), e.getCorePoolSize(), e.getMaximumPoolSize(), e.getLargestPoolSize(),
					e.getTaskCount(), e.getCompletedTaskCount(), e.isShutdown(), e.isTerminated(), e.isTerminating());
			log.warn(msg);
			log.warn("ThreadPoolComponent Task {}  rejected from {}" ,r.toString(),e.toString());
		}
	}


	@Bean("userActiveSave")
	public ThreadPoolTaskExecutor userActiveSave() {
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("userActiveSave");
		taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		return taskExecutor;
	}

	@Bean("lockRetryGy")
	public ThreadPoolTaskExecutor lockRetryGy(){
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("lockRetryGy");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("matchClickBroadSave"));
		return taskExecutor;
	}

	@Bean("matchCrowdBroadSave")
	public ThreadPoolTaskExecutor matchCrowdBroadSave(){
		ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
		taskExecutor.setCorePoolSize(20);
		taskExecutor.setMaxPoolSize(40);
		taskExecutor.setAllowCoreThreadTimeOut(true);
		taskExecutor.setQueueCapacity(500);
		taskExecutor.setKeepAliveSeconds(60);
		taskExecutor.setThreadNamePrefix("matchCrowdBroadSave");
		taskExecutor.setRejectedExecutionHandler(new ThreadPollComponent.RejectedLogAbortPolicy("matchCrowdBroadSave"));
		return taskExecutor;
	}
}
