package com.coohua.core.caf.dispense.dsp.service.bytemin;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;

@Component
@Slf4j
public class ByteMinThirdService {

    @Autowired
    ToutiaoCallService toutiaoCallService;
    public String getEpmUrl = "https://minigame.zijieapi.com/mgplatform/api/apps/data/get_ecpm";

    public List<ByteEcpm> getEcmp(String userId,String product, String openId, String appid, String appsecret, String dateHour){
        String ackToken = getAckTokenC(appid,appsecret);

        List<ByteEcpm> dlist = new ArrayList();
        String reqUrl = getEpmUrl+"?open_id="+openId+"&mp_id="+appid+"&access_token="+ackToken+"&date_hour="+ URLEncoder.encode(dateHour);
        CloseableHttpClient client = toutiaoCallService.getHttpClient();
        try {
//            JSONObject body = new JSONObject()
//                    .fluentPut("open_id", openId)
//                    .fluentPut("mp_id", appid).fluentPut("access_token", ackToken)
//                    .fluentPut("date_hour", dateHour);

            String rsp = toutiaoCallService.resendUrl(reqUrl,client);
            JSONObject jsonObject  = JSON.parseObject(rsp);
            JSONObject dataObj  = jsonObject.getJSONObject("data");
            Integer errNo = jsonObject.getInteger("err_no");
            if(errNo.equals(28001003)){
                //错误码为 28001003 重新请求
                log.info("重新请求ack "+userId+" "+rsp );
                ackToken = reshAckTokenC(appid,appsecret);
                String reqUrlD = getEpmUrl+"?open_id="+openId+"&mp_id="+appid+"&access_token="+ackToken+"&date_hour="+ URLEncoder.encode(dateHour);
                rsp = toutiaoCallService.resendUrl(reqUrlD,client);
                jsonObject  = JSON.parseObject(rsp);
                dataObj  = jsonObject.getJSONObject("data");
            }
            log.info("请求ecpm "+userId+" reqUrl "+reqUrl+" "+rsp );
            JSONArray jsonArray = dataObj.getJSONArray("records");
            if(jsonArray!=null){
                for(int i=0;i<jsonArray.size();i++){
                    ByteEcpm  byteAdEcpm = new ByteEcpm();
                    JSONObject adJobj  =  jsonArray.getJSONObject(i);
                    Double cost = adJobj.getDouble("cost"); // 1/10000 元
                    Date eventDate = adJobj.getDate("event_time");
                    String id = adJobj.getString("id");
                    String openIdTr = adJobj.getString("open_id");

                    byteAdEcpm.setCost(cost);
                    byteAdEcpm.setEventDate(eventDate);
                    byteAdEcpm.setId(id);
                    byteAdEcpm.setOpenId(openIdTr);
                    byteAdEcpm.setUserId(userId);
                    byteAdEcpm.setProduct(product);
                    Date date = new Date();
                    byteAdEcpm.setCreateTime(date);
                    byteAdEcpm.setUpdateTime(date);
                    dlist.add(byteAdEcpm);
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
        return dlist;
    }


    public  Pair<Integer,List<ByteEcpm>> getDayEcmp(String userId,String product,String openId, String appid, String appsecret, String dateStr,int pageNo,Integer pageSize){
        String ackToken = getAckTokenC(appid,appsecret);
        List<ByteEcpm> dlist = new ArrayList();
        String reqUrl = getEpmUrl+"?open_id="+openId+"&mp_id="+appid+"&access_token="+ackToken+"&page_no="+pageNo+"&page_size="+pageSize+"&date_hour="+ URLEncoder.encode(dateStr);
        Integer total = 0;
        CloseableHttpClient client = toutiaoCallService.getHttpClient();
        try {
//            JSONObject body = new JSONObject()
//                    .fluentPut("open_id", openId)
//                    .fluentPut("mp_id", appid).fluentPut("access_token", ackToken)
//                    .fluentPut("date_hour", dateHour);

            String rsp = toutiaoCallService.resendUrl(reqUrl,client);
            JSONObject jsonObject  = JSON.parseObject(rsp);
            JSONObject dataObj  = jsonObject.getJSONObject("data");

            log.info("请求ecpm "+product+" "+rsp );
            JSONArray jsonArray = dataObj.getJSONArray("records");
            total = dataObj.getInteger("total");
            if(jsonArray!=null){
                for(int i=0;i<jsonArray.size();i++){
                    ByteEcpm  byteAdEcpm = new ByteEcpm();
                    JSONObject adJobj  =  jsonArray.getJSONObject(i);
                    Double cost = adJobj.getDouble("cost"); // 1/10000 元
                    Date eventDate = adJobj.getDate("event_time");
                    String id = adJobj.getString("id");
                    String openIdTr = adJobj.getString("open_id");

                    byteAdEcpm.setCost(cost);
                    byteAdEcpm.setEventDate(eventDate);
                    byteAdEcpm.setId(id);
                    byteAdEcpm.setOpenId(openIdTr);
                    byteAdEcpm.setUserId(userId);
                    byteAdEcpm.setProduct(product);
                    Date date = new Date();
                    byteAdEcpm.setCreateTime(date);
                    byteAdEcpm.setUpdateTime(date);
                    dlist.add(byteAdEcpm);
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
        Pair<Integer,List<ByteEcpm>> pair = new Pair<Integer,List<ByteEcpm>>(total,dlist);
        return pair;
    }


    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    /**
     * appid：
     * tt2a325c8fb50193a802
     * appsecret：
     * 900c3cc0384b98bd2f4c25cf81255ea6ce468c68
     * @param appid
     * @param appsecret
     */
    public String getAckTokenC(String appid,String appsecret){
        String redisKey = appid+"ack";
        String redisVal = bpDispenseJedisClusterClient.get(redisKey);
        if(StringUtils.isNotBlank(redisVal)){
            log.info("命中redis 缓存 "+redisVal);
            return redisVal;
        }
        String ackToken =   getAckToken(appid,appsecret);
        if(StringUtils.isNotBlank(ackToken)){
            log.info("刷新acktokne 缓存 "+appid+" "+redisVal);
            bpDispenseJedisClusterClient.setex(redisKey, DateTimeConstants.SECONDS_PER_HOUR*2,ackToken);
        }
        return ackToken;

    }

    public String reshAckTokenC(String appid,String appsecret){
        String redisKey = appid+"ack";
        String ackToken =   getAckToken(appid,appsecret);
        if(StringUtils.isNotBlank(ackToken)){
            log.info("刷新acktokne 缓存刷新 "+appid);
            bpDispenseJedisClusterClient.setex(redisKey, DateTimeConstants.SECONDS_PER_HOUR*2,ackToken);
        }
        return ackToken;
    }

    private String getAckToken(String appid,String appsecret){
        String ackUrl = "https://minigame.zijieapi.com/mgplatform/api/apps/v2/token";
        CloseableHttpClient client = toutiaoCallService.getHttpClient();
        try {
            ackUrl = ackUrl+"?appid="+appid+"&secret="+appsecret+"&grant_type=client_credential";

            JSONObject body = new JSONObject()
                    .fluentPut("appid", appid)
                    .fluentPut("secret", appsecret)
                    .fluentPut("grant_type", "client_credential");

            String rsp = toutiaoCallService.resendUrlPost(ackUrl,body.toJSONString(),client);

            //{
            //  "err_no": 0,
            //  "err_tips": "success",
            //  "data": {
            //    "access_token": "0801121***********",
            //    "expires_in": 7200
            //  }
            //}
            log.info("请求acktoken "+rsp+" reqUrl: "+ackUrl);
            JSONObject jsonObject  = JSON.parseObject(rsp);
            JSONObject dataObj  =jsonObject.getJSONObject("data");
            return dataObj.getString("access_token");
        }catch (Exception e){
            log.error("获取acktoken 错误 "+appid,e);
        }finally {
            if(client!=null){
                try {
                    client.close();
                }catch (Exception e){
                    log.error("",e);
                }
            }
        }
        return null;
    }

    public static Map<String,Pair<String,Date>> dmap = new HashMap<>();
}
