package com.coohua.core.caf.dispense.enums;

public enum SigmobEventType {
    ACTIVATE_APP(0, "1", "激活"),//	         30天内第一次打开APP
    REGISTER(25, "143", "注册"),// 此项其实是关键行为	         用户点击广告后注册成为APP的新用户
    COMPLETE_ORDER(20, "14", "提交订单"),//	           用户点击下单生成订单
    PURCHASE(2, "3", "付费"),//	         用户完成付费
    ADD_TO_CART(22, "13", "加入购物车"),//	     用户将商品加入购物车
    START_APP(6, "7", "次日留存"),//	     用户激活后第二天打开APP的行为

    ;
    public Integer value;
    public String name;
    public String desc;

    /** 衍生回传时的 key_type 字段*/
    public Integer extKeyType;

    SigmobEventType(Integer value, String name, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

    SigmobEventType(Integer value, String name, String desc, Integer extKeyType) {
        this.value = value;
        this.name = name;
        this.desc = desc;
        this.extKeyType = extKeyType;

    }

    public static SigmobEventType getStatus(Integer value) {
        if (value != null) {
            SigmobEventType[] otypes = SigmobEventType.values();
            for (SigmobEventType memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
