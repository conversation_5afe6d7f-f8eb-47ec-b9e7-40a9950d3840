package com.coohua.core.caf.dispense.ck.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ck.entity.ArpuPvReportRecord;
import com.coohua.core.caf.dispense.ck.entity.GyRecordDataLocal;
import com.coohua.core.caf.dispense.ck.mapper.ArpuPvReportRecordMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ArpuPvReportRecordService extends ServiceImpl<ArpuPvReportRecordMapper, ArpuPvReportRecord> {

    public void saveGyRecordDataBatch(List<ArpuPvReportRecord> arpuPvReportRecords) {
        //DataSourceContextHolder.setDataSource(targetDb);
        this.saveBatch(arpuPvReportRecords);
        //gyRecordDataMapper.insertBatch(gyRecordDatas);
    }
}
