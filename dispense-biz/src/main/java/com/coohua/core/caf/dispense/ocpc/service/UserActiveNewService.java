package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dto.req.DeviceReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.OcpcSourceType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseLockUserActiveService;
import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
import com.coohua.core.caf.dispense.ocpc.entity.GuiStatistics;
import com.coohua.core.caf.dispense.ocpc.entity.UserActiveNew;
import com.coohua.core.caf.dispense.ocpc.mapper.UserActiveNewMapper;
import com.coohua.core.caf.dispense.utils.EventDataCheck;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserActiveNewService extends ServiceImpl<UserActiveNewMapper, UserActiveNew> {
    @Value("#{${channel.source.map: {\"vivo\": \"vivo\",\"oppo\": \"oppo\",\"xiaomi\": \"xiaomi\",\"huawei\": \"huawei\",\"yingyongbao\": \"yingyongbao\",\"neilaxin\":\"neilaxin\",\"update\":\"neilaxin\",\"daoliu\":\"neilaxin\"}}}")
    public Map<String, String> channelSourceMap;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    UserActiveNewMapper UserActiveNewMapper;
    @Autowired
    HbaseUserActiveService hbaseUserActiveService;
    @Autowired
    ProductCache productCache;
    @Autowired
    OceanengineSdkService oceanengineSdkService;

    public UserActiveNew queryActive(String userId, String product){
        try{
            if(StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(product)){
                LambdaQueryChainWrapper<UserActiveNew> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActiveNew::getUserId,userId);
                lambdaQuery.eq(UserActiveNew::getProduct,product);
                lambdaQuery.last(" limit 1 ");
                List<UserActiveNew> UserActiveNewList = lambdaQuery.list();
                if(UserActiveNewList.size()>0){
                    return UserActiveNewList.get(0);
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }
    public UserEventReq getURq(UserActiveNew UserActiveNew) {
        if (StringUtils.isNotBlank(UserActiveNew.getOs())) {
            UserActiveNew.setOs(UserActiveNew.getOs().toLowerCase());
        }
        UserEventReq userEventReq = new UserEventReq();
        if (StringUtils.equalsAnyIgnoreCase("ios", UserActiveNew.getOs())) {
            userEventReq.setOcpcDeviceId(UserActiveNew.getIdfa());
        } else if (StringUtils.isNotBlank(UserActiveNew.getImei())) {
            userEventReq.setOcpcDeviceId(MD5Utils.getMd5Sum(UserActiveNew.getImei()));
        }
        userEventReq.setOaid(UserActiveNew.getOaid());
        if ((StringUtils.isBlank(UserActiveNew.getProduct()) || "null".equalsIgnoreCase(UserActiveNew.getProduct())) && UserActiveNew.getAppId() != null) {
            String pname = productCache.getPname(UserActiveNew.getAppId());
            userEventReq.setProduct(pname);
            UserActiveNew.setProduct(pname);
        } else {
            userEventReq.setProduct(UserActiveNew.getProduct());
        }
        userEventReq.setAppId(UserActiveNew.getAppId());
        userEventReq.setPkgChannel(UserActiveNew.getChannel());
        userEventReq.setOs(UserActiveNew.getOs());
        userEventReq.setMac(UserActiveNew.getMac());
        userEventReq.setUserId(UserActiveNew.getUserId() + "");
        userEventReq.setAndroidId(UserActiveNew.getAndroidId());
        userEventReq.setSouceType(OcpcSourceType.OCPC);
        if(StringUtils.isNotBlank(UserActiveNew.getUa())){
            try {
                userEventReq.setUa(MD5Utils.getMd5Ua(URLDecoder.decode(UserActiveNew.getUa(),"UTF-8")));
            }catch (Exception e){
                log.error("ua decode error ",e);
            }
        }
        userEventReq.setIp(UserActiveNew.getIp());
        userEventReq.setModel(UserActiveNew.getModel());
        userEventReq.setCaid(UserActiveNew.getCaid());
        userEventReq.setOaid2(UserActiveNew.getOaid2());
        if(StringUtils.equalsIgnoreCase("ios",UserActiveNew.getOs())){
            userEventReq.setIdfa2(UserActiveNew.getIdfa2());
            userEventReq.setOcpcDeviceId(UserActiveNew.getIdfa2());
//            userEventReq.setCaid();
        }

        EventDataCheck.replaceZto(userEventReq);
        return userEventReq;
    }


    public List<UserActiveNew> deviceRegisters(DeviceReq deviceReq){
        List<UserActiveNew> UserActiveNewList = new ArrayList<>();
        if("ios".equalsIgnoreCase(deviceReq.getOs())){
            if(StringUtils.isNotBlank(deviceReq.getIdfa())){
                LambdaQueryChainWrapper<UserActiveNew> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActiveNew::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActiveNew::getIdfa,deviceReq.getIdfa());
                UserActiveNewList.addAll(list(lambdaQuery));
            }
        }else{
            if(StringUtils.isNotBlank(deviceReq.getImei())){
                LambdaQueryChainWrapper<UserActiveNew> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActiveNew::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActiveNew::getImei,deviceReq.getImei());
                UserActiveNewList.addAll(lambdaQuery.list());
            }

            if(StringUtils.isNotBlank(deviceReq.getOaid())){
                LambdaQueryChainWrapper<UserActiveNew> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActiveNew::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActiveNew::getOaid,deviceReq.getOaid());
                List<UserActiveNew>  oaIdUserActiveNewList = lambdaQuery.list();
                if(oaIdUserActiveNewList.size()>0){
                    UserActiveNewList.addAll(oaIdUserActiveNewList);
                }
            }


            if(StringUtils.isNotBlank(deviceReq.getAndroidId())){
                LambdaQueryChainWrapper<UserActiveNew> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActiveNew::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActiveNew::getAndroidId,deviceReq.getAndroidId());
                List<UserActiveNew>  oaIdUserActiveNewList = lambdaQuery.list();
                if(oaIdUserActiveNewList.size()>0){
                    UserActiveNewList.addAll(oaIdUserActiveNewList);
                }
            }

            if(StringUtils.isNotBlank(deviceReq.getMac())){
                LambdaQueryChainWrapper<UserActiveNew> lambdaQuery = lambdaQuery();
                lambdaQuery.eq(UserActiveNew::getOs,deviceReq.getOs());
                lambdaQuery.eq(UserActiveNew::getMac,deviceReq.getMac());
                List<UserActiveNew>  oaIdUserActiveNewList = lambdaQuery.list();
                if(oaIdUserActiveNewList.size()>0){
                    UserActiveNewList.addAll(oaIdUserActiveNewList);
                }
            }
        }
        return UserActiveNewList;
    }

    private String getStoreChannel(UserActiveNew UserActiveNew){
        if (StringUtils.isNotBlank(UserActiveNew.getChannel())) {
            AtomicReference<String> sourceName = new AtomicReference<>("");
            channelSourceMap.keySet().forEach(pkgName -> {
                if (UserActiveNew.getChannel().toLowerCase().contains(pkgName)) {
                    sourceName.set(channelSourceMap.get(pkgName));
                }
            });

            if (StringUtils.isNotBlank(sourceName.get())) {
                return sourceName.get();
            }
            if("ALIYUN_MAN_CHANNEL".equalsIgnoreCase(UserActiveNew.getChannel())){
                return "自然ALIYUN量";
            }
        }
        return null;
    }
    @Autowired
    HbaseLockUserActiveService hbaseLockUserActiveService;
    public ToutiaoClick guiOcpc(UserEventReq userEventReq){
        //复制一份实时归因逻辑
        ToutiaoClick toutiaoClick = hbaseClickService.getHbaseClickLongByDevice(userEventReq);//14天过期数据 存储
        return toutiaoClick;
    }

    public ToutiaoClick guiOcpc2(UserEventReq userEventReq){
        //复制一份实时归因逻辑
        ToutiaoClick toutiaoClick = hbaseClickService.getHbaseClickLongByDevice(userEventReq);
        return toutiaoClick;
    }

    public ToutiaoClick guiOcpcBySdk(UserEventReq userEventReq) {
        if (ocpcSwitcher.userActiveSdk && StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && StringUtils.isNotBlank(userEventReq.getSourceDeviceId())) {
            ToutiaoClick toutiaoClick = oceanengineSdkService.querySdkClick(userEventReq, true, true);
            log.info("user gui ocpc, userId:{}, req:{} res:{}", userEventReq.getUserId(), userEventReq, toutiaoClick);
            return toutiaoClick;
        }
        return null;
    }



    @Resource(name = "userActiveSave")
    ThreadPoolTaskExecutor poolTaskExecutor;

    public void activeUser(UserActiveNew userActiveNew, boolean isBuShu, String sourceDeviceId) {
        UserEventReq userEventReq = getURq(userActiveNew);
        userEventReq.setSourceDeviceId(sourceDeviceId);

        if(ocpcSwitcher.baiduGy && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && userEventReq.getPkgChannel().startsWith("bd")
                && ocpcSwitcher.baiduGyChannel.contains(userEventReq.getPkgChannel())){
            log.info("uactivebaidu渠道替换product "+"bd"+userEventReq.getProduct()+"@"+userEventReq.getPkgChannel());
            userEventReq.setProduct("bd"+userEventReq.getProduct());
        }

        if (isBuShu || hbaseUserActiveService.queryByHbase(userEventReq.getUserId(), userEventReq.getProduct()) == null) {
            poolTaskExecutor.execute(() -> {
                ToutiaoClick toutiaoClick = null;
                //复制一份实时归因逻辑
                if (StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
                    //andoidId 就是 ocpcSourceId 不用查2遍
                    toutiaoClick = oceanengineSdkService.querySdkClick(userEventReq, true, false);
                    //log.info("user active sdk, userId:{}, res:{}", userEventReq.getUserId(), toutiaoClick);
                }
                if (null == toutiaoClick) {
                    if (ocpcSwitcher.readClickLongFlag) {
                        toutiaoClick = hbaseClickService.getHbaseClickLongByDevice(userEventReq);//14天过期数据 存储
                    } else {
                        toutiaoClick = redisClickService.getClick(userEventReq, ocpcSwitcher.readUserHbaseSwitch);
                    }
                }
                userActiveNew.setUa(null);
                if (toutiaoClick != null) {
                    userActiveNew.setAccountId(toutiaoClick.getAccountId());
                    userActiveNew.setSource(toutiaoClick.getDsp());
                    userActiveNew.setGyType(userEventReq.getGuiType());
                    userActiveNew.setClickTime(toutiaoClick.getCreateTime());
                    userActiveNew.setCid(toutiaoClick.getCid() + "");
                    userActiveNew.setPid(toutiaoClick.getPid() + "");
                    userActiveNew.setClickId(toutiaoClick.getClickId());
                    userActiveNew.setGid(toutiaoClick.getGid() + "");
                    userActiveNew.setMid(toutiaoClick.getMid());
                    userActiveNew.setIdfa2(userActiveNew.getIdfa2());
                    userActiveNew.setIdfa(userActiveNew.getIdfa());
                    userActiveNew.setIp(userEventReq.getIp());
                    userActiveNew.setModel(userEventReq.getModel());
                    userActiveNew.setUa2(userEventReq.getUa());
                    if (isBuShu) {
                        userActiveNew.setUpdateTime(new Date());
                        userActiveNew.setAcDesc("回调延迟");
                        int updNum = UserActiveNewMapper.updateById(userActiveNew);
                        log.info("ocpc补偿激活成功 " + userActiveNew.getUserId() + "@" + userActiveNew.getProduct() + "" + updNum);
                    }
                } else {
                    String storeChannel = getStoreChannel(userActiveNew);
                    if (StringUtils.isBlank(storeChannel)) {
                        userActiveNew.setSource("自然量");
                    } else {
                        userActiveNew.setSource(storeChannel);
                    }
                }


                if (!isBuShu) {
                    Date date = new Date();
                    userActiveNew.setCreateTime(date);
                    userActiveNew.setUpdateTime(date);
                    UserActiveNewMapper.insert(userActiveNew);
                }
                //hbaseUserActiveService.saveUserActive(userActiveNew);
            });
        } else {
            log.warn(userEventReq.getUserId() + "" + userEventReq.getProduct() + " 已经产生归因");
        }
    }


    private Set<String> dset = new HashSet<>();

    {
        dset.add("neilaxin");
        dset.add("share");
        dset.add("yingyongbao");
    }

    public List<UserActiveNew> queryZiranSourceActive(Date beforeDate, long limit, long offset, String product,boolean isNew) {
        LambdaQueryChainWrapper<UserActiveNew> lambdaQueryChainWrapper = lambdaQuery().gt(UserActiveNew::getCreateTime, beforeDate)
                .eq(UserActiveNew::getSource, "自然量")
                .isNotNull(UserActiveNew::getUserId)
                .isNotNull(UserActiveNew::getAppId)
                .last(" limit " + offset + "," + limit);
        if(StringUtils.isNotBlank(product)){
            lambdaQueryChainWrapper.eq(UserActiveNew::getProduct,product);
        }
        if(isNew){
            lambdaQueryChainWrapper.isNull(UserActiveNew::getAcDesc);
        }else{
            lambdaQueryChainWrapper.ne(UserActiveNew::getAcDesc,"参数不合法");
            lambdaQueryChainWrapper.orderByDesc(UserActiveNew::getCreateTime);
        }
        List<UserActiveNew> dlist = lambdaQueryChainWrapper.list();
        List<UserActiveNew> fllist = dlist.stream().filter(UserActiveNew -> {
            if(StringUtils.isBlank(UserActiveNew.getChannel())){
                return true;
            }
            final boolean[] isFl = {false};
            dset.forEach(channelName -> {
                isFl[0] = isFl[0] || UserActiveNew.getChannel().contains(channelName);
            });
            return !isFl[0];
        }).collect(Collectors.toList());

        return fllist;
    }
    public List<UserActiveNew> queryZiranSourceActive(Date beforeDate, long limit, long offset,boolean isNew) {
        return queryZiranSourceActive(beforeDate,limit,offset,null,isNew);
    }

    public long sumZiranCount(Date beforeDate, String product,boolean isNew) {
        LambdaQueryChainWrapper<UserActiveNew> lambdaQueryChainWrapper = lambdaQuery().gt(UserActiveNew::getCreateTime, beforeDate)
                .eq(UserActiveNew::getSource, "自然量")
                .isNotNull(UserActiveNew::getUserId)
                .isNotNull(UserActiveNew::getAppId);
        if (StringUtils.isNotBlank(product)) {
            lambdaQueryChainWrapper.eq(UserActiveNew::getProduct, product);
        }
        if(isNew){
            lambdaQueryChainWrapper.isNull(UserActiveNew::getAcDesc);
        }else{
            lambdaQueryChainWrapper.ne(UserActiveNew::getAcDesc,"参数不合法");
        }
        return lambdaQueryChainWrapper.count();
    }


    public long sumZiranCount(Date beforeDate) {
        return sumZiranCount(beforeDate, null,true);
    }



    public UserActiveNew queryActiveByDevice(String deviceId, String product, String os) {
        if (StringUtils.isBlank(deviceId) || StringUtils.isBlank(product) || StringUtils.isBlank(os)) {
            return null;
        }
        LambdaQueryChainWrapper<UserActiveNew> lambdaQuery = lambdaQuery();
        lambdaQuery.eq(UserActiveNew::getProduct, product);
        lambdaQuery.eq(UserActiveNew::getOs, os);
        if ("ios".equals(os)) {
            lambdaQuery.eq(UserActiveNew::getCaid,deviceId);
        }else {
            lambdaQuery.eq(UserActiveNew::getOaid,deviceId);
        }
        lambdaQuery.last(" limit 1 ");
        List<UserActiveNew> list = lambdaQuery.list();
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }
}
