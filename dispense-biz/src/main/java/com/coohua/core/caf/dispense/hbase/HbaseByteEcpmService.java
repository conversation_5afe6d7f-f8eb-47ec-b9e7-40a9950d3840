package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class HbaseByteEcpmService {
    @Resource
    private Connection hbaseConnection;

    @Autowired
    ConstApolloConfig constApolloConfig;

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    static String tableName = "ByteEcpm";
    @PostConstruct
    public HTableDescriptor createOcpcTable() {
        try (Admin admin = hbaseConnection.getAdmin()) {
            // 建表
            try {
                HTableDescriptor htdDb = admin.getTableDescriptor(TableName.valueOf(tableName));
            } catch (TableNotFoundException te) {
                HTableDescriptor htd = new HTableDescriptor(TableName.valueOf(tableName));
                HColumnDescriptor hcd = new HColumnDescriptor("family");
                hcd.setTimeToLive(184512); // 设置TTL过期时间4天 4 * 24 * 60 * 60
                htd.addFamily(hcd);
                admin.createTable(htd);
            }
        } catch (IOException e) {
            log.error("", e);
        }
        return null;
    }

    public static String getHbaseKey(String product,String openId,Date hourDate){
        String dateStr = DateUtils.formatDateForHH(new Date());
        String key = product+"@"+openId+"@"+dateStr;
        return key;
    }

    public List<ByteEcpm> getUserHourEcpms(Date eventDate, String product, String openId){
        String key = getHbaseKey(product,openId,eventDate);
        List<ByteEcpm> byteAdEcpms = new ArrayList<>();
        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
            Get get = new Get(Bytes.toBytes(key));
            Result res = table.get(get);
            String s = HbaseUtils.getCellValStr(res);
            if (StringUtils.isNotBlank(s)) {
                byteAdEcpms = JSON.parseArray(s, ByteEcpm.class);
            }
        }catch (Exception e) {
            log.error("byte hbaseGetUserEcpms error ", e);
        }
        return byteAdEcpms;
    }


    public void saveKeyActionExtEvent(List<ByteEcpm> byteAdEcpmList,List<ByteEcpm> hbaseByteAdEcpms) {
        for(ByteEcpm byteAdEcpm : byteAdEcpmList){
            boolean isCt = false;
            for(ByteEcpm hbaseEcpm : hbaseByteAdEcpms){
                if(!isCt && StringUtils.equalsIgnoreCase(hbaseEcpm.getId(),byteAdEcpm.getId())){
                    isCt = true;
                }
            }
            if(!isCt){
                hbaseByteAdEcpms.add(byteAdEcpm);
            }
        }

        if(hbaseByteAdEcpms.size()>0){
            ByteEcpm hec = hbaseByteAdEcpms.get(0);
            String key = getHbaseKey(hec.getProduct(),hec.getOpenId(),hec.getEventDate());
            String eventJson = JSON.toJSONString(hbaseByteAdEcpms);
            try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
                byte[] eventBytes = Bytes.toBytes(eventJson);
                Put put = new Put(Bytes.toBytes(key));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), eventBytes);
                table.put(put);
            } catch (Exception e) {
                log.error("抖音存储byte至hbase出现异常 ", e);
            }
        }
    }


}
