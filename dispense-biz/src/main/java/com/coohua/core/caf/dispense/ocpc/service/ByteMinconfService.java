package com.coohua.core.caf.dispense.ocpc.service;

import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.ocpc.entity.ByteMinconf;
import com.coohua.core.caf.dispense.ocpc.mapper.ByteMinconfMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-04-30
*/
@Service
@Slf4j
public class ByteMinconfService extends ServiceImpl<ByteMinconfMapper, ByteMinconf> {

    public void addUpdMinconf(ByteMinconf byteMinconf){
        List<ByteMinconf> byteEcpms = lambdaQuery()
                .eq(ByteMinconf::getProduct, byteMinconf.getProduct())
                .eq(ByteMinconf::getAppId, byteMinconf.getAppId())
                .list();

        if(byteEcpms.size()==0){
            byteMinconf.setCreateTime(new Date());
            save(byteMinconf);
        }else{
            byteMinconf.setId(byteEcpms.get(0).getId());
            byteMinconf.setCreateTime(byteEcpms.get(0).getCreateTime());
            byteMinconf.setUpdateTime(new Date());
            updateById(byteMinconf);
        }

        log.info("头条min更新ack成功 "+byteMinconf.getAcktoken());
    }

    public ByteMinconf getByteMinconf(String product){
        List<ByteMinconf> byteEcpms = lambdaQuery()
                .eq(ByteMinconf::getProduct, product)
                .list();

        if(byteEcpms.size()>0){
            return byteEcpms.get(0);
        }
        return null;
    }
}
