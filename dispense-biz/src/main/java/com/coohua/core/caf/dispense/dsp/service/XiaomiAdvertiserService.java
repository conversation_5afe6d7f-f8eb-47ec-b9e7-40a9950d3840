package com.coohua.core.caf.dispense.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.XiaomiAction;
import com.coohua.core.caf.dispense.dsp.entity.XiaomiAdvertiser;
import com.coohua.core.caf.dispense.dsp.mapper.XiaomiAdvertiserMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.XiaomiActiveReq;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.coohua.core.caf.dispense.constant.BaseConstants.UNDERLINE;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.matchNewRule;

/**
 * 
 * <AUTHOR>
 * @date 2022/4/25 20:52
 */
@Service
public class XiaomiAdvertiserService extends ServiceImpl<XiaomiAdvertiserMapper, XiaomiAdvertiser> {

    @Autowired
    XiaomiActionService xiaomiActionService;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    public static Map<String,XiaomiAdvertiser> AccountMap = new HashMap<>();

    /** <账户id_eventType, XiaomiAction> */
    public static Map<String, XiaomiAction> ActionMap = new HashMap<>();

    @Scheduled(cron = "0 1/3 * * * ?")
    public void reloadConfig(){
        try {
            List<XiaomiAdvertiser> xiaomiAdvertiserList = lambdaQuery().eq(XiaomiAdvertiser::getDelFlag,0).list();
            AccountMap = xiaomiAdvertiserList.stream().collect(Collectors.toMap(XiaomiAdvertiser::getAdvertiserId, Function.identity()));

            List<XiaomiAction> xiaomiActionList = xiaomiActionService.lambdaQuery().eq(XiaomiAction::getDelFlag,0).list();
            ActionMap = xiaomiActionList.stream().collect(Collectors.toMap(k-> k.getAdvertiserId() + UNDERLINE + k.getEventType(), Function.identity()));
        }catch (Exception e){
            log.error("",e);
        }
    }

    public XiaomiAdvertiser getByAccountId(String accountId){
        return AccountMap.get(accountId);
    }

    public XiaomiAction getAction(String accountId, Integer eventType){
        return ActionMap.get(accountId + UNDERLINE + eventType);
    }

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            String  accountId = toutiaoClick.getAccountId();
            XiaomiAdvertiser xiaomiAdvertiser = getByAccountId(accountId);
            if(xiaomiAdvertiser==null){
                log.error("xiaomi无配置，请配置 "+accountId+" ");
                return false;
            }
            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();


            // 若无配置或未配置行为参数 仅当默认值时上报
            if (xiaomiAdvertiser == null || StringUtils.isBlank(xiaomiAdvertiser.getEventTypes()) || StringUtils.isBlank(xiaomiAdvertiser.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, xiaomiAdvertiser.getEventTypes(),
                        xiaomiAdvertiser.getEventValues(), firstTimeJudge, "xiaomi", xiaomiAdvertiser.getProductName(),
                        xiaomiAdvertiser.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, xiaomiAdvertiser.getEventTypes(),
                        xiaomiAdvertiser.getEventValues(), firstTimeJudge, "xiaomi", xiaomiAdvertiser.getProductName(),
                        xiaomiAdvertiser.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }
}
