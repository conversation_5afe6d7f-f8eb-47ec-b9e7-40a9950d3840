package com.coohua.core.caf.dispense.dsp.service.wmin;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.dto.req.Props;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.WxActiveReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;

@Component
@Slf4j
public class TTDaRenWxService {
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    RedisClickService redisClickService;
    //头条 微信小程序激活
    public void sendDRWxActive(WxActiveReq wxActiveReq){
        try {
            //url：https://clue.oceanengine.com/outer/wechat/applet/token/1743915176074243
            //token：5EC571E03469980C8ADDC3ACC65CC711
            String reqUrl = "https://ad.oceanengine.com/track/activate/?callback="+wxActiveReq.getClickid()+"&event_type=0";
            boolean isActive = redisClickService.setOpenidActive(wxActiveReq.getProduct(),wxActiveReq.getOpenId());
            if(isActive){
                log.info("wx小游戏达人开始请求激活 "+wxActiveReq.getUserId()+" "+reqUrl);
                CloseableHttpClient client = toutiaoCallService.getHttpClient();
                String rsp = toutiaoCallService.resendUrl(reqUrl,client);
                //clueToken 存储在callBack中
                ToutiaoClick toutiaoClick = getTclick(wxActiveReq,wxActiveReq.getClickid());
                UserEventReq userEventReq = getUevent(wxActiveReq);
                UserEvent userEvent = ocpcEventService.saveUserEvent(toutiaoClick, userEventReq, reqUrl, rsp);

                log.info("wx小游戏达人开始请求激活 "+reqUrl+" 响应为 "+rsp);
            }else{
                log.info("wx小游戏达人已经激活 直接忽略 "+JSON.toJSONString(wxActiveReq));
            }
        } catch (Exception e) {
            log.error("头条回传异常", e);
        }
    }


    public boolean sendDRWxEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent){
        try {
            //url：https://clue.oceanengine.com/outer/wechat/applet/token/1743915176074243
            //token：5EC571E03469980C8ADDC3ACC65CC711
            String reqUrl = "https://ad.oceanengine.com/track/activate";

            if(ToutiaoEventTypeEnum.PURCHASE.value.equals(userEventReq.getEventType())){
                Props props = new Props();
                props.setPay_amount(userEventReq.getPayAmount());
                String reqJsn = JSON.toJSONString(props);
                reqUrl = reqUrl + "?callback="+userEvent.getCallbackUrl()+"&event_type="+ToutiaoEventTypeEnum.PURCHASE.value;
                log.info("wx小游戏达人开始请求关键行为 "+reqUrl);
                CloseableHttpClient client = toutiaoCallService.getHttpClient();
                String rsp = toutiaoCallService.resendUrlPost(reqUrl,reqJsn,client);
                Integer rstatus = JSON.parseObject(rsp).getInteger("code");
                if(!rstatus.equals(0)){
                    log.error("wx小游戏达人开始请求关键行为失败 "+reqUrl+" 响应为 "+rsp);
                    return false;
                }else{
                    log.info("wx小游戏达人开始请求关键行为 "+reqUrl+" 响应为 "+rsp);
                }
                return true;
            }

        } catch (Exception e) {
            log.error("头条达人回传异常", e);
        }
        return false;
    }


    private ToutiaoClick getTclick(WxActiveReq wxActiveReq,String clickId){
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(DspType.TTWMINDR.value);
        toutiaoClick.setOpenId(wxActiveReq.getOpenId());
        toutiaoClick.setOs(wxActiveReq.getOs());
        toutiaoClick.setAccountId(wxActiveReq.getAdvertiser_id());
        toutiaoClick.setProduct(wxActiveReq.getProduct());
        toutiaoClick.setPid(wxActiveReq.getAd_id());
        toutiaoClick.setCid(wxActiveReq.getCreative_id());
        toutiaoClick.setClickId(wxActiveReq.getClickid());
        toutiaoClick.setCallbackUrl(clickId);
        return toutiaoClick;
    }

    private UserEventReq getUevent(WxActiveReq wxActiveReq){
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setProduct(wxActiveReq.getProduct());
        userEventReq.setOs(wxActiveReq.getOs());
        userEventReq.setEventType(0);
        userEventReq.setUserId(wxActiveReq.getUserId());
        userEventReq.setOpenId(wxActiveReq.getOpenId());
        userEventReq.setAppId(wxActiveReq.getAppId());
        userEventReq.setClickId(wxActiveReq.getClickid());
        return userEventReq;
    }

}
