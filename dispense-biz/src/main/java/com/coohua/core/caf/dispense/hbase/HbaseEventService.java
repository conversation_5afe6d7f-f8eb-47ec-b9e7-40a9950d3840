package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.LogNum;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.utils.ConstCls;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import com.coohua.core.caf.dispense.utils.OcpcEventKeyJoinUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.HColumnDescriptor;
import org.apache.hadoop.hbase.HTableDescriptor;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

@Component
@Slf4j
public class HbaseEventService {

    @Resource
    private Connection hbaseConnection;

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Resource(name = "ocpcToHbaseActive")
    ThreadPoolTaskExecutor poolTaskExecutor;

    static String tableName = "OcpcEvent";
    static String tableName_UserArpu = "UserArpu";
    public static String tableName_TempUserArpu = "TempUserArpu5";

    //    @PostConstruct
    public HTableDescriptor createOcpcTable() {
        try (Admin admin = hbaseConnection.getAdmin()) {
            // 建表
            try {
                HTableDescriptor htdDb = admin.getTableDescriptor(TableName.valueOf(tableName));
                return htdDb;
            } catch (TableNotFoundException te) {
                HTableDescriptor htd = new HTableDescriptor(TableName.valueOf(tableName));
                HColumnDescriptor hcd = new HColumnDescriptor("family");
                hcd.setTimeToLive(184512); // 设置TTL过期时间4天 2 * 24 * 60 * 60
                htd.addFamily(hcd);
                admin.createTable(htd);
                return htd;
            }
        } catch (IOException e) {
            log.error("", e);
        }
        return null;
    }

    public HTableDescriptor createUserArpuTable() {
        try (Admin admin = hbaseConnection.getAdmin()) {
            // 建表
            try {
                HTableDescriptor htdDb = admin.getTableDescriptor(TableName.valueOf(tableName_UserArpu));
                return htdDb;
            } catch (TableNotFoundException te) {
                HTableDescriptor htd = new HTableDescriptor(TableName.valueOf(tableName_UserArpu));
                HColumnDescriptor hcd = new HColumnDescriptor("family");
                hcd.setTimeToLive(184512); // 设置TTL过期时间4天 2 * 24 * 60 * 60
                htd.addFamily(hcd);
                admin.createTable(htd);
                return htd;
            }
        } catch (IOException e) {
            log.error("hbase建UserArpu表异常", e);
        }
        return null;
    }
    public HTableDescriptor createTempUserArpuTable() {
        try (Admin admin = hbaseConnection.getAdmin()) {
            // 建表
            try {
                HTableDescriptor htdDb = admin.getTableDescriptor(TableName.valueOf(tableName_TempUserArpu));
                return htdDb;
            } catch (TableNotFoundException te) {
                HTableDescriptor htd = new HTableDescriptor(TableName.valueOf(tableName_TempUserArpu));
                HColumnDescriptor hcd = new HColumnDescriptor("family");
                hcd.setTimeToLive(184512); // 设置TTL过期时间4天 2 * 24 * 60 * 60
                htd.addFamily(hcd);
                admin.createTable(htd);
                return htd;
            }
        } catch (IOException e) {
            log.error("hbase建UserArpu表异常", e);
        }
        return null;
    }

    public OcpcEvent getActiveEventByHbase(UserEventReq request){

        OcpcEvent ocpcEvent = null;
        try {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("hbasegetocpcEvent product is null ! {}", JSONObject.toJSONString(request));
                return ocpcEvent;
            }

            String key = "";

            try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
                /**
                 * 先按设备号查询
                 */
                if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                    key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.device.value, request.getOcpcDeviceId());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                /**
                 * 按idfa_md5查询
                 */
                if (ocpcEvent == null && StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                    key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.idfa2.value,request.getIdfa2());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                /**
                 * click不存在再查oaId
                 */
                if (ocpcEvent == null && StringUtils.isNotBlank(request.getOaid())&& !ConstCls.emptyMd5.equals(request.getOaid())) {
                    key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.oaid.value,request.getOaid());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }

                if (StringUtils.isNotBlank(request.getCaid()) && !ConstCls.emptyMd5.equals(request.getCaid())) {
                    key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.caid.value, request.getCaid());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                /**
                 * caid2 = md5(caid)
                 */
                if (StringUtils.isNotBlank(request.getCaid2()) && !ConstCls.emptyMd5.equals(request.getCaid2())) {
                    key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.caid2.value, request.getCaid2());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                /**
                 * oaid2 = md5(oaid)
                 */
                if (ocpcEvent == null && StringUtils.isNotBlank(request.getOaid2())&& !ConstCls.emptyMd5.equals(request.getOaid2())) {
                    key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.oaid2.value,request.getOaid2());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                /**
                 * 设备号和 oaId 查不到 再用mac地址查
                 */
                if (ocpcEvent == null && StringUtils.isNotBlank(request.getMac())&& !ConstCls.emptyMd5.equals(request.getMac())) {
                    key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.mac.value,request.getMac());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                /**
                 * androidId查
                 */
                if (ocpcEvent == null && StringUtils.isNotBlank(request.getAndroidId())&& !ConstCls.emptyMd5.equals(request.getAndroidId())) {
                    String androidIdMd5 = MD5Utils.getMd5Sum(request.getAndroidId());
                    key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.androidId.value,androidIdMd5);
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                if (ocpcEvent == null && StringUtils.isNotBlank(request.concatIpua())) {
                    key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.ipua.value,request.concatIpua());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }

                if (ocpcEvent == null && StringUtils.isNotBlank(request.getUserId())) {
                    key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.userId.value,request.getUserId());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }

                if (ocpcEvent == null && StringUtils.isNotBlank(request.getOpenId())&& !ConstCls.emptyMd5.equals(request.getOpenId())) {
                    key = RedisKeyConstants.getActiveOpenIdKey(product,request.getWAppId(),request.getOpenId());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                        log.info("openId 归因成功 " + product+" "+request.getAndroidId());
                        request.setGuiType(GuiyingType.openid.name);
                    }
                }
                if (ocpcEvent != null) {
                    log.info("hbase getocpcEvent success req={} ,rsp={} key={}", JSONObject.toJSONString(request),JSONObject.toJSONString(ocpcEvent),key);
                }
            }catch (Exception e){
                log.error("",e);
            }
        } catch (Exception e) {
            log.error("hbase getocpcEvent error ", e);
        }

        return ocpcEvent;
    }

    public OcpcEvent getUserIdActiveEventByHbase(UserEventReq request){

        OcpcEvent ocpcEvent = null;
        try {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("hbasegetocpcEvent product is null ! {}", JSONObject.toJSONString(request));
                return ocpcEvent;
            }

            String key = "";

            try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
                /**
                 * 先按设备号查询
                 */
                if (StringUtils.isNotBlank(request.getUserId())) {
                    key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.userId.value, request.getUserId());
                    Result res = table.get(new Get(Bytes.toBytes(key)));
                    String s = HbaseUtils.getCellValStr(res);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    }
                }
                if (ocpcEvent != null && ocpcSwitcher.logOpen) {
                    log.info("hbase getUserIdActive success req={} ,rsp={} key={}", JSONObject.toJSONString(request),JSONObject.toJSONString(ocpcEvent),key);
                }
            }catch (Exception e){
                log.error("",e);
            }
        } catch (Exception e) {
            log.error("hbase getUserIdActive error ", e);
        }

        return ocpcEvent;
    }

    /**
     * 存储关键行为事件
     *
     * @param ocpcEvent 事件
     */
    public void saveKeyEvent(OcpcEvent ocpcEvent) {

        String eventJson = JSON.toJSONString(ocpcEvent);

        List<String> orderlyKeys = OcpcEventKeyJoinUtil.generatePossibleEventKeyOrderly(ocpcEvent, ToutiaoEventTypeEnum.KEY_EVENT);

        if (CollectionUtils.isEmpty(orderlyKeys)) {
            log.warn("关键行为hbaseKeys拼接结果为空 {}", eventJson);
            return;
        }

        byte[] eventBytes = Bytes.toBytes(eventJson);

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
            for (String orderlyKey : orderlyKeys) {

                Put put = new Put(Bytes.toBytes(orderlyKey));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), eventBytes);
                table.put(put);

            }
        } catch (Exception e) {
            log.error("存储关键行为事件至hbase出现异常 ", e);
        }
    }

    public void saveKeyActionExtEvent(OcpcEvent ocpcEvent) {

        String eventJson = JSON.toJSONString(ocpcEvent);

        String extEventKey = OcpcEventKeyJoinUtil.generateExtEventKey(ocpcEvent);

        if (StringUtils.isBlank(extEventKey)) {
            log.warn("关键行为衍生事件hbaseKeys拼接结果为空 {}", eventJson);
            return;
        }

        byte[] eventBytes = Bytes.toBytes(eventJson);

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
            Put put = new Put(Bytes.toBytes(extEventKey));
            put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), eventBytes);
            table.put(put);
        } catch (Exception e) {
            log.error("存储关键行为衍生事件至hbase出现异常 ", e);
        }
    }

    /**
     * 查询关键行为事件记录
     *
     * @param userEventReq 请求
     * @return 关键行为事件记录
     */
    public OcpcEvent queryKeyEventByHbase(UserEventReq userEventReq) {
        List<String> orderlyKeys = OcpcEventKeyJoinUtil.generatePossibleEventKeyOrderly(userEventReq, ToutiaoEventTypeEnum.KEY_EVENT);

        if (CollectionUtils.isEmpty(orderlyKeys)) {
            log.warn("关键行为hbaseKeys拼接结果为空 {}", JSON.toJSONString(userEventReq));
            return null;
        }

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {

            for (String orderlyKey : orderlyKeys) {

                Get get = new Get(Bytes.toBytes(orderlyKey));
                Result result = table.get(get);
                String content = HbaseUtils.getCellValStr(result);
                if (StringUtils.isNotBlank(content)) {
                    if(ocpcSwitcher.logOpen){
                        log.info("查询hbase关键行为事件成功 request={} response={} key={}", JSON.toJSONString(userEventReq), content, orderlyKey);
                    }

                    if("ios".equalsIgnoreCase(userEventReq.getOs())){
                        OcpcEvent oc1 = JSONObject.parseObject(content, OcpcEvent.class);
                        if((StringUtils.isBlank(oc1.getUserId()) || oc1.getUserId().length()<=4 || !StringUtils.equalsIgnoreCase(oc1.getUserId(),userEventReq.getUserId()))){
                            log.warn("ios去重问题 request={} response={} key={}",JSON.toJSONString(userEventReq), content, orderlyKey);
                        }else{
                            log.info("uid功 u1"+userEventReq.getUserId()+" u2"+oc1.getUserId());
                            log.info("查询重复hbase关键行为事件成功 request={} response={} key={}", JSON.toJSONString(userEventReq), content, orderlyKey);
                            return JSONObject.parseObject(content, OcpcEvent.class);
                        }
                    }else{
                        return JSONObject.parseObject(content, OcpcEvent.class);
                    }
                }
            }
        } catch (Exception e) {
            log.error("从hbase查询关键行为事件出现异常 ", e);
        }

        return null;
    }



    public OcpcEvent queryUidKeyEventByHbase(UserEventReq userEventReq) {
        try {
            if(StringUtils.isBlank(userEventReq.getUserId())){
                log.info("userId为空关键行为 "+JSON.toJSONString(userEventReq));
            }
            List<String> orderlyKeys = OcpcEventKeyJoinUtil.generateUserIdKey(userEventReq, ToutiaoEventTypeEnum.KEY_EVENT);

            if (CollectionUtils.isEmpty(orderlyKeys)) {
                log.warn("userid关键行为hbaseKeys拼接结果为空  {}", JSON.toJSONString(userEventReq));
                return null;
            }

            try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {

                for (String orderlyKey : orderlyKeys) {

                    Get get = new Get(Bytes.toBytes(orderlyKey));
                    Result result = table.get(get);
                    String content = HbaseUtils.getCellValStr(result);
                    if (StringUtils.isNotBlank(content)) {
                        if("ios".equalsIgnoreCase(userEventReq.getOs())){
                            OcpcEvent oc1 = JSONObject.parseObject(content, OcpcEvent.class);
                            if((StringUtils.isBlank(oc1.getUserId()) || oc1.getUserId().length()<=4 || !StringUtils.equalsIgnoreCase(oc1.getUserId(),userEventReq.getUserId()))){
                                log.warn("useridios去重问题 request={} response={} key={}",JSON.toJSONString(userEventReq), content, orderlyKey);
                            }else{
                                log.info("useriduid功 u1"+userEventReq.getUserId()+" u2"+oc1.getUserId());
                                log.info("userid查询重复hbase关键行为事件成功 request={} response={} key={}", JSON.toJSONString(userEventReq), content, orderlyKey);
                                return JSONObject.parseObject(content, OcpcEvent.class);
                            }
                        }else{
                            return JSONObject.parseObject(content, OcpcEvent.class);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("从hbase查询关键行为事件出现异常 ", e);
            }
        }catch (Exception e){
            log.error("关键行为userId ",e);
        }

        return null;
    }

    public OcpcEvent queryUserActiveEvent(String key){
        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {

            Get get = new Get(Bytes.toBytes(key));
            Result result = table.get(get);
            String content = HbaseUtils.getCellValStr(result);
            if (StringUtils.isNotBlank(content)) {
                log.info("查询hbase激活事件成功  response={} key={}",  content, key);
                return JSONObject.parseObject(content, OcpcEvent.class);
            }
        }catch (Exception e) {
            log.error("从hbase查询激活事件出现异常 ", e);
        }
        return null;
    }

    /**
     * 获取用户已上传衍生事件相关信息
     */
    public OcpcEvent queryKeyActionExtEvent(UserEventReq userEventReq) {

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {

            String extEventKey = OcpcEventKeyJoinUtil.generateExtEventKey(userEventReq);

            Get get = new Get(Bytes.toBytes(extEventKey));
            Result result = table.get(get);
            String content = HbaseUtils.getCellValStr(result);
            if (StringUtils.isNotBlank(content)&& ocpcSwitcher.needLog(LogNum.yanshLog)) {
                log.info("查询hbase关键行为衍生事件成功 request={} response={} key={}", JSON.toJSONString(userEventReq), content, extEventKey);
                return JSONObject.parseObject(content, OcpcEvent.class);
            }
        } catch (Exception e) {
            log.error("从hbase查询关键行为衍生事件出现异常 ", e);
        }

        return null;
    }

    /**
     * 获取用户累计arpu，单位为分
     */
    public BigDecimal addUserArpu(String userId, OcpcEvent ocpcEvent, String price) {
        return addUserArpu(userId, ocpcEvent, price, tableName_UserArpu);
    }
    public BigDecimal addUserArpu(String userId, OcpcEvent ocpcEvent, String price, String tableName) {

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {

            String extEventKey = String.format(RedisKeyConstants.USER_ACC_ARPU_FORMATTER, ocpcEvent.getProduct(), ocpcEvent.getOs(), userId);

            Get get = new Get(Bytes.toBytes(extEventKey));
            Result result = table.get(get);
            String content = HbaseUtils.getCellValStr(result);

            // 最终的累计userArpu
            BigDecimal finalUserArpu;
            // 本次需增加的userArpu -> price 为千次曝光价格(元) ，除以10即为单次曝光价格(分)
            BigDecimal onceArpu = new BigDecimal(price).divide(new BigDecimal(10));

            if (StringUtils.isNotBlank(content)) {
                finalUserArpu = new BigDecimal(content).add(onceArpu);
            } else {
                finalUserArpu = onceArpu;
            }

            // 累计值存回hbase
            byte[] finalUserArpuBytes = Bytes.toBytes(finalUserArpu.toString());
            Put put = new Put(Bytes.toBytes(extEventKey));
            put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), finalUserArpuBytes);
            table.put(put);

            log.info("查询hbase中头条ltv成功 userId={} advertId={} product={}, {}+{}={}, key={} request={}", userId, ocpcEvent.getAccountId(), ocpcEvent.getProduct(),
                    content, onceArpu, finalUserArpu, extEventKey, JSON.toJSONString(ocpcEvent));

            return finalUserArpu;

        } catch (Exception e) {
            log.error("查询hbase中头条ltv异常 ", e);
            return null;
        }
    }

    /**
     * 获取用户累计次数
     */
    public Integer addUserNum(String userId, OcpcEvent ocpcEvent) {

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName_TempUserArpu))) {

            String extEventKey = String.format(RedisKeyConstants.USER_ACC_NUM_FORMATTER, ocpcEvent.getProduct(), ocpcEvent.getOs(), userId);

            Get get = new Get(Bytes.toBytes(extEventKey));
            Result result = table.get(get);
            String content = HbaseUtils.getCellValStr(result);

            // 最终的累计userArpu
            Integer finalUserArpu;

            if (StringUtils.isNotBlank(content)) {
                finalUserArpu = Integer.valueOf(content) + 1;
            } else {
                finalUserArpu = 1;
            }

            // 累计值存回hbase
            byte[] finalUserArpuBytes = Bytes.toBytes(finalUserArpu.toString());
            Put put = new Put(Bytes.toBytes(extEventKey));
            put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), finalUserArpuBytes);
            table.put(put);

            log.info("查询hbase中头条ltv成功 userId={} advertId={} product={}, num={}, key={} request={}", userId, ocpcEvent.getAccountId(), ocpcEvent.getProduct(),
                    finalUserArpu, extEventKey, JSON.toJSONString(ocpcEvent));

            return finalUserArpu;

        } catch (Exception e) {
            log.error("查询hbase中头条ltv异常 ", e);
            return null;
        }
    }

    /**
     * 获取用户累计次数
     */
    public void testDeleteHbase(String key) {

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {

            Delete delete = new Delete(Bytes.toBytes(key));
            table.delete(delete);

            log.info("测试删除hbase中数据成功 " + key);

        } catch (Exception e) {
            log.error("测试删除hbase中数据异常 " + key, e);
        }
    }

    public void syncSaveUserActive(OcpcEvent ocpcEvent) {
        if (ocpcSwitcher.writeHbaseSwitch) {
            poolTaskExecutor.execute(() -> {
                try {
                    saveActiveEventToHbase(ocpcEvent);
                }catch (Exception e){
                    log.error("异步写激活事件到hbase异常", e);
                }
            });
        }
    }

    private void saveActiveEventToHbase(OcpcEvent ocpcEvent) {

        String eventJson = JSON.toJSONString(ocpcEvent);

        List<String> orderlyKeys = OcpcEventKeyJoinUtil.generatePossibleEventKeyOrderly(ocpcEvent, ToutiaoEventTypeEnum.ACTIVATE_APP);

        if (CollectionUtils.isEmpty(orderlyKeys)) {
            log.warn("激活hbaseKeys拼接结果为空 {}", eventJson);
            return;
        }

        byte[] eventBytes = Bytes.toBytes(eventJson);

        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
            for (String orderlyKey : orderlyKeys) {

                Put put = new Put(Bytes.toBytes(orderlyKey));
                put.addColumn(Bytes.toBytes("family"), Bytes.toBytes("qualifier"), eventBytes);
                table.put(put);

            }
        } catch (Exception e) {
            log.error("存储激活事件至hbase出现异常 ", e);
        }

    }
}
