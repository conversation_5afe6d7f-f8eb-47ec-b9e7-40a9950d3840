package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.SigmobEventType;
import com.coohua.core.caf.dispense.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.StandardHttpRequestRetryHandler;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Date 2024/11/26 20:25
 * @Description:
 */

@Service
@Slf4j
public class SigmobUserEventService {

    @Autowired
    OcpcSwitcher ocpcSwitcher;

    @Autowired
    private AlertService alertService;

    @Autowired
    SigmobAccountService sigmobAccountService;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {

        try {
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            SigmobEventType sigmobEventType = SigmobEventType.getStatus(userEventReq.getEventType());

            if (sigmobEventType == null) {
                log.info("sigmob不需回传的类型 {} {} {} {}", sigmobEventType, dspType,  JSON.toJSONString(userEventReq), JSON.toJSONString(toutiaoClick));
                return false;
            }
            //仅仅回传关键行为,
            if (sigmobEventType != SigmobEventType.REGISTER){
                log.info("sigmob仅回传关键行为的事件: {} {} {} {}", sigmobEventType, dspType,  JSON.toJSONString(userEventReq), JSON.toJSONString(toutiaoClick));
                return false;
            }

            requestSigmob(toutiaoClick,userEvent,sigmobEventType);

        } catch (Exception e) {
            log.error(String.format("sigmob关键行为回传异常 userEventReq: %s, toutiaoClick: %s", JSON.toJSONString(userEventReq), JSON.toJSONString(toutiaoClick)), e);
            return false;
        }
        return false;
    }

    private void requestSigmob(ToutiaoClick toutiaoClick, UserEvent userEvent, SigmobEventType sigmobEventType) throws Exception {
        try {


            String url = toutiaoClick.getCallbackUrl();

            log.info("sigmob待回传事件 {} {} {}", JSON.toJSONString(toutiaoClick), sigmobEventType, url);

            HttpConfig httpConfig = HttpConfig.custom().timeout(2000);
            SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(
                    SSLContexts.createDefault(),
                    new String[]{"TLSv1.2"},
                    null,
                    SSLConnectionSocketFactory.getDefaultHostnameVerifier());
            CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).setRetryHandler(new StandardHttpRequestRetryHandler(2, false)).build();
            httpConfig.client(httpclient);
            Integer requestStatus = HttpClientUtil.status(httpConfig.timeout(3000).url(url));

            if (requestStatus != null && requestStatus==200) {
                log.info("sigmob回调成功 {} url:{}, responseStatus:{}, userEvent:{}", sigmobEventType, url, requestStatus, JSON.toJSONString(userEvent));
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "sigmob回调成功");
            } else {
                log.info("sigmob回调失败 {} url:{}, responseStatus:{}, userEvent:{}", sigmobEventType, url, requestStatus, JSON.toJSONString(userEvent));
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "sigmob回调失败");
            }
            if (userEvent != null) {
                // 额外的回传通过分隔符累加在后面
                userEvent.setReqUrl(url);
                userEvent.setReqRsp("执行sigmob回调,回调http请求的status结果值为:"+requestStatus);
            }
        } catch (Exception e) {
            log.error("sigmob回调失败并捕捉到异常为:", e);
        }
    }

}
