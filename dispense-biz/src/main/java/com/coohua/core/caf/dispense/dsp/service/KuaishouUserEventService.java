package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.apollo.KuaishouExtApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.AuthKuaishouApp;
import com.coohua.core.caf.dispense.dsp.entity.ThirdExtEvent;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dto.req.KuaishouExtDefineProps;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.AccountActionTypeEnum;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.KuaishouEventType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.KuaishouExtEventService;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import top.zrbcool.pepper.boot.httpclient.HttpBioClient;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class KuaishouUserEventService {

    @Autowired
    private AlertService alertService;
    @Autowired
    private HttpBioClient httpBioClient;
    @Autowired
    AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    @Autowired
    KuaishouExtEventService kuaishouExtEventService;
    @Autowired
    KuaishouExtApolloConfig apolloConfig;
    @Resource(name = "jobForKuaishouExtProcessCallback")
    ThreadPoolTaskExecutor jobForKuaishouExtProcessCallbackExecutor;

    public boolean isRecall(List<ToutiaoClick> toutiaoClickList, UserEventReq userEventReq) {
        if (CollectionUtils.isEmpty(toutiaoClickList)) {
            return userEventReq.getOcpcDeviceId() != null && "ios".equals(userEventReq.getOs());
        }
        return false;
    }

    @Autowired
    OcpcSwitcher ocpcSwitcher;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent, boolean isDirectMatch) {

        try {
            KuaishouEventType kuaishouEventType = KuaishouEventType.getStatus(userEventReq.getEventType());

            String callbackUrl = toutiaoClick.getCallbackUrl();

            AuthKuaishouApp authKuaishouApp = authKuaishouAdvertiserService.getByAccountId(toutiaoClick.getAccountId());
            boolean isQualityUserAccount = authKuaishouApp != null && Objects.equals(authKuaishouApp.getAccountType(), 2);
            // 测试优质用户的账户
            if (isQualityUserAccount) {
                if (!Objects.equals(authKuaishouApp.getDelFlag(), 0)) {
                    log.info("快手回传优质账户 已关闭该账户回传 {}", toutiaoClick.getAccountId());
                    userEvent.setReqRsp("快手回传优质账户 已关闭该账户回传");
                    return false;
                }

                callbackUrl = URLDecoder.decode(callbackUrl, "utf-8");
                //TODO 判断是助推的点击 且是回传行为的账户 修改回传值
                // 此种账户关键行为回传到优质用户
                if (kuaishouEventType == KuaishouEventType.REGISTER) {
                    // 6表示 优质用户 -> 22.03.17 改回回传到1（激活）
                    kuaishouEventType = KuaishouEventType.ACTIVATE_APP;
                    log.info("快手回传优质账户 {}", toutiaoClick.getAccountId());
                } else {
                    log.info("快手回传优质账户 不回传除关键行为外的事件 {}", toutiaoClick.getAccountId());
                    userEvent.setReqRsp("快手回传优质账户 不回传除关键行为外的事件");
                    return false;
                }
            }

            String reqUrl = callbackUrl + "&event_type=" + kuaishouEventType.name + "&event_time=" + System.currentTimeMillis();
            if(KuaishouEventType.PURCHASE.equals(kuaishouEventType) && userEventReq.getPayAmount()!=null && userEventReq.getPayAmount()>0){
                reqUrl = reqUrl +"&purchase_amount="+ DoubleUtil.getDoubleByTwo(userEventReq.getPayAmount()/100d);
                log.info("ocpc支付 快手 "+reqUrl);
            }
            if (!isDirectMatch) {
                reqUrl = reqUrl + "&is_direct_match=false";
            }

            String keyEventValue = Optional.ofNullable(userEventReq.getActionValues())
                    .map(OcpcNewCallbackRuleValidateUtil::analyzeNewValueToMap)
                    .map(map -> map.get(AccountActionTypeEnum.WATCH_VIDEO.getTypeKey()))
                    .orElse(null);

            if (keyEventValue == null) {
                keyEventValue = Optional.ofNullable(userEventReq.getActionValues())
                        .map(OcpcNewCallbackRuleValidateUtil::analyzeNewValueToMap)
                        .map(map -> map.get(AccountActionTypeEnum.ARPU_ONE_DAY.getTypeKey()))
                        .orElse(null);
            }

            if (keyEventValue == null) {
                keyEventValue = userEventReq.getActionNumber();
            }

            reqUrl = reqUrl + "&key_action_category=0";
            if (keyEventValue != null) {
                reqUrl = reqUrl + "&key_action_threshold=" + keyEventValue;
            } else {
                log.warn("快手本次回传关键行为值为空 {}", JSON.toJSONString(userEventReq));
            }

            String requestId = userEventReq.getUserId() + userEventReq.getProduct() + userEventReq.getEventType() + System.currentTimeMillis();

            String html = requestKuaishou(requestId, toutiaoClick.getProduct(), toutiaoClick.getAccountId(), kuaishouEventType, reqUrl);

            if (userEvent != null) {
                userEvent.setReqRsp(html);
                userEvent.setReqUrl(reqUrl);
            }
        } catch (Exception e) {
            log.error("kuaishou userEvent unknown error. userEventReq: {}, toutiaoClick: {}", userEventReq, toutiaoClick, e);
            return false;
        }
        return false;
    }

    private String requestKuaishou(String requestId, String product, String accountId, KuaishouEventType eventType, String reqUrl) throws Exception {
        String html = "";

        if (ocpcSwitcher.toNewHttpClient) {
            html = HttpClientUtil.get(HttpConfig.custom().timeout(5000).url(reqUrl));
        } else {
            URI uri = new URI(reqUrl);
            HttpGet httpGet = new HttpGet(uri);
            try {
                HttpResponse response = httpBioClient.execute(requestId, httpGet, 7000, TimeUnit.MILLISECONDS);
                if (response.getStatusLine().getStatusCode() == 200) {
                    html = EntityUtils.toString(response.getEntity(), "UTF-8");
                } else {
                    alertService.report(product, accountId, "快手回调失败");
                }
            } catch (Exception e) {
                log.error("发送快手请求异常", e);
            }
        }

        JSONObject jobj = JSON.parseObject(html);
        log.info("激活快手   " + html);
        if (jobj != null && jobj.getInteger("result") == 1) {
            log.info("快手回调成功 {} {} {} req {} res {}", eventType.name(), product, accountId, reqUrl, html);
            String reportName = ToutiaoEventTypeEnum.getKuaishouReportName(eventType.value, true);
            alertService.report(product, accountId, reportName);
        } else {
            log.info("快手回调失败 {} {} {} req {} res {}", eventType.name(), product, accountId, reqUrl, html);
            String reportName = ToutiaoEventTypeEnum.getKuaishouReportName(eventType.value, false);
            alertService.report(product, accountId, reportName);
        }
        return html;
    }

    /**
     * 回传快手衍生过程
     */
    public void callbackExtProcess(UserEventReq userEventReq, OcpcEvent ocpcEvent, String product, String price, Long eventTime, KuaishouEventType eventType) {

        if (!DspType.KUAISHOU.value.equals(ocpcEvent.getDsp())) {
            return;
        }

        if (apolloConfig.illegalProduct(product)) {
            return;
        }

        if (apolloConfig.illegalAccount(ocpcEvent.getAccountId())) {
            return;
        }

        // 仅回传激活后72小时内的数据
        if (eventTime > ocpcEvent.getCreateTime().getTime() + TimeUnit.DAYS.toMillis(3)) {
            return;
        }

        AuthKuaishouApp authKuaishouApp = authKuaishouAdvertiserService.getByAccountId(StringUtils.isBlank(ocpcEvent.getAccountId()) ? "0" : ocpcEvent.getAccountId());
        boolean isNormalAccount = authKuaishouApp != null && Objects.equals(authKuaishouApp.getAccountType(), 1);
        // 非普通账户不回传 (达人账户不回传)
        if (!isNormalAccount) {
            return;
        }

        jobForKuaishouExtProcessCallbackExecutor.execute(() -> callbackExtProcessOne(userEventReq, ocpcEvent, price, eventType, eventTime));
    }

    /**
     * 回传快手EVENT_MINIGAME_IAA
     */
    public void callbackIAAProcess(UserEventReq userEventReq, OcpcEvent ocpcEvent, String product, String price, Long eventTime, KuaishouEventType eventType) {

        if (!DspType.KUAISHOU.value.equals(ocpcEvent.getDsp())) {
            return;
        }

        if (apolloConfig.illegalIAAProduct(product)) {
            return;
        }

        if (apolloConfig.illegalIAAAccount(ocpcEvent.getAccountId())) {
            return;
        }

        // 仅回传激活后7天内的数据
        if (eventTime > ocpcEvent.getCreateTime().getTime() + TimeUnit.DAYS.toMillis(7)) {
            return;
        }

        AuthKuaishouApp authKuaishouApp = authKuaishouAdvertiserService.getByAccountId(StringUtils.isBlank(ocpcEvent.getAccountId()) ? "0" : ocpcEvent.getAccountId());
        boolean isNormalAccount = authKuaishouApp != null && Objects.equals(authKuaishouApp.getAccountType(), 1);
        // 非普通账户不回传 (达人账户不回传)
        if (!isNormalAccount) {
            return;
        }
        if (authKuaishouApp.getEventTypes() == null || !authKuaishouApp.getEventTypes().contains(eventType.name)) {
            return;
        }

        jobForKuaishouExtProcessCallbackExecutor.execute(()-> callbackIAAProcessOne(authKuaishouApp,userEventReq, ocpcEvent, price, eventType, eventTime));
    }

    private void callbackIAAProcessOne(AuthKuaishouApp authKuaishouApp,UserEventReq userEventReq, OcpcEvent ocpcEvent, String price, KuaishouEventType eventType, Long eventTime) {
        int eventTypeVal = eventType.value;
        userEventReq.setEventType(eventTypeVal);

        String requestId = userEventReq.getUserId() + userEventReq.getProduct() + userEventReq.getEventType() + System.currentTimeMillis();
        String[] split = authKuaishouApp.getEventValues().split(",");
        // price单位是元，转为ecpm(单位:分)
        double ecpm = new BigDecimal(price).divide(new BigDecimal(1000),10,RoundingMode.HALF_UP).multiply(new BigDecimal(split.length < 3 ? "0.7" : split[2])).setScale(2, RoundingMode.HALF_UP).doubleValue();
        Integer saveEcpm = new BigDecimal(price).multiply(new BigDecimal(split.length < 3 ? "0.7" : split[2])).intValue();

        String reqUrl = ocpcEvent.getCallbackUrl() + "&event_type=" + eventTypeVal + "&purchase_amount=" + ecpm;


        String res = "";

        try {
            res = requestKuaishou(requestId, ocpcEvent.getProduct(), ocpcEvent.getAccountId(), eventType, reqUrl);
        } catch (Exception e) {
            log.info("快手回调衍生过程异常 {} {} {} req {} res {}", eventType.desc, ocpcEvent.getProduct(), ocpcEvent.getAccountId(), reqUrl, res);
        }

        kuaishouExtEventService.saveKuaishouExt(userEventReq, ocpcEvent, reqUrl, res, eventType, saveEcpm, eventTime);

    }

    private void callbackExtProcessOne(UserEventReq userEventReq, OcpcEvent ocpcEvent, String price, KuaishouEventType eventType, Long eventTime) {
        int eventTypeVal = Integer.parseInt(eventType.name);
        userEventReq.setEventType(eventTypeVal);

        String requestId = userEventReq.getUserId() + userEventReq.getProduct() + userEventReq.getEventType() + System.currentTimeMillis();

        // price单位是元，转为ecpm(单位:分)
        Integer ecpm = new BigDecimal(price).multiply(new BigDecimal(100)).intValue();

        String reqUrl = ocpcEvent.getCallbackUrl() + "&event_type=" + eventTypeVal;

        if (eventType == KuaishouEventType.ECPM) {
            reqUrl += "&event_props=" + genProps(ecpm);
        }

        String res = "";

        try {
            res = requestKuaishou(requestId, ocpcEvent.getProduct(), ocpcEvent.getAccountId(), eventType, reqUrl);
        } catch (Exception e) {
            log.info("快手回调衍生过程异常 {} {} {} req {} res {}", eventType.desc, ocpcEvent.getProduct(), ocpcEvent.getAccountId(), reqUrl, res);
        }

        kuaishouExtEventService.saveKuaishouExt(userEventReq, ocpcEvent, reqUrl, res, eventType, ecpm, eventTime);

    }

    private String genProps(Integer ecpm) {
        String props = new JSONObject()
                .fluentPut("event_val", ecpm)
                .toJSONString();
        try {
            return URLEncoder.encode(props, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            return props;
        }
    }

    /**
     * 快手关键行为衍生定义回传
     */
    public void callbackExtDefine(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userExtEvent, ThirdExtEvent thirdExtEvent, long callbackMillisecond) {

        KuaishouEventType eventType = KuaishouEventType.getStatus(userExtEvent.getEventType(), false);

        String reqUrl = null;
        if (toutiaoClick != null && StringUtils.isNotBlank(toutiaoClick.getCallbackUrl())) {
            reqUrl = String.format("%s&event_type=%s&event_time=%s&event_props=%s", toutiaoClick.getCallbackUrl(), eventType.name,
                    callbackMillisecond, genProps(thirdExtEvent));
        }

        String requestId = userEventReq.getUserId() + userEventReq.getProduct() + thirdExtEvent.getToutiaoEventType() + System.currentTimeMillis();
        String res = "";

        try {
            res = requestKuaishou(requestId, toutiaoClick.getProduct(), toutiaoClick.getAccountId(), eventType, reqUrl);
        } catch (Exception e) {
            log.info("快手回调衍生事件异常 {} {} {} req {} res {}", eventType.desc, toutiaoClick.getProduct(), toutiaoClick.getAccountId(), reqUrl, res);
        }

        if (userExtEvent != null) {
            userExtEvent.setReqUrl(reqUrl);
            userExtEvent.setReqRsp(res);
        }
    }

    private String genProps(ThirdExtEvent thirdExtEvent) {
        String props = new KuaishouExtDefineProps()
                .setIs_convert(ObjectUtils.defaultIfNull(thirdExtEvent.getIsGaConvert(), 0L))
                .setDepth(thirdExtEvent.getDepth())
                .setKey_action_category1(thirdExtEvent.getActionType1())
                .setKey_action_threshold1(thirdExtEvent.getValue1())
                .setKey_action_category2(thirdExtEvent.getActionType2())
                .setKey_action_threshold2(thirdExtEvent.getValue2())
                .setKey_action_category3(thirdExtEvent.getActionType3())
                .setKey_action_threshold3(thirdExtEvent.getValue3())
                .toString();
        try {
            return URLEncoder.encode(props, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            return props;
        }
    }

}
