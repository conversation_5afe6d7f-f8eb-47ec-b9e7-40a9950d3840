package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.builder.HCB;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.common.Utils;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.SdkProductPkg;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.service.SdkProductPkgService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.ocpc.entity.SdkConf;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.client.StandardHttpRequestRetryHandler;
import org.apache.http.ssl.SSLContexts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class OceanengineSdkService {

    private static org.apache.http.client.HttpClient client;

    static{
        try {
            client = HCB.custom().ssl().pool(500, 200).build();
        } catch (HttpProcessException e) {
            Utils.errorException("巨量sdk创建https协议的HttpClient对象出错: {}", e);
        }
    }

    private static final String URL = "https://analytics.oceanengine.com/sdk/app/attribution";

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    SdkProductPkgService sdkProductPkgService;

    /**
     * https://bytedance.larkoffice.com/docx/CgYBdVzoBogND2xv7PhcSfFNnyc
     * @param userEventReq
     * @return
     * {
     *     "code": 0,
     *     "message": "成功",
     *     "adv_idfv": "xxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxx",
     *     "adv_android_id": "",
     *     "is_dp_open": false,
     *     "activation_window": 30,
     *     "active_time": 123123123,
     *     "active_touch_time": 123123123,
     *     "last_touch_time": 123123123,
     *     "project_id": 123123123123123,
     *     "project_name": "xxxxxxxx",
     *     "promotion_id": 123123123123123,
     *     "promotion_name": "xxxxxxxxxxxxxx",
     *     "aid": 0,
     *     "aid_name": "",
     *     "cid": 0,
     *     "cid_name": "",
     *     "advertiser_id": 232323232323,
     *     "req_id": "xxxxxxxxxxxxxxxxxxxxxxxx",
     *     "track_id": "xxxxxxxxxxxxxxxxxx",
     *     "callback_param": "xxxxxxxxxxxxxxxxxxxxxxxx",
     *     "callback_url": "http://ad.toutiao.com/track/activate/?callback=xxxxxxxxxxxxxxxxxxxxx&os=1",
     *     "mid1": 123123123,
     *     "mid2": 123123123123123,
     *     "mid3": 123123123,
     *     "mid4": 123123123,
     *     "mid5": 123123123,
     *     "mid6": 123123132,
     *     "active_track_url": "http://xxxxxx"
     *     "action_track_url": "http://xxxxxx"
     * }
     *
     * adv_idfv
     * 广告主app采集到的idfv
     * adv_android_id
     * 广告主app采集到的androidid
     * is_dp_open
     * 用户是否通过deeplink直接吊起激活，也代表是否有clickid注入
     * activation_window
     * 激活窗口期，天（默认30天，广告主可配置，可用于校验当前归因窗口期）
     * active_time
     * app激活时间，unix时间戳，秒
     * last_touch_time
     * 激活前最后一个触点时间，unix时间戳，秒（如果是clickid注入场景，该字段不会替换）
     * callback_param
     * 一些跟广告信息相关的回调参数，内容是一个加密字符串，用于回传激活和深度转化事件
     * callback_url
     * 直接把调用事件回传接口的url生成出来，广告主可以直接使用
     * promotion_name
     * 巨量广告升级版中的广告名称
     * promotion_id
     * 巨量广告升级版中的广告ID
     * project_id
     * 巨量广告升级版中的项目ID
     * project_name
     * 巨量广告升级版中的项目名称
     * advertiser_id
     * 广告主id/账户id
     * aid
     * 广告id
     * 广告计划名称
     * cid
     * 创意id
     * cid_name
     * 创意名称
     * req_id
     * 一次广告请求的id（非广告唯一标识）
     * track_id
     * send出的广告唯一标识，可以用来串联同一广告不同行为
     * mid1
     * 针对巨量广告升级版，图片素材宏参数（下发原始素材id）
     * mid2
     * 针对巨量广告升级版，标题素材宏参数（下发原始素材id）
     * mid3
     * 针对巨量广告升级版，视频素材宏参数（下发原始素材id）
     * mid4
     * 针对巨量广告升级版，搭配试玩素材宏参数（下发原始素材id）
     * mid5
     * 针对巨量广告升级版，落地页素材宏参数（下发原始素材id）
     * mid6
     * 针对巨量广告升级版，安卓下载详情页素材宏参数（下发原始素材id）
     * csite
     * 归因到的触点对应广告投放点位信息
     * active_track_url
     * 投放的原始的激活监测链接（某些广告主会带一些定制参数，方便透传）
     * action_track_url
     * 投放的原始的有效触点监测（某些广告主会带一些定制参数，方便透传）
     * convert_source
     * 标识转化的来源，ad表示字节广告，others表示其他渠道
     */
    private JSONObject postResult(HttpConfig httpConfig, JSONObject body, int tryTimes, boolean isGui) throws InterruptedException {
        JSONObject jo = null;
        try {
            String respContent = HttpClientUtil.post(httpConfig.url(URL).json(body.toJSONString()));
            jo = JSONObject.parseObject(respContent);
            int code = jo.getIntValue("code");
            //10 归因失败，请重试 服务错误，请重试
            //202 归因中，请重试 归因过程中，暂未完成，重试即可
            //204 未找到sdk上报的事件，请稍后重试  sdk采集的打开事件未获取到，可能是归因结果调用较早，请等待客户端sdk事件上报成功后重试
            if(10 != code && 202 != code && 204 != code) {
                return jo;
            }
        }catch (Exception e){
            log.error("巨量实时归因接口报错, url:{}, param:{}", URL, body,  e);
        }
        if(tryTimes < 3) {
            TimeUnit.MILLISECONDS.sleep(isGui ? 100 : 300);
            return postResult(httpConfig, body, tryTimes + 1, isGui);
        }
        return jo;
    }
    public ToutiaoClick querySdkClick(UserEventReq userEventReq, boolean isActive, boolean isGui){
        try {
            //只有在配置中的产品才会进行sdk归因  android配置，
            SdkProductPkg productPkg = sdkProductPkgService.getProductPkg(userEventReq.getAppId() + "_" + userEventReq.getOs());
            if (productPkg == null) {
                return null;
            }
            boolean isIos = "ios".equals(userEventReq.getOs().toLowerCase());
            JSONObject body = new JSONObject()
                    .fluentPut("platform", isIos ? "ios" : "android")
                    .fluentPut("package_name", productPkg.getPkgName());

            if(isIos) {
                body.fluentPut("idfv", userEventReq.getIdfv());
            }else {
                body.fluentPut("android_id", StringUtils.isNotBlank(userEventReq.getOriginalAndroidId()) ? userEventReq.getOriginalAndroidId() : userEventReq.getSourceDeviceId());
            }
            HttpConfig httpConfig = HttpConfig.custom().timeout(3000);
            if(Objects.equals("true", System.getProperty("https.skip"))) {
                SSLConnectionSocketFactory ssl = new SSLConnectionSocketFactory(
                        SSLContexts.createDefault(),
                        new String[]{"TLSv1.2"},
                        null,
                        SSLConnectionSocketFactory.getDefaultHostnameVerifier());
                CloseableHttpClient httpclient = HttpClients.custom().setSSLSocketFactory(ssl).setRetryHandler(new StandardHttpRequestRetryHandler(2, false)).build();
                httpConfig.client(httpclient);
            }else {
                httpConfig.client(client);
            }
            JSONObject jo = postResult(httpConfig, body, 1, isGui);
            log.info("巨量实时归因接口 url:{}, userId:{}, channel:{}, param:{}, res:{}", URL, userEventReq.getUserId(), userEventReq.getPkgChannel(), body.toJSONString(), jo);
            //优先使用 sourceDeviceId, 查不到结果用androidId
            if(null == jo || 0 != jo.getIntValue("code")) {
                if(isActive) {
                    return null;
                }
                if(!isIos && StringUtils.isNotBlank(userEventReq.getAndroidId())) {
                    body.fluentPut("android_id", userEventReq.getAndroidId());
                    jo = postResult(httpConfig, body, 1, isGui);
                    log.info("巨量实时归因接口二次 url:{}, userId:{}, channel:{}, param:{}, res:{}", URL, userEventReq.getUserId(), userEventReq.getPkgChannel(), body.toJSONString(), jo);
                }
                // 如果二次返回 归因中-请重试 则放入数据库，通过定时扫描重试获取
                if (jo != null && 202 == jo.getIntValue("code")) {
                    userEventReq.setRetrySdkGy(true);
                }
                if(null == jo || 0 != jo.getIntValue("code")) {
                    return null;
                }
            }
            //标识转化的来源，ad表示字节广告，others表示其他渠道
            if(!"ad".equalsIgnoreCase(jo.getString("convert_source"))) {
                return null;
            }
            ToutiaoClick click = new ToutiaoClick();
            click.setDsp("toutiao");
            click.setProduct(userEventReq.getProduct());
            click.setOs(userEventReq.getOs());
            click.setAccountId(jo.getString("advertiser_id"));
            String ocpcDeviceId = userEventReq.getOcpcDeviceId();
            click.setOcpcDeviceId(StringUtils.isBlank(ocpcDeviceId) && isIos ? userEventReq.getIdfa() : ocpcDeviceId);
            click.setIdfa2(userEventReq.getIdfa2());
            click.setTs((jo.getLong("last_touch_time") * 1000) + "");
            //需要按这个时间排序
            click.setCreateTime(new Date(jo.getLong("last_touch_time") * 1000));
            click.setCallbackUrl(StringUtils.isNotBlank(jo.getString("callback_param")) ? jo.getString("callback_param") : jo.getString("callback_url"));
            click.setCid(jo.getString("mid3"));
            click.setGid(jo.getString("project_id"));
            click.setPid(jo.getString("promotion_id"));
            click.setOaid(userEventReq.getOaid());
            click.setOaid2(userEventReq.getOaid2());
            click.setAidName(jo.getString("project_name"));
            click.setGroupName(jo.getString("promotion_name"));
            click.setCidName(jo.getString("cid_name"));
            click.setPkgChannel(StringUtils.isNotBlank(userEventReq.getPkgChannel()) ? userEventReq.getPkgChannel() : userEventReq.getPkg_channel());
            click.setMac(userEventReq.getMac());
            //放出来看一眼
            click.setUnionSite(jo.getString("csite"));
            click.setAndroidId(jo.getString("adv_android_id"));
            click.setClickId(userEventReq.getIp());
            click.setUa(userEventReq.getUa());
            click.setModel(userEventReq.getModel());
            click.setMid(jo.getString("mid3"));
            click.setTrackId(jo.getString("track_id"));
            //新增归因类型
            click.setGuiType(GuiyingType.sdk.name);
            userEventReq.setGuiType(GuiyingType.sdk.name);
            return click;
        }catch (Exception e){
            log.error("巨量实时归因接口出错 ",e);
        }
        return null;
    }
}
