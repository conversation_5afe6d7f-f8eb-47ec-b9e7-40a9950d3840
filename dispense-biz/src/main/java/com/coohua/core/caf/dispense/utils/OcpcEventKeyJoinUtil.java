package com.coohua.core.caf.dispense.utils;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.coohua.core.caf.dispense.constant.BaseConstants.EMPTY_MD5;
import static com.coohua.core.caf.dispense.constant.BaseConstants.ZERO_MD5_HEX;
import static com.coohua.core.caf.dispense.constant.RedisKeyConstants.*;
import static com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class OcpcEventKeyJoinUtil {


    /**
     * 事件类型-事件key格式化key对应关系
     */
    private static final Map<ToutiaoEventTypeEnum, String> EVENT_KEY_FORMATTER_MAPPER = ImmutableMap.of(
            ACTIVATE_APP, EVENT_DEVICE_ACTIVE, KEY_EVENT, KEY_EVENT_FORMATTER);
    private static final int MIN_DEVICE_LENGTH = 6;


    public static List<String> generateUserIdKey(UserEventReq request, ToutiaoEventTypeEnum eventTypeEnum) {
        String formatter = EVENT_KEY_FORMATTER_MAPPER.get(eventTypeEnum);
        List<String> orderlyKeys = Lists.newArrayListWithExpectedSize(4);
        if (StringUtils.isNotBlank(request.getUserId())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.userId.value, request.getUserId()));
        }
        return orderlyKeys;
    }
    /**
     * 按顺序生成事件查询key
     *
     * @param request       请求
     * @param eventTypeEnum 事件类型
     * @return 事件查询key集合
     */
    public static List<String> generatePossibleEventKeyOrderly(UserEventReq request, ToutiaoEventTypeEnum eventTypeEnum) {

        String formatter = EVENT_KEY_FORMATTER_MAPPER.get(eventTypeEnum);

        if (formatter == null) {
            return null;
        }

        if (StringUtils.isBlank(request.getProduct())) {
            log.warn("上报请求产品为空 {}", JSON.toJSONString(request));
            return null;
        }

        List<String> orderlyKeys = Lists.newArrayListWithExpectedSize(4);

        if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && !EMPTY_MD5.equals(request.getOcpcDeviceId())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.device.value, request.getOcpcDeviceId()));
        }
        if (StringUtils.isNotBlank(request.getIdfa2()) && !EMPTY_MD5.equals(request.getIdfa2())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.idfa2.value, request.getIdfa2()));
        }
        /*
         * click不存在再查oaId
         */
        if (StringUtils.isNotBlank(request.getOaid()) && !EMPTY_MD5.equals(request.getOaid())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.oaid.value, request.getOaid()));
        }
        /*
         * oaId不存在再查oaId2
         */
        if (StringUtils.isNotBlank(request.getOaid2()) && !EMPTY_MD5.equals(request.getOaid2())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.oaid2.value, request.getOaid2()));
        }
        /*
         * 设备号和 oaId 查不到 再用mac地址查
         */
        if (StringUtils.isNotBlank(request.getMac()) && !EMPTY_MD5.equals(request.getMac())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.mac.value, request.getMac()));
        }
        /*
         * androidId查
         */
        if (StringUtils.isNotBlank(request.getAndroidId()) && !EMPTY_MD5.equals(request.getAndroidId())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.androidId.value, request.getMac()));
        }
        if (StringUtils.isNotBlank(request.getCaid())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.caid.value, request.getCaid()));
        }
        if (StringUtils.isNotBlank(request.getCaid2())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.caid2.value, request.getCaid2()));
        }
        /*
         * ipua
         */
        if (StringUtils.isNotBlank(request.concatIpua())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.ipua.value, request.concatIpua()));
        }

        if (StringUtils.isNotBlank(request.getOpenId())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.openid.value, request.getOpenId()));
        }

        if (StringUtils.isNotBlank(request.getUserId())) {
            orderlyKeys.add(String.format(formatter, request.getProduct(), request.getOs(), DeviceType.userId.value, request.getUserId()));
        }
        return orderlyKeys;

    }

    /**
     * 按顺序生成事件存储key
     *
     * @param ocpcEvent     事件记录
     * @param eventTypeEnum 事件类型
     * @return 事件查询key集合
     */
    public static List<String> generatePossibleEventKeyOrderly(OcpcEvent ocpcEvent, ToutiaoEventTypeEnum eventTypeEnum) {

        String formatter = EVENT_KEY_FORMATTER_MAPPER.get(eventTypeEnum);

        if (formatter == null) {
            return null;
        }

        List<String> orderlyKeys = new ArrayList<>();

        if (eventTypeEnum == ACTIVATE_APP) {
            if (StringUtils.isNotBlank(ocpcEvent.getUserId())) {
                orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.userId.value, ocpcEvent.getUserId()));
            }
        }
        if (StringUtils.isNotBlank(ocpcEvent.getOcpcDeviceId()) && !ZERO_MD5_HEX.equals(ocpcEvent.getOcpcDeviceId()) && ocpcEvent.getOcpcDeviceId().length() > MIN_DEVICE_LENGTH) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.device.value, ocpcEvent.getOcpcDeviceId()));
        }
        if (StringUtils.isNotBlank(ocpcEvent.getIdfa2()) && !ZERO_MD5_HEX.equals(ocpcEvent.getIdfa2()) && ocpcEvent.getIdfa2().length() > MIN_DEVICE_LENGTH) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.idfa2.value, ocpcEvent.getIdfa2()));
        }
        if (StringUtils.isNotBlank(ocpcEvent.getOaid()) && !ZERO_MD5_HEX.equals(ocpcEvent.getOaid()) && ocpcEvent.getOaid().length() > MIN_DEVICE_LENGTH) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.oaid.value, ocpcEvent.getOaid()));
        }
        if (StringUtils.isNotBlank(ocpcEvent.getOaid2()) && !ZERO_MD5_HEX.equals(ocpcEvent.getOaid2()) && ocpcEvent.getOaid2().length() > MIN_DEVICE_LENGTH) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.oaid2.value, ocpcEvent.getOaid2()));
        }
        if (StringUtils.isNotBlank(ocpcEvent.getMacId()) && !ZERO_MD5_HEX.equals(ocpcEvent.getMacId()) && ocpcEvent.getMacId().length() > MIN_DEVICE_LENGTH) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.mac.value, ocpcEvent.getMacId()));
        }
        if (StringUtils.isNotBlank(ocpcEvent.getAndroidId()) && !ZERO_MD5_HEX.equals(ocpcEvent.getAndroidId())) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.androidId.value, ocpcEvent.getAndroidId()));
        }
        if (StringUtils.isNotBlank(ocpcEvent.concatIpua())) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.ipua.value, ocpcEvent.concatIpua()));
        }

        if (StringUtils.isNotBlank(ocpcEvent.getOpenId()) && ocpcEvent.getOpenId().length()>5&& !ZERO_MD5_HEX.equals(ocpcEvent.getOpenId())) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getWAppId(),DeviceType.openid.value, ocpcEvent.getOpenId()));
        }
        if (StringUtils.isNotBlank(ocpcEvent.getCaid()) && ocpcEvent.getCaid().length()>5&& !ZERO_MD5_HEX.equals(ocpcEvent.getCaid())) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(),DeviceType.caid.value, ocpcEvent.getCaid()));
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(),DeviceType.caid2.value, MD5Utils.getMd5Sum(ocpcEvent.getCaid())));
        }
        if (StringUtils.isNotBlank(ocpcEvent.getUserId())) {
            orderlyKeys.add(String.format(formatter, ocpcEvent.getProduct(), ocpcEvent.getOs(),DeviceType.userId.value, ocpcEvent.getUserId()));
        }

        return orderlyKeys;
    }

    public static String generateExtEventKey(UserEventReq request) {
        return generateExtEventKey(request.getProduct(), request.getOs(), request.getUserId());
    }

    public static String generateExtEventKey(OcpcEvent ocpcEvent) {
        return generateExtEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), ocpcEvent.getUserId());
    }

    private static String generateExtEventKey(String product, String os, String userId) {

        if (StringUtils.isBlank(product)) {
            log.warn("上报衍生事件请求产品为空 {} {} {}", product, os, userId);
            return null;
        }

        String formatter = KEY_ACTION_EXT_EVENT_LIST_FORMATTER;

        return String.format(formatter, product, os, userId);
    }

}
