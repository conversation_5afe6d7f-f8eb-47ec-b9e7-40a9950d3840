package com.coohua.core.caf.dispense.rta;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.hbase.RtaHbaseService;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class RtaUpPriceRedisService {

    public Pair<GuiyingType,List<RtaUpprice>> queryRtaUpPrice(UserEventReq request){
        String upPricePrefix = "upPrice";
        List<RtaUpprice> deviceList = new ArrayList<>();
        Set<String> dset = new HashSet<>();
        GuiyingType guiyingType = null;
        long ctime = System.currentTimeMillis();
        if(StringUtils.equalsIgnoreCase("ios",request.getOs())){
            if(StringUtils.isNotBlank(request.getCaid())){
                dset = rtaRedis.smembers(upPricePrefix+request.getCaid());
                guiyingType = GuiyingType.caid;
            }
        }else{
            if(StringUtils.isNotBlank(request.getOaid())){
                dset = rtaRedis.smembers(upPricePrefix+request.getOaid());
                guiyingType = GuiyingType.oaid;
            }
            if(dset.size()==0 && StringUtils.isNotBlank(request.getOcpcDeviceId())){
                dset = rtaRedis.smembers(upPricePrefix+request.getOcpcDeviceId());
                guiyingType = GuiyingType.imei;
            }
        }

        if((System.currentTimeMillis()-ctime)>2000){
            log.info("cost "+(System.currentTimeMillis()-ctime)+""+JSON.toJSONString(dset));
        }
        if(dset.size()>0 && dset.size()<30) {
            deviceList = converDeviceSet(request.getProduct(),dset);
        }

        Pair<GuiyingType,List<RtaUpprice>> pair = new Pair<>(guiyingType,deviceList);
        return pair;
    }
    @Autowired
    RtaHbaseService rtaHbaseService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    public ToutiaoClick queryJJClick(UserEventReq userEventReq){
        try {
            if(!ocpcSwitcher.rtaProjectAccounts.contains(userEventReq.getProduct())){
                return null;
            }
            Pair<GuiyingType,List<RtaUpprice>> pair =queryRtaUpPrice(userEventReq);
            if(pair.getValue()!=null && pair.getValue().size()>0){
                log.info("rta竞价归因优先 "+userEventReq.getProduct()+" caid "+userEventReq.getCaid()+" oaid "+userEventReq.getOaid());
                List<RtaUpprice> rtaUpPriceList = pair.getValue();

                RtaUpprice lowPc = rtaUpPriceList.get(0);
                for(RtaUpprice lwd : rtaUpPriceList){
                    if(lowPc.getUppriceFloat() > lwd.getUppriceFloat()){
                        lowPc = lwd;
                    }
                }

                DspType dspType = DspType.TOUTIAO;
                if(lowPc.getDspType()!=null){
                    dspType = DspType.getDspType(lowPc.getDspType());
                }

                ToutiaoClick toutiaoClick = rtaHbaseService.getHbaseClickByDevice(dspType,userEventReq);
                return toutiaoClick;
            }
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }
    @Autowired
    @Qualifier("rta-redisJedisClusterClient")
    private JedisClusterClient rtaRedis;
    public List<RtaUpprice> converDeviceSet(String product,Set<String>  dset){
        if(dset.size()>0){
            List<RtaUpprice> rlist = dset.stream().map(a-> JSON.parseObject(a,RtaUpprice.class)).filter(rtaUpprice -> StringUtils.equalsIgnoreCase(rtaUpprice.getProduct(),product)).collect(Collectors.toList());
            return rlist;
        }
        return new ArrayList<>();
    }
}
