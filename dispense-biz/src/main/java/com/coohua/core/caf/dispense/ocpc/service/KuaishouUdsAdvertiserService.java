package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.ocpc.entity.KuaishouUdsAdvertiser;
import com.coohua.core.caf.dispense.ocpc.mapper.KuaishouUdsAdvertiserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-08-01
*/
@Service
@Slf4j
public class KuaishouUdsAdvertiserService extends ServiceImpl<Ku<PERSON>houUdsAdvertiserMapper, KuaishouUdsAdvertiser> {

    /**
     * 产品中文名 -> KuaishouUdsAdvertiser
     */
    private static ConcurrentMap<String, KuaishouUdsAdvertiser> kuaishouUdsAdvertiserMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        reload();
    }

    @Scheduled(cron = "10 */3 * * * ?")
    public void reload() {
        kuaishouUdsAdvertiserMap = list().stream().collect(Collectors.toConcurrentMap(k-> k.getProductName(), k-> k, (oldVal, newVal)-> oldVal));
        log.info("加载快手uds账户配置成功");
    }

    public ConcurrentMap<String, KuaishouUdsAdvertiser> getKuaishouUdsAdvertiserMap() {
        return kuaishouUdsAdvertiserMap;
    }

    public KuaishouUdsAdvertiser getKuaishouUdsAdvertiser(String productCn) {
        return kuaishouUdsAdvertiserMap.get(productCn);
    }

}
