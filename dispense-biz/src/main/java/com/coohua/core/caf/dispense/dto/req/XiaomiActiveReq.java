package com.coohua.core.caf.dispense.dto.req;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.XiaomiAction;
import com.coohua.core.caf.dispense.enums.XiaomiEventType;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 *
 * <AUTHOR>
 * @date 2022/4/26 14:45
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class XiaomiActiveReq {

    /** 传入参数值 */
    private String imeiMd5;
    private String oaid;
    private Long conv_time;

    /** 最终生成参数数据 */
    private transient String infoJson;
    private transient String reqUrl;

    private static final String CallbackUrl = "http://trail.e.mi.com/api/callback?";

    public XiaomiActiveReq() {
        this.conv_time = System.currentTimeMillis();
    }

    public void buildParams(XiaomiEventType eventType, XiaomiAction action, String callbackParam) throws UnsupportedEncodingException {
        infoJson = JSON.toJSONString(this);

        StringBuffer stringBuffer = new StringBuffer();

        stringBuffer.append("callback=").append(URLEncoder.encode(callbackParam, "UTF-8"));
        if (StringUtils.isNotBlank(imeiMd5)) {
            stringBuffer.append("&imei=").append(imeiMd5);
        } else if (StringUtils.isNotBlank(oaid)) {
            stringBuffer.append("&oaid=").append(oaid);
        }
        stringBuffer.append("&conv_time=").append(conv_time);
        stringBuffer.append("&convType=").append(eventType.code);

        String url_param = stringBuffer.toString();

//        String sign = MD5Utils.getMd5Sum(url_param + action.getSecretKey());

        reqUrl = new StringBuffer()
                .append(CallbackUrl)
                .append(url_param)
//                .append("&sign=").append(sign)
                .toString();
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        XiaomiAction action = new XiaomiAction();
        action.setSecretKey("oSLAkvLAAmeebUdi");

        XiaomiActiveReq req = new XiaomiActiveReq();
//        req.setImeiMd5("91b9185dba1772851dd02b276a6c969e");
        req.setOaid("12a7f9dec57adda4");
        req.setConv_time(1635335972760L);

        req.buildParams(XiaomiEventType.parse(Integer.valueOf(0)), action, "FqK3xF8WvgkYJGU4NDJkNjQwLWI2MzMtNDMxYS1hNTA0LTcyNDVhNWEyMDE2YlgQMTJhN2Y5ZGVjNTdhZGRhNBgFQ0xJQ0sA");

        System.out.println(req.getReqUrl());
    }


}
