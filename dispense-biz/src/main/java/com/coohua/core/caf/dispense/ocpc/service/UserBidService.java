package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.WeekendUpArpu;
import com.coohua.core.caf.dispense.dsp.service.RedisRelationshipService;
import com.coohua.core.caf.dispense.ocpc.entity.UserBid;
import com.coohua.core.caf.dispense.ocpc.mapper.UserBidMapper;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class UserBidService extends ServiceImpl<UserBidMapper, UserBid> {

    public static Map<String,List<WeekendUpArpu>> cacheProductEx = new HashMap<>();
    @Autowired
    private SafeDataRedisService safeDataRedisService;


    public Map queryUserCpaBy(String userId, String product) {
        return safeDataRedisService.getBidMap(userId, product);
    }
}
