package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广告title
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="TencentDeveloper对象", description="广告title")
public class TencentDeveloper implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "应用ID")
    private String clientId;

    @ApiModelProperty(value = "appId")
    @TableField()
    private String appId;

    @ApiModelProperty(value = "产品名称")
    private String appName;

    @ApiModelProperty(value = "广告主ID")
    private String advertiserId;

    @ApiModelProperty(value = "广告主账户")
    private String advertiserAccount;

    @ApiModelProperty(value = "广告主密码")
    private String advertiserPassword;

    @ApiModelProperty(value = "开发者账户")
    private String loginAccount;

    @ApiModelProperty(value = "开发者密码")
    private String loginPassword;

    @ApiModelProperty(value = "登录URL")
    private String loginUrl;

    @ApiModelProperty(value = "数据源ID")
    private String userActionSetId;

    @ApiModelProperty(value = "密钥")
    private String secret;

    @ApiModelProperty(value = "回调地址")
    private String callBackurl;

    @ApiModelProperty(value = "授权URL")
    private String authUrl;

    @ApiModelProperty(value = "公司主体")
    private String companyName;

    @ApiModelProperty(value = "access_token")
    private String accessToken;

    @ApiModelProperty(value = "refresh_token")
    private String refreshToken;

    @ApiModelProperty(value = "accesscode")
    private String accessCode;

    @ApiModelProperty(value = "刷新token返回")
    private String rspContent;

    @ApiModelProperty(value = "刷新时间")
    private Date refreshTime;

    @ApiModelProperty(value = "分组ID")
    private Integer groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private Integer createId;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Integer updateId;

    @ApiModelProperty(value = "更新人")
    private String updateName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "关键行为回传配置")
    private String eventTypes;

    @ApiModelProperty(value = "关键行为回传数值")
    private String eventValues;

    /**
     * 数据累积周期
     */
    private Integer maxAccumulatePeriod;

}
