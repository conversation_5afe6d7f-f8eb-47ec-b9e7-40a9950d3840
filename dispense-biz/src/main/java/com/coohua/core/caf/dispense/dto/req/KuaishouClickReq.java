package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/7/6
 **/
@Data
public class KuaishouClickReq {
    private String dsp;//头条
    private String product;//写死的
    private String os;//写死的
    private String account_id;//账户ID
    private String account;//账户ID

    private String aid;//广告组
    private String cid;//广告创意
    private String did;//广告计划
    private String  imeiMd5;//(设备ID md5 值)
    private String oaid;//oaid 原值
    private String oaid2;//oaid md5
    private String idfaMd5;//idfa md5 值（大写）
    private String ts;//点击时间
    private String callbackUrl;//回调地址
    /**
     * 对 MAC 去除分隔符之后进行 MD5
     */
    private String mac;

    private String androidId;

    /**
     * 媒体投放系统获取的用户终端的公共IP地址
     */
    private String ip;

    /**
     * 用户代理(User Agent)，一个特殊字符串头，使得服务器能够识别客户使用的操作系统及版本、CPU类型、浏览器及版本、浏览器渲染引擎、浏览器语言、浏览器插件等。
     */
    private String ua;

    /**
     * 手机型号
     */
    private String model;

    private String caid;

    private String aidName;//（达人用来存助推id）
    private String mid;//__PHOTOID__	iOS/Android	素材ID加密值

    private String csite; // 广告投放场景

    private String taskType; // 任务类型，2代表直播类型、1代表视频类型
    private String task_type; // 任务类型，2代表直播类型、1代表视频类型
}
