package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.OcpcPayReq;
import com.coohua.core.caf.dispense.dsp.mapper.OcpcPayReqMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-08-08
*/
@Service
@Slf4j
public class OcpcPayReqService extends ServiceImpl<OcpcPayReqMapper, OcpcPayReq> {
    public OcpcPayReq savePayReq(UserEventReq userEventReq){
        try {
            OcpcPayReq ocpcPayReq = new OcpcPayReq();
            ocpcPayReq.setUserId(userEventReq.getUserId());
            ocpcPayReq.setOs(userEventReq.getOs());
            ocpcPayReq.setProduct(userEventReq.getProduct());
            ocpcPayReq.setEventType(userEventReq.getEventType());
            ocpcPayReq.setPayAmount(userEventReq.getPayAmount());
            ocpcPayReq.setOpenId(userEventReq.getOpenId());
            ocpcPayReq.setIp(userEventReq.getIp());
            ocpcPayReq.setPayOrderNo(userEventReq.getPayOrderNo());

            if(StringUtils.isNotBlank(userEventReq.getPayOrderNo()) && userEventReq.getPayAmount()!=null){
                if(StringUtils.endsWith(userEventReq.getPayOrderNo(),"_od")){
                    //ios
                    int payAllAmount = DoubleUtil.divideDouble(userEventReq.getPayAmount()*1.0d,0.997d).intValue();
                    ocpcPayReq.setAllAmount(payAllAmount);
                }
            }else if(userEventReq.getPayAmount()!=null){
                int payAllAmount = DoubleUtil.divideDouble(userEventReq.getPayAmount()*1.0d,0.6d).intValue();
                ocpcPayReq.setAllAmount(payAllAmount);
            }
            ocpcPayReq.setCreateTime(new Date());
            ocpcPayReq.setUpdateTime(new Date());
            boolean isSav = save(ocpcPayReq);
            log.info("保存支付请求 "+ JSON.toJSONString(ocpcPayReq)+" 成功");
            return ocpcPayReq;
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }
}
