package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ocpc.entity.MatchClickBroad;
import com.coohua.core.caf.dispense.ocpc.entity.MatchCrowdBroadNew;
import com.coohua.core.caf.dispense.ocpc.mapper.MatchCrowdBroadNewMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class MatchCrowdBroadService extends ServiceImpl<MatchCrowdBroadNewMapper, MatchCrowdBroadNew> {
    @Resource(name = "matchCrowdBroadSave")
    ThreadPoolTaskExecutor poolTaskExecutor;

    public void saveMatchCrowdBroad(MatchCrowdBroadNew matchCrowdBroad) {
        poolTaskExecutor.execute(() -> {
            try {
                matchCrowdBroad.setCreateTime(new Date());
                matchCrowdBroad.setUpdateTime(new Date());
                //没有来源不进行插入
                if (StringUtils.isNotBlank(matchCrowdBroad.getSource())) {
                    save(matchCrowdBroad);
                }
            }catch (Exception e){
                log.error("异步写入tencent_ltv异常", e);
            }
        });
    }
}
