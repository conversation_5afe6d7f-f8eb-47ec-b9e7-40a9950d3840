package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 广告主
 * </p>
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value="BaiduAdvertiser对象", description="广告主")
public class BaiduAdvertiser implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "广告主ID（自动生成）")
    private Long advertiserId;

    @ApiModelProperty(value = "1百度信息流 2百度搜索")
    private Integer channel;

    @ApiModelProperty(value = "名称")
    private String advertiserName;

    @ApiModelProperty(value = "akey")
    private String akey;

    @ApiModelProperty(value = "产品表ID（运营选择）")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private Integer creatorId;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "更新人ID")
    private Integer editorId;

    @ApiModelProperty(value = "修改人")
    private String editorName;

    @ApiModelProperty(value = "修改人修改时间")
    private Date editorTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "行为类型集合")
    private String eventTypes;

    @ApiModelProperty(value = "行为数值集合")
    private String eventValues;

    @ApiModelProperty(value = "数据累积期限，单位为分钟")
    private Integer maxAccumulatePeriod;

    private Integer enable;

    private Integer ocpcType;

}
