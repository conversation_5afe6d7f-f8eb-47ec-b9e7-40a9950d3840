package com.coohua.core.caf.dispense.dsp.entity;

import com.coohua.core.caf.dispense.enums.DspType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/18 17:51
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class ThirdAdvertiser {

    private DspType dsp;
    private String eventTypes;
    private String eventValues;
    private String product;
    private String advertiserId;
    private Integer maxAccumulatePeriod;

    public ThirdAdvertiser(AuthToutiaoAdvertiser advertiser) {
        this.dsp = DspType.TOUTIAO;
        this.eventTypes = advertiser.getEventTypes();
        this.eventValues = advertiser.getEventValues();
        this.product = advertiser.getProductName();
        this.advertiserId = advertiser.getAdvertiserId().toString();
        this.maxAccumulatePeriod = advertiser.getMaxAccumulatePeriod();
    }

    public ThirdAdvertiser(AuthKuaishouApp advertiser) {
        this.dsp = DspType.KUAISHOU;
        this.eventTypes = advertiser.getEventTypes();
        this.eventValues = advertiser.getEventValues();
        this.product = advertiser.getAppName();
        this.advertiserId = advertiser.getAdvertiserId().toString();
        this.maxAccumulatePeriod = advertiser.getMaxAccumulatePeriod();
    }

    public ThirdAdvertiser(TencentDeveloper advertiser) {
        this.dsp = DspType.GUANGDIANTONG;
        this.eventTypes = advertiser.getEventTypes();
        this.eventValues = advertiser.getEventValues();
        this.product = advertiser.getAppName();
        this.advertiserId = advertiser.getAdvertiserId();
        this.maxAccumulatePeriod = advertiser.getMaxAccumulatePeriod();
    }

    public ThirdAdvertiser(BaiduAdvertiser advertiser) {
        this.dsp = Objects.equals(advertiser.getChannel(), 2) ? DspType.BAIDUSEM : DspType.BAIDUFEED;
        this.eventTypes = advertiser.getEventTypes();
        this.eventValues = advertiser.getEventValues();
        this.product = advertiser.getProductName();
        this.advertiserId = String.valueOf(advertiser.getAdvertiserId());
        this.maxAccumulatePeriod = advertiser.getMaxAccumulatePeriod();
    }

    public ThirdAdvertiser(SigmobAccount sigmobAccount) {
        this.dsp = DspType.SIGMOB;
        this.eventTypes = sigmobAccount.getEventTypes();
        this.eventValues = sigmobAccount.getEventValues();
        this.product = sigmobAccount.getAppName();
        this.advertiserId = sigmobAccount.getAdvertiserId().toString();
    }
}
