package com.coohua.core.caf.dispense.constant;

import lombok.Getter;
import org.apache.hadoop.hbase.util.Bytes;

/**
 * <AUTHOR>
 * @since 2021/5/27
 */
@Getter
public enum HBaseParameterEnum {

    /**
     * 唯一指定family
     */
    FAMILY("family", Bytes.toBytes("family")),

    /**
     * 默认column
     */
    DEFAULT_COLUMN("qualifier", Bytes.toBytes("qualifier")),

    ;

    /**
     * 编码名称
     */
    private String code;

    /**
     * 二进制内容
     */
    private byte[] binaryContent;

    HBaseParameterEnum(String code, byte[] binaryContent) {
        this.code = code;
        this.binaryContent = binaryContent;
    }
}
