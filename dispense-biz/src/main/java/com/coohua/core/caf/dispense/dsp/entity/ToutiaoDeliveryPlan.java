package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广告计划
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ToutiaoDeliveryPlan对象", description="广告计划")
public class ToutiaoDeliveryPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "头条版本")
    private Integer adType;

    @ApiModelProperty(value = "获取时间年月日")
    private Date crawlDate;

    @ApiModelProperty(value = "获取时间")
    private Date crawlTime;

    @ApiModelProperty(value = "计划ID")
    private Long adId;

    @ApiModelProperty(value = "计划名称")
    private String adName;

    @ApiModelProperty(value = "广告主id")
    private Long advertiserId;

    @ApiModelProperty(value = "广告组ID")
    private Long campaignId;

    @ApiModelProperty(value = "计划创建时间")
    private Date adCreateTime;

    @ApiModelProperty(value = "计划最近更新时间")
    private Date adUpdateTime;

    @ApiModelProperty(value = "广告计划投放状态")
    private String status;

    @ApiModelProperty(value = "广告计划操作状态")
    private String optStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
