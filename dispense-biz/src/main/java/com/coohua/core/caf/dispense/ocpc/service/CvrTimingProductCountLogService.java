package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.CvrTimingProduct;
import com.coohua.core.caf.dispense.ocpc.entity.CvrTimingProductCountLog;
import com.coohua.core.caf.dispense.ocpc.mapper.CvrTimingProductCountLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;
import java.util.Date;

@Service
@Slf4j
public class CvrTimingProductCountLogService extends ServiceImpl<CvrTimingProductCountLogMapper, CvrTimingProductCountLog> {

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Autowired
    OcpcSwitcher ocpcSwitcher;

    /**
     * 穿山甲
     * @return
     */
    public boolean limitCvrAdvertiserId(String product, String accountId) {
        if(!ocpcSwitcher.cvrTimingOpen) {
            return false;
        }
        String key = RedisKeyConstants.getCvrTimingValidProductAccount(product, accountId);
        String value = bpDispenseJedisClusterClient.get(key);
        if(StringUtils.isNotBlank(value)) {
            return "1".equals(value);
        }
        CvrTimingProductCountLog lastOne = lambdaQuery().eq(CvrTimingProductCountLog::getProduct, product).orderByDesc(CvrTimingProductCountLog::getId).last("limit 1").one();
        if(null == lastOne) {
            value = "0";
        }else {
            Date logTime = lastOne.getLogTime();
            String[] remarkArray = lastOne.getRemark().split(";");
            CvrTimingProduct cvrTimingProduct = JSONObject.parseObject(remarkArray[0], CvrTimingProduct.class);
            //超过检查时间了, 上一条已经失效
            if(System.currentTimeMillis() - logTime.getTime() > cvrTimingProduct.getIntervalMinute() * 60000L
                    //不包含该账户
                    || !remarkArray[1].contains(accountId)
                    //不需要限制cvr
                    || 2 != lastOne.getStatus()) {
                value = "0";
            }else {
                value = "1";
            }
        }
        //半个5min
        bpDispenseJedisClusterClient.setex(key, 150, value);
        return "1".equals(value);
    }
}
