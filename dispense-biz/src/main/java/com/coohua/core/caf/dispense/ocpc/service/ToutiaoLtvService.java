package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.ToutiaoLtv;
import com.coohua.core.caf.dispense.ocpc.mapper.ToutiaoLtvMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ToutiaoLtvService extends ServiceImpl<ToutiaoLtvMapper, ToutiaoLtv> {

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Resource(name = "ocpcToutiaoLtvPool")
    ThreadPoolTaskExecutor poolTaskExecutor;

    public ToutiaoLtv saveToutiaoLtv(UserEventReq userEventReq, OcpcEvent ocpcEvent, String reqUrl, String rspStr, BigDecimal userArpu) {
        try {
            ToutiaoLtv toutiaoLtv = new ToutiaoLtv();
            Date date = new Date();
            toutiaoLtv.setAppId(userEventReq.getAppId());
            toutiaoLtv.setCreateTime(date);
            toutiaoLtv.setUpdateTime(date);
            toutiaoLtv.setUserId(userEventReq.getUserId());
            toutiaoLtv.setProduct(userEventReq.getProduct());

            toutiaoLtv.setClickId(ocpcEvent.getId());
            toutiaoLtv.setAccountId(ocpcEvent.getAccountId());
            toutiaoLtv.setAccountName(ocpcEvent.getAccountName());
            toutiaoLtv.setDsp(ocpcEvent.getDsp());
            toutiaoLtv.setCallbackUrl(ocpcEvent.getCallbackUrl());

            toutiaoLtv.setOaid(userEventReq.getOaid());
            toutiaoLtv.setSourceOaid(userEventReq.getOaid());
            toutiaoLtv.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
            toutiaoLtv.setSourceDeviceId(userEventReq.getSourceDeviceId());
            toutiaoLtv.setOs(userEventReq.getOs());
            toutiaoLtv.setReqUrl(reqUrl);
            toutiaoLtv.setReqRsp(rspStr);

            toutiaoLtv.setArpu(userArpu.doubleValue());

            try {
                asyncSave(toutiaoLtv);
            } catch (Exception e) {
                log.error("", e);
            }

            return toutiaoLtv;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    private void asyncSave(ToutiaoLtv toutiaoLtv) {
        if (ocpcSwitcher.writeToutiaoLtvSwitch) {
            poolTaskExecutor.execute(() -> {
                try {
                    save(toutiaoLtv);
                }catch (Exception e){
                    log.error("异步写入toutiao_ltv成功异常", e);
                }
            });
        }
    }


}
