package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.entity.XimalayaAdvertiser;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.XimalayaEventType;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_URL_BODY;

@Component
@Slf4j
public class XimalayaUserEventService {
    @Autowired
    AlertService alertService;
    @Autowired
    XimalayaAdvertiserService ximalayaAdvertiserService;
    @Autowired
    RedisEventService redisEventService;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {
        Date date = new Date();
        try {
            XimalayaEventType ximalayaEventType = XimalayaEventType.parse(userEventReq.getEventType());
            if (ximalayaEventType == null) {
                userEvent.setReqRsp("ximalaya无需回传的类型");
                return false;
            }

            XimalayaAdvertiser ximalayaAdvertiser = ximalayaAdvertiserService.getByAccountId(toutiaoClick.getAccountId());
            if(ximalayaAdvertiser==null){
                log.error("ximalaya无对应账户配置 " + toutiaoClick.getAccountId());
                userEvent.setReqRsp("ximalaya无对应账户配置");
                return false;
            }

            String reqUrl = toutiaoClick.getCallbackUrl();
            if (ximalayaEventType != XimalayaEventType.ACTIVATE_APP) {
                reqUrl = reqUrl.replace("type=act", "type=" + ximalayaEventType.code);
            }

            String rspStr = sendXimalaya(ximalayaAdvertiser, reqUrl, toutiaoClick.getProduct());

            if (userEvent != null) {
                userEvent.setReqUrl(reqUrl);
                userEvent.setReqRsp(rspStr);
            }
        } catch (Exception e) {
            log.error("ximalaya userEvent unknown error. userEventReq: {}, toutiaoClick: {}", userEventReq, toutiaoClick, e);
            return false;
        }
        return false;
    }

    public String sendXimalaya(XimalayaAdvertiser ximalayaAdvertiser, String reqUrl, String product) {
        try {
            String accountId = ximalayaAdvertiser.getAdvertiserId();

            CloseableHttpClient httpClient = getHttpClient();
            HttpConfig config = HttpConfig.custom()
                    .url(reqUrl)
                    .client(httpClient);

            String rspStr = HttpClientUtil.get(config);
            log.info("ximalaya回调结果 {} {} ,res: {} ,reqUrl: {}", accountId, product, rspStr, reqUrl);

            JSONObject jobj = JSON.parseObject(rspStr);
            if ("SUCCESS".equals(jobj.getString("type"))) {
                alertService.report(product, accountId, "ximalaya回调成功");
            } else {
                alertService.report(product, accountId, "ximalaya回调失败");
            }
            return rspStr;
        } catch (Exception e) {
            log.error("ximalaya请求异常 ", e);
            return null;
        }
    }

    private static CloseableHttpClient getHttpClient() {
        CloseableHttpClient httpclient = HttpClients.custom().build();
        return httpclient;
    }

}
