package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.CheckClickRsp;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.UserMetaOsEnum;
import com.coohua.core.caf.dispense.dto.req.UserMetaParam;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class UserEventService {

    @Autowired
    private UserEventMapper userEventMapper;
    @Autowired
    private ToutiaoClickService toutiaoClickService;
    @Autowired
    TencentUserActionService tencentUserActionService;
    @Autowired
    KuaishouUserEventService kuaishouUserEventService;
    @Autowired
    SogouUserEventService sogouUserEventService;
    @Autowired
    BaiduUserEventService baiduUserEventService;
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    TianweiUserEventService tianweiUserEventService;
    @Autowired
    private OcpcEventService ocpcEventService;
    @Autowired
    private InnerUserEventService innerUserEventService;
    /**
     * 用户表pkgId和click表 product映射
     */
    @ApolloJsonValue("${productMap:{} }")
    private Map<String, String> productMap;

    @Value("${toutiao.event.enable:false }")
    private Boolean toutaioEnable;

    @Value("${tianwei.event.enable:false }")
    private Boolean tianweiEnable;


    @Autowired
    KuaishouClickService kuaishouClickService;

    /**
     * key pkgId
     * value appId
     */
    @ApolloJsonValue("${appIdProductMap:{}}")
    private Map<String, Integer> appIdProductMap;

    @ApolloJsonValue("${priority.toutiao.app:[]}")
    private Set<String> priorityToutiaoApp;

    @Value("${priority.dsp:toutiao}")
    private String priorityDsp;


    @Value("${replace.all.click.toutiao:false}")
    private Boolean openAllReplace;

    @ApolloJsonValue("${ replace.toutiao.app:[\"huankuaizou\"]}")
    private Set<String> replaceClickToutiaoApp;

    private UserEvent getOne(QueryWrapper<UserEvent> objectQueryWrapper){
        List<UserEvent> ocpcEventDbList = userEventMapper.selectList(objectQueryWrapper);
        if(ocpcEventDbList.size()>0){
            return ocpcEventDbList.get(0);
        }
        return null;
    }


    public void updEventNoClick() {
        QueryWrapper<UserEvent> objectQueryWrapper = new QueryWrapper<UserEvent>();

        //只调度2天以内的数据
        Date crdate = new Date(System.currentTimeMillis() - 20 * 60 * 1000l);
        objectQueryWrapper.lambda().gt(UserEvent::getLastRecallTime, crdate);
        objectQueryWrapper.lambda().orderByDesc(UserEvent::getCreateTime);
//        objectQueryWrapper.lambda().isNull(UserEvent::getClickId);
        long countNum = userEventMapper.selectCount(objectQueryWrapper);

        long pageNum = countNum / 1000l;
        for (int i = 1; i <= (pageNum + 1); i++) {
            try {
                IPage<UserEvent> page = new Page(i, 1000);
                IPage<UserEvent> userEventIPage = userEventMapper.selectPage(page, objectQueryWrapper);
                List<UserEvent> dlist = userEventIPage.getRecords();
                long ctime = System.currentTimeMillis();
                if (dlist.size() > 0) {
                    for (UserEvent userEvent : dlist) {
                        recallEvent(userEvent);
                    }
                }
                log.info("共跑【{}】条，耗时{}", dlist.size(), (System.currentTimeMillis() - ctime));
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    private void recallEvent(UserEvent userEvent) {
        try {
            UserEventReq userEventReq = new UserEventReq();
            userEventReq.setOcpcDeviceId(userEvent.getOcpcDeviceId());
            userEventReq.setOaid(userEvent.getOaid());
            userEventReq.setEventType(userEvent.getEventType());
            userEventReq.setProduct(userEvent.getProduct());
            userEventReq.setUserId(userEvent.getUserId());
            userEventReq.setOs(userEvent.getOs());
            List<ToutiaoClick> toutiaoClickList = toutiaoClickService.queryClick(userEventReq, true);

            if (toutiaoClickList != null && toutiaoClickList.size() > 0) {
                for (ToutiaoClick toutiaoClick : toutiaoClickList) {
                    DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
                    if (DspType.TOUTIAO.equals(dspType)) {
                        toutiaoCallService.touTiaoActive(userEventReq, toutiaoClick, userEvent);
                        UserEvent userEventRecord = new UserEvent();

                        userEventRecord.setClickId(toutiaoClick.getId());
                        userEventRecord.setAccountId(toutiaoClick.getAccountId());
                        userEventRecord.setAccountName(toutiaoClick.getAidName());
                        userEventRecord.setDsp(toutiaoClick.getDsp());

                        userEventRecord.setId(userEvent.getId());
                        userEventRecord.setUpdateTime(new Date());
                        userEventRecord.setReqUrl(userEvent.getReqUrl());
                        userEventRecord.setReqRsp(userEvent.getReqRsp());

                        Date date = new Date(System.currentTimeMillis() - 3 * 24 * 60 * 60 * 1000l);
                        userEventRecord.setLastRecallTime(date);
                        updateUserEvent(userEventRecord);

                        log.info("补偿 deviceId【{}】，调取头条{} 完成", userEvent.getOcpcDeviceId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }


    private void updateUserEvent(UserEvent userEvent) {
        try {
            userEventMapper.updateById(userEvent);
        } catch (Exception e) {
            log.error("", e);
        }
    }
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    RedisEventService redisEventService;
    private UserEvent saveUserEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq, String reqUrl, String rspStr) {
        try {
            UserEvent userEvent = new UserEvent();

            Date date = new Date();

            Integer appId = ProductCache.getAppId(toutiaoClick.getProduct());
            if (appId == null) {
                log.error("产品不存在{}", toutiaoClick.getProduct());
                appId = -1;
            }
            userEvent.setAppId(appId);
            userEvent.setCreateTime(date);
            userEvent.setUpdateTime(date);
            userEvent.setUserId(userEventReq.getUserId());
            userEvent.setEventType(userEventReq.getEventType());
            userEvent.setProduct(userEventReq.getProduct());
            if (toutiaoClick != null) {
                userEvent.setClickId(toutiaoClick.getId());
                userEvent.setAccountId(toutiaoClick.getAccountId());
                userEvent.setAccountName(toutiaoClick.getAidName());
                userEvent.setDsp(toutiaoClick.getDsp());
                setPlanIdAndCreativeId(toutiaoClick, userEvent);
                userEvent.setCallbackUrl(toutiaoClick.getCallbackUrl());
            } else {
                userEvent.setLastRecallTime(new Date());
            }
            userEvent.setOaid(userEventReq.getOaid());
            userEvent.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
            userEvent.setOs(userEventReq.getOs());
            userEvent.setReqUrl(reqUrl);
            userEvent.setReqRsp(rspStr);
            try {
                userEventMapper.insert(userEvent);
                // 双写ocpcUserEvent
                if(ocpcSwitcher.writeOcpcSwitch){
                    ocpcEventService.dspInsertEvent(toutiaoClick,userEvent);
                }

                ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEventReq.getEventType());
                if(ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)){
                    redisEventService.saveActiveUEventToRedis(userEvent);
                }
            } catch (Exception e) {
                log.error("", e);
            }
            return userEvent;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    public static void setPlanIdAndCreativeId(ToutiaoClick toutiaoClick, UserEvent userEvent) {
        try {
            if (DspType.KUAISHOU.name.equals(toutiaoClick.getDsp())) {
                //get ideaday
                userEvent.setPlanId(toutiaoClick.getGid());
                userEvent.setGroupId(toutiaoClick.getPid());
                userEvent.setCreativeId(toutiaoClick.getCid());
                userEvent.setMid(toutiaoClick.getMid());
            } else {
                userEvent.setPlanId(toutiaoClick.getPid());
                userEvent.setCreativeId(toutiaoClick.getCid());
                userEvent.setGroupId(toutiaoClick.getGid());
                userEvent.setMid(toutiaoClick.getMid());
            }
        } catch (Exception e) {
            log.error("setPlanIdAndCreativeId异常", e);
        }
    }

    /**
     * user_mata新增后检查是否 上报
     *
     * @param userMetaParam
     */
    public void checkAddUserMeta(UserMetaParam userMetaParam) {
        String product = productMap.get(userMetaParam.getPkgId());

        if (StringUtils.isEmpty(product)) {
            return;
        }
        if (StringUtils.isEmpty(userMetaParam.getDeviceId())) {
            return;
        }
        userMetaParam.setDeviceId(DigestUtils.md5Hex(userMetaParam.getDeviceId()));
        String osName = UserMetaOsEnum.IOS.value.equals(userMetaParam.getOs()) ? UserMetaOsEnum.IOS.name : UserMetaOsEnum.ANDROID.name;

        ToutiaoClick toutiaoClick = toutiaoClickService.queryClickByDeviceId(userMetaParam.getDeviceId(), osName, product);
        if (toutiaoClick == null) {
            return;
        }
        DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setUserId(userMetaParam.getUserId());
        userEventReq.setProduct(product);
        userEventReq.setEventType(0);
        userEventReq.setOcpcDeviceId(userMetaParam.getDeviceId());
        userEventReq.setOs(osName);
        Integer appId = appIdProductMap.get(userMetaParam.getPkgId());
        userEventReq.setAppId(Optional.ofNullable(appId).orElse(-1));
        if (DspType.TOUTIAO.equals(dspType) && toutaioEnable) {
            QueryWrapper<UserEvent> userEventQueryWrapper = new QueryWrapper<>();
            userEventQueryWrapper.lambda().eq(UserEvent::getProduct, product)
                    .eq(UserEvent::getOcpcDeviceId, userMetaParam.getDeviceId())
                    .eq(UserEvent::getClickId, toutiaoClick.getId())
                    .eq(UserEvent::getEventType, userEventReq.getEventType());
            Integer countUserEvent = userEventMapper.selectCount(userEventQueryWrapper);
            //已上报,无需上报
            if (countUserEvent > 0) {
                return;
            }
            //未上报，则继续上报
            UserEvent userEvent = saveUserEvent(toutiaoClick, userEventReq, "", "");
            toutiaoCallService.touTiaoActive(userEventReq, toutiaoClick, userEvent);
            updateUserEvent(userEvent);
        } else if (DspType.TIANWEI.equals(dspType) && tianweiEnable) {
            QueryWrapper<UserEvent> userEventQueryWrapper = new QueryWrapper<>();
            userEventQueryWrapper.lambda().eq(UserEvent::getProduct, product)
                    .eq(UserEvent::getOcpcDeviceId, userMetaParam.getDeviceId())
                    .eq(UserEvent::getClickId, toutiaoClick.getId())
                    .eq(UserEvent::getEventType, userEventReq.getEventType());
            Integer countUserEvent = userEventMapper.selectCount(userEventQueryWrapper);
            //已上报,无需上报
            if (countUserEvent > 0) {
                return;
            }
            //未上报，则继续上报
            tianweiUserEventService.activeEvent(userEventReq, toutiaoClick);
        }
    }

    public boolean tianweiUEvent(UserEventReq userEventReq) {
        ToutiaoClick toutiaoClick = null;
        boolean isActive = false;
        List<ToutiaoClick> toutiaoClickList = toutiaoClickService.queryClick(userEventReq, true);
        if (toutiaoClickList.size() > 0) {
            for (ToutiaoClick toutiaoClickDb : toutiaoClickList) {
                DspType dspType = DspType.getDspType(toutiaoClickDb.getDsp());
                if (DspType.TIANWEI.equals(dspType)) {
                    toutiaoClick = toutiaoClickList.get(0);
                    break;
                }
            }
        }
        if (toutiaoClick == null) {
            log.warn("无法找到click事件 {}", JSONObject.toJSONString(userEventReq));
            return false;
        }
        DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
        if (DspType.TIANWEI.equals(dspType)) {
            isActive = tianweiUserEventService.activeEvent(userEventReq, toutiaoClick);
        } else {
            log.info("无需上报天威 deviceId {}", userEventReq.getOcpcDeviceId());
        }
        return isActive;
    }

}
