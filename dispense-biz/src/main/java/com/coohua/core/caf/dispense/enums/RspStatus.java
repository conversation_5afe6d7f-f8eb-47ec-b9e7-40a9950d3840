package com.coohua.core.caf.dispense.enums;

public enum RspStatus {
	SUCCESS(200, "成功"),
	FAILED(100, "失败"),
	PARAMERROR(400, "参数错误"),
	;

	public Integer value;
	public String name;

	RspStatus(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public static RspStatus getStatus(Integer value) {
		if (value != null) {
			RspStatus[] otypes = RspStatus.values();
			for (RspStatus memberType : otypes) {
				if (value.equals(memberType.value)) {
					return memberType;
				}
			}
		}
		return null;
	}
}
