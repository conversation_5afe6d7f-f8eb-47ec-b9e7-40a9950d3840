package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="UserEvent对象", description="")
public class UserEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "应用ID")
    private Integer appId;

    @ApiModelProperty(value = "用户ID")
    @TableField("userId")
    private String userId;

    @ApiModelProperty(value = "原始deviceId")
    private String sourceDeviceId;

    @ApiModelProperty(value = "原始oaId")
    private String sourceOaid;

    private String product;

    private String os;

    private String ocpcDeviceId;

    private String oaid;

    @ApiModelProperty(value = "事件类型")
    private Integer eventType;

    @ApiModelProperty(value = "类型名称")
    private String eventTypeName;

    private Long toutiaoClickId;

    @ApiModelProperty(value = "clickId")
    private Long clickId;

    @ApiModelProperty(value = "accountId")
    private String accountId;

    @ApiModelProperty(value = "account名称")
    private String accountName;

    @ApiModelProperty(value = "创意ID")
    private String creativeId;

    @ApiModelProperty(value = "计划ID")
    private String planId;

    @ApiModelProperty(value = "GID")
    private String groupId;

    @ApiModelProperty(value = "消耗金额")
    private Double cost;

    @ApiModelProperty(value = "优惠金额")
    private Double rebeatCost;

    @ApiModelProperty(value = "激活数")
    private Integer activeNum;

    @ApiModelProperty(value = "dsp平台")
    private String dsp;

    private String macId;

    private String callbackUrl;

    private String reqUrl;

    private String reqRsp;

    @ApiModelProperty(value = "补偿回调时间")
    private Date lastRecallTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private String androidId;

    private String remark;

    @ApiModelProperty(value = "union_site")
    private String unionSite;

    @ApiModelProperty(value = "oaid加md5")
    private String oaid2;

    private String channel;

    @ApiModelProperty(value = "idfa加md5")
    private String idfa2;

    @ApiModelProperty(value = "用户终端的公共IP地址")
    private String ip;

    @ApiModelProperty(value = "用户代理(User Agent)")
    private String ua;

    @ApiModelProperty(value = "手机型号")
    private String model;

    private String openId;

    private String wAppId;

    private String caid;
    @ApiModelProperty(value = "素材信息")
    private String mid;
}
