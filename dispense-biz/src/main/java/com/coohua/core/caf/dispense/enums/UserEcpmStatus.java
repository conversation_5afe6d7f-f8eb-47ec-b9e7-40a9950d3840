package com.coohua.core.caf.dispense.enums;

public enum UserEcpmStatus {

    INIT("未回传", 0),
    WAIT("等待回传", 1),
    DEAL("已回传", 2),
    ;

    private String desc;
    private int value;

    UserEcpmStatus(String desc, int value) {
        this.desc = desc;
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public int getValue() {
        return value;
    }
}
