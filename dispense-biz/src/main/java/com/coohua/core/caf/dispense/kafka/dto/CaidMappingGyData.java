package com.coohua.core.caf.dispense.kafka.dto;

import lombok.Data;

@Data
public class CaidMappingGyData {
    private String os;
    private String product;
    private String userId;
    private String sourceDeviceId;
    private String caid;
    private String idfa;
    private String androidId;
    private String oaid;
    private String oaid2;
    private String accountId;
    private String accountName;
    private String dsp;
    private String mappingSource;
    private String dataObtainTime;
}
