package com.coohua.core.caf.dispense.ocpc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.coohua.core.caf.dispense.ocpc.entity.UserExposureEcpmNew;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface UserExposureEcpmNewMapper extends BaseMapper<UserExposureEcpmNew> {

    @Insert("<script> INSERT INTO `user_exposure_ecpm_new` (`id`,`data_date`, `user_id`, `product`, `os`, `device_id`, `oaid`, `caid`, `imei`, `pv`, `ecpm`, `create_time`, `update_time`) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.id}, #{item.dataDate}, #{item.userId}, #{item.product}, #{item.os}, #{item.deviceId}, #{item.oaid}, #{item.caid}, #{item.imei}, #{item.pv}, #{item.ecpm}, now(), now())" +
            "</foreach>" +
            "on duplicate key update pv = pv + values(pv), ecpm = ecpm + values(ecpm), device_id=values(device_id), oaid=values(oaid), caid=values(caid), imei=values(imei), update_time = now()" +
            "</script>")
    void saveOrUpdateOnDuplicateKey(@Param("list") List<UserExposureEcpmNew> list);

    @Update("<script>" +
            "<foreach collection='list' item='item' separator=';'>" +
            "update user_exposure_ecpm_new set pv = pv + #{item.pv}, ecpm = ecpm + #{item.ecpm}, update_time = now() where id = #{item.id}" +
            "</foreach>" +
            "</script>")
    void batchAddPvEcpm(@Param("list") List<UserExposureEcpmNew> list);

    @Select("<script>select group_concat(id) id, sum(pv) pv, sum(if(data_date >= #{today}, ecpm, ecpm / ${rate})) ecpm, max(update_time) update_time, user_id  from `user_exposure_ecpm_new` " +
            "where data_date >= #{yesterday} and product = #{product} and status = 0 and user_id in (" +
            "<foreach collection='userIds' item='item' separator=','>" +
            "#{item}"+
            "</foreach>" +
            ") group by user_id having pv >= #{pv} and ecpm >= #{ecpm}" +
            "</script>")
    List<UserExposureEcpmNew> query2Send(@Param("rate") Double rate, @Param("product") String product, @Param("pv") Integer pv, @Param("ecpm") Double ecpm, @Param("userIds") Set<Long> userIds, @Param("today") Date today, @Param("yesterday") Date yesterday);
}
