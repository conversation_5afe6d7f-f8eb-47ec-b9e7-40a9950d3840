package com.coohua.core.caf.dispense.ocpc.mapper;

import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-29
 */
public interface OcpcEventMapper extends BaseMapper<OcpcEvent> {

    @Select({
            "select id, userId, product, os, dsp from ocpc_event where id > #{startId}",
            "and event_type = 0 order by id limit #{limit}"
    })
    List<OcpcEvent> selectActivateEventByIdLimit(@Param("startId") Long startId, @Param("limit") int limit);

}
