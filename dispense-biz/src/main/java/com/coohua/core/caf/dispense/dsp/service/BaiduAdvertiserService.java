package com.coohua.core.caf.dispense.dsp.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.BaiduExtApolloConfig;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.mapper.BaiduAdvertiserMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.utils.PairUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.constant.BaseConstants.NUM_ARPU_RULE;
import static com.coohua.core.caf.dispense.constant.BaseConstants.NUM_ECPM_RULE;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getEcpmLevel10;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getFixedUserEventReq;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.*;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.analyzeNewValueToMap;

/**
 * <p>
 * 广告主 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BaiduAdvertiserService extends ServiceImpl<BaiduAdvertiserMapper, BaiduAdvertiser> implements IService<BaiduAdvertiser> {

    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    BaiduExtApolloConfig extApolloConfig;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    ThirdExtEventService thirdExtEventService;
    @Autowired
    HbaseEventService hbaseEventService;

    // <advertiserId, BaiduAdvertiser>
    private Map<String, BaiduAdvertiser> baiduAdevertiserMap = Maps.newHashMap();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();

    // 关键行为模型
    private Set<String> actionAdvertiserSet = new HashSet<>();

    //    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void initAccountActionConfig() {
        List<BaiduAdvertiser> list = lambdaQuery()
                .isNotNull(BaiduAdvertiser::getAdvertiserId)
                .eq(BaiduAdvertiser::getDelFlag, DELFLAG.weishanchu.value)
                .list();

        baiduAdevertiserMap = list.stream().collect(Collectors.toMap(k-> k.getAdvertiserId().toString(), k->k, (oldVal, newVal)-> oldVal));

        appMap = list.stream()
                .filter(a -> a.getProductName() != null)
                .collect(Collectors.groupingBy(BaiduAdvertiser::getProductName,
                        Collectors.collectingAndThen(Collectors.toList(), apps -> apps.stream()
                                .flatMap(this::convertAction)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()))));

        actionAdvertiserSet = list.stream().filter(f -> Objects.equals(f.getOcpcType(), 1))
                .map(BaiduAdvertiser::getAdvertiserId).map(String::valueOf).collect(Collectors.toSet());
    }

    private Stream<AccountAction> convertAction(BaiduAdvertiser baiduAdvertiser) {
        try {
            // 无配置
            if (StringUtils.isBlank(baiduAdvertiser.getEventTypes())) {
                return null;
            }

            if (StringUtils.isBlank(baiduAdvertiser.getEventValues()) || StringUtils.isBlank(baiduAdvertiser.getEventTypes())) {
                return Stream.empty();
            }

            return AuthKuaishouAdvertiserService.buildEventStandardConsideringMultiEcpm(baiduAdvertiser.getEventTypes(),
                    baiduAdvertiser.getEventValues(), new BigInteger(baiduAdvertiser.getAdvertiserId().toString()),
                    baiduAdvertiser.getMaxAccumulatePeriod());

        }catch (Exception e){
            log.error("百度广告主配置信息解析出错 ",e);
        }

        return null;
    }

    public BaiduAdvertiser getByAccountId(String advertiserId){
        return baiduAdevertiserMap.get(advertiserId);
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        return appMap;
    }

    public List<AccountAction> queryActionConfig(String appName) {
        return appMap.getOrDefault(appName, Lists.newArrayList());
    }

    public boolean actionAdvertiser(String advertiserId) {
        return actionAdvertiserSet.contains(advertiserId);
    }

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();

            String advertiserId = toutiaoClick.getAccountId();
            BaiduAdvertiser baiduAdvertiser = getByAccountId(advertiserId);

            // 若无配置或未配置行为参数 仅当默认值时上报
            if (baiduAdvertiser == null || StringUtils.isBlank(baiduAdvertiser.getEventTypes()) || StringUtils.isBlank(baiduAdvertiser.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, baiduAdvertiser.getEventTypes(),
                        baiduAdvertiser.getEventValues(), firstTimeJudge, dspType.value, baiduAdvertiser.getProductName(),
                        advertiserId, baiduAdvertiser.getMaxAccumulatePeriod());
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, baiduAdvertiser.getEventTypes(),
                        baiduAdvertiser.getEventValues(), firstTimeJudge, dspType.value, baiduAdvertiser.getProductName(),
                        advertiserId, baiduAdvertiser.getMaxAccumulatePeriod());
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }

    public void tryCallbackExtDefine(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        BaiduAdvertiser advertiser = getBaiduAdvertiserForExtEvent(toutiaoClick, userEventReq);

        if (advertiser == null) {
            return;
        }

        ThirdAdvertiser thirdAdvertiser = new ThirdAdvertiser(advertiser);
        DspType dspType = thirdAdvertiser.getDsp();

        boolean locked = false;
        int eventTypeKey = ToutiaoEventTypeEnum.KEY_ACTION_EXT_EVENT.value;
        String lockKey = RedisKeyConstants.getReportLockKey(userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
        try {
            if (extApolloConfig.lockExtDefineAllEnable || extApolloConfig.lockExtDefineEnableProducts.contains(userEventReq.getProduct())) {
                // 要使用分布式锁保证回传顺序执行
                locked = ocpcEventService.tryGetDistributedLock(lockKey, "1", 10000);

                if (!locked) {
                    log.warn("百度衍生上报并发抢占资格失败 {} {} {}", userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
                    return;
                }

                if (NUM_ARPU_RULE.equals(advertiser.getEventTypes())) {
                    // 获取符合的关键行为衍生事件
                    List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, userEventReq);
                    if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                        OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                        List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_2(dspType, matchExtEvents, ocpcEvent ,
                                ()-> checkExtDefineCount(toutiaoClick, userEventReq, advertiser));
                        ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                    }
                } else if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())) {
                    // ecpm需要先判断是否符合账户的配置条件
                    Pair<Boolean, ActionTypes> actionTypesPair = checkExtDefineCount(toutiaoClick, userEventReq, advertiser);
                    if (actionTypesPair.getLeft()) {
                        ActionTypes ecpmLevel10 = getEcpmLevel10(advertiser.getEventValues());
                        UserEventReq fixedUserEventReq = getFixedUserEventReq(userEventReq, ecpmLevel10);
                        // 获取符合的关键行为衍生事件
                        List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, fixedUserEventReq);
                        if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                            OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                            List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_3(dspType, ecpmLevel10, matchExtEvents, ocpcEvent);
                            ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("衍生事件分布式锁抢占出现异常 ", e);
        } finally {
            if (locked) {
                ocpcEventService.unlockDistributedLock(lockKey);
            }
        }
    }


    private BaiduAdvertiser getBaiduAdvertiserForExtEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return null;
            }

            if (!DspType.isBaidu(toutiaoClick.getDsp())) {
                return null;
            }

            // 限制产品
            if (!extApolloConfig.extEnableAll && !extApolloConfig.extEnableProducts.contains(userEventReq.getProduct())) {
                return null;
            }

            BaiduAdvertiser advertiser = getByAccountId(toutiaoClick.getAccountId());

            //若无配置 不上报
            if (advertiser == null || StringUtils.isAnyBlank(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // 分时arpu账户 不上报
            if (isMultiArpu(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // 次数ecpm账户，若没有10这一档位时 不上报
            if (checkEcpmHasNotLevel10(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            Map<String, String> reportValues = analyzeNewValueToMap(userEventReq.getActionValues());
            String reqEventTypes = reportValues.keySet().stream().sorted(Comparator.comparing(Integer::valueOf)).collect(Collectors.joining(","));
            // 若请求的类型和当前账户的类型不一致，不上报
            if (!Objects.equals(reqEventTypes, advertiser.getEventTypes())) {
                return null;
            }

            // 限制适用类型
            if (!extApolloConfig.extEnableEventTypes.contains(advertiser.getEventTypes())) {
                return null;
            }

            // 限制适用账户
            if (!extApolloConfig.extEnableAllAccount && !extApolloConfig.extEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                return null;
            }

            // 对于1,3的账户限制产品
            if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())
                    && !extApolloConfig.extEnableAllFor_1_3
                    && !extApolloConfig.extEnableProductsFor_1_3.contains(userEventReq.getProduct())) {
                return null;
            }

            return advertiser;

        } catch (Exception e) {
            log.error("getBaiduAdvertiserForExtEvent error ", e);
        }
        return null;
    }
    /**
     * 检查衍生事件计数
     * @return
     */
    public Pair<Boolean, ActionTypes> checkExtDefineCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq, BaiduAdvertiser advertiser) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return PairUtil.ofFalse();
            }

            //非百度不回传衍生定义
//            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            if (!DspType.isBaidu(toutiaoClick.getDsp())) {
                return PairUtil.ofFalse();
            }

            //若无配置 仅当默认值时上报
            if (advertiser == null) {
                return PairUtil.ofFalse();
            }

            // 限制适用类型
            if (!extApolloConfig.extEnableEventTypes.contains(advertiser.getEventTypes())) {
                return PairUtil.ofFalse();
            }

            // 限制适用账户
            if (!extApolloConfig.extEnableAllAccount && !extApolloConfig.extEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                return PairUtil.ofFalse();
            }

            // 不需判断是否首次满足条件
            BooleanSupplier firstTimeJudge = () -> true;

            Pair<Boolean, ActionTypes> matchNewRuleWithResult = matchNewRuleWithResult(userEventReq, advertiser.getEventTypes(),
                    advertiser.getEventValues(), firstTimeJudge, "百度",
                    advertiser.getProductName(), String.valueOf(advertiser.getAdvertiserId()), advertiser.getMaxAccumulatePeriod());
            if (matchNewRuleWithResult.getLeft()) {
                return matchNewRuleWithResult;
            }

        } catch (Exception e) {
            log.error("checkExtEventCount error ", e);
        }
        return PairUtil.ofFalse();
    }

}
