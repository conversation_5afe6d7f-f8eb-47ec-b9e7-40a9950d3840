package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

@Service
@Slf4j
public class ApRedisService {
    @Autowired
    @Qualifier("ap-clusterJedisClusterClient")
    private JedisClusterClient apClusterJedisClusterClient;


    public void saveKsDrData(Integer appId, String os, Long userId, String value) {
        String key = buildUserDsp(appId, os, userId);
        log.info("快手达人数据存储到redis key {} value {}", key, value);
        apClusterJedisClusterClient.setex(key,3*24*60*60, value);
    }

    private   String buildUserDsp(Integer appId, String os, Long userId) {
        return String.format("user:dsp:%s:%s:%s", appId, os, userId);
    }


}
