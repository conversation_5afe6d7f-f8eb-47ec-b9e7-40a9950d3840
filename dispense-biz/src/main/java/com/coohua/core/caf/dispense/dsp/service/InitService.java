package com.coohua.core.caf.dispense.dsp.service;

import com.coohua.core.caf.dispense.cache.InnerPkgToProductCache;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.AccountAction;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.hbase.HbaseUserActiveService;
import com.coohua.core.caf.dispense.ocpc.service.TablesService;
import com.coohua.core.caf.dispense.utils.DistinctByKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/6/10
 **/
@Service
@Slf4j
public class InitService {

    @Autowired
    AlertService alertService;
    @Autowired
    AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    HbaseEventService hbaseEventService;

    @Autowired
    InnerPkgToProductCache innerPkgToProductCache;
    @Autowired
    ProductCache productCache;
    @Autowired
    TablesService tablesService;
    @Autowired
    TencentDeveloperService tencentDeveloperService;
    @Autowired
    BaiduAdvertiserService baiduAdvertiserService;
    @Autowired
    ThirdExtEventService thirdExtEventService;
    @Autowired
    VivoAdvertiserService vivoAdvertiserService;
    @Autowired
    SigmobAccountService sigmobAccountService;
    @Autowired
    OppoAdvertiserService oppoAdvertiserService;
    @Autowired
    IqiyiAdvertiserService iqiyiAdvertiserService;
    @Autowired
    XiaomiAdvertiserService xiaomiAdvertiserService;
    @Autowired
    XimalayaAdvertiserService ximalayaAdvertiserService;
    @Autowired
    HbaseUserActiveService hbaseUserActiveService;

    @PostConstruct
    public void init() {

        long initStart = System.currentTimeMillis();

        long start = System.currentTimeMillis();
        alertService.init();
        log.info("初始化耗时 alertService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        authKuaishouAdvertiserService.initAccountActionConfig();
        log.info("初始化耗时 authKuaishouAdvertiserService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        authToutiaoAdvertiserService.initAccountActionConfig();
        log.info("初始化耗时 authToutiaoAdvertiserService: {} ms", System.currentTimeMillis() - start);


        start = System.currentTimeMillis();
        hbaseClickService.createOcpcTable();
        log.info("初始化耗时 hbaseClickService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        hbaseEventService.createOcpcTable();
        hbaseEventService.createUserArpuTable();
        hbaseEventService.createTempUserArpuTable();
        log.info("初始化耗时 hbaseEventService: {} ms", System.currentTimeMillis() - start);

        System.out.println("-------------");

        start = System.currentTimeMillis();
        innerPkgToProductCache.initPkgToProductMap();
        log.info("初始化耗时 innerPkgToProductCache: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        productCache.initProductNameCache();
        log.info("初始化耗时 productCache: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        tablesService.initCache();
        log.info("初始化耗时 tablesService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        tencentDeveloperService.initAccountActionConfig();
        log.info("初始化耗时 tencentDeveloperService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        baiduAdvertiserService.initAccountActionConfig();
        log.info("初始化耗时 baiduAdvertiserService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        iqiyiAdvertiserService.initAccountActionConfig();
        log.info("初始化耗时 iqiyiAdvertiserService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        thirdExtEventService.reloadConfigs();
        log.info("初始化耗时 toutiaoExtEventService: {} ms", System.currentTimeMillis() - start);

        start = System.currentTimeMillis();
        sigmobAccountService.initAccountActionConfig();
        log.info("初始化耗时 sigmobAccountService: {} ms", System.currentTimeMillis() - start);

        log.info("初始化耗时 total: {} ms", System.currentTimeMillis() - initStart);
        System.out.println("-------------");

        vivoAdvertiserService.startInitVivoAdvertiser();

        oppoAdvertiserService.startInitOppoAdvertiser();

        xiaomiAdvertiserService.reloadConfig();

        ximalayaAdvertiserService.reloadConfig();

        hbaseUserActiveService.initUserAciveHtable();

        // 所有行为配置放到缓存，必须在本初始化方法的最后执行，因为其中部分数据依赖于之前的初始化
        refreshAccountAction();

    }

    private Map<String, List<AccountAction>> AllAccountActions = new HashMap<>();

    public List<AccountAction> getAccountActions(String appName) {
        return AllAccountActions.getOrDefault(appName, new ArrayList<>());
    }

    @Scheduled(cron = "0 1/3 * * * ? ")
    public void refreshAccountAction() {
        log.info("重新加载所有行为配置");
        Map<String, List<AccountAction>> accountActions = authToutiaoAdvertiserService.queryActionConfig();
        Map<String, List<AccountAction>> kuaishouActions = authKuaishouAdvertiserService.queryActionConfig();
        Map<String, List<AccountAction>> tencentActions = tencentDeveloperService.queryActionConfig();
        Map<String, List<AccountAction>> baiduActions = baiduAdvertiserService.queryActionConfig();
        Map<String, List<AccountAction>> iqiyiActions = iqiyiAdvertiserService.queryActionConfig();
        Map<String, List<AccountAction>> oppoActions = oppoAdvertiserService.queryActionConfig();
        Map<String, List<AccountAction>> vivoActions = vivoAdvertiserService.queryActionConfig();
        Map<String, List<AccountAction>> sigmobActions = sigmobAccountService.queryActionConfig();

        // 衍生事件相关配置
        Map<String, List<AccountAction>> extEventActions = thirdExtEventService.queryActionConfig();

        AllAccountActions = mergeActions(accountActions, kuaishouActions, tencentActions,
                baiduActions, iqiyiActions, oppoActions
                , vivoActions, extEventActions, sigmobActions
        );
    }

    private Map<String, List<AccountAction>> mergeActions(Map<String, List<AccountAction>>... maps) {
        Map<String, List<AccountAction>> allActions = new HashMap<>();

        // 合并
        for (Map<String, List<AccountAction>> map : maps) {
            map.forEach((key, val)-> allActions.merge(key, val, (oldVal, newVal)-> ListUtils.union(oldVal, newVal)));
        }

        // 相同配置去重
        for (Map.Entry<String, List<AccountAction>> entry : allActions.entrySet()) {
            allActions.put(entry.getKey(), entry.getValue().stream()
                    .filter(DistinctByKey.apply(k-> AccountAction.getDistinctKey(k))).collect(Collectors.toList()));
        }

        return allActions;
    }


}
