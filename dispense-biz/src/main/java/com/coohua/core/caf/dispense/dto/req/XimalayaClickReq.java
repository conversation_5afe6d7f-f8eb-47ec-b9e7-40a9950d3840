package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

@Data
public class XimalayaClickReq {

    /**
     * https://api.e.mi.com/doc.html#/1.0.0-mdtag9b26f-omd/document-2bd1c4c260259b072818205a8ae20139
     *
     * 监测链接示例
     * ios :
     * https://ocpc-api.shinet.cn/dispense/ximalaya/click?dsp=ximalaya&product=yydxny&accountId=111&os=_OS_&ip=_IP_&timestamp=_TS_&taskId=_TASK_ID_&planId=_PLAN_ID_&materialId=_MATERIAL_ID_&callback_url=_CALLBACK_URL_&model=_MODEL_&width=_WIDTH_&height=_HEIGHT_&wh=_WH_&task_name=_TASK_NAME_&plan_name=_PLAN_NAME_&material_name=_MATERIAL_NAME_&deviceId=_IDFA_&ua=_UA_
     * android :
     * https://ocpc-api.shinet.cn/dispense/ximalaya/click?dsp=ximalaya&product=yydxny&accountId=111&os=_OS_&ip=_IP_&timestamp=_TS_&taskId=_TASK_ID_&planId=_PLAN_ID_&materialId=_MATERIAL_ID_&callback_url=_CALLBACK_URL_&model=_MODEL_&width=_WIDTH_&height=_HEIGHT_&wh=_WH_&task_name=_TASK_NAME_&plan_name=_PLAN_NAME_&material_name=_MATERIAL_NAME_&deviceId=_IMEI_MD5_&oaid=_OAID_&androidid=_ANDROIDID_
     */

    private String dsp;
    private String product;
    private String os;
    /** 账户id */
    private String accountId;
    /** 广告点击发生时间 */
    private String timestamp;
    /** 平台提供的广告组ID */
    private String taskId;
    /** 平台提供的计划ID */
    private String planId;
    /** 平台提供的创意ID */
    private String materialId;
    private String callback_url;

    private String model;
    private String width;
    private String height;
    private String wh;
    private String task_name;
    private String plan_name;
    private String material_name;
    private String ip;
    private String ua;

    private String deviceId;
    private String oaid;
    private String androidid;

    public void formatNull() {
        deviceId = formatNull(deviceId);
        oaid = formatNull(oaid);
        androidid = formatNull(androidid);
        taskId = formatNull(taskId);
        planId = formatNull(planId);
        materialId = formatNull(materialId);
    }

    private static String formatNull(String input) {
        if (input != null && input.startsWith("_") && input.endsWith("_")) {
            return null;
        }
        return input;
    }

}
