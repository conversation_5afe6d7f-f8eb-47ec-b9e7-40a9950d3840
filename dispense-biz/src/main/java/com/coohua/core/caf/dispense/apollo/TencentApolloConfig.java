package com.coohua.core.caf.dispense.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 关键行为衍生事件相关apollo配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class TencentApolloConfig {


    /**
     * 腾讯衍生回传 是否对全部产品生效
     */
    @Value("${ocpc.tencent.ext.enable.all:false}")
    public boolean extEnableAll;

    /**
     * 腾讯衍生回传 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.tencent.ext.enable.products:[]}")
    public Set<String> extEnableProducts;

    /**
     * 全部使用分布式锁保证 衍生回传 不重复
     */
    @Value("${lock.tencent.ext.all.enable:true}")
    public boolean lockExtDefineAllEnable;

    /**
     * 启用分布式锁保证 衍生回传 不重复的产品打点名称集合
     */
    @ApolloJsonValue("${lock.tencent.ext.enable.products:[]}")
    public Set<String> lockExtDefineEnableProducts;

    /**
     * 衍生回传 适用的账户 eventTypes范围
     */
    @ApolloJsonValue("${ocpc.tencent.ext.enable.eventTypes:[\"1,2\",\"1,3\"]}")
    public Set<String> extEnableEventTypes;

    /**
     * 衍生回传 是否对全部账户生效
     */
    @Value("${ocpc.tencent.ext.enable.account.all:false}")
    public boolean extEnableAllAccount;

    /**
     * 衍生回传 生效的账户id列表
     */
    @ApolloJsonValue("${ocpc.tencent.ext.accounts:[]}")
    public Set<String> extEnableAccounts;

    /**
     * 衍生回传 (1,3类型) 是否对全部产品生效
     */
    @Value("${ocpc.tencent.ext.1_3.enable.all:true}")
    public boolean extEnableAllFor_1_3;

    /**
     * 衍生回传 (1,3类型) 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.tencent.ext.1_3.enable.products:[]}")
    public Set<String> extEnableProductsFor_1_3;

    /**
     * 广点通用户每次看广告时，回传广告变现价值，是否全部账户生效
     */
    @Value("${ocpc.tencent.callback.ltv.enable.all.account:false}")
    public boolean callbackLtvEnableAllAccount;

    /**
     * 广点通用户每次看广告时，回传广告变现价值，生效的账户列表
     */
    @ApolloJsonValue("${ocpc.tencent.callback.ltv.enable.accounts:[]}")
    public Set<String> callbackLtvEnableAccounts;

    public boolean illegalAccount(String accountId) {
        return !callbackLtvEnableAllAccount && !callbackLtvEnableAccounts.contains(accountId);
    }

    /**
     * 广点通用户每次看广告时，回传广告变现价值，是否全部产品生效
     */
    @Value("${ocpc.tencent.callback.ltv.enable.all.product:false}")
    public boolean callbackLtvEnableAllProduct;

    /**
     * 广点通用户每次看广告时，回传广告变现价值，生效的产品列表
     */
    @ApolloJsonValue("${ocpc.tencent.callback.ltv.enable.products:[]}")
    public Set<String> callbackLtvEnableProducts;

    public boolean illegalProduct(String product) {
        return !callbackLtvEnableAllProduct && !callbackLtvEnableProducts.contains(product);
    }

}
