package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ocpc.entity.ToutiaoSdkGyRetry;
import com.coohua.core.caf.dispense.ocpc.mapper.ToutiaoSdkGyRetryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
public class ToutiaoSdkGyRetryService extends ServiceImpl<ToutiaoSdkGyRetryMapper, ToutiaoSdkGyRetry> {

    public void saveToutiaoSdkGyRetry(ToutiaoSdkGyRetry toutiaoSdkGyRetry) {
        this.save(toutiaoSdkGyRetry);
    }

    public List<ToutiaoSdkGyRetry> queryRetrySdkGy() {
        QueryWrapper<ToutiaoSdkGyRetry> queryWrapper = new QueryWrapper<>();
        LocalDateTime now = LocalDateTime.now().minusMinutes(15);
        queryWrapper.gt("create_time", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        queryWrapper.lt("retry_count", 2);
        queryWrapper.eq("del_flag", 0);
        return list(queryWrapper);
    }
}
