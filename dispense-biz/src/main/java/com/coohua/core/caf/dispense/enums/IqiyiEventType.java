package com.coohua.core.caf.dispense.enums;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum IqiyiEventType {
    Active(0, 0, "active", "激活"),
    Retention(6, 3, "Retention", "次留"),
    KeyEvent(25, 1, "register", "关键行为回传到注册"),
    // -999 表示是额外回传的，不来源于项目组上报的用户行为事件
    Pay(-999, 2, "pay", "首次付费"),

    ;
    public Integer val;
    public Integer iqiyiVal;
    public String type;
    public String comment;


    public static IqiyiEventType parse(int val) {
        for (IqiyiEventType item : IqiyiEventType.values()) {
            if (item.val.equals(val)) {
                return item;
            }
        }
        return null;
    }

}
