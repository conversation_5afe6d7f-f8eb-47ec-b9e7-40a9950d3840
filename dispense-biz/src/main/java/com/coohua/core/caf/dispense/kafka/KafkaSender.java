package com.coohua.core.caf.dispense.kafka;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.component.ThreadPollComponent;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.pepper.metrics.integration.custom.Profile;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;


@Slf4j
@Component
public class KafkaSender implements InitializingBean {
    private KafkaProducer<String, String> producer;

    private AtomicLong counter = new AtomicLong(0);

    private String topic = "toufang_click";
    private String caidTopic = "caid_mapping_topic";
    private String caidGyDataTopic = "caid_mapping_gy_data";
    static ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("kafka_to_toutiaoclick",null,false);

    ExecutorService toutiaoReuserExecutor = new ThreadPoolExecutor(
            5
            , 30,
            60,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2000),
            threadFactory,
            new ThreadPollComponent.RejectedLogAbortPolicy("toKafkaClick"));

    @Profile
    public void sendClick(ToutiaoClick toutiaoClick){
        String jsonString = JSON.toJSONString(toutiaoClick);
        toutiaoReuserExecutor.submit(()->{
            try {
                ProducerRecord<String, String>  kafkaMessage = new ProducerRecord<>(topic, jsonString);
                producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
            }catch (Exception e){
                log.error("",e);
            }
        });
    }

    public void sendCaidMapping(String caidMappingStr){
        toutiaoReuserExecutor.submit(()->{
            try {
                ProducerRecord<String, String>  kafkaMessage = new ProducerRecord<>(caidTopic, caidMappingStr);
                producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
            }catch (Exception e){
                log.error("",e);
            }
        });
    }

    public void sendCaidMappingGyData(String data) {
        toutiaoReuserExecutor.submit(()->{
            try {
                ProducerRecord<String, String>  kafkaMessage = new ProducerRecord<>(caidGyDataTopic, data);
                producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
            }catch (Exception e){
                log.error("",e);
            }
        });
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (SystemInfo.isWin()) {
            log.info("win启动时不开启kafkaSender");
            return;
        }
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-zvp2j2vhf007-1-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-2-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-zvp2j2vhf007-3-vpc.alikafka.aliyuncs.com:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
//        props.put(ProducerConfig.ACKS_CONFIG, "1");
        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        this.producer = new KafkaProducer<>(props);

        new Thread(new Runnable() {
            @Override
            public void run() {
                while (true) {
                    try {
                        TimeUnit.SECONDS.sleep(LogKafkaSender.sendPd);
                        long sec = counter.getAndSet(0);
                        log.info("MSG.SEC={}", sec);
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
            }
        }).start();
    }
}
