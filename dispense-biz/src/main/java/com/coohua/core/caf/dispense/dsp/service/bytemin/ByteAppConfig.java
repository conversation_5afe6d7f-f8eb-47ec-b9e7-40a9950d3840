package com.coohua.core.caf.dispense.dsp.service.bytemin;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
@Slf4j
public class ByteAppConfig {
    //appid：
    //tt2a325c8fb50193a802
    //appsecret：
    //900c3cc0384b98bd2f4c25cf81255ea6ce468c68
    public static String yyaccAppId = "tt2a325c8fb50193a802";
    public static String yyaccAppsecret = "900c3cc0384b98bd2f4c25cf81255ea6ce468c68";

    @ApolloJsonValue("${ocpc.bytemin.appidSec:[]}")
    public Set<String> appidSec;

    public Pair<String,String> getAppPair(String productName){
        Pair<String,String> dpair = new Pair<>(yyaccAppId,yyaccAppsecret);

        log.info("设置bytemin配置为 "+yyaccAppId+" "+ JSON.toJSONString(dpair));
        for(String aidStr : appidSec){
            String[] aidstrs = aidStr.split("-");
            if(aidstrs.length==3){
                if(StringUtils.equalsIgnoreCase(aidstrs[0],productName)){
                    dpair = new Pair<>(aidstrs[1],aidstrs[2]);
                    log.info("设置byteminappid成功 "+yyaccAppId+" appId:"+aidstrs[1]+" secret:"+aidstrs[2]);
                }
            }
        }

        return dpair;
    }
}
