package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.KuaishouEventType;
import com.coohua.core.caf.dispense.enums.OcpcSourceType;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk DateTime: 2020/12/3 12:00
 */
@Service
@Slf4j
public class InnerUserEventService {
    @Autowired
    private UserEventMapper userEventMapper;
    @Autowired
    private OcpcEventService ocpcEventService;
    @Autowired
    private AlertService alertService;
    @Autowired
    private DspUserEventService dspEventService;

	/**
	 * 自家视频转化
	 * @param userEventReq
	 * @param toutiaoClick
	 * @return
	 */
	public boolean activeVideoEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick) {

        try {
            saveUserEvent(toutiaoClick, userEventReq);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "自家视频转化成功");
	        log.info("自家视频转化成功 [old={}] -> [new={}] [deviceid={}] [userId={}] ",toutiaoClick.getAccountName(),toutiaoClick.getProduct(),userEventReq.getUserId());
        } catch (Exception e) {
            log.error("InnerUserEventService activeVideoEvent. userEventReq: {}, toutiaoClick: {}", userEventReq,
                toutiaoClick, e);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "自家视频转化异常");
            return false;
        }
        return false;
    }

	/**
	 * 内部引流
	 * @param userEventReq
	 * @param toutiaoClick
	 * @return
	 */
    public boolean activeDrainageEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick) {
        try {
            saveUserEvent(toutiaoClick, userEventReq);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "内部引流转化成功");
	        log.info("内部引流转化成功 [old={}] -> [new={}] [deviceid={}] [userId={}] ",toutiaoClick.getAccountName(),toutiaoClick.getProduct(),userEventReq.getUserId());

        } catch (Exception e) {
            log.error("InnerUserEventService activeVideoEvent. userEventReq: {}, toutiaoClick: {}", userEventReq,
                toutiaoClick, e);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "内部引流转化异常");
            return false;
        }
        return false;
    }

	/**
	 * 内拉新转化
	 * @param userEventReq
	 * @param toutiaoClick
	 * @return
	 */
	public boolean activeOldPullEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick) {
        try {
            saveUserEvent(toutiaoClick, userEventReq);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "内拉新转化成功");
	        log.info("内存拉新转化成功 [old={}] -> [new={}] [deviceid={}] [userId={}] ",toutiaoClick.getAccountName(),toutiaoClick.getProduct(),userEventReq.getUserId());
        } catch (Exception e) {
            log.error("InnerUserEventService activeVideoEvent. userEventReq: {}, toutiaoClick: {}", userEventReq,
                toutiaoClick, e);
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "内拉新转化异常");
            return false;
        }
        return false;
    }

    /**
     * 存储到db
     * 
     * @param toutiaoClick
     * @param userEventReq
     */
    private void saveUserEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        UserEvent userEvent = new UserEvent();

        Date date = new Date();
        userEvent.setAppId(userEventReq.getAppId());
        userEvent.setCreateTime(date);
        userEvent.setUpdateTime(date);
        userEvent.setUserId(userEventReq.getUserId());
        userEvent.setEventType(userEventReq.getEventType());
        userEvent.setProduct(userEventReq.getProduct());
        if (toutiaoClick != null) {
            userEvent.setClickId(toutiaoClick.getId());
            userEvent.setAccountId(toutiaoClick.getAccountId());
            userEvent.setAccountName(toutiaoClick.getAccountName());
            userEvent.setDsp(toutiaoClick.getDsp());
        }
        userEvent.setOaid(userEventReq.getOaid());
        userEvent.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
        userEvent.setOs(userEventReq.getOs());
        if (StringUtils.isNotBlank(userEventReq.getSourceOaid())){
            userEvent.setSourceOaid(userEventReq.getSourceOaid());
        }
        if (StringUtils.isNotBlank(userEventReq.getSourceDeviceId())){
            userEvent.setSourceDeviceId(userEventReq.getSourceDeviceId());
        }
        UserEventService.setPlanIdAndCreativeId(toutiaoClick, userEvent);

        if (OcpcSourceType.OCPC.equals(userEventReq.getSouceType())) {
            OcpcEvent ocpcEvent = new OcpcEvent();
            BeanUtils.copyProperties(userEvent, ocpcEvent);
            ocpcEventService.save(ocpcEvent);
            dspEventService.ocpcInsertEvent(toutiaoClick, userEvent);
            log.info("ocpc内部引流上报成功-{}", ocpcEvent.getId());
            return;
        }
        userEventMapper.insert(userEvent);
        ocpcEventService.dspInsertEvent(toutiaoClick, userEvent);
    }
}
