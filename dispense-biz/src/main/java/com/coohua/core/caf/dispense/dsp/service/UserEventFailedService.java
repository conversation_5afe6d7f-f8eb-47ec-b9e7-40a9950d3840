package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coohua.caf.core.kv.DistributedLock;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.entity.UserEventFailed;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventFailedMapper;
import com.coohua.core.caf.dispense.enums.CompensateStatusEnum;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import top.zrbcool.pepper.boot.httpclient.HttpBioClient;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class UserEventFailedService {

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Autowired
    private UserEventFailedMapper userEventFailedMapper;
    @Autowired
    private HttpBioClient httpBioClient;

    @Autowired
    AlertService alertService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    public void runUserEventError(){
        DistributedLock.Lock lock = DistributedLock.tryLock(bpDispenseJedisClusterClient, RedisKeyConstants.EVENT_COMPENSATE_LOCK, 60 * DateTimeConstants.MILLIS_PER_SECOND);
        if (!lock.success()) {
            return;
        }
        QueryWrapper<UserEventFailed> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.lambda().eq(UserEventFailed::getRecallStatus, CompensateStatusEnum.FAILED.value);
        objectQueryWrapper.lambda().isNotNull(UserEventFailed::getReqUrl);
        Date date = new Date(System.currentTimeMillis()-2*DateTimeConstants.MILLIS_PER_DAY);
        objectQueryWrapper.lambda().gt(UserEventFailed::getCreateTime,date);
        List<UserEventFailed> userEventFaileds = userEventFailedMapper.selectList(objectQueryWrapper);
        int successCount = 0;
        if (!CollectionUtils.isEmpty(userEventFaileds)){
            //补偿
            for (UserEventFailed userEventFailed : userEventFaileds){
                try{
                    UserEventFailed userEventUpdate = new UserEventFailed();
                    userEventUpdate.setId(userEventFailed.getId());
                    String requestId = userEventFailed.getUserId() + userEventFailed.getProduct() + userEventFailed.getEventType() + System.currentTimeMillis();
                    HttpResponse response = httpBioClient.execute(requestId, new HttpGet(userEventFailed.getReqUrl()), 3000, TimeUnit.MILLISECONDS);
                    userEventUpdate.setReqRsp(EntityUtils.toString(response.getEntity(), "UTF-8"));
                    userEventUpdate.setLastRecallTime(new Date());
                    if (response.getStatusLine().getStatusCode() == 200) {
                        userEventUpdate.setRecallStatus(CompensateStatusEnum.SUCCESS.value);
                        successCount++;
                        log.info("补偿失败回调成功 {} requestId={},reqUrl={}", userEventFailed.getAccountId(),requestId,userEventFailed.getReqUrl());
                        alertService.report(userEventFailed.getProduct(), userEventFailed.getAccountId(), "补偿失败回调成功");
                    } else {
                        log.warn("补偿失败 {} requestId={},reqUrl={}",userEventFailed.getAccountId(),requestId,userEventFailed.getReqUrl());
                    }
                    //修改回调状态
                    int i = userEventFailedMapper.updateById(userEventUpdate);
                    if (i < 0){
                        log.warn("头条回调成功,修改补偿状态失败 failedId={}",userEventFailed.getId());
                    }
                }catch (Exception e){
                    log.warn("补偿失败failedId= {} {}",userEventFailed.getId(),e);
                }
            }
            log.info("补偿{}条,成功{}条",userEventFaileds.size(),successCount);
        }
    }


    public void rerunGdt(){
        //String html = HttpClientUtil.post(HttpConfig.custom().timeout(3000).url(reqUrl).json(jsonStr));
        QueryWrapper<UserEventFailed> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.lambda().eq(UserEventFailed::getRecallStatus, CompensateStatusEnum.FAILED.value);
        objectQueryWrapper.lambda().isNotNull(UserEventFailed::getReqUrl);
        Date date = new Date(System.currentTimeMillis()-1*DateTimeConstants.MILLIS_PER_DAY);
        objectQueryWrapper.lambda().gt(UserEventFailed::getCreateTime,date);
        objectQueryWrapper.lambda().eq(UserEventFailed::getDsp,"guangdiantong");
        List<UserEventFailed> userEventFaileds = userEventFailedMapper.selectList(objectQueryWrapper);
        if (!CollectionUtils.isEmpty(userEventFaileds)){
            //补偿
            for (UserEventFailed userEventFailed : userEventFaileds){
                try{
                    UserEventFailed userEventUpdate = new UserEventFailed();
                    userEventUpdate.setId(userEventFailed.getId());
                    String reqUrl = userEventFailed.getReqUrl().split(";")[0].trim();
                    String reqJson = userEventFailed.getReqUrl().split(";")[1];
                    String html = HttpClientUtil.post(HttpConfig.custom().timeout(3000).url(reqUrl).json(reqJson));

                    JSONObject jobj =  JSON.parseObject(html);
                    userEventUpdate.setRecallStatus(CompensateStatusEnum.SUCCESS.value);
                    userEventUpdate.setReqRsp(html);
                    if (jobj != null && Objects.equals(jobj.getInteger("code"), 0)) {
                        log.info("腾讯回调成功V2 {} {} {}" , userEventUpdate.getProduct(), userEventUpdate.getReqUrl() ,html);
                    }else{
                        log.info("腾讯回调失败V2 {} {} {} , req {} reqBody {}" , userEventUpdate.getProduct(), userEventUpdate.getReqUrl() ,html);
                    }

                    //修改回调状态
                    int i = userEventFailedMapper.updateById(userEventUpdate);
                    if (i < 0){
                        log.warn("头条回调成功,修改补偿状态失败 failedId={}",userEventFailed.getId());
                    }
                }catch (Exception e){
                    log.warn("补偿失败failedId={}",userEventFailed.getId(),e);
                }
            }
        }
    }
    public void insertUserEventFailed(UserEvent userEvent, ToutiaoClick toutiaoClick){
        if (toutiaoClick == null || StringUtils.isBlank(toutiaoClick.getCallbackUrl())){
            return;
        }
        //若回调头条失败
        if (StringUtils.isBlank(userEvent.getReqRsp()) || userEvent.getReqRsp().startsWith("请求失败")){
            try {
                Date date = new Date();
                UserEventFailed userEventFailed = new UserEventFailed();
                userEventFailed.setAppId(userEvent.getAppId());
                userEventFailed.setCreateTime(date);
                userEventFailed.setUpdateTime(date);
                userEventFailed.setUserId(userEvent.getUserId());
                userEventFailed.setEventType(userEvent.getEventType());
                userEventFailed.setProduct(userEvent.getProduct());
                userEventFailed.setClickId(toutiaoClick.getId());
                userEventFailed.setAccountId(toutiaoClick.getAccountId());
                userEventFailed.setAccountName(toutiaoClick.getAidName());
                userEventFailed.setDsp(toutiaoClick.getDsp());
                userEventFailed.setOaid(userEvent.getOaid());
                userEventFailed.setOcpcDeviceId(userEvent.getOcpcDeviceId());
                userEventFailed.setOs(userEvent.getOs());
                String reqUrl = toutiaoClick.getCallbackUrl() + "&event_type=" + userEvent.getEventType();
                userEventFailed.setReqUrl(ObjectUtils.defaultIfNull(userEvent.getReqUrl(), reqUrl));
                userEventFailed.setReqRsp("");
                userEventFailed.setUserEventId(userEvent.getId());

                int i = userEventFailedMapper.insert(userEventFailed);
                if (i < 0){
                    log.error("记录头条回调异常入库失败");
                }
            } catch (Exception e) {
                log.error("记录头条回调异常失败", e);
            }
        }
    }
}
