package com.coohua.core.caf.dispense.dsp.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.dsp.entity.AccountAction;
import com.coohua.core.caf.dispense.dsp.entity.OppoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.mapper.OppoAdvertiserMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.matchNewRule;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-11-12
*/
@Service
public class OppoAdvertiserService extends ServiceImpl<OppoAdvertiserMapper, OppoAdvertiser> {
    public static Map<String,OppoAdvertiser> dmap = new HashMap<>();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();

    @Scheduled(cron = "0 1/3 * * * ?")
    public void startInitOppoAdvertiser(){
        try {
            LambdaQueryWrapper<OppoAdvertiser> objectQueryWrapper = new QueryWrapper<OppoAdvertiser>().lambda();
            objectQueryWrapper.eq(OppoAdvertiser::getDelFlag,0);
            List<OppoAdvertiser> vivoAdvertiserList = list(objectQueryWrapper);
            dmap = vivoAdvertiserList.stream().collect(Collectors.toMap(OppoAdvertiser::getAdvertiserId, Function.identity()));

            appMap = vivoAdvertiserList.stream()
                    .filter(a -> a.getProductName() != null && ProductCache.productEn2CnMap.get(a.getProductName()) != null)
                    .collect(Collectors.groupingBy(a-> ProductCache.productEn2CnMap.get(a.getProductName()),
                            Collectors.collectingAndThen(Collectors.toList(), apps -> apps.stream()
                                    .flatMap(this::convertAction)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList()))));
        }catch (Exception e){
            log.error("oppo加载配置异常",e);
        }
    }

    public OppoAdvertiser getByAdId(String adId){
        OppoAdvertiser oppoAdvertiser = dmap.get(adId);
        return oppoAdvertiser;
    }

    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            String  adId = toutiaoClick.getAccountId();
            OppoAdvertiser oppoAdvertiser = getByAdId(adId);
            if(oppoAdvertiser==null){
                log.error("oppo无配置，请配置 "+adId+" ");
                return false;
            }
            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();


            // 若无配置或未配置行为参数 仅当默认值时上报
            if (oppoAdvertiser == null || StringUtils.isBlank(oppoAdvertiser.getEventTypes()) || StringUtils.isBlank(oppoAdvertiser.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, oppoAdvertiser.getEventTypes(),
                        oppoAdvertiser.getEventValues(), firstTimeJudge, "OPPO", oppoAdvertiser.getProductName(),
                        oppoAdvertiser.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, oppoAdvertiser.getEventTypes(),
                        oppoAdvertiser.getEventValues(), firstTimeJudge, "OPPO", oppoAdvertiser.getProductName(),
                        oppoAdvertiser.getAdvertiserId(), DateTimeConstants.MINUTES_PER_DAY);
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }

    private Stream<AccountAction> convertAction(OppoAdvertiser advertiser) {
        try {
            // 无配置
            if (StringUtils.isBlank(advertiser.getEventTypes())) {
                return null;
            }

            if (StringUtils.isBlank(advertiser.getEventValues()) || StringUtils.isBlank(advertiser.getEventTypes())) {
                return Stream.empty();
            }

            return AuthKuaishouAdvertiserService.buildEventStandardConsideringMultiEcpm(advertiser.getEventTypes(),
                    advertiser.getEventValues(), new BigInteger(advertiser.getAdvertiserId()),
                    null);

        }catch (Exception e){
            log.error("vivo广告主配置信息解析出错 ",e);
        }

        return null;
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        return appMap;
    }
}
