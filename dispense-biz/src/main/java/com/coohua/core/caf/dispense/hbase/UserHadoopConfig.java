package com.coohua.core.caf.dispense.hbase;

import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Configuration
public class UserHadoopConfig {

    @Bean(name = "userHadoopConnection")
    public Connection init() throws IOException {
        // 新建一个Configuration
        org.apache.hadoop.conf.Configuration conf = HBaseConfiguration.create();
        conf.set("hbase.zookeeper.quorum", "ld-2ze1vdu9684b75dkc-proxy-lindorm.lindorm.rds.aliyuncs.com:30020");
        conf.set("hbase.client.username", "root");
        conf.set("hbase.client.password", "root");
        return ConnectionFactory.createConnection(conf);
    }

}
