package com.coohua.core.caf.dispense.dsp.service.ksmin;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.caf.core.kv.DistributedLock;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthKuaishouApp;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.AuthKuaishouAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.dto.req.ByteActiveReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.KuaishouEventType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.ByteEcpmService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import com.coohua.core.caf.dispense.utils.LongUtil;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class KsMinService {
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    RedisClickService redisClickService;

    //头条 微信小程序激活
    public void sendKsActive(KsMinReq ksMinReq){
        try {
            //url：https://clue.oceanengine.com/outer/wechat/applet/token/1743915176074243
            //token：5EC571E03469980C8ADDC3ACC65CC711
            String reqUrl = "https://ad.partner.gifshow.com/track/activate";
            String callBackUrl = ksMinReq.getCallBackUrl();
            String openId = ksMinReq.getOpenId();
            String product = ksMinReq.getProduct();
            String userId = ksMinReq.getUserId();


            reqUrl = reqUrl+"?callback="+callBackUrl+"&event_type="+ KuaishouEventType.ACTIVATE_APP.name+"&event_time="+System.currentTimeMillis();
            boolean isActive = redisClickService.setOpenidActive(ksMinReq.getProduct(),ksMinReq.getOpenId(),"kmin");
            if(isActive){
                log.info("快手激活 "+product+" "+openId+" "+userId+" "+reqUrl);
                String html = HttpClientUtil.get(HttpConfig.custom().timeout(3000).url(reqUrl));
                //clueToken 存储在callBack中
                ToutiaoClick toutiaoClick = getTclick(ksMinReq);
                UserEventReq userEventReq = getUevent(ksMinReq,KuaishouEventType.ACTIVATE_APP.value);
                UserEvent userEvent = ocpcEventService.saveUserEvent(toutiaoClick, userEventReq, reqUrl, html);

                log.info("快手请求激活 "+reqUrl+" 响应为 "+html);
            }else{
                log.info("快手已经激活 直接忽略 "+JSON.toJSONString(ksMinReq));
            }
        } catch (Exception e) {
            log.error("头条回传异常", e);
        }
    }
    @Autowired
    KsEcpmService ksEcpmService;
    @Autowired
    RedisEventService redisEventService;

    @Value("${ksmin.ks.itest:false}")
    public boolean ksminIsT;
    public boolean sendKxEvent(KsMinReq ksMinReq){

        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setUserId(ksMinReq.getUserId());
        userEventReq.setOs(ksMinReq.getOs());
        userEventReq.setProduct(ksMinReq.getProduct());
        userEventReq.setOpenId(ksMinReq.getOpenId());
        OcpcEvent ocpcActiveEvent = redisEventService.getActiveEvent(userEventReq);
        if(ocpcActiveEvent!=null && StringUtils.isNotBlank(ocpcActiveEvent.getAccountId())){
            try {
                String reqUrl = "https://ad.partner.gifshow.com/track/activate";
                String hourStr = DateUtil.format(new Date(), "yyyy-MM-dd");

                Pair<Long,Long>  adPair = ksEcpmService.getEcmp(ksMinReq.getUserId(),ksMinReq.getProduct(),ksMinReq.getOpenId(),ksMinReq.getAppId(),ocpcActiveEvent.getAccountId()+"",hourStr);
                String yesDayStr = DateUtil.format(new Date(System.currentTimeMillis()- DateTimeConstants.MILLIS_PER_DAY), "yyyy-MM-dd");
                Pair<Long,Long>  adPairYes = ksEcpmService.getEcmp(ksMinReq.getUserId(),ksMinReq.getProduct(),ksMinReq.getOpenId(),ksMinReq.getAppId(),ocpcActiveEvent.getAccountId()+"",yesDayStr);

                Double arpu = DoubleUtil.divideDouble((LongUtil.addLong(adPair.getValue(), adPairYes.getValue()))*1.0d,1000d);
                Pair<Long,Double>  adPairAl = new  Pair<Long,Double>(LongUtil.addLong(adPair.getKey(),adPairYes.getKey()),arpu);
                log.info("ks小游戏  广告次数： "+adPairAl.getKey()+" arpu为: "+adPairAl.getValue());
                boolean isManju = isManzuArpu(ksMinReq,ocpcActiveEvent.getAccountId(),adPairAl);
                if((isManju || ksminIsT)){
                    boolean isEvt = redisClickService.setOpenidActive(ksMinReq.getProduct(),ksMinReq.getOpenId(),"kminevt2");
                    if(isEvt){
                        reqUrl = reqUrl+"?callback="+ocpcActiveEvent.getCallbackUrl()+"&event_type="+ KuaishouEventType.REGISTER.name+"&event_time="+System.currentTimeMillis();
                        ToutiaoClick toutiaoClick = redisEventService.convertEventToClick(ocpcActiveEvent);
                        toutiaoClick.setClickId(ocpcActiveEvent.getCallbackUrl());
                        String rsp = HttpClientUtil.get(HttpConfig.custom().timeout(3000).url(reqUrl));
                        Integer rstatus = JSON.parseObject(rsp).getInteger("status");
                        log.info("ks小游戏开始请求关键行为 "+reqUrl+" 响应为 "+rsp);
                        UserEventReq userEventReq2 = getUevent(ksMinReq, ToutiaoEventTypeEnum.KEY_EVENT.value);
                        userEventReq2.setClickId(ocpcActiveEvent.getCallbackUrl());
                        userEventReq2.setActionValues(adPair.getKey()+"-"+adPair.getValue());
                        UserEvent userEvent2 = ocpcEventService.saveUserEvent(toutiaoClick, userEventReq2, reqUrl, rsp);
                        log.info("ks用户 "+ksMinReq.getUserId()+" 回传关键行为成功");
                    }else{
                        log.info("ks用户 "+ksMinReq.getUserId()+" 已经回传关键行为 过滤");
                    }
                }

                return true;
            } catch (Exception e) {
                log.error("头条回传异常", e);
            }
        }else{
            log.info("ksmin 无激活归因 "+ksMinReq.getUserId()+" "+ksMinReq.getProduct()+" "+ksMinReq.getOpenId());
        }
        return false;
    }
    @Autowired
    AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    public boolean isManzuArpu(KsMinReq ksMinReq, String accountId, Pair<Long,Double> adPair){

        AuthKuaishouApp authKuaishouApp = authKuaishouAdvertiserService.getByAccountId(accountId);

        if(authKuaishouApp!=null){
            if(BaseConstants.NUM_ARPU_RULE.equalsIgnoreCase(authKuaishouApp.getEventTypes())){
                //一期只支持 次数+arpu
                String evals = authKuaishouApp.getEventValues();
                if(StringUtils.isNotBlank(evals)){
                    Long cishu = Long.parseLong(evals.split(",")[0]);
                    Double arpu = Double.parseDouble(evals.split(",")[1]);
                    //实时计算当前用户的arpu ecpm
                    if(adPair.getKey()>=cishu && adPair.getValue()>=arpu){
                        log.info("符合ksmin回传条件 "+ksMinReq.getUserId()+"  次数为 "+JSON.toJSONString(adPair));
                        return true;
                    }else{
                        log.info("不符合ksmin回传条件 "+ksMinReq.getUserId()+"  次数为 "+JSON.toJSONString(adPair));
                    }
                }else{
                    log.warn("ksmin账户配置错误 为空 "+evals+" "+accountId);
                }
            }else {
                log.warn("ksmin账户配置错误 只支持 "+BaseConstants.NUM_ARPU_RULE+" "+accountId);
            }
        }else{
            log.warn("ksmin账户为空 "+accountId);
        }
        return false;
    }
    private ToutiaoClick getTclick(KsMinReq ksMinReq){
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setDsp(DspType.KSMIN.value);
        toutiaoClick.setOpenId(ksMinReq.getOpenId());
        toutiaoClick.setProduct(ksMinReq.getProduct());
//        toutiaoClick.setPid(ksMinReq.getAd_id());
//        toutiaoClick.setCid(ksMinReq.getCreative_id());
        toutiaoClick.setAccountId(ksMinReq.getAccountId());
        toutiaoClick.setOs(ksMinReq.getOs());
        toutiaoClick.setCallbackUrl(ksMinReq.getCallBackUrl());
        return toutiaoClick;
    }

    private UserEventReq getUevent(KsMinReq ksMinReq,Integer evetInt){
        UserEventReq userEventReq = new UserEventReq();
        userEventReq.setProduct(ksMinReq.getProduct());
        userEventReq.setOs(ksMinReq.getOs());
        userEventReq.setEventType(evetInt);
        userEventReq.setUserId(ksMinReq.getUserId());
        userEventReq.setOpenId(ksMinReq.getOpenId());
//        userEventReq.setAppId(ksMinReq.getAppId());
        return userEventReq;
    }
    @Autowired
    ByteEcpmService byteEcpmService;
    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Scheduled(cron = "0 0/10 * * * ?")
    public void bushuD(){
        DistributedLock.Lock lock = DistributedLock.tryLock(bpDispenseJedisClusterClient, RedisKeyConstants.KS_BURUN_LOCK, 10 * DateTimeConstants.MILLIS_PER_MINUTE);
        if (!lock.success()) {
            return;
        }
        //select count(1) from ocpc_event where create_time>'2023-12-25' and create_time<'2023-12-26' and dsp='ksmin' limit 100;
        Date date = DateUtils.getDayBeginDate(new Date());

        List<OcpcEvent> dlist = ocpcEventService.lambdaQuery().select(OcpcEvent::getProduct,OcpcEvent::getUserId,OcpcEvent::getOpenId,OcpcEvent::getDsp).eq(OcpcEvent::getDsp,"ksmin")
                .gt(OcpcEvent::getCreateTime,date)
                .groupBy(OcpcEvent::getOpenId,OcpcEvent::getUserId,OcpcEvent::getProduct).list();
        log.info("ksmin开始全局拉数 "+dlist.size());
        for(OcpcEvent byteEcpm : dlist){
            try {
                KsMinReq ksMinReq = new KsMinReq();
                ksMinReq.setProduct(byteEcpm.getProduct());
                ksMinReq.setOpenId(byteEcpm.getOpenId());
                ksMinReq.setOs(byteEcpm.getDsp());
                ksMinReq.setUserId(byteEcpm.getUserId());
                sendKxEvent(ksMinReq);
            }catch (Exception e){
                log.error("",e);
            }
        }
    }

}
