package com.coohua.core.caf.dispense.utils;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dto.req.DeviceReq;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

@Slf4j
public class EventDataCheck {
    private static final Set<String> DEFAULT_MAC = ImmutableSet.of("02:00:00:00:00:00", "000-000-000-111", "ee46730a4f36102bca50fca37079f3a5",ConstCls.emptyMd5,
            DigestUtils.md5Hex("02:00:00:00:00:00"),"1111-0000-1111-0000",DigestUtils.md5Hex("1111-0000-1111-0000"));
    public static void replaceZto(UserEventReq userEventReq) {
        if(userEventReq.getOs() == null){
            log.warn(" osisnull "+ JSON.toJSONString(userEventReq));
            userEventReq.setOs("android");//默认android
        }
        if("ios".equals(userEventReq.getOs())){
            if(StringUtils.isNotBlank(userEventReq.getOcpcDeviceId())){
                if(userEventReq.getOcpcDeviceId().length()<5){
//                    log.info(userEventReq.getProduct()+" deviceId小于5 "+ JSON.toJSONString(userEventReq));
                    printActInfo(userEventReq);
                }else if(userEventReq.getOcpcDeviceId().contains("0000-0000-0000-0000")){
//                    log.info(userEventReq.getProduct()+" deviceId包含0000 "+ JSON.toJSONString(userEventReq));
                    printActInfo(userEventReq);
                }else if(userEventReq.getOcpcDeviceId().contains("000000-0000-0000-000000") || userEventReq.getOcpcDeviceId().contains("00000000-0000-0000-0000-000000000000")){
//                    log.info(userEventReq.getProduct()+" iosdeviceId包含0000 "+ JSON.toJSONString(userEventReq));
                    userEventReq.setSourceDeviceId(null);
                    userEventReq.setOcpcDeviceId(null);
                    printActInfo(userEventReq);
                }
            }else{
                log.info(userEventReq.getProduct()+" deviceId为空 "+ JSON.toJSONString(userEventReq));
            }
        }
        if (StringUtils.isNotBlank(userEventReq.getOaid()) && (userEventReq.getOaid().contains("0000-0000-0000-0000")|| userEventReq.getOaid().contains("0000") || userEventReq.getOaid().contains("null"))) {
            userEventReq.setOaid(null);
        }

        if (StringUtils.isNotBlank(userEventReq.getOcpcDeviceId()) && (userEventReq.getOcpcDeviceId().length()<6 || userEventReq.getOcpcDeviceId().contains("0000") || userEventReq.getOcpcDeviceId().contains("null"))) {
            userEventReq.setOcpcDeviceId(null);
        }

        if (StringUtils.isNotBlank(userEventReq.getMac()) && (userEventReq.getMac().length()<6 || userEventReq.getMac().contains("0000") || userEventReq.getMac().contains("null"))) {
            userEventReq.setMac(null);
        }

        userEventReq.setOcpcDeviceId(getStrNoBlack(userEventReq.getOcpcDeviceId()));
        userEventReq.setOaid(getStrNoBlack(userEventReq.getOaid()));
        userEventReq.setMac(getStrNoBlack(userEventReq.getMac()));
        userEventReq.setAndroidId(getStrNoBlack(userEventReq.getAndroidId()));
        //0000-0000-0000-0000
        userEventReq.setMac(null);
    }

    public static void replaceZto(DeviceReq deviceReq) {
        if(deviceReq.getOs() == null){
            log.warn(" osisnull "+ JSON.toJSONString(deviceReq));
            deviceReq.setOs("android");//默认android
        }
        deviceReq.setImei(getStrNoBlack(deviceReq.getImei()));
        deviceReq.setOaid(getStrNoBlack(deviceReq.getOaid()));
        deviceReq.setMac(getStrNoBlack(deviceReq.getMac()));
        deviceReq.setAndroidId(getStrNoBlack(deviceReq.getAndroidId()));
        deviceReq.setIdfa(getStrNoBlack(deviceReq.getIdfa()));
    }

    private static void printActInfo(UserEventReq userEventReq){
        if(userEventReq.getEventType()==0){
            log.info("printiosactive "+userEventReq.getProduct()+" "+userEventReq.getOcpcDeviceId()+" "+userEventReq.getUserId());
        }
    }
    public   static String getStrNoBlack(String str){
        if (StringUtils.isNotBlank(str) && (str.length()<6  ||
                str.contains("00000000-0000-0000-0000-000000000000") ||
                str.contains("000000-0000-0000-000000") ||
                str.contains("0000-0000-0000-0000") ||
                str.contains("default") ||
                str.equalsIgnoreCase("c21f969b5f03d33d43e04f8f136e7682") ||//default md5
                str.equalsIgnoreCase("null")  ||
                str.contains("0000-") ||
                DEFAULT_MAC.contains(str))) {
//            log.warn("str不合法直接替换  {}", str);
            return null;
        }
        return str;
    }

    public static void main(String[] args){
        System.out.println(MD5Utils.getMd5AndroidId("default"));
    }
}
