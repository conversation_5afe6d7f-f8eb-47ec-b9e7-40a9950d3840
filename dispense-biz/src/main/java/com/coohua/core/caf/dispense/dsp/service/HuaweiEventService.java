package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dto.req.UploadAdvBehaviorReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Consts;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.Charset;

/**
 * 华为暂时不接
 */
@Slf4j
public class HuaweiEventService {
    private String huaweiUrl = "https://connect-api.cloud.huawei.com/api/datasource/v1/track/activate";

    /**
     * 获取 Token 认证信息
     * https://connect-api.cloud.huawei.com/api/oauth2/v1/token
     *
     * @param clientId     clientId
     * @param clientSecret clientSecret
     * @return token 认证信息
     */
    public static String getToken(String clientId, String clientSecret) {
        /** 认证信息 */
        String token = null;
        CloseableHttpClient httpClient = null;
        HttpPost post = null;
        try {
            /** 新建 POST 请求 */
            post = new HttpPost("https://connect-api.cloud.huawei.com/api/oauth2/v1/token");
            /** 组装请求 Body, 使用 JSON 格式携带查询信息 */
            JSONObject keyString = new JSONObject();
            keyString.put("client_id", clientId);
            keyString.put("client_secret", clientSecret);
            keyString.put("grant_type", "client_credentials");
            StringEntity entity = new StringEntity(keyString.toString(), Charset.forName("UTF-8"));
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            /** 设置请求 Body */
            post.setEntity(entity);
            /** 发送请求 */
            httpClient = HttpClients.createDefault();
            HttpResponse response = httpClient.execute(post);
            /** 处理返回参数 */
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), Consts.UTF_8));
                String result = br.readLine();
                JSONObject object = JSON.parseObject(result);
                token = object.getString("access_token");
            }
        } catch (Exception e) {
            log.error("",e);
        }finally {
            if(httpClient!=null){
                try {
                    httpClient.close();
                }catch (Exception e){
                    log.error("",e);
                }
            }

            if(post!=null){
                post.releaseConnection();
            }
        }
        return token;
    }


    private String appId = "10***68";

    /**
     * 上传用户在应用内的行为数据
     *
     * @param token 认证信息
     * @throws java.lang.reflect.InvocationTargetException
     * @throws IllegalAccessException
     */
    public static void uploadAdvBehavior(String token) throws InvocationTargetException {
        /** 应用 ID*/

        /** 用户标识 Id 类型 取值范围: OAID, OTHER*/
        String deviceIdType = "OAID";

        /** 用户标识 Id.用户标识类型为 OAID 时限制为 OAID，否则自由定义 */
        String deviceId = "12345678";

        /** 行为时间戳，UTC 时间 */
        Long actionTime = 1593608299858L;

        /** 用户发生的标准行为 标准行为必须与库内参数定义值一致，标准值见标准行为表。与自定义行为，必须二者其一
         空 */
        String actionType = "1";

        /** 用户发生的自定义行为 非标准数据，CP 自定义字符串。与标准行为，必须二者其一不为空 */
        String customAction = "";

        /** 用户行为对象(商品/内容),非必填，非标准数据，CP 自定义字符串 */
        String productId = "";

        /** 用户行为对象所属的类目 非必填，非标准数据，CP 自定义字符串 */
        String productClass = "";

        /** 用户行为对象附带的属性参数 非必填，非标准数据，CP 自定义字符串 */
        String productParam = "";

        /** 此次记录是否为补报记录，非必填，Y（补报）/留空（非补报） */
        String missType = "";

        /** 此记录本应记录所在日期范围 如果 miss_type 为 Y 则必填，留空需置空 */
        Long missTime = 0L;

        /** callback 匹配参数 call_back callback 任务请求匹配标记 必填，按监测传递参数回传,如果非任务归因回传，
         则填 0，系统生成 callback 不含 0 */
        String callBack =
                "security:3745353132324336313339453544343842344531373435413534384442313232:5DDD0F5A81CBEBD543C07631F1A3CCB2FEABE4A4A802526D25FC88F2B56F3494D383390DB993D140EE597B06837CC585051";

        /** 新建上传用户在应用内行为数据的请求条件，并插入数据 */
        UploadAdvBehaviorReq reqInfo = new UploadAdvBehaviorReq();
        reqInfo.setAppId("");
        reqInfo.setDeviceIdType(deviceIdType);
        reqInfo.setDeviceId(deviceId);
        reqInfo.setActionTime(actionTime);
        reqInfo.setActionType(actionType);
        reqInfo.setCustomAction(customAction);
        reqInfo.setProductId(productId);
        reqInfo.setProductClass(productClass);
        reqInfo.setProductParam(productParam);
        reqInfo.setMissType(missType);
        reqInfo.setMissTime(missTime);
        reqInfo.setCallBack(callBack);

        /** 发送请求 */
        upLoadAdvBehavior("clientId", token, reqInfo);
    }


    public static void upLoadAdvBehavior(String clientId, String token, UploadAdvBehaviorReq req) {

        try {
            /** 新建 POST 请求 */
            HttpPost post = new HttpPost("https://connect-api.cloud.huawei.com/api/datasource/v1/track/activate");

            /** 设置 Header 认证信息 */
            post.setHeader("Authorization", "Bearer " + token);
            post.setHeader("client_id", clientId);

            /** 组装请求 Body，使用 JSON 格式携带查询信息 */
            String reqStr = JSON.toJSONString(req);
            StringEntity entity = new StringEntity(reqStr, Charset.forName("UTF-8"));
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");

            /** 设置请求 Body */
            post.setEntity(entity);

            /** 发送请求 */
            CloseableHttpClient httpClient = HttpClients.createDefault();
            CloseableHttpResponse httpResponse = httpClient.execute(post);

            /** 处理返回参数 */
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == HttpStatus.SC_OK) {
                BufferedReader br =
                        new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent(),
                                Consts.UTF_8));
                String result = br.readLine();

                JSONObject object = JSON.parseObject(result);
                System.out.println(object);
            }
        } catch (Exception e) {
            System.out.println(e);
        }
    }
}
