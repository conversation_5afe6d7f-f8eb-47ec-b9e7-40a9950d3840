package com.coohua.core.caf.dispense.dto.req;


import lombok.Data;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2019/8/26
 **/
@Data
public class UserMetaParam {
    private String pkgId;
    private String userId;
    private String unionId;
    private String photoUrl;
    private String nickName;
    private String password;
    /**
     * 0: 正常
     * 1: 注销【不再允许注册】
     * 2: 拉灰
     */
    private Integer state;
    /**
     * 0: android
     * 1: ios
     */
    private Integer os;
    private String brand;
    /**
     * Android:  IMEI
     * IOS: idfa
     */
    private String channel;
    private String deviceId;
    private String appVersion;
    /**
     * 厂商 UI 版本号
     * 华为： EMUI 5.0.2
     * IOS:
     */
    private String romVersion;
    /**
     * 操作系统版本
     * IOS：  10.0.1
     * Android： 8.0.9
     */
    private String osVersion;
    private Long createTime;
    private Long updateTime;

}
