package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

@Data
public class WxActiveReq {
    String clue_token;
    String ad_id;
    String creative_id;
    String req_id;
    String advertiser_id;
    String openId;
    String userId;
    Integer appId;
    String product;
    String requestId;

    String clickid;
    String item_id;
    String qjs;

    public static String os="wmin";

    public static String getOs() {
        return os;
    }

    public static void setOs(String os) {
        WxActiveReq.os = os;
    }
}
