package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户注册消息消费异常记录表
 *
 * @since 2025-07-30
 */
@Data
@Builder
@TableName("user_active_mq_save_error_record")
public class UserActiveMqSaveErrorRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息Topic
     */
    @TableField("topic")
    private String topic;

    /**
     * 原始消息体内容
     */
    @TableField("message_body")
    private String messageBody;

    /**
     * 异常堆栈信息
     */
    @TableField("error_stack_trace")
    private String errorStackTrace;

    /**
     * 记录创建时间
     */
    @TableField("create_time")
    private Date createTime;
}
