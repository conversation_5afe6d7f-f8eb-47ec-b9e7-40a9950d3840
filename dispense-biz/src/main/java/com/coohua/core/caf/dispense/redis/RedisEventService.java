package com.coohua.core.caf.dispense.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.apollo.ExtEventApolloConfig;
import com.coohua.core.caf.dispense.apollo.LogNum;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;

import com.coohua.core.caf.dispense.ck.entity.GyRecordDataLocal;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.AuthToutiaoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.OcpcPayReq;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.service.*;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.WxActiveReq;
import com.coohua.core.caf.dispense.enums.*;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.hbase.RtaHbaseService;
import com.coohua.core.caf.dispense.kafka.LogKafkaSender;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.entity.ToutiaoSdkGyRetry;
import com.coohua.core.caf.dispense.ocpc.service.*;
import com.coohua.core.caf.dispense.rta.RtaUpPriceRedisService;
import com.coohua.core.caf.dispense.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Component
@Slf4j
public class RedisEventService {

    @Autowired
    ExtEventApolloConfig extEventApolloConfig;

    @Resource(name = "ocpcToRedisEevent")
    ThreadPoolTaskExecutor poolTaskExecutor;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    CvrProductService cvrProductService;
    @Autowired
    ProductAdvertiserPoolService productAdvertiserPoolService;
    @Autowired
    CvrTimingProductCountLogService cvrTimingProductCountLogService;
    @Autowired
    private ToutiaoSdkGyRetryService toutiaoSdkGyRetryService;

    public void redisInsertEvent(OcpcEvent ocpcEvent) {
        if (ocpcSwitcher.writeRedisSwitch) {
            poolTaskExecutor.execute(() -> {
                try {
                    saveActiveEventToRedis(ocpcEvent,3);
                }catch (Exception e){
                    log.error("双写ocpcEvent成功异常", e);
                }
            });
        }
    }

    public void redisInsertEvent(OcpcEvent ocpcEvent,int days) {
        if (ocpcSwitcher.writeRedisSwitch) {
            poolTaskExecutor.execute(() -> {
                try {
                    saveActiveEventToRedis(ocpcEvent,days);
                }catch (Exception e){
                    log.error("双写ocpcEvent成功异常", e);
                }
            });
        }
    }
    @Autowired
    RedisClickService redisClickService;
    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    /**
     * 目前来看无用 toutiao.event.enable 参数关闭，无入口
     */
    public void saveActiveUEventToRedis(UserEvent ocpcEvent){
        try {
            if (StringUtils.isNotBlank(ocpcEvent.getOcpcDeviceId()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getOcpcDeviceId()) && ocpcEvent.getOcpcDeviceId().length()>6) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.device.value, ocpcEvent.getOcpcDeviceId()),
                        DateTimeConstants.SECONDS_PER_DAY * 3, JSONObject.toJSONString(ocpcEvent));
            }
            if (StringUtils.isNotBlank(ocpcEvent.getOaid()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getOaid()) && ocpcEvent.getOaid().length()>6) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.oaid.value, ocpcEvent.getOaid()),
                        DateTimeConstants.SECONDS_PER_DAY * 3, JSONObject.toJSONString(ocpcEvent));
            }
            if (StringUtils.isNotBlank(ocpcEvent.getMacId()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getMacId()) && ocpcEvent.getMacId().length()>6) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.mac.value, ocpcEvent.getMacId()),
                        DateTimeConstants.SECONDS_PER_DAY * 3, JSONObject.toJSONString(ocpcEvent));
            }

            String allKey = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.device.value, ocpcEvent.getOcpcDeviceId())
                    +","+RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.oaid.value, ocpcEvent.getOaid()
                    +","+RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.mac.value, ocpcEvent.getMacId()));
            // log.info("redissetocpcEvent success req={} ,rsp={} key={}", JSONObject.toJSONString(ocpcEvent),JSONObject.toJSONString(ocpcEvent),allKey);

        } catch (Exception e) {
            log.error("redis add error ", e);
        }
    }
    public void saveActiveEventToRedis(OcpcEvent ocpcEvent,int days){
        try {
            List<String> allKeys = new ArrayList<>();
            if (ocpcSwitcher.needCheckRepeatActive(ocpcEvent.getProduct())) {
                // 存userId维度，用于激活判重
                if (StringUtils.isNotBlank(ocpcEvent.getUserId())) {
                    String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.userId.value, ocpcEvent.getUserId());
                    allKeys.add(key);
                    bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
                }
            }
            if (StringUtils.isNotBlank(ocpcEvent.getOcpcDeviceId()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getOcpcDeviceId()) && ocpcEvent.getOcpcDeviceId().length() > 6) {
                // 将sourceDeviceId转化下，看是否为，0000-0000-0000-0000、000000-0000-0000-000000、00000000-0000-0000-0000-000000000000格式未识别数据, 此类数据不加缓存
                if (StringUtils.isNotBlank(ocpcEvent.getSourceDeviceId()) && !ocpcEvent.getSourceDeviceId().replaceAll("-", "").matches("^0*$")) {
                    String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.device.value, ocpcEvent.getOcpcDeviceId());
                    allKeys.add(key);
                    bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
                }
            }
            if (StringUtils.isNotBlank(ocpcEvent.getIdfa2()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getIdfa2()) && ocpcEvent.getIdfa2().length()>6) {
                String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.idfa2.value, ocpcEvent.getIdfa2());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }
            if (StringUtils.isNotBlank(ocpcEvent.getOaid()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getOaid()) && ocpcEvent.getOaid().length()>6) {
                String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.oaid.value, ocpcEvent.getOaid());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }
            if (StringUtils.isNotBlank(ocpcEvent.getOaid2()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getOaid2()) && ocpcEvent.getOaid2().length()>6) {
                String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.oaid2.value, ocpcEvent.getOaid2());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }
            if (StringUtils.isNotBlank(ocpcEvent.getMacId()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getMacId()) && ocpcEvent.getMacId().length()>6) {
                String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.mac.value, ocpcEvent.getMacId());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }
            if (StringUtils.isNotBlank(ocpcEvent.getAndroidId()) && !DigestUtils.md5Hex("0").equals(ocpcEvent.getAndroidId())) {
                String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.androidId.value, ocpcEvent.getAndroidId());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }
            if (StringUtils.isNotBlank(ocpcEvent.concatIpua())) {
                String key = RedisKeyConstants.getActiveEventKey(ocpcEvent.getProduct(), ocpcEvent.getOs(), DeviceType.ipua.value, ocpcEvent.concatIpua());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }

            if (StringUtils.isNotBlank(ocpcEvent.getOpenId()) && ocpcEvent.getOpenId().length()>5 && !DigestUtils.md5Hex("0").equals(ocpcEvent.getOpenId())) {
                String key = RedisKeyConstants.getActiveOpenIdKey(ocpcEvent.getProduct(), ocpcEvent.getWAppId(), ocpcEvent.getOpenId());
                allKeys.add(key);
                int day = 3;
                if(StringUtils.equalsIgnoreCase(ocpcEvent.getOs(), WxActiveReq.os)){
                    day = 8;
                }
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * day, JSONObject.toJSONString(ocpcEvent));
                //openId 小程序直接用8天存储
            }

            if (StringUtils.isNotBlank(ocpcEvent.getCaid()) && ocpcEvent.getCaid().length()>5 && !DigestUtils.md5Hex("0").equals(ocpcEvent.getCaid())) {
                String key = RedisKeyConstants.getActiveCaidKey(ocpcEvent.getProduct(), ocpcEvent.getCaid());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }

            if (StringUtils.isNotBlank(ocpcEvent.getUserId())) {
                String key = RedisKeyConstants.getActiveUidKey(ocpcEvent.getProduct(),ocpcEvent.getOs(), ocpcEvent.getUserId());
                allKeys.add(key);
                bpDispenseJedisClusterClient.setex(key, DateTimeConstants.SECONDS_PER_DAY * days, JSONObject.toJSONString(ocpcEvent));
            }
            String allKey = StringUtils.join(allKeys, ",");
            // log.info("redissetocpcEvent success req={} ,rsp={} key={}", JSONObject.toJSONString(ocpcEvent),JSONObject.toJSONString(ocpcEvent),allKey);

        } catch (Exception e) {
            log.error("redis add error ", e);
        }

    }

    /**
     * 获取激活事件
     */
    public OcpcEvent getActiveEvent(UserEventReq userEventReq){
        if (ocpcSwitcher.readOcpcHbaseSwitch) {  // 开关关闭
            return hbaseEventService.getActiveEventByHbase(userEventReq);
        } else {
            return getActiveEventByRedis(userEventReq);
        }
    }
    @Autowired
    ConstApolloConfig constApolloConfig;
    public OcpcEvent getActiveEventByRedis(UserEventReq request){

        OcpcEvent ocpcEvent = null;
        try {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("redisgetocpcEvent product is null ! {}", JSONObject.toJSONString(request));
                return ocpcEvent;
            }
            String key = "";
            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                // 如果是android 设备, 但传值为"0000-0000-0000-0000","000000-0000-0000-000000","00000000-0000-0000-0000-000000000000"就不走这个 OcpcDeviceId
                if ("android".equals(request.getOs()) &&
                        (request.getSourceDeviceId().contains("0000-0000-0000-0000") ||
                                request.getSourceDeviceId().contains("000000-0000-0000-000000") ||
                                request.getSourceDeviceId().contains("00000000-0000-0000-0000-000000000000"))) {
                    log.warn("android设备传sourceDeviceId值为未知值,不使用OcpcDeviceId进行数据匹配:"+ JSONObject.toJSONString(request));
                } else {
                    key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.device.value, request.getOcpcDeviceId());
                    String s = bpDispenseJedisClusterClient.get(key);
                    if (StringUtils.isNotBlank(s)) {
                        ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                        request.setGuiType(GuiyingType.deive.name);
                    }
                }
            }
            /**
             * 按idfa_md5查询
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.idfa2.value,request.getIdfa2());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.idfa.name);
                }
            }
            /**
             * click不存在再查oaId
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getOaid())&& !ConstCls.emptyMd5.equals(request.getOaid())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.oaid.value,request.getOaid());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.oaid.name);
                }
            }
            /**
             * oaid2 = md5(oaid)
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getOaid2())&& !ConstCls.emptyMd5.equals(request.getOaid2())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.oaid2.value, request.getOaid2());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.oaid.name);
                }
            }
            /**
             * 设备号和 oaId 查不到 再用mac地址查
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getMac())&& !ConstCls.emptyMd5.equals(request.getMac())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.mac.value,request.getMac());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("macevent归因成功 " + product+" "+request.getMac());
                    request.setGuiType(GuiyingType.mac.name);
                }
            }
            /**
             * androidId查
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getAndroidId())&& !ConstCls.emptyMd5.equals(request.getAndroidId())) {
                String androidIdMd5 = MD5Utils.getMd5Sum(request.getAndroidId());
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.androidId.value,androidIdMd5);
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("androidId归因成功 " + product+" "+request.getAndroidId());
                    request.setGuiType(GuiyingType.androidId.name);
                }
            }

            /**
             * androidIdMD5查
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getAndroidMd5Id())&& !ConstCls.emptyMd5.equals(request.getAndroidMd5Id())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.androidId.value,request.getAndroidMd5Id());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
//                    log.info("androidId归因成功 " + product+" "+request.getAndroidId());
                    request.setGuiType(GuiyingType.androidId.name);
                }
            }
            /**
             * 查ipua
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.concatIpua())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.ipua.value,request.concatIpua());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("redisActive归因成功ipua " + product+" "+request.concatIpua());
                    request.setGuiType(GuiyingType.ipua.name);
                }
            }


            if (ocpcEvent == null && StringUtils.isNotBlank(request.getOpenId())&& !ConstCls.emptyMd5.equals(request.getOpenId())) {
                key = RedisKeyConstants.getActiveOpenIdKey(product,request.getWAppId(),request.getOpenId());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("openId 归因成功 " + product+" "+request.getAndroidId());
                    request.setGuiType(GuiyingType.openid.name);
                }
            }

            //caid
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getCaid())&& !ConstCls.emptyMd5.equals(request.getCaid())) {
                key = RedisKeyConstants.getActiveCaidKey(product,request.getCaid());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("caid 归因成功 " + product+" "+request.getCaid());
                    request.setGuiType(GuiyingType.caid.name);
                }
            }
            /**
             * caid2 = md5(caid)
             */
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getCaid2())&& !ConstCls.emptyMd5.equals(request.getCaid2())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.caid2.value, request.getCaid2());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    request.setGuiType(GuiyingType.caid.name);
                }
            }
            //USERid
            if (ocpcEvent == null && StringUtils.isNotBlank(request.getUserId())) {
                key = RedisKeyConstants.getActiveUidKey(product,request.getOs(),request.getUserId());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                    log.info("userId激活归因成功 " + product+" "+request.getOs()+" uid:"+request.getUserId());
                    request.setGuiType(GuiyingType.uid.name);
                }
            }
            if (ocpcEvent != null && ocpcSwitcher.needLog(LogNum.RedisGetOcpcEvent)) {
                log.info("redis getocpcEvent success req={} ,rsp={} key={}", JSONObject.toJSONString(request),JSONObject.toJSONString(ocpcEvent),key);
            }
        } catch (Exception e) {
            log.error("redis getocpcEvent error ", e);
        }

        return ocpcEvent;
    }

    public OcpcEvent getUserIdActiveEvent(UserEventReq userEventReq){
        if (ocpcSwitcher.readOcpcHbaseSwitch) {
            return hbaseEventService.getUserIdActiveEventByHbase(userEventReq);
        } else {
            return getUserIdActiveEventByRedis(userEventReq);
        }
    }

    public OcpcEvent getUserIdActiveEventByRedis(UserEventReq request){

        OcpcEvent ocpcEvent = null;
        try {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("redis getUserIdActive product is null ! {}", JSONObject.toJSONString(request));
                return ocpcEvent;
            }
            String key = "";
            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getUserId())) {
                key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.userId.value,request.getUserId());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    ocpcEvent = JSONObject.parseObject(s, OcpcEvent.class);
                }
            }
            if (ocpcEvent != null && ocpcSwitcher.needLog(LogNum.RedisGetOcpcEvent)) {
                log.info("redis getUserIdActive success req={} ,rsp={} key={}", JSONObject.toJSONString(request),JSONObject.toJSONString(ocpcEvent),key);
            }
        } catch (Exception e) {
            log.error("redis getUserIdActive error ", e);
        }

        return ocpcEvent;
    }


    /**
     * 判断激活事件是通过 imei 还是 oaid 匹配上的，目前仅用于oppo和vivo的回传
     * @param request
     * @return
     */
    public DeviceType checkActiveEventFrom(UserEventReq request){

        try {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("checkActiveEventFrom product is null ! {}", JSONObject.toJSONString(request));
                return null;
            }
            String key = "";
            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                key = RedisKeyConstants.getActiveEventKey(product, request.getOs(), DeviceType.device.value,request.getOcpcDeviceId());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    return DeviceType.device;
                }
            }
            /**
             * click不存在再查oaId
             */
            if (StringUtils.isNotBlank(request.getOaid())&& !ConstCls.emptyMd5.equals(request.getOaid())) {
                key = RedisKeyConstants.getActiveEventKey(product,request.getOs(), DeviceType.oaid.value,request.getOaid());
                String s = bpDispenseJedisClusterClient.get(key);
                if (StringUtils.isNotBlank(s)) {
                    return DeviceType.oaid;
                }
            }
        } catch (Exception e) {
            log.error("redis checkActiveEventFrom error ", e);
        }

        return null;
    }

    /**
     * 存储关键行为事件
     *
     * @param ocpcEvent 事件
     */
    public void saveKeyEvent(OcpcEvent ocpcEvent) {

        String eventJson = JSON.toJSONString(ocpcEvent);

        List<String> orderlyKeys = OcpcEventKeyJoinUtil.generatePossibleEventKeyOrderly(ocpcEvent, ToutiaoEventTypeEnum.KEY_EVENT);

        if (CollectionUtils.isEmpty(orderlyKeys)) {
            log.warn("关键行为redisKeys拼接结果为空 {}", eventJson);
            return;
        }

        try {
            orderlyKeys.forEach(orderlyKey -> bpDispenseJedisClusterClient.setex(orderlyKey,
                    DateTimeConstants.SECONDS_PER_DAY * 3, eventJson));
        } catch (Exception e) {
            log.error("存储关键行为事件至redis出现异常 ", e);
        }
    }
    public void saveKeyActionExtEvent(OcpcEvent ocpcEvent) {

        String eventJson = JSON.toJSONString(ocpcEvent);

        String extEventKey = OcpcEventKeyJoinUtil.generateExtEventKey(ocpcEvent);

        if (StringUtils.isBlank(extEventKey)) {
            log.warn("关键行为衍生事件redisKeys拼接结果为空 {}", eventJson);
            return;
        }

        try {
            bpDispenseJedisClusterClient.setex(extEventKey,
                    DateTimeConstants.SECONDS_PER_DAY * 3, eventJson);
        } catch (Exception e) {
            log.error("存储关键行为衍生事件至redis出现异常 ", e);
        }
    }

    /**
     * 查询关键行为事件记录
     *
     * @param userEventReq 请求
     * @return 关键行为事件记录
     */
    public OcpcEvent queryKeyEvent(UserEventReq userEventReq) {
        if(ocpcSwitcher.readOcpcHbaseSwitch){
            return hbaseEventService.queryKeyEventByHbase(userEventReq);
        } else {
            return queryKeyEventByRedis(userEventReq);
        }
    }

    public OcpcEvent queryKeyEventByRedis(UserEventReq userEventReq) {

        List<String> orderlyKeys = OcpcEventKeyJoinUtil.generatePossibleEventKeyOrderly(userEventReq, ToutiaoEventTypeEnum.KEY_EVENT);

        if (CollectionUtils.isEmpty(orderlyKeys)) {
            log.warn("关键行为redisKeys拼接结果为空 {}", JSON.toJSONString(userEventReq));
            return null;
        }

        try {

            for (String orderlyKey : orderlyKeys) {

                String content = bpDispenseJedisClusterClient.get(orderlyKey);
                if (StringUtils.isNotBlank(content)) {
                    log.info("查询redis关键行为事件成功 request={} response={} key={}", JSON.toJSONString(userEventReq), content, orderlyKey);
                    return JSONObject.parseObject(content, OcpcEvent.class);
                }
            }
        } catch (Exception e) {
            log.error("从redis查询关键行为事件出现异常 ", e);
        }

        return null;
    }

    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    HbaseClickService hbaseClickService;
    public static ToutiaoClick convertEventToClick(OcpcEvent ocpcEvent){
        if(ocpcEvent==null){
            return null;
        }
        ToutiaoClick toutiaoClick = new ToutiaoClick();

        toutiaoClick.setOs(ocpcEvent.getOs());
        toutiaoClick.setAccountId(ocpcEvent.getAccountId());
        toutiaoClick.setAccountName(ocpcEvent.getAccountName());
        toutiaoClick.setCallbackUrl(ocpcEvent.getCallbackUrl());
        toutiaoClick.setCreateTime(ocpcEvent.getClickTime());
        toutiaoClick.setDsp(ocpcEvent.getDsp());
//        toutiaoClick.setGid(ocpcEvent.get);
        toutiaoClick.setMac(ocpcEvent.getMacId());
        toutiaoClick.setOaid(ocpcEvent.getOaid());
        toutiaoClick.setOcpcDeviceId(ocpcEvent.getOcpcDeviceId());
        if (DspType.OPPO.value.equals(toutiaoClick.getDsp())) {
            toutiaoClick.setPid(ocpcEvent.getPlanId());
        } else if (DspType.TOUTIAO.value.equals(toutiaoClick.getDsp())) {
            toutiaoClick.setCid(ocpcEvent.getCreativeId());
            toutiaoClick.setGid(ocpcEvent.getGroupId());
            toutiaoClick.setPid(ocpcEvent.getPlanId());
        } else if (DspType.KUAISHOU.value.equals(toutiaoClick.getDsp())) {
            toutiaoClick.setAidName(ocpcEvent.getAccountName());
            toutiaoClick.setCid(ocpcEvent.getCreativeId());
            toutiaoClick.setGid(ocpcEvent.getPlanId());
            toutiaoClick.setPid(ocpcEvent.getGroupId());
        }
//        toutiaoClick.setPkgChannel(ocpcEvent.getP);
        toutiaoClick.setProduct(ocpcEvent.getProduct());
//        toutiaoClick.setUnionSite(ocpcEvent.getU);
//        toutiaoClick.setTs(ocpcEvent.getT);
//        toutiaoClick.setUpdateTime();
        toutiaoClick.setMid(ocpcEvent.getMid());
        return toutiaoClick;
    }
    @Autowired
    PayEventService payEventService;
    @Autowired
    OcpcMisActiveService ocpcMisActiveService;
    @Autowired
    OcpcPayReqService ocpcPayReqService;
    @Autowired
    UserEventActiveService userEventActiveService;
    @Autowired
    RtaUpPriceRedisService rtaSetSigleService;
    @Autowired
    RtaHbaseService rtaHbaseService;
    @Autowired
    OceanengineSdkService oceanengineSdkService;
    @Autowired
    LogKafkaSender logKafkaSender;
    public boolean ocpcUevent(UserEventReq userEventReq, List<ToutiaoClick> clicks, OcpcPayReq ocpcPayReq) {

        boolean isHbaseBu = false;
        userEventReq.setSouceType(OcpcSourceType.OCPC);
        if (StringUtils.isBlank(userEventReq.getPkgChannel()) && StringUtils.isNotBlank(userEventReq.getPkg_channel())) {
            userEventReq.setPkgChannel(userEventReq.getPkg_channel());
            log.info("pkg_替换 " + userEventReq.getProduct() + " " + userEventReq.getPkgChannel() + " ");
        }

        if (ocpcSwitcher.baiduGy && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && userEventReq.getPkgChannel().startsWith("bd")
                && ocpcSwitcher.baiduGyChannel.contains(userEventReq.getPkgChannel())) {
            log.info("baidu渠道替换product " + "bd" + userEventReq.getProduct() + "@" + userEventReq.getPkgChannel());
            userEventReq.setProduct("bd" + userEventReq.getProduct());
        }

        ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEventReq.getEventType());
        List<ToutiaoClick> clickList = new ArrayList<>();

        if (ToutiaoEventTypeEnum.KEY_EVENT.equals(toutiaoEventTypeEnum) || ToutiaoEventTypeEnum.START_APP.equals(toutiaoEventTypeEnum)
                || ToutiaoEventTypeEnum.PURCHASE.equals(toutiaoEventTypeEnum)) {
            OcpcEvent ocpcEvent = getActiveEvent(userEventReq);
            if (ocpcEvent == null && ocpcSwitcher.redisBuf) {
                ocpcEvent = hbaseEventService.getActiveEventByHbase(userEventReq);
                if (ocpcEvent != null) {
                    isHbaseBu = true;
                }
            }
            //如果是默认归因类型则不走后续流程,defaultGy是为了兼容已存储的数据
            if (ocpcEvent != null && (GuiyingType.packageGy.name.equals(ocpcEvent.getGyType())
                    || GuiyingType.defaultGy.name.equals(ocpcEvent.getGyType()))) {
                return true;
            }

            if (ocpcEvent != null) {
                ToutiaoClick toutiaoClick = convertEventToClick(ocpcEvent);
                if (ToutiaoEventTypeEnum.START_APP.equals(toutiaoEventTypeEnum)) {
                    // 判断是否满足回传次留的时间条件
                    checkCiLiuCondition(userEventReq, clickList, toutiaoClick);
                } else {
                    clickList.add(toutiaoClick);
                }

                if (ToutiaoEventTypeEnum.PURCHASE.equals(toutiaoEventTypeEnum) && ocpcPayReq != null) {
                    try {
                        ocpcPayReq.setDsp(ocpcEvent.getDsp());
                        ocpcPayReq.setClickTime(ocpcEvent.getCreateTime());
                        ocpcPayReq.setPid(ocpcEvent.getAccountId());
                        ocpcPayReq.setUpdateTime(new Date());
                        ocpcPayReqService.updateById(ocpcPayReq);
                    } catch (Exception e) {
                        log.error("", e);
                    }
                }
            }
        } else {
            //激活 核心行为 次留 按照最近点击时间归因
            OcpcEvent ocpcEvent = getActiveEvent(userEventReq);
            if (ocpcEvent != null && toutiaoEventTypeEnum.equals(ToutiaoEventTypeEnum.ACTIVATE_APP) &&
                    StringUtils.equalsIgnoreCase(ocpcEvent.getUserId(), userEventReq.getUserId()) &&
                    StringUtils.equalsIgnoreCase(ocpcEvent.getProduct(), userEventReq.getProduct()) &&
                    StringUtils.equalsIgnoreCase(ocpcEvent.getOs(), userEventReq.getOs())) {
                log.info("ocpc重复激活 " + JSON.toJSONString(userEventReq) + "@" + JSON.toJSONString(ocpcEvent));
                return false;
            }

            //通过开关以及配置产品判断走心的归因匹配逻辑还是老归因匹配逻辑
            if (ocpcSwitcher.gySwitcher && (ocpcSwitcher.gyProductList.contains(userEventReq.getProduct()) || ocpcSwitcher.gyProductList.isEmpty())) {
                clickList = getNewClickList(userEventReq);
            } else {
                clickList = getClickList(userEventReq);
            }

            //根据开关判断是否走默认归因 且 sdk归因无需重试
            if (ocpcSwitcher.gyDefaultSwitcher && (ocpcSwitcher.gyDefaultProductList.contains(userEventReq.getProduct()) || ocpcSwitcher.gyDefaultProductList.isEmpty())
                    && ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)) {
                //如果没有查询到点击事件，且渠道包不是商店渠道，则根据渠道包生成默认的归因
                if (clickList.isEmpty() && StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
                    if (ocpcSwitcher.retrySdkGyProductList.contains(userEventReq.getProduct())
                            && userEventReq.getRetrySdkGy() != null
                            && userEventReq.getRetrySdkGy()) {
                        // 如果是u3d产品，且sdk归因需重试，则直接返回
                        return true;
                    }
                    ToutiaoClick toutiaoClick = getDefaultClickList(userEventReq);
                    clickList.add(toutiaoClick);
                    //保存归因
                    ocpcEventService.saveUserEvent(toutiaoClick, userEventReq, "", "");
                    GyRecordDataLocal gyRecordData = fillGyRecordData(userEventReq);
                    gyRecordData.setGyType(GuiyingType.packageGy.name);
                    //通过Kafka记录埋点数据
                    logKafkaSender.sendRecordData(JSONObject.toJSONString(gyRecordData));
                    ocpcMisActiveService.saveOcpcMisActive(userEventReq, null);

                    // 存储匹配信息 异步去匹配点击 如果匹配到点击则将自然量归为买量
                    // 获取当前时间
                    LocalTime currentTime = LocalTime.now();
                    // 定义格式 (HH: 24小时制, mm: 分钟)
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
                    String key = "package:gy:retry:"+formatter.format(currentTime);
                    if (bpDispenseJedisClusterClient.get(key) == null) {
                        List<UserEventReq> userEventReqList = new ArrayList<>();
                        userEventReqList.add(userEventReq);
                        bpDispenseJedisClusterClient.setex(key,60*61,JSONObject.toJSONString(userEventReqList));
                    }else {
                        String userEventReqListStr = bpDispenseJedisClusterClient.get(key);
                        List<UserEventReq> userEventReqList = JSONObject.parseArray(userEventReqListStr, UserEventReq.class);
                        userEventReqList.add(userEventReq);
                        bpDispenseJedisClusterClient.setex(key,60*61,JSONObject.toJSONString(userEventReqList));
                    }
                    return true;
                }
            }
            //保证每个渠道只有一个点击事件
            clickList = ocpcEventService.getDspClickList(clickList);
            if (clickList.size() > 1) {
                clickList = ocpcEventService.sortClickList(clickList);
            }

            // 如果当前点击是商店渠道，则查询多次点击，看是否要替换为三方渠道
            if (!clickList.isEmpty() && "oppo".equals(clickList.get(0).getDsp())) {
                // 从hbase中查询多次点击
                List<ToutiaoClick> multiClickList = hbaseClickService.getToutiaoClickList(userEventReq);
                log.info("多次点击查询 点击次数 {}", multiClickList.size());
                if (multiClickList.size() != 1) {
                    for (int i = 0; i < multiClickList.size(); i++) {
                        if (!"oppo".equals(multiClickList.get(i).getDsp())) {
                            clickList.set(0, multiClickList.get(i));
                            log.info("oppo渠道点击替换 第 {} 次点击 原渠道 {} 新渠道 {}", i, clickList.get(0).getDsp(), JSONObject.toJSONString(multiClickList.get(i)));
                            break;
                        }
                    }
                }
            }

        }


        GyRecordDataLocal gyRecordData = null;
        if (ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)) {
            //记录没归因到的数据
            if (clickList.isEmpty()) {
                ocpcMisActiveService.saveOcpcMisActive(userEventReq, null);
                gyRecordData = fillGyRecordData(userEventReq);
            } else {
                ocpcMisActiveService.saveOcpcMisActive(userEventReq, clickList.get(0));
                gyRecordData = fillGyRecordData(userEventReq);
            }
        }

        boolean isActive = clickList.size() > 0;

        for (int i = 0; i < clickList.size(); i++) {
            try {
//                if(i==0){
//                    userEventActiveService.saveUserEvent(clickList.get(i), userEventReq,"","");
//                }
                if (authToutiaoAdvertiserService.checkActionCount(clickList.get(i), userEventReq)) {
                    String accountId = clickList.get(i).getAccountId();
                    String product = clickList.get(i).getProduct();
                    String dsp = clickList.get(i).getDsp();
                    String os = clickList.get(i).getOs();
                    if (ToutiaoEventTypeEnum.KEY_EVENT.equals(toutiaoEventTypeEnum)) {
                        if (StringUtils.isBlank(accountId)) {
                            log.info("click没有账户id, event click:{}, userEventReq:{}", JSONObject.toJSONString(clickList.get(i)), JSONObject.toJSONString(userEventReq));
                        } else {
                            //是否凌晨cvr受限
                            if (cvrTimingProductCountLogService.limitCvrAdvertiserId(product, accountId)) {
                                log.info("凌晨cvr拉线账户, product:{}, accountId:{}, os:{}, event click:{}, userEventReq:{}", userEventReq.getProduct(), accountId, os, JSONObject.toJSONString(clickList.get(i)), JSONObject.toJSONString(userEventReq));
                                continue;
                            } else if (null != cvrProductService.getByProduct(dsp, product) && productAdvertiserPoolService.isValid(product, accountId, os)) {
                                //cvr账户不回传关键行为
                                log.info("cvr账户不回传关键行为, product:{}, accountId:{}, os:{}, event click:{}, userEventReq:{}", userEventReq.getProduct(), accountId, os, JSONObject.toJSONString(clickList.get(i)), JSONObject.toJSONString(userEventReq));
                                continue;
                            } else {
                                if ("toutiao".equals(clickList.get(i).getDsp())) {
                                    AuthToutiaoAdvertiser authToutiaoAdvertiser = authToutiaoAdvertiserService.getAdvertiser(accountId);
                                    //比例不回传关键行为
                                    if (null != authToutiaoAdvertiser && authToutiaoAdvertiser.getEventTypes().startsWith(AccountActionTypeEnum.KA_RATE.getTypeKey())) {
                                        log.info("配置该账户按比例回传, product:{}, accountId:{}, event click:{}, userEventReq:{}", userEventReq.getProduct(), accountId, JSONObject.toJSONString(clickList.get(i)), JSONObject.toJSONString(userEventReq));
                                        continue;
                                    }
                                }
                            }
                        }
                    }
                    //若为头条正常归因  非直接归因
                    if (i == 0) {
                        log.info("redis 产生归因 " + clickList.get(i).getOcpcDeviceId());
                        boolean isAct = ocpcEventService.guiyingDspClick(clickList.get(i), userEventReq, true);
                        //给埋点数据赋值
                        if (ToutiaoEventTypeEnum.ACTIVATE_APP.value.equals(userEventReq.getEventType()) && gyRecordData != null) {
                            gyRecordData.setGyType(clickList.get(i).getGuiType());
                        }
                        clicks.add(clickList.get(i));
                        if (isHbaseBu) {
                            log.info("hbase补数 hbase补数关键行为 " + JSON.toJSONString(clickList.get(i)));
                        }
                        //付费额外回传关键行为
                        if (userEventReq.getEventType() == ToutiaoEventTypeEnum.PURCHASE.value) {
                            payEventService.payKeyEvent(clickList.get(i), userEventReq);
                        }
                    } else {
                        DspType dspType = DspType.getDspType(clickList.get(i).getDsp());

                        if (DspType.KUAISHOU.equals(dspType)) {
                            boolean isAct = ocpcEventService.guiyingDspClick(clickList.get(i), userEventReq, false);
                        }
                    }
                }

                if (i == 0 && userEventReq.getEventType() != ToutiaoEventTypeEnum.PURCHASE.value
                        && !GuiyingType.packageGy.name.equals(clickList.get(0).getGuiType())) {
                    // 尝试回传衍生事件
                    authToutiaoAdvertiserService.tryCallbackExtEvent(clickList.get(i), userEventReq);
                }

            } catch (Exception e) {
                log.error("redisEvent回传异常", e);
            }
        }

        if (gyRecordData != null) {
            //通过Kafka记录埋点数据
            logKafkaSender.sendRecordData(JSONObject.toJSONString(gyRecordData));
        }
        return isActive;
    }

    private ToutiaoClick getDefaultClickList(UserEventReq userEventReq) {
        ToutiaoClick toutiaoClick = new ToutiaoClick();
        toutiaoClick.setProduct(userEventReq.getProduct());
        toutiaoClick.setOs(userEventReq.getOs());
        toutiaoClick.setIdfa2(userEventReq.getIdfa2());
        toutiaoClick.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
        toutiaoClick.setOaid(userEventReq.getOaid());
        toutiaoClick.setOaid2(userEventReq.getOaid2());
        toutiaoClick.setPkgChannel(StringUtils.isNotBlank(userEventReq.getPkgChannel()) ? userEventReq.getPkgChannel() : userEventReq.getPkg_channel());
        toutiaoClick.setUa(userEventReq.getUa());
        if (userEventReq.getPkgChannel().startsWith("ks") || userEventReq.getPkgChannel().endsWith("ks")) {
            toutiaoClick.setDsp("kuaishou");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "bd", "cbd") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "bd", "cbd")) {
            toutiaoClick.setDsp("baidufeed");
        } else if (userEventReq.getPkgChannel().startsWith("gdt") || userEventReq.getPkgChannel().endsWith("gdt")) {
            toutiaoClick.setDsp("guangdiantong");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "csj", "tt") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "csj", "tt")) {
            toutiaoClick.setDsp("toutiao");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "oppo") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "oppo")) {
            toutiaoClick.setDsp("oppo");
        } else if (StringUtils.startsWithAny(userEventReq.getPkgChannel(), "vivo") || StringUtils.endsWithAny(userEventReq.getPkgChannel(), "vivo")) {
            toutiaoClick.setDsp("vivo");
        } else {
            toutiaoClick.setDsp("other");
        }
        toutiaoClick.setGuiType(GuiyingType.packageGy.name);
        userEventReq.setGuiType(GuiyingType.packageGy.name);
        return toutiaoClick;
    }

    private List<ToutiaoClick> getClickList(UserEventReq userEventReq) {
        List<ToutiaoClick> clickList = new ArrayList<>();
        ToutiaoClick sdkClick = null;
        if(StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
            sdkClick = oceanengineSdkService.querySdkClick(userEventReq, false, false);
            if (null != sdkClick) {
                clickList.add(sdkClick);
            }
        }
        ToutiaoClick toutiaoClick = rtaSetSigleService.queryJJClick(userEventReq);
        if(toutiaoClick == null){
            toutiaoClick = redisClickService.getClick(userEventReq, ocpcSwitcher.readOcpcHbaseSwitch);
            if(ocpcSwitcher.ksHbaseGy){
                ToutiaoClick toutiaoClick2 = redisClickService.getClick(userEventReq, true);
                if(toutiaoClick2!=null){
                    clickList.add(toutiaoClick2);
                    log.info("ks补充完成 "+toutiaoClick2.getProduct());
                }
            }
        }else{
            log.info("rta优先账户 "+toutiaoClick.getProduct()+" "+toutiaoClick.getDsp()+" "+toutiaoClick.getAccountId()+" "+toutiaoClick.getCaid());
        }
        if(toutiaoClick==null && (ocpcSwitcher.redisBuf)){
            //redis 补偿下
            toutiaoClick = redisClickService.getClick(userEventReq, true);
            if(toutiaoClick!=null){
                log.info("hbase补数 hbase补数激活归因 "+JSON.toJSONString(toutiaoClick));
            }
        }
        //头条的优先sdk归因
        if(toutiaoClick!=null && (null == sdkClick || !"toutiao".equals(toutiaoClick.getDsp()))){
            clickList.add(toutiaoClick);
        }

        return clickList;
    }

    public List<ToutiaoClick> getNewClickList(UserEventReq userEventReq) {
        List<ToutiaoClick> clickList = new ArrayList<>();
        //查询精准归因
        ToutiaoClick accurateClick = redisClickService.getAccurateClick(userEventReq, ocpcSwitcher.readOcpcHbaseSwitch);
        if (accurateClick != null) {
            clickList.add(accurateClick);
            // log.info("redis精准匹配成功 click {}", JSONObject.toJSONString(accurateClick));
        }
        if (ocpcSwitcher.ksHbaseGy && clickList.isEmpty()) {
            //查询hbase，hbase只存储3天的数据
            ToutiaoClick accurateClick2 = redisClickService.getAccurateClick(userEventReq, true);
            if (accurateClick2 != null) {
                clickList.add(accurateClick2);
                // log.info("hbase精准匹配成功 click {}", JSONObject.toJSONString(accurateClick2));
            }
        }
        // 请求穿山甲归因sdk
        if (StringUtils.isNotBlank(userEventReq.getProduct()) && StringUtils.isNotBlank(userEventReq.getPkgChannel())) {
            ToutiaoClick sdkClick = oceanengineSdkService.querySdkClick(userEventReq, false, false);
            // 只有第一次sdk点击才会被使用
            if (null != sdkClick) {
                // 只有用trackId查询到点击才使用
                String trackClickFlag = redisClickService.getClickByTrackId(sdkClick,false);
                if (trackClickFlag == null) {
                    trackClickFlag = redisClickService.getClickByTrackId(sdkClick,true);
                }
                // if (null != trackClickFlag) {
                // 只有第一次查询出的sdk点击才会使用
                String key = sdkClick.getProduct() + ":" + sdkClick.getTrackId();
                if (bpDispenseJedisClusterClient.get(key) == null) {
                    bpDispenseJedisClusterClient.setex(key, 86400 * 3, "1");
                    // 将点击按照时间进行排序
                    clickList = ocpcEventService.getDspClickList(clickList);
                    //精准点击未找到，直接使用sdk归因匹配
                    if (clickList.isEmpty()) {
                        if (trackClickFlag == null) {
                            log.info("增量sdk根据trackId未找到对应点击 {}", JSONObject.toJSONString(sdkClick));
                            sdkClick.setGuiType(GuiyingType.sdkNoClick.name);
                            userEventReq.setGuiType(GuiyingType.sdkNoClick.name);
                        }else {
                            log.info("通过trackId找到点击 sdkClick {} trackId {}", JSONObject.toJSONString(sdkClick),sdkClick.getTrackId());
                        }
                        log.info("未找到精准点击 使用sdk进行归因 click {} key {}", JSONObject.toJSONString(sdkClick), key);
                        clickList.add(sdkClick);
                        //找到精准点击且渠道为头条，替换为sdk归因匹配
                    } else if ("toutiao".equals(clickList.get(0).getDsp())) {
                        log.info("精准点击为头条渠道 替换sdk进行归因 click {} gyType {} key {}", JSONObject.toJSONString(sdkClick), clickList.get(0).getGuiType(), key);
                        if (trackClickFlag == null) {
                            log.info("根据trackId未找到对应点击 {}", JSONObject.toJSONString(sdkClick));
                        }
                        // 替换为sdkClick；
                        sdkClick.setGuiType(clickList.get(0).getGuiType());
                        clickList.set(0, sdkClick);
                    }
                } else {
                    log.info("sdk点击结果非首次查询 key {} click {}", key, JSONObject.toJSONString(sdkClick));
                }
                /*}else {
                    log.info("根据trackId未找到对应点击,不使用sdk点击 {}", JSONObject.toJSONString(sdkClick));
                }*/
            }
        }
        // 如果需要sdk归因重试，则返回空list，不进行归因
        if (userEventReq.getRetrySdkGy() != null && userEventReq.getRetrySdkGy()
                && ocpcSwitcher.retrySdkGyProductList.contains(userEventReq.getProduct())) {
            // 将数据存入数据库，后续异步进行sdk进行归因
            ToutiaoSdkGyRetry toutiaoSdkGyRetry = new ToutiaoSdkGyRetry();
            BeanUtils.copyProperties(userEventReq, toutiaoSdkGyRetry);
            toutiaoSdkGyRetry.setRetryCount(0);
            toutiaoSdkGyRetry.setCreateTime(new Date());
            toutiaoSdkGyRetry.setUpdateTime(new Date());
            toutiaoSdkGyRetryService.saveToutiaoSdkGyRetry(toutiaoSdkGyRetry);
            return new ArrayList<>();
        }

        //如果精准归因和sdk归因均没有结果则查询非精准归因
        if (clickList.isEmpty()) {
            ToutiaoClick obscureClick = redisClickService.getObscureClick(userEventReq, ocpcSwitcher.readOcpcHbaseSwitch);
            if (null != obscureClick) {
                clickList.add(obscureClick);
                // log.info("redis非精准匹配成功 click {}", JSONObject.toJSONString(obscureClick));
            }
            if (ocpcSwitcher.ksHbaseGy && clickList.isEmpty()) {
                ToutiaoClick obscureClick2 = redisClickService.getObscureClick(userEventReq, true);
                if (obscureClick2 != null) {
                    clickList.add(obscureClick2);
                    // log.info("hbase非精准匹配成功 click {}", JSONObject.toJSONString(obscureClick2));
                }
            }
        }
        return clickList;
    }

    private GyRecordDataLocal fillGyRecordData(UserEventReq userEventReq) {
        GyRecordDataLocal gyRecordData = new GyRecordDataLocal();
        gyRecordData.setLogday(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        gyRecordData.setOs(userEventReq.getOs());
        gyRecordData.setProduct(userEventReq.getProduct());
        gyRecordData.setUserId(userEventReq.getUserId());
        gyRecordData.setAndroidId(userEventReq.getAndroidId());
        gyRecordData.setOaid(userEventReq.getOaid());
        gyRecordData.setCaid(userEventReq.getCaid() != null ? userEventReq.getCaid() : userEventReq.getCaid2());
        gyRecordData.setIdfa(userEventReq.getIdfa());
        String sourceDeviceId = userEventReq.getSourceDeviceId();
        if (StringUtils.isNotBlank(sourceDeviceId) && (sourceDeviceId.length()<6 || sourceDeviceId.contains("0000") || sourceDeviceId.contains("null")
            || "default".equals(sourceDeviceId) || "undefined".equals(sourceDeviceId))) {
            gyRecordData.setDeviceId(null);
        }else {
            gyRecordData.setDeviceId(sourceDeviceId);
        }
        gyRecordData.setMac(userEventReq.getMac());
        gyRecordData.setIpua(userEventReq.concatIpua());
        gyRecordData.setOpenid(userEventReq.getOpenId());
        gyRecordData.setIunod(userEventReq.concatIpuaMd());
        gyRecordData.setIpmd(userEventReq.concatIpMd());
        gyRecordData.setIp(userEventReq.concatIp());
        return gyRecordData;
    }

    private void checkCiLiuCondition(UserEventReq userEventReq, List<ToutiaoClick> clickList, ToutiaoClick toutiaoClick) {
        //次留取前一天数据
        Date nowDay = DateUtils.getNowDayDate();
        // oppo 和 vivo的次留以转化为准，转化的第二天有次留才算作次留
        if (DspType.ciLiuByKeyEvent(toutiaoClick.getDsp())) {
            OcpcEvent keyEvent = queryKeyEvent(userEventReq);
            if (keyEvent != null && keyEvent.getCreateTime() != null && keyEvent.getCreateTime().getTime() < nowDay.getTime()) {
                log.info("按转化计算次留成功 dsp:{} keyEvent: {}", toutiaoClick.getDsp(), JSON.toJSONString(keyEvent));
                clickList.add(toutiaoClick);
            }
        } else {
            if(toutiaoClick.getCreateTime()!=null && toutiaoClick.getCreateTime().getTime() < nowDay.getTime()){
                clickList.add(toutiaoClick);
            }
        }
    }

    public String changePhoneAscribe(UserEventReq userEventReq) {
        // 查询最新的点击
        List<ToutiaoClick> newClickList = getNewClickList(userEventReq);

        if (!newClickList.isEmpty()) {
            if (newClickList.size() > 1) {
                newClickList = ocpcEventService.sortClickList(newClickList);
            }
            // 将归因结果存储到新表中

            return "click-"+newClickList.get(0).getDsp();
        }
        // 查询用户之前的归因信息 使用用户id、product、os
        OcpcEvent ocpcEvent = getUserIdActiveEventByRedis(userEventReq);
        if (ocpcEvent != null) {
            return "ocpcEvent-"+ocpcEvent.getDsp();
        }
       return null;
    }
}
