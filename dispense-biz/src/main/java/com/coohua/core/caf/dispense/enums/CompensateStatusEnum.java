package com.coohua.core.caf.dispense.enums;

public enum CompensateStatusEnum {

	FAILED(0, "失败"),SUCCESS(1, "成功");

	public Integer value;
	public String name;

	CompensateStatusEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public static CompensateStatusEnum getStatus(Integer value) {
		if (value != null) {
			CompensateStatusEnum[] otypes = CompensateStatusEnum.values();
			for (CompensateStatusEnum memberType : otypes) {
				if (value.equals(memberType.value)) {
					return memberType;
				}
			}
		}
		return null;
	}
}
