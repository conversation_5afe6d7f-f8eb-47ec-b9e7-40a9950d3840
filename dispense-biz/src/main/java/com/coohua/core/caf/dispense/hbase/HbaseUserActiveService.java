package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.utils.HBaseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class HbaseUserActiveService {
    @Resource
    private Connection hbaseConnection;

    static String userAcitveByUser = "userAcitveByUser";
    static String userAcitveEventByUser = "userAcitveByEvent";

    public void initUserAciveHtable(){
        try {
            HBaseUtils.initHadoopTable(hbaseConnection, userAcitveByUser, DateTimeConstants.SECONDS_PER_DAY);
        }catch (Exception e){
            log.error("",e);
        }

        try {
            HBaseUtils.initHadoopTable(hbaseConnection, userAcitveEventByUser, 3*365*DateTimeConstants.SECONDS_PER_DAY);
        }catch (Exception e){
            log.error("",e);
        }
    }
    public void saveUserEvent(UserEvent userEvent) {
        try {
            if (userEvent.getUserId() != null && StringUtils.isNotBlank(userEvent.getProduct())) {
                boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userAcitveEventByUser, userEvent.getUserId() + "@" + userEvent.getProduct(), Bytes.toBytes(JSON.toJSONString(userEvent)));
            } else {
                log.info("用户ID为空Hbase不存储 " + JSON.toJSONString(userEvent));
            }
        }catch (Exception e){
            log.error("",e);
        }
    }
    public UserEvent queryByHbaseActiveEvent(String userId, String product) {
        String rowKey = userId + "@" + product;
        String hbaseValue = HBaseUtils.searchDataFromHadoop(hbaseConnection, userAcitveEventByUser, rowKey);
        if(StringUtils.isNotBlank(hbaseValue)){
            UserEvent userActive = JSON.parseObject(hbaseValue,UserEvent.class);
            return userActive;
        }
        return null;
    }
    @Autowired
    HbaseLockUserActiveService hbaseLockUserActiveService;
    public void saveUserActive(UserActive userActive) {
        try {
            if (userActive.getUserId() != null && StringUtils.isNotBlank(userActive.getProduct())) {
                boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userAcitveByUser, userActive.getUserId() + "@" + userActive.getProduct(), Bytes.toBytes(JSON.toJSONString(userActive)));
                // 额外存储userActive，为反作弊使用
                HBaseUtils.saveToHadoop(hbaseConnection, userAcitveByUser, userActive.getUserId() + "@" + userActive.getAppId(), Bytes.toBytes(JSON.toJSONString(userActive)));
            } else {
                log.info("用户ID为空Hbase不存储 " + JSON.toJSONString(userActive));
            }

            if("ios".equalsIgnoreCase(userActive.getOs()) && StringUtils.isNotBlank(userActive.getCaid())&& StringUtils.isNotBlank(userActive.getProduct())){
                String hkey =userActive.getCaid() + "@" + userActive.getProduct();
                boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userAcitveByUser, hkey, Bytes.toBytes(JSON.toJSONString(userActive)));
                log.info("ios存储key成功 "+hkey);
            }

            hbaseLockUserActiveService.saveHbaseUserActByDevice(userActive);

        }catch (Exception e){
            log.error("",e);
        }

    }

    /**
     * 同@saveUserActive方法，但不捕获异常,
     *
     * @param userActive 用户活跃信息
     */
    public void saveUserActiveNoCatchError(UserActive userActive) {
        if (userActive.getUserId() != null && StringUtils.isNotBlank(userActive.getProduct())) {
            boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userAcitveByUser, userActive.getUserId() + "@" + userActive.getProduct(), Bytes.toBytes(JSON.toJSONString(userActive)));
            // 额外存储userActive，为反作弊使用
            HBaseUtils.saveToHadoop(hbaseConnection, userAcitveByUser, userActive.getUserId() + "@" + userActive.getAppId(), Bytes.toBytes(JSON.toJSONString(userActive)));
        } else {
            log.info("用户ID为空Hbase不存储 " + JSON.toJSONString(userActive));
        }

        if ("ios".equalsIgnoreCase(userActive.getOs()) && StringUtils.isNotBlank(userActive.getCaid()) && StringUtils.isNotBlank(userActive.getProduct())) {
            String hkey = userActive.getCaid() + "@" + userActive.getProduct();
            boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userAcitveByUser, hkey, Bytes.toBytes(JSON.toJSONString(userActive)));
            log.info("ios存储key成功 " + hkey);
        }

        hbaseLockUserActiveService.saveHbaseUserActByDevice(userActive);
    }



    public UserActive queryByHbase(String userId, String product) {
        String rowKey = userId + "@" + product;
        String hbaseValue = HBaseUtils.searchDataFromHadoop(hbaseConnection, userAcitveByUser, rowKey);
        if(StringUtils.isNotBlank(hbaseValue)){
            UserActive userActive = JSON.parseObject(hbaseValue,UserActive.class);
            return userActive;
        }
        return null;
    }
}
