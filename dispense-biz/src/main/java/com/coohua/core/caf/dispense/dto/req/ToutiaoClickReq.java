package com.coohua.core.caf.dispense.dto.req;


import lombok.Data;

@Data
public class ToutiaoClickReq {
    //dsp=toutiao&product=aishangxiaoshuiguo&os=android&account_id=****************&
    // ocpc_device_id=__IMEI__&ts=__TS__&callback_url=__CALLBACK_URL__&cid=__CID__&gid=__CAMPAIGN_ID__&pid=__AID__&oaid=__OAID__
    private String dsp;//头条
    private String product;//写死的
    private String os;//写死的
    private String account_id;//账户ID
    private String ocpc_device_id; // imei(md5) or idfa
    private String idfa2; // md5(idfa)
    private String ts;//时间戳
//    private String accountName;//账户名称
    private String callback_url;//回调URL
    private String cid;//创意ID
    private String gid;//广告组 id
    private String pid;//广告计划id
    private String oaid; // Android Q及更高版本的设备号
    private String oaid2; // md5(oaid)
    private String aidName;//计划名称
    private String groupName;//分组名称
    private String cidName;//广告创意名称
    private String clickId;
    /**
     * mac地址
     */
    private String mac;
    //广告位编码
    private String union_site;
    //androidId
    private String androidId;

    /**
     * 媒体投放系统获取的用户终端的公共IP地址
     * 样例：**************或240e:398:1c90:9d00:5513:b9c9:650a:d9d2
     */
    private String ip;

    /**
     * 用户代理(User Agent)，一个特殊字符串头，使得服务器能够识别客户使用的操作系统及版本、CPU类型、浏览器及版本、浏览器渲染引擎、浏览器语言、浏览器插件等。
     * 经过url encode，样例：News****.5+rv%3A7.4.5.23%5C%28iPhone%3B+iOS+12.4.1%3B+zh_CN%5C%29Cronet
     */
    private String ua;

    /**
     * 手机型号
     * 包含多种格式，如iPhone12,2 (urlencode之后为iPhone12%2c2)，iPhone X (urlencode之后为iPhone+X)，SM-A750GN (urlencode之后为SM-A750GN)
     */
    private String model;

    private String caid;
    private String caid2; // md5(caid)
    private String mid; //__MID3__ //针对巨量广告体验版，视频素材宏参数（下发原始素材id）

    private String trackId; // __TRACK_ID__ 用于给巨量实时归因接口匹配
}
