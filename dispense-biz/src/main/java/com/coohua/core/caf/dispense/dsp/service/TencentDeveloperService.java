package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.apollo.TencentApolloConfig;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.mapper.TencentDeveloperMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.EnumCrowdType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.motan.service.UserEventRpcService;
import com.coohua.core.caf.dispense.ocpc.entity.*;
import com.coohua.core.caf.dispense.ocpc.service.*;
import com.coohua.core.caf.dispense.utils.IterateUtils;
import com.coohua.core.caf.dispense.utils.PairUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.constant.BaseConstants.*;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getEcpmLevel10;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getFixedUserEventReq;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.*;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.analyzeNewValueToMap;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
* <p>
    * 广告title 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2020-07-03
*/
@Service
@Slf4j
public class TencentDeveloperService extends ServiceImpl<TencentDeveloperMapper, TencentDeveloper> {

    @Value("${tencent.event.switch:false}")
    private Boolean tencentEventSwitch;

    /** 关键行为回传激活账户，主要用来测试视频号 */
    @ApolloJsonValue("${tencent.event.actAccount:[]}")
    private Set<String> tencentActiveAccounts;
    /** 关键行为回传注册账户，主要用来测试视频号 */
    @ApolloJsonValue("${tencent.event.regAccount:[]}")
    private Set<String> tencentRegisterAccounts;

    @Autowired
    TencentApolloConfig extApolloConfig;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    ThirdExtEventService thirdExtEventService;
    @Autowired
    HbaseEventService hbaseEventService;
    @Autowired
    TencentAccountConfigService tencentAccountConfigService;

    @Resource
    private AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    private Map<String, TencentDeveloper> tencentAccountMap = Maps.newHashMap();

    private Map<String, TencentAccountConfig> tencentAccountConfigMap = Maps.newHashMap();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();

    private Set<String> tencentChannelAccountSet = new HashSet<>();
    private Set<String> tencentChannelAccountSet2 = new HashSet<>();
    //    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void initAccountActionConfig() {
        List<TencentDeveloper> list = lambdaQuery()
                .isNotNull(TencentDeveloper::getAdvertiserId)
                .eq(TencentDeveloper::getDelFlag, DELFLAG.weishanchu.value)
                .list();

        tencentAccountMap = list.stream().collect(Collectors.toMap(k-> k.getAdvertiserId(), k->k, (oldVal, newVal)-> oldVal));

        appMap = list.stream()
                .filter(a -> a.getAppName() != null)
                .collect(Collectors.groupingBy(TencentDeveloper::getAppName,
                        Collectors.collectingAndThen(Collectors.toList(), apps -> apps.stream()
                                .flatMap(this::convertAction)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()))));

        tencentAccountConfigMap = tencentAccountConfigService.lambdaQuery()
                .eq(TencentAccountConfig::getDelFlag, DELFLAG.weishanchu.value)
                .list().stream()
                .collect(Collectors.toMap(k-> k.getAdvertiserId(), k->k));

        tencentChannelAccountSet = tencentActiveAccounts;
        tencentChannelAccountSet2 = tencentRegisterAccounts;
//        tencentChannelAccountSet = baseMapper.getChannelAccount();
    }

    public List<TencentDeveloper> getAllDevelops(int appId){
        return tencentAccountMap.values().stream().filter(k-> k.getAppId().equals(appId + "")).collect(Collectors.toList());
    }


    public TencentDeveloper getByAccountId(String  accountId){
        return tencentAccountMap.get(accountId);
    }

    @Autowired
    DeviceSundyService deviceSundyService;
    @Autowired
    UserBidService userBidService;
    @Autowired
    MatchCrowdBroadService matchCrowdBroadService;
    @Autowired
    ProductService productService;
    @Autowired
    UserEventRpcService userEventRpcService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    PeopleDevice30NoRetainService peopleDevice30NoRetainService;
    @Autowired
    RetainOneDayPeopleService retainOneDayPeopleService;

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();

            if (!tencentEventSwitch) {
                return matchDefaultConfig;
            }

            TencentDeveloper tencentDeveloper = getByAccountId(StringUtils.isNotBlank(toutiaoClick.getAccountId())?toutiaoClick.getAccountId():"");

            // 若无配置或未配置行为参数 仅当默认值时上报
            if (tencentDeveloper == null || StringUtils.isBlank(tencentDeveloper.getEventTypes()) || StringUtils.isBlank(tencentDeveloper.getEventValues())) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);


            javafx.util.Pair<String, DeviceSundy>  pair = deviceSundyService.queryByDevice(userEventReq);
            //通过开关判断是否要匹配低质量人群
            javafx.util.Pair<String, PeopleDevice30NoRetain> pair2 = new javafx.util.Pair<>(null, null);

            if (ocpcSwitcher.kuaishouCallBackTestSwitch) {
                //匹配低质量人群包
                log.info("匹配低质量人，userEventReq=" + JSONObject.toJSONString(userEventReq));
                pair2 = peopleDevice30NoRetainService.queryLowQualifyByDevice(userEventReq);
            }
            //查询TL 7天留存一天用户
            javafx.util.Pair<String, Integer> retainOneDayPeoplePair = retainOneDayPeopleService.queryRetainOneDayPeopleByDevice(userEventReq);

            Integer retainOneDayPeople = retainOneDayPeoplePair.getValue();
            DeviceSundy deviceSundy = pair.getValue();
            PeopleDevice30NoRetain peopleDevice30NoRetain = pair2.getValue();

            int sundyCpaType = 0; //周人群cpa
            int lowQualityCpaType = 0; //低质量人群cpa
            int retainOneDayCapType = 0; //
            if (deviceSundy != null) {
                sundyCpaType = deviceSundy.getCpaType();
            }
            if (peopleDevice30NoRetain != null) {
                lowQualityCpaType = peopleDevice30NoRetain.getCapType();
            }

            if (retainOneDayPeople != null) {
                retainOneDayCapType = retainOneDayPeople;
            }

            //如果retainOneDayCpaType最大则走bid出价回传
            if (retainOneDayCapType != 0 && retainOneDayCapType > sundyCpaType && retainOneDayCapType > lowQualityCpaType) {
                //当前产品是否走cpa回传
                boolean cpaCallBack = ocpcSwitcher.callBackProductList.isEmpty() || ocpcSwitcher.callBackProductList.contains(userEventReq.getProduct());
                if (cpaCallBack) {
                    log.info("bidCallBackSwitcher {} sundyCpaType {} lowQualityCpaType{} firstCallback{}",ocpcSwitcher.bidCallBackSwitcher,sundyCpaType,lowQualityCpaType,firstTimeJudge.getAsBoolean());
                }
                //CpaType >=2的人需要判断 (收入-提醒) > cpa 回传逻辑
                if (ocpcSwitcher.bidCallBackSwitcher && cpaCallBack && retainOneDayCapType >= 2 && firstTimeJudge.getAsBoolean()){
                    if (cpaCanCallBack(userEventReq)){
                        log.info("tencent cpaType>=2 当前用户可以回传 userId {} product {}",userEventReq.getUserId(),userEventReq.getProduct());
                        MatchCrowdBroadNew matchCrowdBroadNew = new MatchCrowdBroadNew();
                        //入库统计数据
                        matchCrowdBroadNew.setSource("tencent");
                        matchCrowdBroadNew.setUserId(userEventReq.getUserId());
                        matchCrowdBroadNew.setProduct(userEventReq.getProduct());
                        matchCrowdBroadNew.setOs(userEventReq.getOs());
                        matchCrowdBroadNew.setCanCallback(0);
                        matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                        return true;
                    }else {
                        log.info("tencent cpaType>=2 当前用户不能回传 userId {} product {}",userEventReq.getUserId(),userEventReq.getProduct());
                        return false;
                    }
                }
            }


            EnumCrowdType crowdType = null;
            String oldEventValues = null;
            String eventValues = null;
            MatchCrowdBroadNew matchCrowdBroadNew = new MatchCrowdBroadNew();
            matchCrowdBroadNew.setUserId(userEventReq.getUserId());
            matchCrowdBroadNew.setProduct(userEventReq.getProduct());
            matchCrowdBroadNew.setCanCallback(1);

            //判断谁的倍数高使用哪个人群
            Integer capType = deviceSundyService.compareMultiple(userEventReq.getProduct(), deviceSundy != null ? deviceSundy.getOs() : "", sundyCpaType, lowQualityCpaType);

            if (pair.getValue() != null && capType.equals(pair.getValue().getCpaType())) {
                userEventReq.setUarpuGy(pair.getKey());
                 eventValues = deviceSundyService.authAccountAppFl(userEventReq,pair.getValue(),tencentDeveloper.getEventTypes(),tencentDeveloper.getEventValues(),tencentDeveloper.getAdvertiserId(),"ten");
                oldEventValues = tencentDeveloper.getEventValues();
                crowdType = EnumCrowdType.SUNDAY_CROWD;
                if(!StringUtils.equalsIgnoreCase(tencentDeveloper.getEventValues(),eventValues)){
                    log.info("tencent etypes赋值改变 "+tencentDeveloper.getEventValues()+"->"+eventValues+" capType {}",capType);
                    // tencentDeveloper.setEventValues(eventValues);
                }
            } else if (pair2.getValue() != null && capType.equals(pair2.getValue().getCapType())) {
                userEventReq.setUarpuGy(pair2.getKey());
                eventValues = deviceSundyService.authAccountAppFl(userEventReq, pair2.getValue(), tencentDeveloper.getEventTypes(), tencentDeveloper.getEventValues(), tencentDeveloper.getAdvertiserId(), "ten");
                oldEventValues = tencentDeveloper.getEventValues();
                crowdType = EnumCrowdType.LOW_QUALITY_CROWD;
                log.info("tencent etypes2赋值改变 " + tencentDeveloper.getEventValues() + "->" + eventValues + "命中低质量人群包 用户id {} 产品名称 {} cpaType {}", userEventReq.getUserId(), userEventReq.getProduct(),capType);
                // tencentDeveloper.setEventValues(eventValues);
            }

            fillMatchCrowdBroadTencent(userEventReq, matchCrowdBroadNew, crowdType, oldEventValues, eventValues);
            matchCrowdBroadNew.setCpaType(capType);

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                if (matchNewRule(userEventReq, tencentDeveloper.getEventTypes(),
                        eventValues == null?tencentDeveloper.getEventValues():eventValues, firstTimeJudge, "广点通", tencentDeveloper.getAppName(),
                        tencentDeveloper.getAdvertiserId(), tencentDeveloper.getMaxAccumulatePeriod())){
                    matchCrowdBroadNew.setCanCallback(0);
                    matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                    return true;
                }else if (StringUtils.isNotBlank(oldEventValues)) {
                    if (matchNewRule(userEventReq, tencentDeveloper.getEventTypes(),
                            oldEventValues, firstTimeJudge, "广点通", tencentDeveloper.getAppName(),
                            tencentDeveloper.getAdvertiserId(), tencentDeveloper.getMaxAccumulatePeriod()) && capType > 0){
                        matchCrowdBroadNew.setCanCallback(1);
                        matchCrowdBroadNew.setOriginalCanCallback(0);
                    }
                    matchCrowdBroadNew.setOriginalCanCallback(1);
                    matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                    return false;
                }
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                 boolean result =  matchNewRule(userEventReq, tencentDeveloper.getEventTypes(),
                         eventValues == null?tencentDeveloper.getEventValues():eventValues, firstTimeJudge, "广点通", tencentDeveloper.getAppName(),
                        tencentDeveloper.getAdvertiserId(), tencentDeveloper.getMaxAccumulatePeriod());
                if (result) {
                    matchCrowdBroadNew.setCanCallback(0);
                }else {
                    matchCrowdBroadNew.setCanCallback(1);
                    matchCrowdBroadNew.setOriginalCanCallback(1);
                    if (StringUtils.isNotBlank(oldEventValues)&&matchNewRule(userEventReq, tencentDeveloper.getEventTypes(),
                            oldEventValues, firstTimeJudge, "广点通", tencentDeveloper.getAppName(),
                            tencentDeveloper.getAdvertiserId(), tencentDeveloper.getMaxAccumulatePeriod()) && capType > 0){
                        matchCrowdBroadNew.setOriginalCanCallback(0);
                    }
                }
                matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                return result;
            }
        } catch (Exception e) {
            log.error("checkActionCount error", e);
        }

        return false;
    }

    private void fillMatchCrowdBroadTencent(UserEventReq userEventReq, MatchCrowdBroadNew matchCrowdBroadNew, EnumCrowdType crowdType, String oldEventValues, String eventValues) {
        matchCrowdBroadNew.setSource(crowdType == null ? "" : crowdType.name());
        matchCrowdBroadNew.setProduct(userEventReq.getProduct());
        matchCrowdBroadNew.setUserId(userEventReq.getUserId());
        matchCrowdBroadNew.setOldEventValues(oldEventValues);
        matchCrowdBroadNew.setNewEventValues(eventValues);
        matchCrowdBroadNew.setOs(userEventReq.getOs());
    }

    private Boolean cpaCanCallBack(UserEventReq userEventReq) {
        if (userEventReq.getActionValues() == null) {
            return false;
        }
        log.info("Tencent用户信息 userEventReq {}",JSONObject.toJSONString(userEventReq));
        //查询用户的提现和出价
        Map userBidMap = userBidService.queryUserCpaBy(userEventReq.getUserId(), userEventReq.getProduct());
        if (userBidMap == null || userBidMap.get("withdraw") == null || userBidMap.get("bid") == null) {
            return false;
        }
        Double withdraw = (Double) userBidMap.get("withdraw");
        Double bid = (Double) userBidMap.get("bid");
        //根据产品名称查询appiId
        Product product = productService.getByName(userEventReq.getProduct());
        if (product == null) {
            return false;
        }
        //查询ecpm、pv
        String mapStr = userEventRpcService.queryUserECPMBatch(userEventReq.getOs(), product.getAppId(), Arrays.asList(Long.parseLong(userEventReq.getUserId())));
        log.info("ecpm、pv查询 mapStr {} userId {}",mapStr,userEventReq.getUserId());
        Map map = JSONObject.parseObject(mapStr, Map.class);
        if (map == null || map.get("ecpm") == null || map.get("pv") == null) {
            return false;
        }
        Double ecpm = (Double) map.get("ecpm");
        Double pv = (Double) map.get("pv");
        log.info("当前用户收入、提现、出价 userId={} product={} income={} withdraw={} bid={}",userEventReq.getUserId(),userEventReq.getProduct(),ecpm*pv,withdraw,bid);
        return (ecpm * pv - withdraw) / bid >= ocpcSwitcher.callBackMutiple;
    }


    public Map<String, List<AccountAction>> queryActionConfig() {
        if (tencentEventSwitch) {
            return appMap;
        } else {
            return new HashMap<>();
        }
    }

    public List<AccountAction> queryActionConfig(String appName) {
        if (tencentEventSwitch) {
            return appMap.getOrDefault(appName, Lists.newArrayList());
        } else {
            return Lists.newArrayList();
        }
    }

    private Stream<AccountAction> convertAction(TencentDeveloper tencentDeveloper) {
        try {
            // 无配置
            if (StringUtils.isBlank(tencentDeveloper.getEventTypes())) {
                return null;
            }

            if (StringUtils.isBlank(tencentDeveloper.getEventValues()) || StringUtils.isBlank(tencentDeveloper.getEventTypes())) {
                return Stream.empty();
            }

            return AuthKuaishouAdvertiserService.buildEventStandardConsideringMultiEcpm(tencentDeveloper.getEventTypes(),
                    tencentDeveloper.getEventValues(), new BigInteger(tencentDeveloper.getAdvertiserId()),
                    tencentDeveloper.getMaxAccumulatePeriod());

        }catch (Exception e){
            log.error("广点通广告主配置信息解析出错 ",e);
        }

        return null;
    }

    public void tryCallbackExtDefine(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        TencentDeveloper advertiser = getTencentAdvertiserForExtEvent(toutiaoClick, userEventReq);

        if (advertiser == null) {
            return;
        }

        ThirdAdvertiser thirdAdvertiser = new ThirdAdvertiser(advertiser);
        DspType dspType = thirdAdvertiser.getDsp();

        boolean locked = false;
        int eventTypeKey = ToutiaoEventTypeEnum.KEY_ACTION_EXT_EVENT.value;
        String lockKey = RedisKeyConstants.getReportLockKey(userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
        try {
            if (extApolloConfig.lockExtDefineAllEnable || extApolloConfig.lockExtDefineEnableProducts.contains(userEventReq.getProduct())) {
                // 要使用分布式锁保证回传顺序执行
                locked = ocpcEventService.tryGetDistributedLock(lockKey, "1", 10000);

                if (!locked) {
                    log.warn("腾讯衍生上报并发抢占资格失败 {} {} {}", userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
                    return;
                }

                if (NUM_ARPU_RULE.equals(advertiser.getEventTypes())) {
                    // 获取符合的关键行为衍生事件
                    List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, userEventReq);
                    if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                        OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                        List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_2(dspType, matchExtEvents, ocpcEvent ,
                                ()-> checkExtDefineCount(toutiaoClick, userEventReq, advertiser));
                        ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                    }
                } else if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())) {
                    // ecpm需要先判断是否符合账户的配置条件
                    Pair<Boolean, ActionTypes> actionTypesPair = checkExtDefineCount(toutiaoClick, userEventReq, advertiser);
                    if (actionTypesPair.getLeft()) {
                        ActionTypes ecpmLevel10 = getEcpmLevel10(advertiser.getEventValues());
                        UserEventReq fixedUserEventReq = getFixedUserEventReq(userEventReq, ecpmLevel10);
                        // 获取符合的关键行为衍生事件
                        List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, fixedUserEventReq);
                        if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                            OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                            List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_3(dspType, ecpmLevel10, matchExtEvents, ocpcEvent);
                            ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                        }
                    }
                }

            }
        } catch (Exception e) {
            log.error("衍生事件分布式锁抢占出现异常 ", e);
        } finally {
            if (locked) {
                ocpcEventService.unlockDistributedLock(lockKey);
            }
        }
    }

    private TencentDeveloper getTencentAdvertiserForExtEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return null;
            }

            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            if (dspType != DspType.GUANGDIANTONG) {
                return null;
            }

            // 只有新链路回传衍生
            if (!TencentUserActionService.useV2Callback(toutiaoClick.getCallbackUrl())) {
                return null;
            }

            // 限制产品
            if (!extApolloConfig.extEnableAll && !extApolloConfig.extEnableProducts.contains(userEventReq.getProduct())) {
                return null;
            }

            TencentDeveloper advertiser = getByAccountId(toutiaoClick.getAccountId());

            //若无配置 不上报
            if (advertiser == null || StringUtils.isAnyBlank(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // 分时arpu账户 不上报
            if (isMultiArpu(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // 次数ecpm账户，若没有10这一档位时 不上报
            if (checkEcpmHasNotLevel10(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            Map<String, String> reportValues = analyzeNewValueToMap(userEventReq.getActionValues());
            String reqEventTypes = reportValues.keySet().stream().sorted(Comparator.comparing(Integer::valueOf)).collect(Collectors.joining(","));
            // 若请求的类型和当前账户的类型不一致，不上报
            if (!Objects.equals(reqEventTypes, advertiser.getEventTypes())) {
                return null;
            }

            // 限制适用类型
            if (!extApolloConfig.extEnableEventTypes.contains(advertiser.getEventTypes())) {
                return null;
            }

            // 限制适用账户
            if (!extApolloConfig.extEnableAllAccount && !extApolloConfig.extEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                return null;
            }

            // 对于1,3的账户限制产品
            if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())
                    && !extApolloConfig.extEnableAllFor_1_3
                    && !extApolloConfig.extEnableProductsFor_1_3.contains(userEventReq.getProduct())) {
                return null;
            }

            return advertiser;

        } catch (Exception e) {
            log.error("getTencentAdvertiserForExtEvent error ", e);
        }
        return null;
    }

    /**
     * 检查衍生事件计数
     * @return
     */
    public Pair<Boolean, ActionTypes> checkExtDefineCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq, TencentDeveloper advertiser) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return PairUtil.ofFalse();
            }

            //非快手不回传衍生定义
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            if (dspType != DspType.GUANGDIANTONG) {
                return PairUtil.ofFalse();
            }

            //若无配置 仅当默认值时上报
            if (advertiser == null) {
                return PairUtil.ofFalse();
            }

            // 限制适用类型
            if (!extApolloConfig.extEnableEventTypes.contains(advertiser.getEventTypes())) {
                return PairUtil.ofFalse();
            }

            // 限制适用账户
            if (!extApolloConfig.extEnableAllAccount && !extApolloConfig.extEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                return PairUtil.ofFalse();
            }

            // 不需判断是否首次满足条件
            BooleanSupplier firstTimeJudge = () -> true;

            Pair<Boolean, ActionTypes> matchNewRuleWithResult = matchNewRuleWithResult(userEventReq, advertiser.getEventTypes(),
                    advertiser.getEventValues(), firstTimeJudge, "广点通",
                    advertiser.getAppName(), advertiser.getAdvertiserId(), advertiser.getMaxAccumulatePeriod());
            if (matchNewRuleWithResult.getLeft()) {
                return matchNewRuleWithResult;
            }

        } catch (Exception e) {
            log.error("checkExtEventCount error ", e);
        }
        return PairUtil.ofFalse();
    }

    public boolean isKeyActionWhiteAccount(String accountId) {
        Integer keyActionWhite = Optional.ofNullable(tencentAccountConfigMap.get(accountId)).map(k -> k.getKeyActionWhite()).orElse(null);
        boolean result = Objects.equals(keyActionWhite, 1);
        if (result) {
            log.info("广点通关键行为加白账户不回传注册和下单 " + accountId);
        }
        return result;
    }

    public boolean isChannelAccount(String accountId) {
        boolean result = tencentChannelAccountSet.contains(accountId);
        if (result) {
            log.info("广点通微信视频号版位关键行为回传激活 " + accountId);
        }
        return result;
    }

    public boolean isKeyRegisterAccount(String accountId) {
        boolean result = tencentChannelAccountSet2.contains(accountId);
        if (result) {
            log.info("广点通关键行为回传注册 " + accountId);
        }
        return result;
    }

    public void setEventValues(List<ProductAdvertiserPool> advertiserPools) {
        IterateUtils.iterateByStepSize(advertiserPools, 5000, data -> {
            List<TencentDeveloper> list = this.lambdaQuery().select(TencentDeveloper::getAdvertiserId, TencentDeveloper::getEventValues)
                    .in(TencentDeveloper::getAdvertiserId, data.stream().map(ProductAdvertiserPool::getAccountId).collect(toList()))
                    .eq(TencentDeveloper::getEventTypes, BaseConstants.NUM_ARPU_RULE)
                    .list();
            Map<String, String> eventValues = list.stream().collect(toMap(TencentDeveloper::getAdvertiserId, TencentDeveloper::getEventValues, (o1, o2) -> o1));
            data.forEach(o -> o.setEventValues(eventValues.get(o.getAccountId())));
        });
    }
}
