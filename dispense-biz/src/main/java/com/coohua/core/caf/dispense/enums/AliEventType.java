package com.coohua.core.caf.dispense.enums;

public enum AliEventType {
    // 30天内第一次打开APP
    ACTIVATE_APP(1, "activate", "激活"),
    // 用户激活后第二天打开APP的行为
    START_APP(1001, "retain_1day", "次日留存"),

    KEY_ACTION(1023, "key_action", "关键行为"),
    ;
    public Integer value;
    public String desc;
    public DspType dspType;
    public String name;

    AliEventType(Integer value, String name, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

    AliEventType(Integer value, String name, DspType dspType, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
        this.dspType = dspType;
    }

    public static AliEventType getStatus(Integer value, DspType dspType) {
        if (value != null) {
            AliEventType[] otypes = AliEventType.values();
            for (AliEventType memberType : otypes) {
                if ((memberType.dspType == null || memberType.dspType == dspType) && value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
