package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.entity.XiaomiAction;
import com.coohua.core.caf.dispense.dsp.entity.XiaomiAdvertiser;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dto.req.XiaomiActiveReq;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.XiaomiEventType;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_URL_BODY;

@Component
@Slf4j
public class XiaomiUserEventService {
    @Autowired
    AlertService alertService;
    @Autowired
    XiaomiAdvertiserService xiaomiAdvertiserService;
    @Autowired
    RedisEventService redisEventService;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {
        Date date = new Date();
        try {
            /**
             * 转化数据类型：1、激活，2、注册，4、次留， 8、自定义目标
             */
            XiaomiEventType xiaomiEventType = XiaomiEventType.parse(userEventReq.getEventType());
            if (xiaomiEventType == null) {
                userEvent.setReqRsp("xiaomi无需回传的类型");
                return false;
            }

            XiaomiAdvertiser xiaomiAdvertiser = xiaomiAdvertiserService.getByAccountId(toutiaoClick.getAccountId());
            if(xiaomiAdvertiser==null){
                log.error("xiaomi无对应账户配置 " + toutiaoClick.getAccountId());
                userEvent.setReqRsp("xiaomi无对应账户配置");
                return false;
            }

            XiaomiAction xiaomiAction = xiaomiAdvertiserService.getAction(toutiaoClick.getAccountId(), xiaomiEventType.val);
            if(xiaomiAction==null){
                log.error("xiaomi无对应账户下行为配置 {} {}", toutiaoClick.getAccountId(), xiaomiEventType.val);
                userEvent.setReqRsp("xiaomi无对应账户下行为配置 " + xiaomiEventType.val);
                return false;
            }

            XiaomiActiveReq req = new XiaomiActiveReq();

            // 需要判断激活事件是通过哪个字段归因到的
            DeviceType deviceType;
            if (xiaomiEventType == XiaomiEventType.ACTIVATE_APP) {
                deviceType = StringUtils.isNotBlank(toutiaoClick.getOcpcDeviceId()) ? DeviceType.device : DeviceType.oaid;
            } else {
                deviceType = redisEventService.checkActiveEventFrom(userEventReq);
            }

            if (deviceType == DeviceType.device) {
                req.setImeiMd5(userEventReq.getOcpcDeviceId());
            } else if (deviceType == DeviceType.oaid) {
                req.setOaid(userEventReq.getOaid());
            } else {
                log.info("xiaomi回传未匹配到imei或oaid {} {} {} ", toutiaoClick.getAccountId(), JSON.toJSONString(toutiaoClick), JSON.toJSONString(userEventReq));
                userEvent.setReqRsp("xiaomi回传未匹配到imei或oaid");
                return false;
            }

            req.buildParams(xiaomiEventType, xiaomiAction, toutiaoClick.getCallbackUrl());

            String rspStr = sendXiaomi(xiaomiAdvertiser, req, toutiaoClick.getProduct());

            if (userEvent != null) {
                userEvent.setReqUrl(req.getReqUrl() + DELIMITER_URL_BODY + req.getInfoJson());
                userEvent.setReqRsp(rspStr);
            }
        } catch (Exception e) {
            log.error("xiaomi userEvent unknown error. userEventReq: {}, toutiaoClick: {}", userEventReq, toutiaoClick, e);
            return false;
        }
        return false;
    }

    public String sendXiaomi(XiaomiAdvertiser xiaomiAdvertiser, XiaomiActiveReq req, String product) {
        try {
            String accountId = xiaomiAdvertiser.getAdvertiserId();

            CloseableHttpClient httpClient = getHttpClient();
            HttpConfig config = HttpConfig.custom()
                    .url(req.getReqUrl())
                    .client(httpClient);

            String rspStr = HttpClientUtil.get(config);
            log.info("xiaomi回调结果 {} {} ,res: {} ,json: {} ,reqUrl: {}", accountId, product, rspStr, req.getInfoJson(), req.getReqUrl());

            JSONObject jobj = JSON.parseObject(rspStr);
            int code = jobj.getInteger("code");
            if (code == 0) {
                alertService.report(product, accountId, "xiaomi回调成功");
            } else {
                alertService.report(product, accountId, "xiaomi回调失败");
            }
            return rspStr;
        } catch (Exception e) {
            log.error("xiaomi userEvent unknown error. xiaomiActiveReq: " + req.getInfoJson(), e);
            return null;
        }
    }

    private static CloseableHttpClient getHttpClient() {
        CloseableHttpClient httpclient = HttpClients.custom().build();
        return httpclient;
    }

}
