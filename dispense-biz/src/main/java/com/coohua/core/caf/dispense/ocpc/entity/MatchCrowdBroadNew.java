package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="MatchCrowdBroad对象", description="")
public class MatchCrowdBroadNew {
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "匹配人群包来源")
    private String source;
    @ApiModelProperty(value = "用户id")
    private String userId;
    @ApiModelProperty(value = "设备类型")
    private String os;
    @ApiModelProperty(value = "产品名称")
    private String product;
    private Integer cpaType;
    @ApiModelProperty(value = "用户能否回传")
    private Integer canCallback;
    @ApiModelProperty(value = "旧事件阈值")
    private String oldEventValues;
    @ApiModelProperty(value = "新事件阈值")
    private String newEventValues;
    @ApiModelProperty(value = "原始值能否回传")
    private Integer originalCanCallback;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
