package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.dsp.entity.BaiduAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ThirdExtEvent;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.BaiduEventType;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_MULTI_URL;

@Service
@Slf4j
public class BaiduUserEventService {

    @Autowired
    private BaiduAdvertiserService baiduAdvertiserService;
    @Autowired
    private AlertService alertService;

    @Autowired
    OcpcSwitcher ocpcSwitcher;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {

        try {
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            BaiduEventType baiduEventType = BaiduEventType.getStatus(userEventReq.getEventType(), dspType);

            if (baiduEventType == null) {
                log.info("百度不需回传的类型 {} {} {} {}", baiduEventType, dspType, userEventReq, toutiaoClick);
                return false;
            }
            if (ocpcSwitcher.baiduKeyAccount.contains(toutiaoClick.getAccountId()) || baiduAdvertiserService.actionAdvertiser(toutiaoClick.getAccountId())) {
                log.info("百度回传不需要转换关键行为 {} {} {} {}", baiduEventType, dspType, toutiaoClick.getAccountId(), userEventReq);
                if (userEventReq.getEventType() == ToutiaoEventTypeEnum.KEY_EVENT.value) {
                    requestBaidu(toutiaoClick, userEvent, BaiduEventType.KEY_ACTION, true);
                }

                if (userEventReq.getEventType() == ToutiaoEventTypeEnum.ACTIVATE_APP.value) {
                    requestBaidu(toutiaoClick, userEvent, BaiduEventType.ACTIVATE_APP, true);
                }

            }else{
                if (userEventReq.getEventType() == ToutiaoEventTypeEnum.KEY_EVENT.value) {
                    requestBaidu(toutiaoClick, userEvent, BaiduEventType.ACTIVATE_APP, true);
                }

                if (userEventReq.getEventType() == ToutiaoEventTypeEnum.ACTIVATE_APP.value) {
                    requestBaidu(toutiaoClick, userEvent, BaiduEventType.USER_DEFINED, true);
                }

            }

//            else if(userEventReq.getEventType() == ToutiaoEventTypeEnum.START_APP.value) {
//                requestBaidu(toutiaoClick, userEvent, BaiduEventType.START_APP);
//            }

        } catch (Exception e) {
            log.error(String.format("百度关键行为回传异常 userEventReq: %s, toutiaoClick: %s", JSON.toJSONString(userEventReq), JSON.toJSONString(toutiaoClick)), e);
            return false;
        }
        return false;
    }


    private void requestBaidu(ToutiaoClick toutiaoClick, UserEvent userEvent, BaiduEventType baiduEventType) throws Exception {
        requestBaidu(toutiaoClick, userEvent, baiduEventType, false);
    }

    /**
     * @param isExtraCallback 是否是额外的回传
     */
    private void requestBaidu(ToutiaoClick toutiaoClick, UserEvent userEvent, BaiduEventType baiduEventType, boolean isExtraCallback) throws Exception {
        try {
            String callbackUrl = toutiaoClick.getCallbackUrl();
            callbackUrl = callbackUrl.replace("{{ATYPE}}", baiduEventType.name);
            callbackUrl = callbackUrl.replace("{{AVALUE}}", "0");
            BaiduAdvertiser baiduAdvertiser = baiduAdvertiserService.getByAccountId(toutiaoClick.getAccountId());
            String url = callbackUrl + "&sign=" + DigestUtils.md5Hex(callbackUrl + baiduAdvertiser.getAkey());

            log.info("百度待回传事件 {} {}", baiduEventType, url);
            String html = HttpClientUtil.get(HttpConfig.custom().timeout(3000).url(url));

            JSONObject jobj = JSON.parseObject(html);
            if (jobj != null && Objects.equals(jobj.getInteger("error_code"), 0)) {
                log.info("百度回调成功 {} url:{}, res:{}", baiduEventType, url, html);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "百度回调成功");
            } else {
                log.info("百度回调失败 {} url:{}, res:{}", baiduEventType, url, html);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "百度回调失败");
            }
            if (userEvent != null) {
                // 额外的回传通过分隔符累加在后面
                userEvent.setReqUrl(!isExtraCallback ? url : userEvent.getReqUrl() + DELIMITER_MULTI_URL + url);
                userEvent.setReqRsp(!isExtraCallback ? html : userEvent.getReqRsp() + DELIMITER_MULTI_URL + html);
            }
        } catch (Exception e) {
            log.error("百度回调失败 ", e);
        }
    }


    /**
     * 腾讯关键行为衍生回传
     */
    public void callbackExtEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userExtEvent, ThirdExtEvent thirdExtEvent, long callbackSecond) {

        DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
        BaiduEventType baiduEventType = BaiduEventType.getStatus(userEventReq.getEventType(), dspType);
        String accountId =  toutiaoClick.getAccountId();

        String reqUrl = null;
        String html = null;
        try {
            String callbackUrl = toutiaoClick.getCallbackUrl();
            callbackUrl = callbackUrl.replace("{{ATYPE}}", baiduEventType.name);
            callbackUrl = callbackUrl.replace("{{AVALUE}}", "1");
             // 1,2 先传1,8
            String actionType =  Objects.equals(thirdExtEvent.getEventTypes(), BaseConstants.NUM_ARPU_RULE) ?  "1,8" : thirdExtEvent.getEventTypes();
            String actionValue = thirdExtEvent.getValue1() + "," + (Objects.equals(thirdExtEvent.getEventTypes(), BaseConstants.NUM_ECPM_RULE) ?  thirdExtEvent.getValue2() :thirdExtEvent.getValue3());
            callbackUrl = callbackUrl + "&action_type=" + actionType + "&action_value=" + actionValue +
                    "&action_ts=" + callbackSecond + "&is_ga_convert=" + ObjectUtils.defaultIfNull(thirdExtEvent.getIsGaConvert(), 0L)+ "&depth=" + thirdExtEvent.getDepth();
            BaiduAdvertiser baiduAdvertiser = baiduAdvertiserService.getByAccountId(toutiaoClick.getAccountId());
            reqUrl = callbackUrl + "&sign=" + DigestUtils.md5Hex(callbackUrl + baiduAdvertiser.getAkey());

            log.info("百度待回传事件 {} {}", baiduEventType, reqUrl);
             html = HttpClientUtil.get(HttpConfig.custom().timeout(3000).url(reqUrl));
            JSONObject jobj = JSON.parseObject(html);
            if (jobj != null && Objects.equals(jobj.getInteger("error_code"), 0)) {
                log.info("百度衍生回传 成功 {} url:{}, res:{}", baiduEventType, reqUrl, html);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "百度衍生回调成功");
            } else {
                log.info("百度衍生回传 失败 {} url:{}, res:{}", baiduEventType, reqUrl, html);
                alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "百度衍生回调失败");
            }

        } catch (Exception e) {
            log.info("百度衍生回传 异常 {} {} {} , req {} reqBody {}" , accountId, baiduEventType.name ,html, reqUrl, e);
            log.info("百度衍生回传 异常 " + accountId , e);
        }
        if(userExtEvent !=null){
            userExtEvent.setReqRsp(html);
            userExtEvent.setReqUrl(reqUrl);
        }
    }

}
