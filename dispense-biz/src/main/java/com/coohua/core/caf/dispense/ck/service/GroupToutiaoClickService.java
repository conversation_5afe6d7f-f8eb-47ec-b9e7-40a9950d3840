package com.coohua.core.caf.dispense.ck.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ck.entity.GroupToutiaoClickLocal;
import com.coohua.core.caf.dispense.ck.entity.GyRecordDataLocal;
import com.coohua.core.caf.dispense.ck.mapper.GroupToutiaoClickMapper;
import com.coohua.core.caf.dispense.ck.mapper.GyRecordDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GroupToutiaoClickService extends ServiceImpl<GroupToutiaoClickMapper, GroupToutiaoClickLocal> {
    @Autowired
    GroupToutiaoClickMapper groupToutiaoClickMapper;

    public void saveGyRecordDataBatch(List<GroupToutiaoClickLocal> gyRecordDatas) {
        this.saveBatch(gyRecordDatas);
    }
}
