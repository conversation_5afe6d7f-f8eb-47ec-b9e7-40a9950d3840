package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.ExtEventApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.BaseConstants;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.mapper.AuthToutiaoAdvertiserMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.*;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.motan.service.UserEventRpcService;
import com.coohua.core.caf.dispense.ocpc.entity.*;
import com.coohua.core.caf.dispense.ocpc.service.*;
import com.coohua.core.caf.dispense.redis.PayEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.IterateUtils;
import com.coohua.core.caf.dispense.utils.PairUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.constant.BaseConstants.NUM_ARPU_RULE;
import static com.coohua.core.caf.dispense.constant.BaseConstants.NUM_ECPM_RULE;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getEcpmLevel10;
import static com.coohua.core.caf.dispense.dsp.service.ThirdExtEventService.getFixedUserEventReq;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.*;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

/**
 * <p>
 * 广告主 服务实现类
 * </p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthToutiaoAdvertiserService extends ServiceImpl<AuthToutiaoAdvertiserMapper, AuthToutiaoAdvertiser> implements IService<AuthToutiaoAdvertiser> {

    public Map<BigInteger, AuthToutiaoAdvertiser> accountActionMap = Maps.newHashMap();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();
    @ApolloJsonValue("${key.event.account:[********]}")
    private Set<Integer> keyEventAccount;
    @Autowired
    AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    @Autowired
    TencentDeveloperService tencentDeveloperService;

    @Resource
    private OcpcEventService ocpcEventService;

    @Resource
    private OcpcSwitcher ocpcSwitcher;

    @Resource
    private HbaseEventService hbaseEventService;

    @Autowired
    ExtEventApolloConfig extEventApolloConfig;
    @Autowired
    ThirdExtEventService thirdExtEventService;

    @Autowired
    VivoAdvertiserService vivoAdvertiserService;
    @Autowired
    OppoAdvertiserService oppoAdvertiserService;
    @Autowired
    BaiduAdvertiserService baiduAdvertiserService;
    @Autowired
    IqiyiAdvertiserService iqiyiAdvertiserService;
    @Autowired
    XiaomiAdvertiserService xiaomiAdvertiserService;
    @Autowired
    XimalayaAdvertiserService ximalayaAdvertiserService;
    @Autowired
    RedisEventService redisEventService;

    /**
     * 跳过mac判重的产品
     */
    @ApolloJsonValue("${skip.mac.check.apps:[]}")
    private Set<String> skipMacCheckApps;

    /**
     * 全部跳过mac判重
     */
    @Value("${all.skip.mac.check:false}")
    private boolean allSkipMacCheck;

    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void initAccountActionConfig() {
        List<AuthToutiaoAdvertiser> list = this.lambdaQuery().isNotNull(AuthToutiaoAdvertiser::getProductName).list();

        accountActionMap = list
                .stream()
                .collect(Collectors.toMap(AuthToutiaoAdvertiser::getAdvertiserId, Function.identity(), (a, b) -> a));

        appMap = list.stream()
                .collect(Collectors.groupingBy(AuthToutiaoAdvertiser::getProductName,
                        Collectors.collectingAndThen(toList(), advertisers -> advertisers.stream()
                                .flatMap(AuthToutiaoAdvertiserService::convertAction)
                                .filter(Objects::nonNull)
                                .collect(toList()))));
    }

    public static void main(String[] args) {
//        List<AuthToutiaoAdvertiser> list = new ArrayList<>();
//        list.add(new AuthToutiaoAdvertiser(){{
//            setAdvertiserId(new BigInteger("1"));
//            setProductName("a");
//            setEventTypes("1,2");
//            setEventValues("8/10/10,1.5/2.5/5,30/60/1440");
//            setMaxAccumulatePeriod(30);
//        }});
//        list.add(new AuthToutiaoAdvertiser(){{
//            setAdvertiserId(new BigInteger("2"));
//            setProductName("a");
//            setEventTypes("1,2");
//            setEventValues("10,1.5/2.5/5,40/60/1440");
//            setMaxAccumulatePeriod(30);
//        }});
//        list.add(new AuthToutiaoAdvertiser(){{
//            setAdvertiserId(new BigInteger("3"));
//            setProductName("c");
//            setEventTypes("1,3");
//            setEventValues("10,5");
//            setMaxAccumulatePeriod(30);
//        }});
//
//        Map<String, List<AccountAction>> appMap = list.stream()
//                .collect(Collectors.groupingBy(AuthToutiaoAdvertiser::getProductName,
//                        Collectors.collectingAndThen(toList(), advertisers -> advertisers.stream()
//                                .flatMap(AuthToutiaoAdvertiserService::convertAction)
//                                .filter(Objects::nonNull)
//                                .collect(toList()))));
//
//        System.out.println(JSON.toJSONString(appMap));
        System.out.println(StringUtils.contains("huawei01","huawei"));
        System.out.println(StringUtils.contains("huawei","huawei"));


        System.out.println(StringUtils.contains("honor01","honor"));
        System.out.println(StringUtils.contains("honor","honor"));
        System.out.println(Double.MAX_VALUE);
        System.out.println(Long.MAX_VALUE);
        System.out.println(Integer.MAX_VALUE);
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        return appMap;
    }

    public List<AccountAction> queryActionConfig(String appName) {
        return appMap.getOrDefault(appName, Lists.newArrayList());
    }
    @Autowired
    AdvertiserAliService advertiserAliService;

    @Autowired
    SigmobAccountService sigmobAccountService;

    public Map<BigInteger, AuthToutiaoAdvertiser> getAccountActionMap(){
        return accountActionMap;
    }
    @Autowired
    PayEventService payEventService;
    @Autowired
    UserActiveService userActiveService;
    @Autowired
    DeviceSundyService deviceSundyService;
    @Autowired
    UserBidService userBidService;
    @Autowired
    MatchCrowdBroadService matchCrowdBroadService;
    @Autowired
    ProductService productService;
    @Autowired
    UserEventRpcService userEventRpcService;
    @Autowired
    PeopleDevice30NoRetainService peopleDevice30NoRetainService;
    @Autowired
    RetainOneDayPeopleService retainOneDayPeopleService;

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {
            if(ocpcSwitcher.productVes.contains(userEventReq.getProduct()) && StringUtils.equalsIgnoreCase("ios",userEventReq.getOs())){
                String userId = userEventReq.getUserId();
                UserActive userActive = userActiveService.queryActive(userId,userEventReq.getProduct());
                Date date = DateUtils.parse("2024-03-20",DateUtils.PATTERN_YMD);

                if(userActive!=null && date.after(userActive.getCreateTime())){
                    log.info("产品激活时间拒绝回传 "+ userEventReq.getProduct()+" "+userEventReq.getOs()+" 创建时间为 "+DateUtils.formatDateForYMDSTR(userActive.getCreateTime()));
                    return false;
                }else{
                    log.info("产品可能回传 "+ userEventReq.getProduct()+" "+userEventReq.getOs()+" 创建时间为 "+DateUtils.formatDateForYMDSTR(userActive.getCreateTime()));
                }
                log.info("放开异常产品依然回传 "+ userEventReq.getProduct()+" "+userEventReq.getOs());
            }
            if (userEventReq.getEventType() == ToutiaoEventTypeEnum.ACTIVATE_APP.value) {
                return checkActiveCount(toutiaoClick, userEventReq);
            }
            if (userEventReq.getEventType() == ToutiaoEventTypeEnum.PURCHASE.value) {
                return payEventService.isPayEventCall(toutiaoClick, userEventReq);
            }
            //过滤行为
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return true;
            }

            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();

            //非头条按是否是默认行为
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            try {
                if (!DspType.TOUTIAO.equals(dspType) && !DspType.TTWX.equals(dspType)) {

                    if (DspType.KUAISHOU.equals(dspType)) {
                        return authKuaishouAdvertiserService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.GUANGDIANTONG.equals(dspType)) {
                        return tencentDeveloperService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.OPPO.equals(dspType)) {
                        return oppoAdvertiserService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.isBaidu(dspType.value)) {
                        return baiduAdvertiserService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.vivo.equals(dspType)) {
                        return vivoAdvertiserService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.IQIYI.equals(dspType)) {
                        return iqiyiAdvertiserService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.XIAOMI.equals(dspType)) {
                        return xiaomiAdvertiserService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.XIMALAYA.equals(dspType)) {
                        return ximalayaAdvertiserService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.ALI.equals(dspType)) {
                        return advertiserAliService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    if (DspType.SIGMOB.equals(dspType)) {
                        return sigmobAccountService.checkActionCount(toutiaoClick, userEventReq);
                    }
                    return matchDefaultConfig;
                }
            } catch (Exception e) {
                log.error("校验ocpc配置异常", e);
                return false;
            }

            if(GuiyingType.packageGy.name.equals(toutiaoClick.getGuiType())){
                return false;
            }

            //若无配置 仅当默认值时上报
            if (StringUtils.isNotBlank(toutiaoClick.getAccountId()) && !accountActionMap.containsKey(new BigInteger(toutiaoClick.getAccountId()))) {
                return matchDefaultConfig;
            }

            AuthToutiaoAdvertiser advertiser = accountActionMap.get(StringUtils.isNotBlank(toutiaoClick.getAccountId())?new BigInteger(toutiaoClick.getAccountId()): new BigInteger("0"));
            if (advertiser == null) {
                return matchDefaultConfig;
            }

            javafx.util.Pair<String, DeviceSundy>  pair = deviceSundyService.queryByDevice(userEventReq);

            //通过开关判断是否要匹配低质量人群
            javafx.util.Pair<String, PeopleDevice30NoRetain> pair2 = new javafx.util.Pair<>(null, null);

            if (ocpcSwitcher.kuaishouCallBackTestSwitch) {
                //匹配低质量人群包
                pair2 = peopleDevice30NoRetainService.queryLowQualifyByDevice(userEventReq);
            }

            //查询TL 7天留存一天用户
            javafx.util.Pair<String, Integer> retainOneDayPeoplePair = retainOneDayPeopleService.queryRetainOneDayPeopleByDevice(userEventReq);

            Integer retainOneDayPeople = retainOneDayPeoplePair.getValue();
            DeviceSundy deviceSundy = pair.getValue();
            PeopleDevice30NoRetain peopleDevice30NoRetain = pair2.getValue();

            int sundyCpaType = 0; //周人群cpa
            int lowQualityCpaType = 0; //低质量人群cpa
            int retainOneDayCpaType = 0;
            if (deviceSundy != null) {
                sundyCpaType = deviceSundy.getCpaType();
            }
            if (peopleDevice30NoRetain != null) {
                lowQualityCpaType = peopleDevice30NoRetain.getCapType();
            }
            if (retainOneDayPeople != null) {
                retainOneDayCpaType = retainOneDayPeople;
            }


            BooleanSupplier firstTimeJudge = () -> isFirstBackRequest(userEventReq);

            //如果retainOneDayCpaType最大则走bid出价回传
            if (retainOneDayCpaType != 0 && retainOneDayCpaType > sundyCpaType && retainOneDayCpaType > lowQualityCpaType) {
                //当前产品是否走cpa回传
                boolean cpaCallBack = ocpcSwitcher.callBackProductList.isEmpty() || ocpcSwitcher.callBackProductList.contains(userEventReq.getProduct());
                if (cpaCallBack) {
                    log.info("bidCallBackSwitcher {} sundyCpaType {} lowQualityCpaType{} firstCallback{} ",ocpcSwitcher.bidCallBackSwitcher,sundyCpaType,lowQualityCpaType,firstTimeJudge.getAsBoolean());
                }
                //CpaType >=2的人需要判断 (收入-提醒) > cpa 回传逻辑
                if (ocpcSwitcher.bidCallBackSwitcher && cpaCallBack && retainOneDayCpaType >= 2 && firstTimeJudge.getAsBoolean()){
                    if (cpaCanCallBack(userEventReq)){
                        log.info("Toutiao cpaType>=2 当前用户可以回传 userId {} product {}",userEventReq.getUserId(),userEventReq.getProduct());
                        MatchCrowdBroadNew matchCrowdBroadNew = new MatchCrowdBroadNew();
                        //入库统计数据
                        matchCrowdBroadNew.setSource("toutiao");
                        matchCrowdBroadNew.setUserId(userEventReq.getUserId());
                        matchCrowdBroadNew.setProduct(userEventReq.getProduct());
                        matchCrowdBroadNew.setCanCallback(0);
                        matchCrowdBroadNew.setOs(userEventReq.getOs());
                        matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                        return true;
                    }else {
                        log.info("Toutiao cpaType>=2 当前用户不能回传 userId {} product {}",userEventReq.getUserId(),userEventReq.getProduct());
                        return false;
                    }
                }
            }

            EnumCrowdType crowdType = null;
            String oldEventValues = null;
            String eventValues = null;
            MatchCrowdBroadNew matchCrowdBroadNew = new MatchCrowdBroadNew();
            matchCrowdBroadNew.setUserId(userEventReq.getUserId());
            matchCrowdBroadNew.setProduct(userEventReq.getProduct());
            matchCrowdBroadNew.setCanCallback(1);
            //判断谁的倍数高使用哪个人群
            Integer capType = deviceSundyService.compareMultiple(userEventReq.getProduct(), deviceSundy != null ? deviceSundy.getOs() : "", sundyCpaType, lowQualityCpaType);

            if(pair.getValue()!=null && capType.equals(pair.getValue().getCpaType())){
                userEventReq.setUarpuGy(pair.getKey());
                eventValues = deviceSundyService.authAccountAppFl(userEventReq,pair.getValue(),advertiser.getEventTypes(),advertiser.getEventValues(),advertiser.getAdvertiserId()+"","toutiao");
                oldEventValues = advertiser.getEventValues();
                crowdType = EnumCrowdType.SUNDAY_CROWD;
                if(!StringUtils.equalsIgnoreCase(advertiser.getEventValues(),eventValues)){
                    log.info("toutiao etypes赋值改变 "+advertiser.getEventValues()+"->"+eventValues+" capTye {}",capType);
                    // advertiser.setEventValues(eventValues);
                }
            } else if (pair2.getValue() != null && capType.equals(pair2.getValue().getCapType())) {
                userEventReq.setUarpuGy(pair2.getKey());
                eventValues = deviceSundyService.authAccountAppFl(userEventReq, pair2.getValue(), advertiser.getEventTypes(),advertiser.getEventValues(),advertiser.getAdvertiserId()+"","toutiao");
                oldEventValues = advertiser.getEventValues();
                crowdType = EnumCrowdType.LOW_QUALITY_CROWD;
                if(!StringUtils.equalsIgnoreCase(advertiser.getEventValues(),eventValues)){
                    log.info("toutiao etypes2赋值改变 " + advertiser.getEventValues() + "->" + eventValues + "命中低质量人群包 用户id {} 产品名称 {} advertiser {} capType {}", userEventReq.getUserId(), userEventReq.getProduct(),JSONObject.toJSONString(advertiser),capType);
                    // advertiser.setEventValues(eventValues);
                }
            }
            fillMatchCrowdBroadToutiao(userEventReq, matchCrowdBroadNew, crowdType, oldEventValues, eventValues);
            matchCrowdBroadNew.setCpaType(capType);

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                if (matchNewRule(userEventReq, advertiser.getEventTypes(),
                        eventValues == null?advertiser.getEventValues():eventValues, firstTimeJudge, "头条",
                        advertiser.getProductName(), advertiser.getAdvertiserId().toString(), advertiser.getMaxAccumulatePeriod())) {
                    matchCrowdBroadNew.setCanCallback(0);
                    matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                    return true;
                }else if (StringUtils.isNotBlank(oldEventValues)){
                    if (matchNewRule(userEventReq, advertiser.getEventTypes(),
                            oldEventValues, firstTimeJudge, "头条", advertiser.getProductName(),
                            advertiser.getAdvertiserId().toString(), advertiser.getMaxAccumulatePeriod()) ){
                        matchCrowdBroadNew.setCanCallback(1);
                        matchCrowdBroadNew.setOriginalCanCallback(0);
                    }
                    matchCrowdBroadNew.setOriginalCanCallback(1);
                    matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                }
                return matchOldRule(userEventReq, advertiser, firstTimeJudge);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                boolean result = matchNewRule(userEventReq, advertiser.getEventTypes(),
                        eventValues == null?advertiser.getEventValues():eventValues, firstTimeJudge, "头条",
                        advertiser.getProductName(), advertiser.getAdvertiserId().toString(), advertiser.getMaxAccumulatePeriod());
                if (result) {
                    matchCrowdBroadNew.setCanCallback(0);
                }else {
                    matchCrowdBroadNew.setCanCallback(1);
                    matchCrowdBroadNew.setOriginalCanCallback(1);
                    if (StringUtils.isNotBlank(oldEventValues) && matchNewRule(userEventReq, advertiser.getEventTypes(),
                            oldEventValues, firstTimeJudge, "头条", advertiser.getProductName(),
                            advertiser.getAdvertiserId().toString(), advertiser.getMaxAccumulatePeriod()) && capType > 0) {
                        matchCrowdBroadNew.setOriginalCanCallback(0);
                    }
                }
                matchCrowdBroadService.saveMatchCrowdBroad(matchCrowdBroadNew);
                return result;
            }
            // 旧版本配置
            if (userEventReq.getActionType() != null) {
                return matchOldRule(userEventReq, advertiser, firstTimeJudge);
            }

        } catch (Exception e) {
            log.error("checkActionCount error ", e);
        }
        return false;
    }

    private void fillMatchCrowdBroadToutiao(UserEventReq userEventReq, MatchCrowdBroadNew matchCrowdBroadNew, EnumCrowdType crowdType, String eventValues, String updateEventValues) {
        matchCrowdBroadNew.setSource(crowdType == null ? "" : crowdType.name());
        matchCrowdBroadNew.setProduct(userEventReq.getProduct());
        matchCrowdBroadNew.setUserId(userEventReq.getUserId());
        matchCrowdBroadNew.setOldEventValues(eventValues);
        matchCrowdBroadNew.setNewEventValues(updateEventValues);
        matchCrowdBroadNew.setOs(userEventReq.getOs());
    }

    private Boolean cpaCanCallBack(UserEventReq userEventReq) {
        if (userEventReq.getActionValues() == null) {
            return false;
        }
        log.info("Toutiao用户信息 userEventReq {}",JSONObject.toJSONString(userEventReq));
        //查询用户的提现和出价
        Map userBidMap = userBidService.queryUserCpaBy(userEventReq.getUserId(), userEventReq.getProduct());
        if (userBidMap == null || userBidMap.get("withdraw") == null || userBidMap.get("bid") == null) {
            return false;
        }
        Double withdraw = (Double) userBidMap.get("withdraw");
        Double bid = (Double) userBidMap.get("bid");
        //根据产品名称查询appiId
        Product product = productService.getByName(userEventReq.getProduct());
        if (product == null) {
            return false;
        }
        //查询ecpm、pv
        String mapStr = userEventRpcService.queryUserECPMBatch(userEventReq.getOs(), product.getAppId(), Arrays.asList(Long.parseLong(userEventReq.getUserId())));
        log.info("ecpm、pv查询 mapStr {} userId {}",mapStr,userEventReq.getUserId());
        Map map = JSONObject.parseObject(mapStr, Map.class);
        if (map == null || map.get("ecpm") == null || map.get("pv") == null) {
            return false;
        }
        Double ecpm = (Double) map.get("ecpm");
        Double pv = (Double) map.get("pv");
        log.info("当前用户收入、提现、出价 userId={} product={} income={} withdraw={} bid={}",userEventReq.getUserId(),userEventReq.getProduct(),ecpm*pv,withdraw,bid);
        return (ecpm * pv - withdraw) / bid >= ocpcSwitcher.callBackMutiple;
    }

    /**
     * 校验激活是否重复
     */
    private Boolean checkActiveCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        if (ocpcSwitcher.needCheckRepeatActive(toutiaoClick.getProduct())) {
            OcpcEvent activeEvent = redisEventService.getUserIdActiveEvent(userEventReq);
            if (activeEvent != null) {
                log.info("重复的激活事件 userEventReq:{}", userEventReq);
                return false;
            }
        }
        return true;
    }

    private static boolean matchOldRule(UserEventReq userEventReq, AuthToutiaoAdvertiser authToutiaoAdvertiser,
                                        BooleanSupplier firstTimeJudge) {

        return matchOldRuleWithResult(userEventReq, authToutiaoAdvertiser, firstTimeJudge).getLeft();
    }

    private static Pair<Boolean, ActionTypes> matchOldRuleWithResult(UserEventReq userEventReq, AuthToutiaoAdvertiser authToutiaoAdvertiser,
                                        BooleanSupplier firstTimeJudge) {

        AccountActionTypeEnum actionType = AccountActionTypeEnum.findTypeByCode(userEventReq.getActionType());

        if (actionType == null) {
            return PairUtil.ofFalse();
        }

        if (authToutiaoAdvertiser.getEventType() == null) {
            return PairUtil.ofFalse();
        }

        log.info("开始匹配头条旧版规则 device {} report {} config {} 产品 {} 广告主 {}", userEventReq.getOcpcDeviceId(),
                userEventReq.getActionNumber(), authToutiaoAdvertiser.getEventNumber(),
                authToutiaoAdvertiser.getProductName(), authToutiaoAdvertiser.getAdvertiserId());

        if (!authToutiaoAdvertiser.getEventType().equals(userEventReq.getActionType())) {
            return PairUtil.ofFalse();
        }

        switch (actionType) {
            // 观看视频次数
            case WATCH_VIDEO:
                if (authToutiaoAdvertiser.getEventNumber() == null) {
                    return PairUtil.ofFalse();
                }
                if (authToutiaoAdvertiser.getEventNumber() == Integer.parseInt(userEventReq.getActionNumber())) {
                    log.info("头条视频次数回传条件满足 device {} report {} config {} 产品 {} 广告主 {}", userEventReq.getOcpcDeviceId(),
                            userEventReq.getActionNumber(), authToutiaoAdvertiser.getEventNumber(),
                            authToutiaoAdvertiser.getProductName(), authToutiaoAdvertiser.getAdvertiserId());
                    return PairUtil.ofTrue(ActionTypes.build(ImmutableMap.of(AccountActionTypeEnum.WATCH_VIDEO.getTypeKey(), userEventReq.getActionNumber())));
                }
                break;
            // 24小时arpu
            case ARPU_ONE_DAY:
                if (authToutiaoAdvertiser.getEventValue() == null) {
                    return PairUtil.ofFalse();
                }
                // arpu不满足的场景
                if (new BigDecimal(userEventReq.getActionNumber()).compareTo(new BigDecimal(authToutiaoAdvertiser.getEventValue())) < 0) {
                    break;
                }
                // arpu满足的场景；但如果已经存在回传
                if (firstTimeJudge.getAsBoolean()) {
                    log.info("头条首次回传arpu device {} report {} config {} 产品 {} 广告主 {}", userEventReq.getOcpcDeviceId(),
                            userEventReq.getActionNumber(), authToutiaoAdvertiser.getEventValue(),
                            authToutiaoAdvertiser.getProductName(), authToutiaoAdvertiser.getAdvertiserId());
                    return PairUtil.ofTrue(ActionTypes.build(ImmutableMap.of(AccountActionTypeEnum.ARPU_ONE_DAY.getTypeKey(), authToutiaoAdvertiser.getEventValue())));
                } else {
                    log.info("头条ocpc重复回传arpu达标");
                    return PairUtil.ofFalse();
                }
            default:
                break;
        }

        return PairUtil.ofFalse();
    }

    /**
     * 判断是否为第一次回传请求
     *
     * @param userEventReq 事件参数
     * @return true-首次回传
     */
    public boolean isFirstBackRequest(UserEventReq userEventReq) {
        OcpcEvent ocpcEventDb = null;
        if (!ocpcSwitcher.readHbaseSwitch) {
            QueryWrapper<OcpcEvent> objectQueryWrapper = new QueryWrapper<>();
            objectQueryWrapper.lambda().eq(OcpcEvent::getOcpcDeviceId, userEventReq.getOcpcDeviceId());
            objectQueryWrapper.lambda().eq(OcpcEvent::getOs, userEventReq.getOs());
            objectQueryWrapper.lambda().eq(OcpcEvent::getProduct, userEventReq.getProduct());
            objectQueryWrapper.lambda().eq(OcpcEvent::getEventType, userEventReq.getEventType());
            objectQueryWrapper.lambda().orderByDesc(OcpcEvent::getCreateTime);
            objectQueryWrapper.lambda().notLike(OcpcEvent::getReqUrl, "%is_direct_match%");
            objectQueryWrapper.lambda().isNotNull(OcpcEvent::getClickId);
            objectQueryWrapper.lambda().last("limit 1");
            ocpcEventDb = ocpcEventService.getOne(objectQueryWrapper, false);

            if (ocpcEventDb == null && StringUtils.isNotBlank(userEventReq.getOaid()) && userEventReq.getOaid().length() > 6) {
                QueryWrapper<OcpcEvent> objectQueryWrapper2 = new QueryWrapper<>();
                objectQueryWrapper2.lambda().eq(OcpcEvent::getOaid, userEventReq.getOaid());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getOs, userEventReq.getOs());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getProduct, userEventReq.getProduct());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getEventType, userEventReq.getEventType());
                objectQueryWrapper2.lambda().notLike(OcpcEvent::getReqUrl, "%is_direct_match%");
                objectQueryWrapper2.lambda().isNotNull(OcpcEvent::getClickId);
                objectQueryWrapper2.lambda().orderByDesc(OcpcEvent::getCreateTime);
                objectQueryWrapper2.lambda().last("limit 1");
                ocpcEventDb = ocpcEventService.getOne(objectQueryWrapper2, false);
            }

            // 跳过mac检查
            if (allSkipMacCheck || skipMacCheckApps.contains(userEventReq.getProduct())) {
                return ocpcEventDb == null;
            }

            if (ocpcEventDb == null && StringUtils.isNotBlank(userEventReq.getMac()) && userEventReq.getMac().length() > 6) {
                QueryWrapper<OcpcEvent> objectQueryWrapper2 = new QueryWrapper<>();
                objectQueryWrapper2.lambda().eq(OcpcEvent::getOs, userEventReq.getOs());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getMacId, userEventReq.getMac());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getProduct, userEventReq.getProduct());
                objectQueryWrapper2.lambda().notLike(OcpcEvent::getReqUrl, "%is_direct_match%");
                objectQueryWrapper2.lambda().isNotNull(OcpcEvent::getClickId);
                objectQueryWrapper2.lambda().eq(OcpcEvent::getEventType, userEventReq.getEventType());
                objectQueryWrapper2.lambda().orderByDesc(OcpcEvent::getCreateTime);
                objectQueryWrapper2.lambda().last("limit 1");
                ocpcEventDb = ocpcEventService.getOne(objectQueryWrapper2, false);
            }


            if (ocpcEventDb == null && StringUtils.isNotBlank(userEventReq.getCaid()) && userEventReq.getCaid().length() > 6) {
                QueryWrapper<OcpcEvent> objectQueryWrapper2 = new QueryWrapper<>();
                objectQueryWrapper2.lambda().eq(OcpcEvent::getOs, userEventReq.getOs());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getCaid, userEventReq.getCaid());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getProduct, userEventReq.getProduct());
                objectQueryWrapper2.lambda().notLike(OcpcEvent::getReqUrl, "%is_direct_match%");
                objectQueryWrapper2.lambda().isNotNull(OcpcEvent::getClickId);
                objectQueryWrapper2.lambda().eq(OcpcEvent::getEventType, userEventReq.getEventType());
                objectQueryWrapper2.lambda().orderByDesc(OcpcEvent::getCreateTime);
                objectQueryWrapper2.lambda().last("limit 1");
                ocpcEventDb = ocpcEventService.getOne(objectQueryWrapper2, false);
            }

            if (ocpcEventDb == null && StringUtils.isNotBlank(userEventReq.getUserId()) && userEventReq.getUserId().length() > 6) {
                QueryWrapper<OcpcEvent> objectQueryWrapper2 = new QueryWrapper<>();
                objectQueryWrapper2.lambda().eq(OcpcEvent::getOs, userEventReq.getOs());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getUserId, userEventReq.getUserId());
                objectQueryWrapper2.lambda().eq(OcpcEvent::getProduct, userEventReq.getProduct());
                objectQueryWrapper2.lambda().notLike(OcpcEvent::getReqUrl, "%is_direct_match%");
                objectQueryWrapper2.lambda().isNotNull(OcpcEvent::getClickId);
                objectQueryWrapper2.lambda().eq(OcpcEvent::getEventType, userEventReq.getEventType());
                objectQueryWrapper2.lambda().orderByDesc(OcpcEvent::getCreateTime);
                objectQueryWrapper2.lambda().last("limit 1");
                ocpcEventDb = ocpcEventService.getOne(objectQueryWrapper2, false);
            }

            if (ocpcEventDb != null) {
                return false;
            }
        } else {
            ocpcEventDb = hbaseEventService.queryKeyEventByHbase(userEventReq);

            try{
                OcpcEvent ocpcEventDb2 = hbaseEventService.queryUidKeyEventByHbase(userEventReq);

                if(ocpcEventDb!=null && ocpcEventDb2==null){
                    log.info("uid多次去重 "+JSON.toJSONString(userEventReq));
                }
                if(
                        (ocpcEventDb==null && ocpcEventDb2!=null)
                ){
                    log.info("userIdeventnoteq "+userEventReq.getProduct()+" "+userEventReq.getOs()+" " +userEventReq.getPkgChannel()+" "+userEventReq.getUserId());
                }else{
                    if(ocpcEventDb!=null && ocpcEventDb2!=null){
                        if(!StringUtils.equalsIgnoreCase(ocpcEventDb.getUserId(),ocpcEventDb2.getUserId())){
                            log.info("userIdeventnoteq2 "+userEventReq.getProduct()+" "+userEventReq.getOs()+" " +userEventReq.getPkgChannel()+" "+userEventReq.getUserId());
                        }else if(StringUtils.equalsIgnoreCase(ocpcEventDb.getUserId(),ocpcEventDb2.getUserId())){
                            log.info("userIdeveneq "+userEventReq.getProduct()+" "+userEventReq.getOs()+" " +userEventReq.getPkgChannel()+" "+userEventReq.getUserId());
                        }
                    }
                }


                if(StringUtils.isNotBlank(userEventReq.getPkgChannel())){
                    ocpcEventDb = ocpcEventDb2;
                    log.info("huaweihonor tonew "+JSON.toJSONString(userEventReq));
                }
            }catch (Exception e){
                log.error("userIdeventnoteq3 ",e);
            }

        }

        return ocpcEventDb == null;
    }

    private static Stream<AccountAction> convertAction(AuthToutiaoAdvertiser authToutiaoAdvertiser) {
        try {

            if (authToutiaoAdvertiser.getEventType() != null) {
                return Stream.of(buildOldAccountAction(authToutiaoAdvertiser));
            }

            if (StringUtils.isBlank(authToutiaoAdvertiser.getEventValues()) || StringUtils.isBlank(authToutiaoAdvertiser.getEventTypes())) {
                return Stream.empty();
            }

            return AuthKuaishouAdvertiserService.buildEventStandardConsideringMultiEcpm(authToutiaoAdvertiser.getEventTypes(),
                    authToutiaoAdvertiser.getEventValues(), authToutiaoAdvertiser.getAdvertiserId(), authToutiaoAdvertiser.getMaxAccumulatePeriod());

        } catch (Exception e) {
            log.error("更新头条广告主回传规则出现异常 ", e);
            log.error("配置有问题的头条广告主 {}", JSON.toJSONString(authToutiaoAdvertiser));
        }

        return null;
    }

    /**
     * 构建老版规则对象
     *
     * @param authToutiaoAdvertiser 头条广告主对象
     * @return 老版规则对象
     */
    private static AccountAction buildOldAccountAction(AuthToutiaoAdvertiser authToutiaoAdvertiser) {

        AccountActionTypeEnum eventTypeEnum = AccountActionTypeEnum.findTypeByCode(authToutiaoAdvertiser.getEventType());

        if (eventTypeEnum == null) {
            return null;
        }

        AccountAction accountAction = new AccountAction();
        accountAction.setAccountId(authToutiaoAdvertiser.getAdvertiserId());
        accountAction.setEventType(authToutiaoAdvertiser.getEventType());
        accountAction.setMaxAccumulatePeriod(authToutiaoAdvertiser.getMaxAccumulatePeriod());

        switch (eventTypeEnum) {
            case WATCH_VIDEO:
                if (authToutiaoAdvertiser.getEventNumber() == null) {
                    return null;
                }
                accountAction.setEventNumber(String.valueOf(authToutiaoAdvertiser.getEventNumber()));
                break;
            case ARPU_ONE_DAY:
                if (StringUtils.isBlank(authToutiaoAdvertiser.getEventValue())) {
                    return null;
                }
                accountAction.setEventNumber(authToutiaoAdvertiser.getEventValue());
                break;
            default:
                return null;
        }

        return accountAction;
    }

    /**
     * 尝试回传衍生事件
     */
    public void tryCallbackExtEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {

        try {
            if (toutiaoClick == null || StringUtils.isBlank(toutiaoClick.getAccountId()) || StringUtils.isBlank(toutiaoClick.getCallbackUrl())) {
                return;
            }
            if (userEventReq.getAppId() != null && userEventReq.getAppId() > 1220) {
                //新产品衍生事件不传了
                return;
            }

            if (DspType.KUAISHOU.value.equals(toutiaoClick.getDsp())) {
                authKuaishouAdvertiserService.tryCallbackExtDefine(toutiaoClick, userEventReq);
                return;
            }
            if (DspType.GUANGDIANTONG.value.equals(toutiaoClick.getDsp())) {
                tencentDeveloperService.tryCallbackExtDefine(toutiaoClick, userEventReq);
                return;
            }

            if (DspType.isBaidu(toutiaoClick.getDsp())) {
                baiduAdvertiserService.tryCallbackExtDefine(toutiaoClick, userEventReq);
                return;
            }

            AuthToutiaoAdvertiser advertiser = getToutiaoAdvertiserForExtEvent(toutiaoClick, userEventReq);

            if (advertiser == null) {
                return;
            }

            //因为不规则头条arpu向下取整， 所以要求 回传衍生事件时已满足关键行为
            if (userEventReq.getEventType() == ToutiaoEventTypeEnum.KEY_EVENT.value && isFirstBackRequest(userEventReq)) {
//                log.info(" 头条首次回传衍生事件时必须已满足关键行为 product {} userId {} actionValues {}" ,userEventReq.getProduct(),userEventReq.getUserId(),userEventReq.getActionValues());
                return;
            }

            ThirdAdvertiser thirdAdvertiser = new ThirdAdvertiser(advertiser);
            DspType dspType = thirdAdvertiser.getDsp();

            boolean locked = false;
            int eventTypeKey = ToutiaoEventTypeEnum.KEY_ACTION_EXT_EVENT.value;
            String lockKey = RedisKeyConstants.getReportLockKey(userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
            try {
                if (extEventApolloConfig.lockExtEventAllEnable || extEventApolloConfig.lockExtEventEnableProducts.contains(userEventReq.getProduct())) {
                    // 要使用分布式锁保证回传顺序执行
                    locked = ocpcEventService.tryGetDistributedLock(lockKey, "1", 10000);

                    if (!locked) {
//                        log.warn("衍生事件上报并发抢占资格失败 {} {} {}", userEventReq.getProduct(), userEventReq.getUserId(), eventTypeKey);
                        return;
                    }

                    if (NUM_ARPU_RULE.equals(advertiser.getEventTypes())) {
                        // 获取符合的关键行为衍生事件
                        List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, userEventReq);
                        if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                            OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                            List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_2(dspType, matchExtEvents,
                                    ocpcEvent, ()-> checkExtEventCount(toutiaoClick, userEventReq, advertiser));
                            ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                        }
                    } else if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())) {
                        // ecpm需要先判断是否符合账户的配置条件
                        Pair<Boolean, ActionTypes> actionTypesPair = checkExtEventCount(toutiaoClick, userEventReq, advertiser);
                        if (actionTypesPair.getLeft()) {
                            ActionTypes ecpmLevel10 = getEcpmLevel10(advertiser.getEventValues());
                            UserEventReq fixedUserEventReq = getFixedUserEventReq(userEventReq, ecpmLevel10);
                            // 获取符合的关键行为衍生事件
                            List<ThirdExtEvent> matchExtEvents = thirdExtEventService.getAllMatchExtEvent(thirdAdvertiser, fixedUserEventReq);
                            if (CollectionUtils.isNotEmpty(matchExtEvents)) {
                                OcpcEvent ocpcEvent = hbaseEventService.queryKeyActionExtEvent(userEventReq);

                                List<ThirdExtEvent> extEventList = thirdExtEventService.getNeedCallbackExtEventFor_1_3(dspType, ecpmLevel10, matchExtEvents, ocpcEvent);
                                ocpcEventService.callbackExtEvent(dspType, toutiaoClick, userEventReq, extEventList, ocpcEvent);
                            }
                        }
                    }

                }
            } catch (Exception e) {
                log.error("衍生事件分布式锁抢占出现异常 ", e);
            } finally {
                if (locked) {
                    ocpcEventService.unlockDistributedLock(lockKey);
                }
            }
        } catch (Exception e) {
            log.error(String.format("tryCallbackExtEvent error %s %s", JSON.toJSONString(toutiaoClick), JSON.toJSONString(userEventReq)), e);
        }

    }





    /**
     * 获取头条账户配置
     * @return
     */
    public AuthToutiaoAdvertiser getToutiaoAdvertiserForExtEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return null;
            }

            //非头条不回传关键行为衍生事件
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            if (dspType != DspType.TOUTIAO) {
                return null;
            }

            if (ToutiaoCallService.isToutiaoCallbackV2(toutiaoClick)) {
                // 限制产品
                if (!extEventApolloConfig.extEventV2EnableAllProduct && !extEventApolloConfig.extEventV2EnableProducts.contains(userEventReq.getProduct())) {
                    return null;
                }
            } else {
                // 限制产品
                if (!extEventApolloConfig.extEventEnableAll && !extEventApolloConfig.extEventEnableProducts.contains(userEventReq.getProduct())) {
                    return null;
                }
            }

            AuthToutiaoAdvertiser advertiser = accountActionMap.get(new BigInteger(toutiaoClick.getAccountId()));

            //若无配置 不上报
            if (advertiser == null || StringUtils.isAnyBlank(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // v2版本限制回传账户
            if (ToutiaoCallService.isToutiaoCallbackV2(toutiaoClick)) {
                // 限制适用账户
                if (!extEventApolloConfig.extEventV2EnableAllAccount && !extEventApolloConfig.extEventV2EnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                    return null;
                }
            } else {
                // 限制适用账户
                if (!extEventApolloConfig.extEventEnableAllAccount && !extEventApolloConfig.extEventEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                    return null;
                }
            }

            // 非普通账户不回传 (达人账户不回传)
            boolean isNormalAccount = Objects.equals(advertiser.getAccountType(), 1);
            if (!isNormalAccount) {
                return null;
            }

            // 分时arpu账户 不上报
            if (isMultiArpu(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            // 次数ecpm账户，若没有10这一档位时 不上报
            if (checkEcpmHasNotLevel10(advertiser.getEventTypes(), advertiser.getEventValues())) {
                return null;
            }

            Map<String, String> reportValues = analyzeNewValueToMap(userEventReq.getActionValues());
            String reqEventTypes = reportValues.keySet().stream().sorted(Comparator.comparing(Integer::valueOf)).collect(Collectors.joining(","));
            // 若请求的类型和当前账户的类型不一致，不上报
            if (!Objects.equals(reqEventTypes, advertiser.getEventTypes())) {
                return null;
            }

            // 限制适用类型
            if (!extEventApolloConfig.extEventEnableEventTypes.contains(advertiser.getEventTypes())) {
                return null;
            }

            // 对于1,3的账户限制产品
            if (NUM_ECPM_RULE.equals(advertiser.getEventTypes())
                    && !extEventApolloConfig.extEventEnableAllFor_1_3
                    && !extEventApolloConfig.extEventEnableProductsFor_1_3.contains(userEventReq.getProduct())) {
                return null;
            }

            return advertiser;

        } catch (Exception e) {
            log.error("getToutiaoAdvertiserForExtEvent error ", e);
        }
        return null;
    }

    /**
     * 检查衍生事件计数
     * @return
     */
    public Pair<Boolean, ActionTypes> checkExtEventCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq, AuthToutiaoAdvertiser advertiser) {
        try {
            //过滤行为，仅当项目组回传关键行为时，才可能回传关键行为衍生事件
            if (userEventReq.getEventType() != ToutiaoEventTypeEnum.KEY_EVENT.value) {
                return PairUtil.ofFalse();
            }

            //非头条不回传关键行为衍生事件
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            if (dspType != DspType.TOUTIAO) {
                return PairUtil.ofFalse();
            }

            //若无配置 仅当默认值时上报
            if (advertiser == null) {
                return PairUtil.ofFalse();
            }

            // 限制适用类型
            if (!extEventApolloConfig.extEventEnableEventTypes.contains(advertiser.getEventTypes())) {
                return PairUtil.ofFalse();
            }

            // 限制适用账户
            if (!extEventApolloConfig.extEventEnableAllAccount && !extEventApolloConfig.extEventEnableAccounts.contains(advertiser.getAdvertiserId().toString())) {
                return PairUtil.ofFalse();
            }

            // 不需判断是否首次满足条件
            BooleanSupplier firstTimeJudge = () -> true;

            Pair<Boolean, ActionTypes> matchNewRuleWithResult = matchNewRuleWithResult(userEventReq, advertiser.getEventTypes(),
                    advertiser.getEventValues(), firstTimeJudge, "头条",
                    advertiser.getProductName(), advertiser.getAdvertiserId().toString(), advertiser.getMaxAccumulatePeriod());
            if (matchNewRuleWithResult.getLeft()) {
                return matchNewRuleWithResult;
            }

        } catch (Exception e) {
            log.error("checkActionCount error ", e);
        }
        return PairUtil.ofFalse();
    }

    public AuthToutiaoAdvertiser getAdvertiser(String accountId) {
        return accountActionMap.get(new BigInteger(accountId));
    }

    public void setEventValues(List<ProductAdvertiserPool> advertiserPools) {
        IterateUtils.iterateByStepSize(advertiserPools, 5000, data -> {
            List<AuthToutiaoAdvertiser> list = this.lambdaQuery().select(AuthToutiaoAdvertiser::getAdvertiserId, AuthToutiaoAdvertiser::getEventValues)
                    .in(AuthToutiaoAdvertiser::getAdvertiserId, data.stream().map(o -> new BigInteger(o.getAccountId())).collect(toList()))
                    .eq(AuthToutiaoAdvertiser::getEventTypes, BaseConstants.NUM_ARPU_RULE)
                    .list();
            Map<String, String> eventValues = list.stream().collect(toMap(o -> o.getAdvertiserId().toString(), AuthToutiaoAdvertiser::getEventValues, (o1, o2) -> o1));
            data.forEach(o -> o.setEventValues(eventValues.get(o.getAccountId())));
        });
    }

}
