package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.SystemInfo;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.service.AuthToutiaoAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.ProductService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.*;
import com.coohua.core.caf.dispense.ocpc.entity.*;
import com.coohua.core.caf.dispense.ocpc.mapper.UserExposureEcpmNewMapper;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.coohua.core.caf.dispense.utils.DoubleUtil;
import com.coohua.core.caf.dispense.utils.IterateUtils;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;


/**
 * 问题: 多数据性能
 *
 */
@Service
@Slf4j
public class UserExposureEcpmNewService extends ServiceImpl<UserExposureEcpmNewMapper, UserExposureEcpmNew> {

    @Autowired
    private OcpcEventService ocpcEventService;
    @Autowired
    private AuthToutiaoAdvertiserService authToutiaoAdvertiserService;
    @Autowired
    private ProductService productService;
    @Value("${ecpm.arpu.rate:1.5}")
    private Double ecpmArpuRate;
    @Autowired
    private UserEcpmOcpcCheckLogService userEcpmOcpcCheckLogService;
    @Autowired
    private ProductAdvertiserPoolService productAdvertiserPoolService;
    @Autowired
    private CvrProductCountLogService cvrProductCountLogService;
    @Autowired
    private OcpcSwitcher ocpcSwitcher;
    @Autowired
    private DeviceSundyService deviceSundyService;
    @Autowired
    private PeopleDevice30NoRetainService peopleDevice30NoRetainService;
    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    private static final long FIVE_MINUTE = 300000L;
    private static final long FIVE_MINUTE_SECOND = 300L;
    private static final long ONE_HOUR = 3600000L;
    private static final long ONE_DAY = 24 * ONE_HOUR;

    /**
     * 数据入库
     * @param eventList
     * @param trace
     */
    public void saveUserDayEcpm(List<EventEntity> eventList, String trace) {
        Date date = new Date();
        //合并同product 和 userId 唯一数据
        List<UserExposureEcpmNew> list = new ArrayList<>(eventList.size());
        Map<String, Integer> keyIndex = new HashMap<>();
        for(EventEntity o : eventList) {
            String product = o.getProperties().getProduct();
            String key = product+"_"+o.getProperties().getUserId();
            if(!keyIndex.containsKey(key)) {
                UserExposureEcpmNew userExposureEcpmNew = new UserExposureEcpmNew();
                userExposureEcpmNew.setId(DateUtils.formatDateForYMDSTR(date)+"_"+key);
                userExposureEcpmNew.setDataDate(date);
                userExposureEcpmNew.setUserId(Long.parseLong(o.getProperties().getUserId()));
                userExposureEcpmNew.setProduct(product);
                userExposureEcpmNew.setOs(o.getProperties().get$os());
                userExposureEcpmNew.setDeviceId(o.getProperties().get$device_id());
                userExposureEcpmNew.setOaid(o.getProperties().getOaid());
                userExposureEcpmNew.setCaid(o.getProperties().getCaid());
                userExposureEcpmNew.setImei(o.getProperties().getImei());
                userExposureEcpmNew.setEcpm(Double.valueOf(o.getProperties().getExtend1()));
                userExposureEcpmNew.setPv(1);
                keyIndex.put(key, list.size());
                list.add(userExposureEcpmNew);
            }else {
                UserExposureEcpmNew userExposureEcpmNew = list.get(keyIndex.get(key));
                userExposureEcpmNew.setEcpm(userExposureEcpmNew.getEcpm() + Double.parseDouble(o.getProperties().getExtend1()));
                userExposureEcpmNew.setPv(userExposureEcpmNew.getPv() + 1);
            }
        }
        //查询
        if(!list.isEmpty()) {
            /*List<String> existsUKeys = lambdaQuery().select(UserExposureEcpm::getUKey).in(UserExposureEcpm::getUKey, list.stream().map(UserExposureEcpm::getUKey).collect(Collectors.toList())).list().stream().map(UserExposureEcpm::getUKey).collect(Collectors.toList());
            List<UserExposureEcpm> insertList = list.stream().filter(o -> !existsUKeys.contains(o.getUKey())).collect(Collectors.toList());
            List<UserExposureEcpm> updateList = list.stream().filter(o -> existsUKeys.contains(o.getUKey())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(insertList)) {
                IterateUtils.iterateByStepSize(insertList, 1000, this.getBaseMapper()::saveOrUpdateOnDuplicateKey);
            }
            if(CollectionUtils.isNotEmpty(updateList)) {
                IterateUtils.iterateByStepSize(updateList, 500, this.getBaseMapper()::batchAddPvEcpm);
            }*/
            IterateUtils.iterateByStepSize(list, 1000, data -> {
                int tryTimes = 0;
                while (tryTimes < 3) {
                    try {
                        this.getBaseMapper().saveOrUpdateOnDuplicateKey(data);
                        break;
                    } catch (org.springframework.dao.DeadlockLoserDataAccessException ex) {
                        if(tryTimes < 3) {
                            try {
                                Thread.sleep(300);
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                            tryTimes++;
                        }else {
                            log.info("尝试入库3次依旧死锁");
                            throw ex;
                        }
                    }
                }
            });
            log.info("处理用户ecpm和arpu条数:{}", list.size());
        }
    }

    private void buildCanSend(String productName, List<OcpcEvent> ocpcEventList, String product, String accountId, Date today, Date yesterday, Double rateNumber, Integer pv, Double ecpm) {
        if(rateNumber < 1) {
            log.error("账户比例配置错误, product:{}, accountId:{}, rateNumber:{}", product, accountId, rateNumber);
            return;
        }
        if(null == pv) {
            pv = 0;
        }
        if(null == ecpm) {
            ecpm = 0D;
        }
        Date todayStart = DateUtils.getStartOfDate(today);
        //查询今日回传激活的人数
        Set<Long> hourActiveUsers = ocpcEventList.stream().filter(o -> Objects.equals(o.getEventType(), 0)).map(o -> Long.parseLong(o.getUserId())).collect(Collectors.toSet());
        Set<Long> natureActiveUsers = ocpcEventList.stream().filter(o -> (o.getCreateTime().equals(todayStart) || o.getCreateTime().after(todayStart)) && Objects.equals(o.getEventType(), 0)).map(o -> Long.parseLong(o.getUserId())).collect(Collectors.toSet());
        int hourActiveCount = hourActiveUsers.size(), natureActiveCount = natureActiveUsers.size();
        Set<Long> hourKeyUsers = ocpcEventList.stream().filter(o -> Objects.equals(o.getEventType(), 25)).map(o -> Long.parseLong(o.getUserId())).collect(Collectors.toSet());
        Set<Long> natureKeyUsers = ocpcEventList.stream().filter(o -> (o.getCreateTime().equals(todayStart) || o.getCreateTime().after(todayStart)) && Objects.equals(o.getEventType(), 25)).map(o -> Long.parseLong(o.getUserId())).collect(Collectors.toSet());
        int hourKeyCount = hourKeyUsers.size(), natureKeyCount = natureKeyUsers.size();
        int hourNeedCount = Double.valueOf(hourActiveCount / rateNumber).intValue(), natureNeedCount = Double.valueOf(natureActiveCount / rateNumber).intValue();

        Date yesterdayStart = new Date(todayStart.getTime() - ONE_DAY);
        hourActiveUsers.removeAll(hourKeyUsers);
        int hourQueryCallback = hourActiveUsers.size();
;       //可以回传的用户
        List<UserExposureEcpmNew> hourList = 0 == hourQueryCallback ? new ArrayList<>() : this.getBaseMapper().query2Send(ecpmArpuRate, product, pv, ecpm, hourActiveUsers, todayStart, yesterdayStart);
        int hourNotCallback = hourList.size();

        natureActiveUsers.removeAll(natureKeyUsers);
        int natureQueryCallback = natureActiveUsers.size();
        //自然日可以回传的用户
        List<UserExposureEcpmNew> natureList = 0 == natureQueryCallback ? new ArrayList<>() : this.getBaseMapper().query2Send(ecpmArpuRate, product, pv, ecpm, natureActiveUsers, todayStart, yesterdayStart);
        int natureNotCallback = natureList.size();

        userEcpmOcpcCheckLogService.saveQueryRes("toutiao", productName, product, accountId, today, natureActiveCount, natureKeyCount, natureNotCallback, Math.max(natureNeedCount - natureKeyCount, 0),
                hourActiveCount, hourKeyCount, hourNotCallback, Math.max(hourNeedCount - hourKeyCount, 0));
        log.info("账户24h内回传情况, product:{}, accountId:{}, rateNumber:{}, pv:{}, ecpm:{}, end:{}, start:{}, " +
                "hourActiveCount:{}, hourKeyCount:{}, hourNeedCount:{}, hourQueryCallback:{}, hourNotCallback:{}, " +
                "natureActiveCount:{}, natureKeyCount:{}, natureNeedCount:{}, natureQueryCallback:{}, natureNotCallback:{}",
                product, accountId, rateNumber, pv, ecpm, DateUtils.formatDateForYMD(today), DateUtils.formatDateForYMD(yesterday),
                hourActiveCount, hourKeyCount, hourNeedCount, hourQueryCallback, hourNotCallback,
                natureActiveCount, natureKeyCount, natureNeedCount, natureQueryCallback, natureNotCallback);
        if(hourKeyCount >= hourNeedCount || 0 == hourNotCallback) {
            return;
        }
        int subSize = hourNeedCount - hourKeyCount;
        //按用户合并应该发起的
        if(subSize < hourList.size()) {
            //按ecpm排序
            hourList.sort(Comparator.comparingDouble(UserExposureEcpmNew::getEcpm).reversed());
        }
        int size = Math.min(subSize, hourList.size());
        List<UserExposureEcpmNew> updateList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            UserExposureEcpmNew db = hourList.get(i);
            long add = ThreadLocalRandom.current().nextLong(FIVE_MINUTE);
            for(String id : db.getId().split(",")) {
                UserExposureEcpmNew update = new UserExposureEcpmNew();
                update.setId(id);
                update.setStatus(UserEcpmStatus.WAIT.getValue());
                update.setSendTime(new Date(db.getUpdateTime().getTime() + add));
                //保留4位小数
                update.setRemark("1-" + db.getPv() + ",2-" + String.format("%.4f", db.getEcpm() / 1000)+";7-"+rateNumber+",1-"+pv+",2-"+String.format("%.4f", ecpm / 1000));
                updateList.add(update);
            }
        }
        log.info("激活用户待回传, product:{}, accountId:{}, active:{}, rateNumber:{}, pv:{}, ecpm:{}, rate:{}, leftList:{}, beforeK:{}, addK:{}", product, accountId, hourActiveCount, rateNumber, pv, ecpm, 100D / rateNumber, hourList.size(), hourKeyCount, size);
        IterateUtils.iterateByStepSize(updateList, 1000, this::updateBatchById);
    }

    public void dealProductOcpcEvent(Date start, ThreadPoolTaskExecutor executor) {
        List<AuthToutiaoAdvertiser> advertisers = authToutiaoAdvertiserService.lambdaQuery().select(AuthToutiaoAdvertiser::getProductName, AuthToutiaoAdvertiser::getAdvertiserId, AuthToutiaoAdvertiser::getEventTypes, AuthToutiaoAdvertiser::getEventValues).isNotNull(AuthToutiaoAdvertiser::getProductName)
                .likeRight(AuthToutiaoAdvertiser::getEventTypes, AccountActionTypeEnum.KA_RATE.getTypeKey()).list();
        //查询24h前之后回传的
        Date yesterday = new Date(start.getTime() - ONE_DAY);
        log.info("检查回传账户数, size:{}, date:{}, yesterday:{}", advertisers.size(), DateUtils.formatDateForYMD(start), DateUtils.formatDateForYMD(yesterday));
        IterateUtils.iterateByStepSize(advertisers, 50, data -> {
            Map<String, AuthToutiaoAdvertiser> advertiserMap = data.stream().collect(Collectors.toMap(o -> o.getAdvertiserId().toString(), o -> o, (o1, o2) -> o2));
            long nowTime = System.currentTimeMillis();
            List<OcpcEvent> ocpcEventList = ocpcEventService.lambdaQuery().select(OcpcEvent::getAccountId, OcpcEvent::getEventType, OcpcEvent::getCreateTime, OcpcEvent::getUserId).in(OcpcEvent::getAccountId, advertiserMap.keySet()).in(OcpcEvent::getEventType, Arrays.asList(0, 25)).ge(OcpcEvent::getCreateTime, yesterday).list();
            Map<String, List<OcpcEvent>> advertiserList = ocpcEventList.stream().collect(Collectors.groupingBy(OcpcEvent::getAccountId));
            log.info("查询账户回传数结果, size:{}, cost:{} ms", ocpcEventList.size(), System.currentTimeMillis() - nowTime);

            //没有查询到的数据，都赋值为0
            List<AuthToutiaoAdvertiser> noDataList = data.stream().filter(o -> !advertiserList.containsKey(o.getAdvertiserId().toString())).collect(Collectors.toList());
            userEcpmOcpcCheckLogService.saveToutiaoNoData(noDataList, start);
            for(Map.Entry<String, List<OcpcEvent>> map : advertiserList.entrySet()) {
                String accountId = map.getKey();
                List<OcpcEvent> list = map.getValue();
                AuthToutiaoAdvertiser authToutiaoAdvertiser = advertiserMap.get(accountId);
                CompletableFuture.runAsync(()-> {
                    try {
                        Product product = productService.getByCnName(authToutiaoAdvertiser.getProductName());
                        String[] values = authToutiaoAdvertiser.getEventValues().split(",");
                        Double rateNumber = Double.parseDouble(values[0]), ecpm = null;
                        Integer pv = null;
                        if(values.length > 1) {
                            pv = Integer.parseInt(values[1]);
                            ecpm = Double.parseDouble(values[2]) * 1000;
                        }
                        buildCanSend(authToutiaoAdvertiser.getProductName(), list, product.getName(), accountId, start, yesterday, rateNumber, pv, ecpm);
                    }catch (Exception ex) {
                        log.error("查询账户需要回传内容error product:{}, accountId:{}, ex", authToutiaoAdvertiser.getProductName(), accountId, ex);
                    }
                }, executor);
            }
        });

    }


    public void send2Third(List<UserExposureEcpmNew> list, ThreadPoolTaskExecutor executor) {
        //分产品查询
        Map<String, List<UserExposureEcpmNew>> productUserEcpmList = list.stream().collect(Collectors.groupingBy(UserExposureEcpmNew::getProduct));
        for(Map.Entry<String, List<UserExposureEcpmNew>> map : productUserEcpmList.entrySet()) {
            String product = map.getKey();
            List<UserExposureEcpmNew> values = map.getValue();
            //不需要回传了，把状态改回来
            /*if(!ocpcSwitcher.ocpcTimerProducts.contains(product)) {
                List<UserExposureEcpm> updateList = values.stream().map(db -> {
                    UserExposureEcpm update = new UserExposureEcpm();
                    update.setId(db.getId());
                    update.setStatus(UserEcpmStatus.INIT.getValue());
                    return update;
                }).collect(Collectors.toList());
                IterateUtils.iterateByStepSize(updateList, 1000, this::updateBatchById);
                continue;
            }*/
            Set<String> userIds = values.stream().map(o -> o.getUserId().toString()).collect(Collectors.toSet());
            List<OcpcEvent> ocpcEventList = ocpcEventService.lambdaQuery().eq(OcpcEvent::getProduct, product).in(OcpcEvent::getUserId, userIds).eq(OcpcEvent::getEventType, 0).list();
            Map<String, List<UserExposureEcpmNew>> userMap = values.stream().collect(Collectors.groupingBy(o -> o.getUserId().toString()));
            for (OcpcEvent ocpcEvent : ocpcEventList) {
                CompletableFuture.runAsync(()-> {
                    ToutiaoClick toutiaoClick = RedisEventService.convertEventToClick(ocpcEvent);
                    UserEventReq userEventReq = new UserEventReq();
                    BeanUtils.copyProperties(ocpcEvent, userEventReq);
                    userEventReq.setEventType(ToutiaoEventTypeEnum.KEY_EVENT.value);
                    List<UserExposureEcpmNew> ecpms = userMap.get(ocpcEvent.getUserId());
                    userEventReq.setActionValues(ecpms.get(0).getRemark());
                    userEventReq.setTimerDelay(true);
                    log.info("准备延迟回传内容, ecpm:{}, click:{}, event:{}", JSONObject.toJSONString(ecpms), JSONObject.toJSONString(toutiaoClick), JSONObject.toJSONString(userEventReq));
                    ocpcEventService.guiyingDspClick(toutiaoClick, userEventReq, true);
                    this.lambdaUpdate().set(UserExposureEcpmNew::getStatus, UserEcpmStatus.DEAL.getValue()).in(UserExposureEcpmNew::getId, ecpms.stream().map(UserExposureEcpmNew::getId).collect(Collectors.toList())).update();
                }, executor);
            }
        }
    }

    private void buildValidRemark(Map<String, Date> activeDateMap, String dsp, String product, String accountNo, String eventValues, Integer limitPv, Double limitEcpm, UserExposureEcpmNew userExposureEcpmNew) {
        String os = userExposureEcpmNew.getOs(),
                pkgChannel = null,
                oaid = userExposureEcpmNew.getOaid(),
                caid = userExposureEcpmNew.getCaid(),
                ocpcDeviceId = PhoneOsEnum.android.name.equalsIgnoreCase(userExposureEcpmNew.getOs()) ? MD5Utils.getMd5Sum(userExposureEcpmNew.getDeviceId()) : userExposureEcpmNew.getDeviceId();
        javafx.util.Pair<String, DeviceSundy>  pair = deviceSundyService.queryByDeviceByProductDevice(product, os, pkgChannel, oaid, caid, ocpcDeviceId,userExposureEcpmNew.getDeviceId());
        //通过开关判断是否要匹配低质量人群
        javafx.util.Pair<String, PeopleDevice30NoRetain>  pair2 = DspType.KUAISHOU.value.equals(dsp) && ocpcSwitcher.kuaishouCallBackTestSwitch ? peopleDevice30NoRetainService.queryLowQualifyByProductDevice(product, os, pkgChannel, oaid, caid, ocpcDeviceId) : new javafx.util.Pair<>(null,null);
        DeviceSundy deviceSundy = pair.getValue();
        PeopleDevice30NoRetain peopleDevice30NoRetain = pair2.getValue();
        int capType1 = 0;
        int capType2 = 0;
        if (deviceSundy != null){
            capType1 = deviceSundy.getCpaType();
        }
        if (peopleDevice30NoRetain != null){
            capType2 = peopleDevice30NoRetain.getCapType();
        }
        userExposureEcpmNew.setRemark("1-" + userExposureEcpmNew.getPv() + ",2-" + String.format("%.4f", userExposureEcpmNew.getEcpm() / 1000));
        userExposureEcpmNew.setActiveTime(activeDateMap.get(userExposureEcpmNew.getUserId().toString()));
        userExposureEcpmNew.setValid(true);

        //判断谁的倍数高使用哪个人群
        Integer capType = deviceSundyService.compareMultiple(product, deviceSundy != null ? deviceSundy.getOs() : "", capType1, capType2);
        double dfl = pair.getValue()!=null&&capType.equals(pair.getValue().getCpaType()) ? deviceSundyService.getArpuFlt(product,deviceSundy) :
                pair2.getValue()!=null&&capType.equals(pair2.getValue().getCapType()) ? deviceSundyService.getArpuFlt4NoRetain(product, peopleDevice30NoRetain.getCapType(),peopleDevice30NoRetain.getOs()) : 1D;
        if(1D == dfl) {
            userExposureEcpmNew.setRemark(userExposureEcpmNew.getRemark() + ";1-"+limitPv+",2-"+String.format("%.4f", limitEcpm / 1000));
        }else {
            Double arpumk = DoubleUtil.multiplicationDouble(limitEcpm, dfl);
            userExposureEcpmNew.setValid(arpumk <= userExposureEcpmNew.getEcpm());
            if(userExposureEcpmNew.isValid()) {
                String eventValuesNw = limitPv+","+arpumk;
                String rmk = userExposureEcpmNew.getUserId()+" "+ product+" xin2替换eventtype "+arpumk+"="+String.format("%.4f", limitEcpm / 1000)+"*"+ dfl +" oldevents:"+ eventValues +" newEvents:"+ eventValuesNw +" userReqEvents:"+ userExposureEcpmNew.getRemark()+"  acc:"+ accountNo +" "+ dsp;
                userExposureEcpmNew.setRemark(userExposureEcpmNew.getRemark() + ";1-" + limitPv + ",2-" + String.format("%.4f", arpumk / 1000) + " llop " + rmk + " " + pair.getKey());
            }
        }
    }

    private List<UserExposureEcpmNew> queryValidOcpcUser(List<OcpcEvent> ocpcEventList, List<ProductAdvertiserPool> accountList, Date todayStart, Date yesterdayStart) {
        List<UserExposureEcpmNew> matchKeyList = new ArrayList<>();
        for(ProductAdvertiserPool advertiser : accountList) {
            Set<Long> activeUsers = ocpcEventList.stream().filter(o -> Objects.equals(0, o.getEventType()) && Objects.equals(advertiser.getAccountId(), o.getAccountId())).map(o -> Long.parseLong(o.getUserId())).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(activeUsers)) {
                continue;
            }
            Set<Long> keyUsers = ocpcEventList.stream().filter(o -> Objects.equals(25, o.getEventType()) && Objects.equals(advertiser.getAccountId(), o.getAccountId())).map(o -> Long.parseLong(o.getUserId())).collect(Collectors.toSet());
            activeUsers.removeAll(keyUsers);
            if(CollectionUtils.isEmpty(activeUsers)) {
                continue;
            }
            String[] values = advertiser.getEventValues().split(",");
            Integer pv = Integer.parseInt(values[0]);
            Double ecpm = Double.parseDouble(values[1]) * 1000;

            Map<String, Date> activeDateMap = ocpcEventList.stream().filter(o -> activeUsers.contains(Long.parseLong(o.getUserId()))).collect(Collectors.toMap(OcpcEvent::getUserId, OcpcEvent::getCreateTime, (o1, o2) -> {
                if(o1.after(o2)) {
                    return o2;
                }
                return o1;
            }));
            List<UserExposureEcpmNew> thisList = this.getBaseMapper().query2Send(ecpmArpuRate, advertiser.getProduct(), pv, ecpm, activeUsers, todayStart, yesterdayStart);
            if(CollectionUtils.isNotEmpty(thisList)) {
                thisList.forEach(db -> {
                    buildValidRemark(activeDateMap, advertiser.getDsp(), advertiser.getProduct(), advertiser.getAccountId(), advertiser.getEventValues(), pv, ecpm, db);
                });
                List<UserExposureEcpmNew> filterList = thisList.stream().filter(UserExposureEcpmNew::isValid).collect(Collectors.toList());
                log.info("ecpmArup校验过滤, dsp:{}, product:{}, name:{}, before:{}, after:{}", advertiser.getDsp(), advertiser.getProduct(), advertiser.getProductName(), thisList.size(), filterList.size());
                matchKeyList.addAll(filterList);
            }
        }
        return matchKeyList;
    }

    enum Cvr3Status {
        INVALID(0),
        VALID(1);
        int val;
        Cvr3Status(int val) {
            this.val = val;
        }
    }

    /**
     * 3.0版本回传
     * @param cvrProduct
     * @param startTime
     * @param endTime
     * @param intervalTime
     * @param queryHour
     */
    public void dealProductOcpcEvent3(CvrProduct cvrProduct, Date startTime, Date endTime, Long intervalTime, int queryHour) {
        //获取上一次检查的内容
        CvrProductCountLog lastLog = cvrProductCountLogService.queryLastLogByCvr(cvrProduct);
        int beforeCanSend = 0;
        if(null != lastLog) {
            long interval = startTime.getTime() - lastLog.getCreateTime().getTime();
            if(interval <= ONE_HOUR) {
                startTime = lastLog.getCreateTime();
                beforeCanSend = lastLog.getLimitKeyCount();// + lastLog.getKeyCount();
            }
        }
        ////如果选择了“池子中的数量*25%”则接下来的4次回传数都是本次“池子中的数量*25%''，即4次传完积压。
        String delaySendKey = cvrProduct.getDsp() + ":" + cvrProduct.getProduct() +":delay_timer_call";
        String redisValue = bpDispenseJedisClusterClient.get(delaySendKey);
        int delaySendSize = StringUtils.isNotBlank(redisValue) ? Integer.parseInt(redisValue) : 0;
        long ttl = bpDispenseJedisClusterClient.ttl(delaySendKey);
        //查询指定h前之后回传的
        Date queryTimeStart = new Date(System.currentTimeMillis() - queryHour * ONE_HOUR);
        List<ProductAdvertiserPool> accountList = productAdvertiserPoolService.lambdaQuery().eq(ProductAdvertiserPool::getProduct, cvrProduct.getProduct()).eq(ProductAdvertiserPool::getDsp, cvrProduct.getDsp()).isNotNull(ProductAdvertiserPool::getEventValues).list();

        PhoneOsEnum osEnum = PhoneOsEnum.getByValue(cvrProduct.getOs());
        log.info("查询对应的cvr, dsp:{}, product:{}, zh:{}, os:{}, account_size:{}", cvrProduct.getDsp(), cvrProduct.getProduct(), cvrProduct.getProductName(), osEnum, accountList.size());
        if(CollectionUtils.isEmpty(accountList)) {
            if(delaySendSize > 0) {
                cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.VALID.val, -1D, null, 0, -1, delaySendSize, -1, -1);
                return;
            }
            cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.INVALID.val, 0D, null, 0, 0);
            return;
        }
        List<String> accountIds = accountList.stream().map(ProductAdvertiserPool::getAccountId).collect(Collectors.toList());
        //查询对应时间内已激活和回传数
        List<OcpcEvent> ocpcEventList = ocpcEventService.lambdaQuery().select(OcpcEvent::getAccountId, OcpcEvent::getEventType, OcpcEvent::getCreateTime, OcpcEvent::getUserId)
                .eq(OcpcEvent::getProduct, cvrProduct.getProduct()).in(OcpcEvent::getAccountId, accountIds).in(OcpcEvent::getEventType, Arrays.asList(0, 25))
                .eq(null != osEnum, OcpcEvent::getOs, null == osEnum ? "" : osEnum.name).ge(OcpcEvent::getCreateTime, queryTimeStart).list();
        if(CollectionUtils.isEmpty(ocpcEventList)) {
            if(delaySendSize > 0) {
                cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.VALID.val, -1D, null, 0, -1, delaySendSize, -1, -1);
                return;
            }
            cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.INVALID.val, 0D, null, 0, 0);
            return;
        }
        //查询严苛时间范围的数据
        Date todayStart = DateUtils.getStartOfDate(new Date()), yesterdayStart = new Date(todayStart.getTime() - ONE_DAY);
        long limitStart = startTime.getTime(), limitEnd = endTime.getTime();
        List<OcpcEvent> timeLimitList = ocpcEventList.stream().filter(o -> {
            long time = o.getCreateTime().getTime();
            return time >= limitStart && time < limitEnd;
        }).collect(Collectors.toList());
        List<String> keyUserIdList = ocpcEventList.stream().filter(o -> Objects.equals(o.getEventType(), 25)).map(OcpcEvent::getUserId).collect(Collectors.toList());
        List<String> activeUserIdList = timeLimitList.stream().filter(o -> Objects.equals(o.getEventType(), 0) && !keyUserIdList.contains(o.getUserId())).map(OcpcEvent::getUserId).collect(Collectors.toList());

        int activeUser = activeUserIdList.size();
        List<Double> cvrLimitRate = Arrays.stream(cvrProduct.getRateLimit().split(",")).map(Double::parseDouble).collect(Collectors.toList());
        if(delaySendSize > 0) {
            //max(本次“池子中的数量*25%''，新“池子中的数量*25%'')。
            List<OcpcEvent> otherQueryList = ocpcEventList.stream().filter(o -> Objects.equals(o.getEventType(), 0) && !keyUserIdList.contains(o.getUserId())).collect(Collectors.toList());
            List<UserExposureEcpmNew> can25UserList = queryValidOcpcUser(otherQueryList, accountList, todayStart, yesterdayStart);
            //这次检查又有可以回传的量
            int size = Double.valueOf(can25UserList.size() * cvrLimitRate.get(2)).intValue();
            if(size == 0) {
                cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.VALID.val, -1D, null, activeUser, -1, delaySendSize, -1, -1);
            }else {
                List<UserExposureEcpmNew> updateList = new ArrayList<>();
                //后几次不需要再计算回传
                bpDispenseJedisClusterClient.incrBy(delaySendKey, size);
                bpDispenseJedisClusterClient.expire(delaySendKey, (int)ttl);
                int times = (int)(ttl / FIVE_MINUTE_SECOND) + (ttl % FIVE_MINUTE_SECOND == 0 ? 0 : 1);
                //仅设置回传剩余时间内的数据，就是还剩几个5min传几个size的用户
                int setSize = Math.min(times * size, can25UserList.size());
                for (int i = 0; i < setSize; i++) {
                    UserExposureEcpmNew db = can25UserList.get(i);
                    long add = ThreadLocalRandom.current().nextLong(FIVE_MINUTE);
                    for (String id : db.getId().split(",")) {
                        UserExposureEcpmNew update = new UserExposureEcpmNew();
                        update.setId(id);
                        update.setStatus(UserEcpmStatus.WAIT.getValue());
                        update.setSendTime(new Date(limitEnd + (FIVE_MINUTE * i / size) + add));
                        update.setRemark(db.getRemark());
                        updateList.add(update);
                    }
                }
                log.info("开始清空积压2, dsp:{}, product:{}, name:{}, os:{}, ttl:{}, activeCount:{}, canCount:{}, old:{}, new:{}, setSize:{}", cvrProduct.getDsp(), cvrProduct.getProduct(), cvrProduct.getProductName(), osEnum, ttl, activeUser, can25UserList.size(), delaySendSize, delaySendSize + size, setSize);
                cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.VALID.val, -1D, "new max", activeUser, -1, delaySendSize + size, -1, -1);
                if(SystemInfo.isNotTest()) {
                    IterateUtils.iterateByStepSize(updateList, 1000, this::updateBatchById);
                }
            }
            return;
        }
        //获得比例数组
        List<Double> cvrArrayRate = Arrays.stream(cvrProduct.getRateArray().split(",")).map(Double::parseDouble).collect(Collectors.toList());
        long rateStartTime = startTime.getTime() - cvrArrayRate.size() * intervalTime;
        List<String> beforeCvrList = new ArrayList<>(cvrArrayRate.size());
        boolean invalid = false;
        double CVRa = 0, leastCvr = cvrLimitRate.get(0);
        for (int i = 0; i < cvrArrayRate.size(); i++) {
            long start = rateStartTime + i * intervalTime, end = start + intervalTime, keyEnd = end + intervalTime;
            int activeCount = (int)ocpcEventList.stream().filter(o -> {
                if(!Objects.equals(o.getEventType(), 0)) {
                    return false;
                }
                long time = o.getCreateTime().getTime();
                return time >= start && time < end;
            }).count(), keyCount = (int)ocpcEventList.stream().filter(o -> {
                if(!Objects.equals(o.getEventType(), 25)) {
                    return false;
                }
                long time = o.getCreateTime().getTime();
                return time >= end && time < keyEnd;
            }).count();
            double cvr = 0 == activeCount ? 0 : (keyCount * 1D / activeCount);
            if(cvr < leastCvr) {
                invalid = true;
            }
            beforeCvrList.add(String.format("%.4f", cvr));
            CVRa += cvr * cvrArrayRate.get(i);
        }
        log.info("获取之前cvr, dsp:{}, product:{}, name:{}, os:{}, rateStart:{}, beforeCvrList:{}, confCvrRate:{}, CVRa:{}, beforeCanSend:{}", cvrProduct.getDsp(), cvrProduct.getProduct(), cvrProduct.getProductName(), osEnum, DateUtils.formatDate(new Date(rateStartTime)), beforeCvrList, cvrProduct.getRateArray(), CVRa, beforeCanSend);

        String remark = StringUtils.join(beforeCvrList, ",");
        List<Double> cvrMRate = Arrays.stream(cvrProduct.getRateConst().split(",")).map(Double::parseDouble).collect(Collectors.toList());
        //去掉所有已经回传的用户
        List<OcpcEvent> otherQueryList = ocpcEventList.stream().filter(o -> Objects.equals(o.getEventType(), 0) && !keyUserIdList.contains(o.getUserId())).collect(Collectors.toList());
        List<UserExposureEcpmNew> can25UserList = queryValidOcpcUser(otherQueryList, accountList, todayStart, yesterdayStart);
        if(CollectionUtils.isEmpty(timeLimitList)) {
            int cvrStatus = invalid ? Cvr3Status.INVALID.val : Cvr3Status.VALID.val;
            cvrProductCountLogService.saveQueryRes(cvrProduct, cvrStatus, invalid ? cvrLimitRate.get(1) : CVRa, remark, 0, can25UserList.size(), 0);
            return;
        }
        int invalidNeed2 = 0, needSend = 0;
        if(invalid) {
            //下5分钟回传方式变更为max（CVRa置为50%，池子中的数量*25%），即选择两种回传方式中，回传数多的一个
            CVRa = cvrLimitRate.get(1);
            invalidNeed2 = Double.valueOf(can25UserList.size() * cvrLimitRate.get(2)).intValue();
        }

        int intervalKeyValue = Math.max(0, can25UserList.size() - beforeCanSend), yCount = 0, zCount = 0;
        double nCVRa = CVRa * cvrMRate.get(0), mCVRa = CVRa * cvrMRate.get(1), x = intervalKeyValue * 1D / activeUser;
        log.info("校验数据, dsp:{}, product:{}, name:{}, os:{}, beforeCanSend:{}, can25UserList:{}, intervalKeyValue:{}, nCVRa:{}, mCVRa:{}, x:{}, activeUser:{},invalidNeed2:{}", cvrProduct.getDsp(), cvrProduct.getProduct(), cvrProduct.getProductName(), osEnum, beforeCanSend, can25UserList.size(), intervalKeyValue, nCVRa, mCVRa, x, activeUser,invalidNeed2);
        if(x > mCVRa) {
            //若x>CVRa*m，则下5分钟回传数为上5分钟激活数*CVRa*m（取整）
            needSend = Double.valueOf(activeUser * mCVRa).intValue();
            yCount = intervalKeyValue - needSend;
        }else if (x< nCVRa){
            //若x<CVRa*n，则下5分钟回传数为上5分钟激活数*CVRa*n（取整）
            needSend = Math.min(can25UserList.size(), Double.valueOf(activeUser * nCVRa).intValue());
            zCount = needSend - intervalKeyValue;
        }else {
            needSend = intervalKeyValue;
        }

        boolean continueFix = false;
        if(invalidNeed2 > needSend) {
            //显示空
            zCount = yCount = -1;
            CVRa = -1D;
            remark += ";" + cvrLimitRate.get(2) + ";";
            continueFix = true;
        }

        int size = Math.max(needSend, invalidNeed2);
        if(size <= 0) {
            log.info("cvr不需要添加回传, dsp:{}, product:{}, name:{}, os:{}, CVRa:{}, mCVRa:{}, nCVRam:{}, activeCount:{}", cvrProduct.getDsp(), cvrProduct.getProduct(), cvrProduct.getProductName(), osEnum, CVRa, mCVRa, nCVRa, activeUser);
            cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.VALID.val, CVRa, remark, activeUser, can25UserList.size(), 0);
            return;
        }
        //回传设备选距离现在时间最远的设备
        if(size < can25UserList.size()) {
            can25UserList.sort(Comparator.comparing(UserExposureEcpmNew::getActiveTime));
        }else if(size > can25UserList.size()) {
            size = can25UserList.size();
        }
        List<UserExposureEcpmNew> updateList = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            UserExposureEcpmNew db = can25UserList.get(i);
            long add = ThreadLocalRandom.current().nextLong(FIVE_MINUTE);
            for (String id : db.getId().split(",")) {
                UserExposureEcpmNew update = new UserExposureEcpmNew();
                update.setId(id);
                update.setStatus(UserEcpmStatus.WAIT.getValue());
                update.setSendTime(new Date(limitEnd + add));
                update.setRemark(db.getRemark());
                updateList.add(update);
            }
        }
        cvrProductCountLogService.saveQueryRes(cvrProduct, Cvr3Status.VALID.val, CVRa, remark, activeUser, can25UserList.size() - size, size, yCount, zCount);
        log.info("cvr需要添加回传处理, needSend:{}, dsp:{}, product:{}, name:{}, os:{}, CVRa:{}, mCVRa:{}, CVRam:{}, activeCount:{}, canCount:{}, size:{}, updateCount:{}", needSend, cvrProduct.getDsp(), cvrProduct.getProduct(), cvrProduct.getProductName(), osEnum, CVRa, mCVRa, nCVRa, activeUser, can25UserList.size(), size, updateList.size());
        if(continueFix) {
            //后几次不需要再计算回传
            bpDispenseJedisClusterClient.setex(delaySendKey, Double.valueOf(FIVE_MINUTE / cvrLimitRate.get(2) / 1000).intValue(), ""+size);
            for (int i = size; i < can25UserList.size(); i++) {
                UserExposureEcpmNew db = can25UserList.get(i);
                long add = ThreadLocalRandom.current().nextLong(FIVE_MINUTE);
                for (String id : db.getId().split(",")) {
                    UserExposureEcpmNew update = new UserExposureEcpmNew();
                    update.setId(id);
                    update.setStatus(UserEcpmStatus.WAIT.getValue());
                    update.setSendTime(new Date(limitEnd + (FIVE_MINUTE * i / size) + add));
                    update.setRemark(db.getRemark());
                    updateList.add(update);
                }
            }
            log.info("开始清空积压, dsp:{}, product:{}, name:{}, os:{}, activeCount:{}, canCount:{}, size:{}, rate:{}", cvrProduct.getDsp(), cvrProduct.getProduct(), cvrProduct.getProductName(), osEnum, activeUser, can25UserList.size(), size, cvrLimitRate.get(2));
        }
        if(SystemInfo.isNotTest()) {
            IterateUtils.iterateByStepSize(updateList, 1000, this::updateBatchById);
        }
    }

    public void releaseByProduct(String product, String dsp, Date startTime, int queryHour) {
        long start = startTime.getTime();
        Date queryTimeStart = new Date(System.currentTimeMillis() - queryHour * ONE_HOUR);
        List<ProductAdvertiserPool> accountList = productAdvertiserPoolService.lambdaQuery().eq(ProductAdvertiserPool::getProduct, product).eq(ProductAdvertiserPool::getDsp, dsp).isNotNull(ProductAdvertiserPool::getEventValues).list();

        if(CollectionUtils.isEmpty(accountList)) {
            return;
        }
        List<String> accountIds = accountList.stream().map(ProductAdvertiserPool::getAccountId).collect(Collectors.toList());
        //查询对应时间内已激活和回传数
        List<OcpcEvent> ocpcEventList = ocpcEventService.lambdaQuery().select(OcpcEvent::getAccountId, OcpcEvent::getEventType, OcpcEvent::getCreateTime, OcpcEvent::getUserId)
                .eq(OcpcEvent::getProduct, product).in(OcpcEvent::getAccountId, accountIds).in(OcpcEvent::getEventType, Arrays.asList(0, 25)).ge(OcpcEvent::getCreateTime, queryTimeStart).list();
        if(CollectionUtils.isEmpty(ocpcEventList)) {
            return;
        }
        List<String> keyUserIdList = ocpcEventList.stream().filter(o -> Objects.equals(o.getEventType(), 25)).map(OcpcEvent::getUserId).collect(Collectors.toList());
        //去掉所有已经回传的用户
        List<OcpcEvent> otherQueryList = ocpcEventList.stream().filter(o -> Objects.equals(o.getEventType(), 0) && !keyUserIdList.contains(o.getUserId())).collect(Collectors.toList());
        //查询严苛时间范围的数据
        Date todayStart = DateUtils.getStartOfDate(new Date()), yesterdayStart = new Date(todayStart.getTime() - ONE_DAY);
        List<UserExposureEcpmNew> can25UserList = queryValidOcpcUser(otherQueryList, accountList, todayStart, yesterdayStart);
        int size = can25UserList.size();
        List<UserExposureEcpmNew> updateList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            UserExposureEcpmNew db = can25UserList.get(i);
            long add = ThreadLocalRandom.current().nextLong(FIVE_MINUTE);
            for (String id : db.getId().split(",")) {
                UserExposureEcpmNew update = new UserExposureEcpmNew();
                update.setId(id);
                update.setStatus(UserEcpmStatus.WAIT.getValue());
                update.setSendTime(new Date(start + add));
                update.setRemark(db.getRemark());
                updateList.add(update);
            }
        }
        IterateUtils.iterateByStepSize(updateList, 1000, this::updateBatchById);
    }
}
