package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.ocpc.entity.UserBid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class RedisRelationshipService {
    @Autowired
    @Qualifier("relationship-redisJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;


    public void saveIdfaCaid(String idfa, String caid, String os, ToutiaoClick click) {
        if (!"ios".equals(os)) {
            return;
        }
        if (StringUtils.isBlank(idfa) || StringUtils.isBlank(caid) || "null".equals(idfa) || "null".equals(caid)) {
            //log.info("idfa或caid为空，不进行存储。idfa={} caid={} click={}", idfa, caid,click);
            return;
        }
        bpDispenseJedisClusterClient.set(caid, idfa);
        if (StringUtils.isNotBlank(click.getCaid2()) && !caid.equals(click.getCaid2())) {
            bpDispenseJedisClusterClient.set(click.getCaid2(), idfa);
        }
    }

    public String getIdfaCaid(String caid){
        if (StringUtils.isBlank(caid) || "null".equals(caid)) {
            return null;
        }
       return bpDispenseJedisClusterClient.get(caid);
    }

    public void incrementCount(String key) {
        Long incr = bpDispenseJedisClusterClient.incr(key);
        //log.info("increment count:{} {}", incr,key);
    }
}
