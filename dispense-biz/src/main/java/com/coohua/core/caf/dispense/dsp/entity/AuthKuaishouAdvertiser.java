package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

/**
 * <p>
 * 广告主
 * </p>
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="AuthKuaishouAdvertiser对象", description="广告主")
public class AuthKuaishouAdvertiser implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private BigInteger id;

    @ApiModelProperty(value = "关联客户中心表ID ")
    private BigInteger customerId;

    @ApiModelProperty(value = "广告主ID（自动生成）")
    private BigInteger advertiserId;

    @ApiModelProperty(value = "产品表ID（运营选择）")
    private BigInteger productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "代理商ID")
    private String agentId;

    @ApiModelProperty(value = "代理商名称")
    private String agentName;

    @ApiModelProperty(value = "投放类型")
    private String putType;

    private Integer eventType;

    private Integer eventNumber;

    @ApiModelProperty(value = "花费")
    private Double costMoney;

    @ApiModelProperty(value = "分组ID")
    private Integer groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private Integer createId;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Integer updateId;

    @ApiModelProperty(value = "更新人")
    private String updateName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "有效标识（目前用在拉广告数据），-1:无效，0：有效")
    private Integer validFlag;
}
