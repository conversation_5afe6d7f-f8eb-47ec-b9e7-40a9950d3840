package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

@Data
public class XiaomiClickReq {

    /**
     * https://api.e.mi.com/doc.html#/1.0.0-mdtag9b26f-omd/document-2bd1c4c260259b072818205a8ae20139
     *
     * 监测链接示例 https://ocpc-api.shinet.cn/dispense/xiaomi/click?dsp=xiaomi&product=yydxny&os=android&imei=__IMEI__&oaid=__OAID__&click_time=__TS__&app_id=__APPID__&adid=__ADID__&campaign_id=__CAMPAIGNID__&customer_id=__CUSTOMERID__&callback=__CALLBACK__&sign=__SIGN__&ip=__IP__&androidId=__ANDROIDID__
     */

    private String dsp;
    private String product;
    private String os;
    private String imei;
    private String oaid;
    /** 广告点击发生时间 */
    private String click_time;
    /** 小米渠道投放的渠道包的 id （可兼容快应用id） */
    private String app_id;
    /** 广告创意 id */
    private String adid;
    /** 广告计划 id */
    private String campaign_id;
    /** 账户id */
    private String customer_id;
    private String callback;
    private String sign;
    private String ip;
    private String androidId;


}
