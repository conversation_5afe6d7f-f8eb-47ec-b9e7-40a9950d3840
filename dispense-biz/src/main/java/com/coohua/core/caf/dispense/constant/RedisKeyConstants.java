package com.coohua.core.caf.dispense.constant;

/**
 * (。≖ˇェˇ≖。)
 * redis key 常数类
 * @author: zkk
 * DateTime: 2020/8/7 17:59
 */
public class RedisKeyConstants {

	/**
	 * click 事件 mac key
	 */
	public static final String REMOVE_EVENT_LOCK ="REMOVE_EVENT_LOCK";
	public static final String REMOVE_CLIKC_LOCK ="REMOVE_CLICK_LOCK";
	/**
	 * click 事件 mac key
	 */
	public static final String CLICK_MAC_PRE ="ck:mac:%s:%s:%s";
	/**
	 * click 事件 androidId key
	 */
	public static final String CLICK_ANDROID_ID_PRE ="ck:android:id:%s:%s:%s";


	public static final String CLICK_OPEN_ID_PRE ="ck:op:%s:%s";
	/**
	 * click 事件 oaid key
	 */
	public static final String CLICK_OAID_PRE ="ck:od:%s:%s:%s";

	public static final String CLICK_CAID_PRE ="ck:caid:%s:%s:%s";
	/**
	 * click 事件 oaid2 key
	 */
	public static final String CLICK_OAID2_PRE ="ck:od2:%s:%s:%s";

	public static final String CLICK_CAID2_PRE ="ck:caid2:%s:%s:%s";
	/**
	 * click 事件 ipua key
	 */
	public static final String CLICK_IPUA_PRE ="ck:ipua:%s:%s:%s";

	public static final String CLICK_UAMD ="ck:uuii:%s:%s:%s";
	public static final String umod ="ck:umod:%s:%s:%s";
	public static final String ipdd ="ck:ipdd:%s:%s:%s";

	/**
	 * click 事件 idfa2 key
	 */
	public static final String CLICK_IDFA2_PRE ="ck:idfa2:%s:%s:%s";
	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String CLICK_DEVICE_PRE ="ck:did:%s:%s:%s";

	public static final String  REPORT_LOCK_KEY= "report:lock:%s:%s:%d";

	/**
	 * 创建ocpc表
	 */
	public static final String CREATE_OCPC_TABLE_LOCK_KEY = "crt_ocpc_table_lock";
	/**
	 * click 事件 androidId key
	 */
	public static final String CLICK_ANDROID_ID_PRE_PKG ="ck:android:id:%s:%s:%s:%s";
	/**
	 * click 事件 mac key
	 */
	public static final String CLICK_MAC_PRE_PKG ="ck:mac:%s:%s:%s:%s";
	/**
	 * click 事件 oaid key
	 */
	public static final String CLICK_OAID_PRE_PKG ="ck:od:%s:%s:%s:%s";
	/**
	 * click 事件 oaid2 key
	 */
	public static final String CLICK_OAID2_PRE_PKG ="ck:od2:%s:%s:%s:%s";
	/**
	 * click 事件 ipua key
	 */
	public static final String CLICK_IPUA_PRE_PKG ="ck:ipua:%s:%s:%s:%s";
	/**
	 * click 事件 idfa2 key
	 */
	public static final String CLICK_IDFA2_PRE_PKG ="ck:idfa2:%s:%s:%s:%s";
	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String CLICK_DEVICE_PRE_PKG ="ck:did:%s:%s:%s:%s";


	/**
	 * click事件 deviceId key（idfa和安卓的设备号）
	 */
	public static final String EVENT_DEVICE_ACTIVE ="event:active:%s:%s:%s:%s";
	public static final String EVENT_OPEN_ACTIVE ="e:op:%s:%s:%s";
	public static final String EVENT_CAID_ACTIVE ="e:caid:%s:%s";
	public static final String EVENT_UID_ACTIVE ="ue:uid:%s:%s:%s";
	public static final String WX_REDIS_LOCK_ACT ="WX:active:lock:%s:%s:%s:%s";

	public static final String EVENT_COMPENSATE_LOCK = "event2:compensate:lock";
	public static final String VIVO_CLICK_LOCK = "vivo:click:lock";

	public static final String EVENT_ACTIVE_USERID = "event:useractive:%s:%s:%s";
	public static final String KS_BURUN_LOCK = "kminurnd:evetbu:lock";

	public  static String getActiveOpenIdKey( String product,String wappid,String openId){
		return String.format(EVENT_OPEN_ACTIVE,product,wappid,openId);
	}

	public  static String getActiveCaidKey( String product,String caid){
		return String.format(EVENT_CAID_ACTIVE,product,caid);
	}

	public  static String getActiveUidKey( String product,String os,String userId){
		return String.format(EVENT_UID_ACTIVE,product,os,userId);
	}

	public  static String getActiveEventKey( String product,String os,String type,String strId){
		return String.format(EVENT_DEVICE_ACTIVE,product,os,type,strId);
	}


	public  static String getWxActiveLockKey( String product,String os,String type,String strId){
		return String.format(WX_REDIS_LOCK_ACT,product,os,type,strId);
	}
	public static final String KEY_EVENT_FORMATTER = "key:event:%s:%s:%s:%s";

	public static final String KEY_ACTION_EXT_EVENT_LIST_FORMATTER = "key:ext_event_list:%s:%s:%s";

	public static final String SYNC_PKG_TO_PRODUCT_TO_OCPC_KEY = "key:sync:pkgToProduct";

	public static final String SYNC_PRODUCT_TO_OCPC_KEY = "key:sync:product";

	public static final String USER_ACC_ARPU_LOCK_KEY = "key:lock:user_acc_arpu:%s:%s:%s";

	public static final String USER_ACC_NUM_LOCK_KEY = "key:lock:user_acc_num:%s:%s:%s";

	public static final String USER_FIX_BUG_LOCK_KEY = "key:lock:user_fix_bug:%s:%s:%s";

	public static final String USER_ACC_ARPU_FORMATTER = "key:user_acc_arpu:%s:%s:%s";

	public static final String USER_ACC_NUM_FORMATTER = "key:user_acc_num:%s:%s:%s";

	public static final String KUAISHOU_UDS_LOCK_KEY = "key:lock:kuaishou_uds:";

	public static final String TOUTIAO_EXTRA_LOCK_KEY = "key:lock:toutiao_extra:";

	/**
	 * 此处caid过滤激活
	 */
	public static final String IOS_NO_ACTIVE_CAID = "ios:no:active:caid:%s";
	public static final String CVR_VALID_PRODUCT_ACCOUNT = "cvr:valid:product:account:%s_%s_%s";
	public static final String CVR_TIMING_VALID_PRODUCT_ACCOUNT = "cvr:timing:valid:product:account:%s_%s";

	public static String  getClickAndroidIdKey( String product,String os, String androidId) {
		return String.format(CLICK_ANDROID_ID_PRE,product,os,androidId);
	}

	public static String  getClickOpenKey( String product, String openId) {
		return String.format(CLICK_OPEN_ID_PRE,product,openId);
	}

	public static String  getClickCaidKey(String product, String caid,String os) {
		return String.format(CLICK_CAID_PRE,product,os,caid);
	}

	public static String getClickCaid2Key(String product, String os,String caid2) {
		return String.format(CLICK_CAID2_PRE,product,os,caid2);
	}

	public static String  getClickMacKey( String product,String os, String mac) {
		return String.format(CLICK_MAC_PRE,product,os,mac);
	}

	public static String getClickOaIdKey(String product, String os,String oaId) {
		return String.format(CLICK_OAID_PRE,product,os,oaId);
	}

	public static String getClickOaId2Key(String product, String os,String oaId2) {
		return String.format(CLICK_OAID2_PRE,product,os,oaId2);
	}

	public static String getClickIpuaKey(String product, String os,String ipua) {
		return String.format(CLICK_IPUA_PRE,product,os,ipua);
	}

	public static String getClickIAKey(String product, String os,String ipua) {
		return String.format(CLICK_UAMD,product,os,ipua);
	}

	public static String getClickIMKey(String product, String os,String upmd) {
		return String.format(umod,product,os,upmd);
	}

	public static String getClickIpKey(String product, String os,String ip) {
		return String.format(ipdd,product,os,ip);
	}
	public static String getClickIdfa2Key(String product, String os,String idfa2) {
		return String.format(CLICK_IDFA2_PRE,product,os,idfa2);
	}

	public static String getClickDeviceKey(String product, String os,String deviceId) {
		return String.format(CLICK_DEVICE_PRE,product,os,deviceId);
	}


	public static String  getClickAndroidIdKeyByPkgChannel( String product,String os, String androidId,String pkgChannel) {
		return String.format(CLICK_ANDROID_ID_PRE_PKG,product,os,androidId,pkgChannel);
	}

	public static String  getClickMacKeyByPkgChannel( String product,String os, String mac,String pkgChannel) {
		return String.format(CLICK_MAC_PRE_PKG,product,os,mac,pkgChannel);
	}

	public static String getClickOaIdKeyByPkgChannel(String product, String os,String oaId,String pkgChannel) {
		return String.format(CLICK_OAID_PRE_PKG,product,os,oaId,pkgChannel);
	}

	public static String getClickOaId2KeyByPkgChannel(String product, String os,String oaId2,String pkgChannel) {
		return String.format(CLICK_OAID2_PRE_PKG,product,os,oaId2,pkgChannel);
	}

	public static String getClickIpuaKeyByPkgChannel(String product, String os,String ipua,String pkgChannel) {
		return String.format(CLICK_IPUA_PRE_PKG,product,os,ipua,pkgChannel);
	}

	public static String getClickIdfa2KeyByPkgChannel(String product, String os,String idfa2,String pkgChannel) {
		return String.format(CLICK_IDFA2_PRE_PKG,product,os,idfa2,pkgChannel);
	}

	public static String getClickDeviceKeyByPkgChannel(String product, String os,String deviceId,String pkgChannel) {
		return String.format(CLICK_DEVICE_PRE_PKG,product,os,deviceId,pkgChannel);
	}

	public static String getReportLockKey(String product, String userId, int eventType) {
		return String.format(REPORT_LOCK_KEY, product, userId, eventType);
	}

	public static String getUserAccArpuLockKey(String product, String os, String userId) {
		return String.format(USER_ACC_ARPU_LOCK_KEY, product, os, userId);
	}

	public static String getUserAccNumLockKey(String product, String os, String userId) {
		return String.format(USER_ACC_NUM_LOCK_KEY, product, os, userId);
	}

	public static String getUserFixBugLockKey(String product, String os, String userId) {
		return String.format(USER_FIX_BUG_LOCK_KEY, product, os, userId);
	}

	public static String  getIosNoActiveCaid( String caid) {
		return String.format(IOS_NO_ACTIVE_CAID,caid);
	}

	public static String getCvrValidProductAccount(String product, String accountId, String os) {
		return String.format(CVR_VALID_PRODUCT_ACCOUNT, product, accountId, os);
	}

	public static String getCvrTimingValidProductAccount(String product, String accountId) {
		return String.format(CVR_TIMING_VALID_PRODUCT_ACCOUNT, product, accountId);
	}
}
