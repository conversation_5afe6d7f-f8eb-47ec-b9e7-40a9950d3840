package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="Product对象", description="")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "同 pkg_id")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "数据打点产品名")
    private String name;

    @ApiModelProperty(value = "产品名称")
    private String remark;

    @ApiModelProperty(value = "项目组名称")
    private String teamName;

    @ApiModelProperty(value = "分组ID")
    private Integer groupId;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人ID")
    private Integer createId;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "更新人ID")
    private Integer updateId;

    @ApiModelProperty(value = "更新人")
    private String updateName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "app_id")
    private Integer appId;

    @ApiModelProperty(value = "头条appid")
    private String appIdTt;

    @ApiModelProperty(value = "广点通appid")
    private String appIdGdt;

    @ApiModelProperty(value = "穿山甲appid_ios")
    private String appIdTtIos;

    @ApiModelProperty(value = "广点通appid_ios")
    private String appIdGdtIos;

    private String appOs;

    @ApiModelProperty(value = "-999未设置 0未分类 1走路类")
    private Integer productType;

    @ApiModelProperty(value = "微信小游戏appId")
    private String wgameAppId;


}
