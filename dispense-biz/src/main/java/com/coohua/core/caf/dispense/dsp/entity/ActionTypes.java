package com.coohua.core.caf.dispense.dsp.entity;

import com.alibaba.fastjson.JSON;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 当用户首次满足关键行为判断条件时，
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
@Slf4j
public class ActionTypes {

    private String eventTypes;


    /**
     * <eventType, eventValue>
     */
    private Map<String, String> map = new HashMap<>();


    public static ActionTypes build(Map<String, String> configurations) {

        try {
            ActionTypes actionTypes = new ActionTypes();

            List<String> keys = new ArrayList<>(configurations.keySet());
            keys.sort(Comparator.comparing(Long::valueOf));
            actionTypes.eventTypes = StringUtils.join(keys, ",");

            actionTypes.map = new HashMap<>(keys.size());

            for (Map.Entry<String, String> entry : configurations.entrySet()) {
                actionTypes.map.put(entry.getKey(), entry.getValue());
            }

            return actionTypes;
        } catch (Exception e) {
            log.error("构造ActionTypes异常 " + JSON.toJSONString(configurations), e);
            return null;
        }
    }

//    public static ActionTypes convertActionType(AuthToutiaoAdvertiser advertiser, UserEventReq userEventReq) {
//
//        //若无配置
//        if (advertiser == null) {
//            log.info("衍生事件回传advertiserId参数不存在 " + advertiser.getAdvertiserId());
//            return null;
//        }
//
//        if (StringUtils.isBlank(advertiser.getEventTypes())) {
//            log.info("衍生事件回传广告主未配置行为类型 " + advertiser.getAdvertiserId());
//            return null;
//        }
//
//        try {
//            // 回调请求的传值
//            Map<String, String> reqValues = OcpcNewCallbackRuleValidateUtil.analyzeNewValueToMap(userEventReq.getActionValues());
//            List<String> keys = new ArrayList<>(reqValues.keySet());
//            keys.sort(Comparator.comparing(Long::valueOf));
//            String reqEventTypes = StringUtils.join(keys, ",");
//
//            // 请求的传值类型需要和账户的类型一致
//            if (!advertiser.getEventTypes().equals(reqEventTypes)) {
//                log.info("转换用户行为类型账户与当前传值不匹配 userId:{} 账户:{} 当前:{}", userEventReq.getUserId(), advertiser.getEventTypes(), reqEventTypes);
//                return null;
//            }
//
//            return new ActionTypes()
//                    .setEventTypes(reqEventTypes)
//                    .setMap(reqValues);
//
//        } catch (Exception e) {
//            log.error("转换用户行为类型异常 " + JSON.toJSONString(userEventReq), e);
//            return null;
//        }
//    }


}
