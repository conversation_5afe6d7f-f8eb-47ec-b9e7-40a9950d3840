package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 快手uds回传-广告主id配置表
 * </p>
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class KuaishouUdsAdvertiser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String productName;

    private Long advertiserId;

    private Date createTime;

    private Date updateTime;

}
