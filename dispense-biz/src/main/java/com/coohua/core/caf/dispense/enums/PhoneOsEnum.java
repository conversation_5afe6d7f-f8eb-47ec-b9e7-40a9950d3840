package com.coohua.core.caf.dispense.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 */

public enum PhoneOsEnum {
	android(1, "android"),
	ios(2, "ios"),
	;

	public Integer value;
	public String name;

	PhoneOsEnum(Integer value, String name) {
		this.value = value;
		this.name = name;
	}

	public static PhoneOsEnum getByValue(Integer value) {
		for(PhoneOsEnum osEnum : PhoneOsEnum.values()) {
			if(Objects.equals(value, osEnum.value)) {
				return osEnum;
			}
		}
		return null;
	}
}
