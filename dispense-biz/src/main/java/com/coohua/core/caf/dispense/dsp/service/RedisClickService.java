package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.ConstApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dsp.entity.OcpcDeviceEntity;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.hbase.HbaseClickService;
import com.coohua.core.caf.dispense.hbase.HbaseUtils;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.coohua.core.caf.dispense.kafka.dto.CaidMappingGyData;
import com.coohua.core.caf.dispense.ocpc.entity.MatchClickBroad;
import com.coohua.core.caf.dispense.ocpc.service.MatchClickBroadService;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import com.coohua.core.caf.dispense.utils.ConstCls;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTimeConstants;
import org.joda.time.LocalDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk DateTime: 2020/8/7 18:24
 */
@Service
@Slf4j
public class RedisClickService {
    @Autowired
    private ToutiaoClickService toutiaoClickService;

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    @Autowired
    RedisRelationshipService redisRelationshipService;


    /**
     * 是否存查询MySQL
     */
    @Value("${mysql.query.enable:true}")
    public boolean queryEnable;

    @Autowired
    private UserEventPkgService userEventPkgService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    ConstApolloConfig constApolloConfig;
    @Autowired
    HbaseClickService hbaseClickService;
    @Autowired
    MatchClickBroadService matchClickBroadService;
    @Autowired
    SafeDataRedisService safeDataRedisService;
    @Autowired
    KafkaSender kafkaSender;

    /**
     * OCPC 归因新增渠道包参数
     *
     * @param product
     * @param deviceId
     * @param oaId
     * @param mac
     * @param click
     */
    public void saveRedisClick(String product, String deviceId, String oaId, String mac, ToutiaoClick click) {
        if (!ocpcSwitcher.writeRedisSwitch) {
            return;
        }
        String pkgChannel = userEventPkgService.queryPkgChannel(click.getPkgChannel());
        // 渠道包为空则走老逻辑
        if (UserEventPkgService.PKG_EMPTY.equals(pkgChannel)) {
            saveRedisClickByDevice(product, deviceId, oaId, mac, click);
        } else {
            saveRedisClickByPkgChanel(product, deviceId, oaId, mac, click);
        }
    }

    public void saveIdfaCaid(String idfa, String caid) {
        if (StringUtils.isNotBlank(idfa) || StringUtils.isNotBlank(caid)) {
            log.info("deviceId或caid为空，不进行存储。idfa={} caid={}", idfa, caid);
            return;
        }
        String key = idfa + "_" + caid;
        log.info("存储deviceId与caid关系的key:{}", key);
        bpDispenseJedisClusterClient.setnx(key, "1");
    }

    /**
     * 存储渠道包 click事件
     *
     * @param product
     * @param deviceId
     * @param oaId
     * @param mac
     * @param click
     */
    private void saveRedisClickByPkgChanel(String product, String deviceId, String oaId, String mac,
                                           ToutiaoClick click) {
        try {
            String os = click.getOs();
            String oaid2 = click.getOaid2();
            String idfa2 = click.getIdfa2();
            if (StringUtils.isNotBlank(deviceId) && !ConstCls.zeroMd5.equals(deviceId)) {
                bpDispenseJedisClusterClient.setex(
                        RedisKeyConstants.getClickDeviceKeyByPkgChannel(product, os, deviceId, click.getPkgChannel()),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(idfa2) && !ConstCls.zeroMd5.equals(idfa2)) {
                bpDispenseJedisClusterClient.setex(
                        RedisKeyConstants.getClickIdfa2KeyByPkgChannel(product, os, idfa2, click.getPkgChannel()),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(oaId)) {
                bpDispenseJedisClusterClient.setex(
                        RedisKeyConstants.getClickOaIdKeyByPkgChannel(product, os, oaId, click.getPkgChannel()),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(oaid2)) {
                bpDispenseJedisClusterClient.setex(
                        RedisKeyConstants.getClickOaId2KeyByPkgChannel(product, os, oaid2, click.getPkgChannel()),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(mac)) {
                bpDispenseJedisClusterClient.setex(
                        RedisKeyConstants.getClickMacKeyByPkgChannel(product, os, mac, click.getPkgChannel()),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(click.getAndroidId())) {
                bpDispenseJedisClusterClient.setex(
                        RedisKeyConstants.getClickAndroidIdKeyByPkgChannel(product, os, click.getAndroidId(), click.getPkgChannel()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(click.concatIpua())) {
                bpDispenseJedisClusterClient.setex(
                        RedisKeyConstants.getClickIpuaKeyByPkgChannel(product, os, click.concatIpua(), click.getPkgChannel()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }

            if (StringUtils.isNotBlank(click.concatIpModel())) {
                String rkey = RedisKeyConstants.getClickIMKey(product, click.getOs(), click.concatIpModel());
                bpDispenseJedisClusterClient.setex(rkey, DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }

            if (StringUtils.isNotBlank(click.getTrackId())) {
                bpDispenseJedisClusterClient.setex(click.getTrackId(), DateTimeConstants.SECONDS_PER_DAY, "1");
            }

            log.info(
                    "【redis saveRedisClickByPkgChanel click success】 product={} ，deviceId={} ,oaId={} ,mac={} ,click={}",
                    product, deviceId, oaId, mac, JSONObject.toJSONString(click));

        } catch (Exception e) {
            log.error("redis add error ", e);
        }
    }

    /**
     * redis写入 click事件
     *
     * @param product
     * @param deviceId
     * @param oaId
     * @param mac
     * @param click
     */
    private void saveRedisClickByDevice(String product, String deviceId, String oaId, String mac, ToutiaoClick click) {
        try {
            String os = click.getOs();
            String oaid2 = click.getOaid2();
            String idfa2 = click.getIdfa2();
            if (StringUtils.isNotBlank(deviceId) && !ConstCls.zeroMd5.equals(deviceId)) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickDeviceKey(product, os, deviceId),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(idfa2) && !ConstCls.zeroMd5.equals(idfa2)) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickIdfa2Key(product, os, idfa2),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(oaId)) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickOaIdKey(product, os, oaId),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(oaid2)) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickOaId2Key(product, os, oaid2),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(mac)) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickMacKey(product, os, mac),
                        DateTimeConstants.SECONDS_PER_DAY * 1, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(click.getAndroidId())) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickAndroidIdKey(product, os, click.getAndroidId()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(click.concatIpua())) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickIpuaKey(product, os, click.concatIpua()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }


            if (StringUtils.isNotBlank(click.concatIpuaMd())) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickIAKey(product, os, click.concatIpuaMd()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }


            if (StringUtils.isNotBlank(click.getOpenId())) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickOpenKey(product, click.getOpenId()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }

            if (StringUtils.isNotBlank(click.getCaid()) && StringUtils.equalsIgnoreCase("ios", os)) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickCaidKey(product, os, click.getCaid()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }
            if (StringUtils.isNotBlank(click.getCaid2()) && StringUtils.equalsIgnoreCase("ios", os)) {
                bpDispenseJedisClusterClient.setex(RedisKeyConstants.getClickCaid2Key(product, os, click.getCaid2()),
                        DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }

            if (StringUtils.isNotBlank(click.concatIpModel()) && StringUtils.equalsIgnoreCase("ios", os)) {
                String rkey = RedisKeyConstants.getClickIMKey(product, click.getOs(), click.concatIpModel());
                bpDispenseJedisClusterClient.setex(rkey, DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }

            if (StringUtils.isNotBlank(click.concatIp()) && StringUtils.equalsIgnoreCase("ios", os)) {
                String rkey = RedisKeyConstants.getClickIpKey(product, click.getOs(), click.concatIp());
//                log.info("保存ipkey "+rkey);
                bpDispenseJedisClusterClient.setex(rkey, DateTimeConstants.SECONDS_PER_DAY, JSONObject.toJSONString(click));
            }

            if (StringUtils.isNotBlank(click.getTrackId())) {
                bpDispenseJedisClusterClient.setex(click.getTrackId(), DateTimeConstants.SECONDS_PER_DAY, "1");
            }

        } catch (Exception e) {
            log.error("redis add error ", e);
        }

    }

    /**
     * 获取点击事件
     *
     * @param getDataFromHbase 为true时 从hbase中获取 为false时 从redis中获取
     */
    public ToutiaoClick getClick(UserEventReq request, boolean getDataFromHbase) {
        if (getDataFromHbase) {
            return hbaseClickService.getHbaseClickByDevice(request);
        } else {
            return getClickByRedis(request);
        }
    }

    private ToutiaoClick getClickByRedis(UserEventReq request) {
        return getClickByDevice(request);
    }

    /**
     * redis 查询click事件
     *
     * @param request
     * @return
     */
    public ToutiaoClick getClickByDevice(UserEventReq request) {
        ToutiaoClick click = null;
        try {
            String product = request.getProduct();
            if (StringUtils.isEmpty(product)) {
                log.warn("redisGetClick product is null ! {}", JSONObject.toJSONString(request));
                return click;
            }
            /**
             * 先按设备号查询
             */
            if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickDeviceKey(product, request.getOs(), request.getOcpcDeviceId()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.deive.name);
                    click.setGuiType(GuiyingType.deive.name);
                    //如果deviceId是OAID，IMEI，安卓ID、CAID，IDFA中的一个则为精准匹配
                    if (isAccurateMatch(request)) {
                        log.info("精准匹配成功 deviceId 设备 {}", request.getOs());
                    } else {
                        log.info("非精准匹配 deviceId 设备 {}", request.getOs());
                        if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                            String s1 = obscure2AccurateMatch(request, product);
                            if (s1 != null) {
                                click = JSONObject.parseObject(s, ToutiaoClick.class);
                                request.setGuiType(GuiyingType.idfa.name);
                                click.setGuiType(GuiyingType.idfa.name);
                            }
                        }
                    }

                }
            }

            if (click == null && StringUtils.isNotBlank(request.getCaid()) && "ios".equals(request.getOs().toLowerCase())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaidKey(product, request.getOs(), request.getCaid()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.caid.name);
                    click.setGuiType(GuiyingType.caid.name);
                    log.info("精准匹配成功 caid查归因成功 设备 {} " + product + " " + request.getCaid(), request.getOs());
                } else {
                    //记录日志
                    log.info("未找到归因事件 idType={} id={}", "caid", request.getCaid());
                }
            }

            if (click == null && StringUtils.isNotBlank(request.getCaid2()) && "ios".equals(request.getOs().toLowerCase())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaid2Key(product, request.getOs(), request.getCaid2()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.caid.name);
                    click.setGuiType(GuiyingType.caid.name);
                    log.info("精准匹配成功 caid2查归因成功 设备 {} " + product + " " + request.getCaid2(), request.getOs());
                } else {
                    //记录日志
                    log.info("未找到归因事件 idType={} id={}", "caid2", request.getCaid2());
                }
            }
            /**
             * 按idfa_md5查询
             */
            if (StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIdfa2Key(product, request.getOs(), request.getIdfa2()));
                if (click != null && StringUtils.isNotBlank(s)) {
                    log.info("prodctclk " + s + "@" + JSON.toJSONString(click));
                }
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.idfa.name);
                    click.setGuiType(GuiyingType.idfa.name);
                    log.info("精准匹配成功 idfa 设备 {} ", request.getOs());
                } else {
                    //记录日志
                    log.info("未找到归因事件 idType={} id={}", "idfa2", request.getIdfa2());
                }
            }
            /**
             * click不存在再查oaId
             */
            if (click == null && StringUtils.isNotBlank(request.getOaid())) {
                String rkey = RedisKeyConstants.getClickOaIdKey(product, request.getOs(), request.getOs());
                String s = bpDispenseJedisClusterClient.get(rkey);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.oaid.name);
                    click.setGuiType(GuiyingType.oaid.name);
                    log.info("精准匹配成功 oaId 设备 {} ", request.getOs());
                }
            }
            if (click == null && StringUtils.isNotBlank(request.getOaid2())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickOaId2Key(product, request.getOs(), request.getOaid2()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.oaid.name);
                    click.setGuiType(GuiyingType.oaid.name);
                    log.info("精准匹配成功 oaId2 设备 {} ", request.getOs());

                }
            }
            /**
             * 设备号和 oaId 查不到 再用mac地址查
             */
            if (click == null && StringUtils.isNotBlank(request.getMac())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickMacKey(product, request.getOs(), request.getMac()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.mac.name);
                    click.setGuiType(GuiyingType.mac.name);
                    log.info("非精准匹配 mac归因成功 设备 {} " + product + " " + request.getMac() + " " + request.getUserId(), request.getOs());
                    if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                        String s1 = obscure2AccurateMatch(request, product);
                        if (StringUtils.isNotBlank(s1)) {
                            click = JSONObject.parseObject(s1, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.idfa.name);
                            click.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }
            }
            /**
             * android查
             */
            if (click == null && StringUtils.isNotBlank(request.getAndroidId())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickAndroidIdKey(product, request.getOs(), request.getAndroidId()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.androidId.name);
                    click.setGuiType(GuiyingType.androidId.name);
                    log.info("精准匹配 android查归因成功 设备 {} " + product + " " + request.getAndroidId() + " " + request.getUserId(), request.getOs());
                }
            }


            /**
             * 查ipua
             */
            if (click == null && StringUtils.isNotBlank(request.concatIpua())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIpuaKey(product, request.getOs(), request.concatIpua()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    log.info("非精准匹配 ipua查归因成功 设备 {} " + product + " " + request.concatIpua() + " " + request.getUserId() + " " + JSON.toJSONString(request), request.getOs());
                    request.setGuiType(GuiyingType.ipua.name);
                    click.setGuiType(GuiyingType.ipua.name);
                    if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                        String s1 = obscure2AccurateMatch(request, product);
                        if (StringUtils.isNotBlank(s1)) {
                            click = JSONObject.parseObject(s1, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.idfa.name);
                            click.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }
            }


            /**
             * openId查
             */
            if (click == null && StringUtils.isNotBlank(request.getOpenId())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickOpenKey(product, request.getOpenId()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.openid.name);
                    click.setGuiType(GuiyingType.openid.name);
                    log.info("非精准匹配 openId查归因成功 设备 {} " + product + " " + request.getAndroidId(), request.getOs());
                    if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                        String s1 = obscure2AccurateMatch(request, product);
                        if (StringUtils.isNotBlank(s1)) {
                            click = JSONObject.parseObject(s1, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.idfa.name);
                            click.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }
            }
            if (click == null && StringUtils.isNotBlank(request.concatIpuaMd())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIAKey(product, request.getOs(), request.concatIpuaMd()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.iunod.name);
                    click.setGuiType(GuiyingType.iunod.name);
                    log.info("非精准匹配 iunod 设备 {} " + product + " " + request.concatIpuaMd() + " " + request.getUserId(), request.getOs());
                    if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                        String s1 = obscure2AccurateMatch(request, product);
                        if (StringUtils.isNotBlank(s1)) {
                            click = JSONObject.parseObject(s1, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.idfa.name);
                            click.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }
            }
            if (click == null && "ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.concatIpMd())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIMKey(product, request.getOs(), request.concatIpMd()));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.ipmd.name);
                    click.setGuiType(GuiyingType.ipmd.name);
                    log.info("非精准匹配 ipmodeliosipmd 设备 {} " + product + " " + request.concatIpMd() + " " + request.getUserId(), request.getOs());
                    if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                        String s1 = obscure2AccurateMatch(request, product);
                        if (StringUtils.isNotBlank(s1)) {
                            click = JSONObject.parseObject(s1, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.idfa.name);
                            click.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }
            }

            if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isBlank(request.concatIp())) {
                log.info("获取ipkey错误 " + JSON.toJSONString(request));
            }
            if (click == null && "ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.concatIp())) {
                String rkey = RedisKeyConstants.getClickIpKey(product, request.getOs(), request.concatIp());
                log.info("获取ipkey " + rkey);
                String s = bpDispenseJedisClusterClient.get(rkey);
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    click.setGuiType(GuiyingType.ip.name);
                    request.setGuiType(GuiyingType.ip.name);
                    log.info("非精准匹配 ipmodeliosip 设备 {} " + product + " " + request.concatIpMd() + " " + request.getUserId(), request.getOs());
                    if ("ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                        String s1 = obscure2AccurateMatch(request, product);
                        if (StringUtils.isNotBlank(s1)) {
                            click = JSONObject.parseObject(s1, ToutiaoClick.class);
                            click.setGuiType(GuiyingType.idfa.name);
                            request.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }
            }

            if (click == null && "ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                String idfa = redisRelationshipService.getIdfaCaid(request.getCaid());
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIdfa2Key(product, request.getOs(), idfa));
                if (StringUtils.isNotBlank(s)) {
                    click = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.idfa.name);
                    click.setGuiType(GuiyingType.idfa.name);
                    log.info("通过映射 由未匹配到精准匹配成功 idfa 设备 {} ", request.getOs());
                }
            }


            if (click != null) {
                log.info("redisGetClick success req={} ,rsp={}", JSONObject.toJSONString(request), JSONObject.toJSONString(click));
            }
        } catch (Exception e) {
            log.error("redisGetClick error ", e);
        }
        return click;
    }

    private String obscure2AccurateMatch(UserEventReq request, String product) {
        String idfa = redisRelationshipService.getIdfaCaid(request.getCaid());
        if (StringUtils.isNotBlank(idfa)) {
            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIdfa2Key(product, request.getOs(), idfa));
            if (StringUtils.isNotBlank(s)) {
                log.info("通过映射 由模糊到精准匹配成功 idfa 设备 {} ", request.getOs());
                return s;
            }
        }
        return null;
    }

    private boolean isAccurateMatch(UserEventReq request) {
        List<String> accurateIdList = new ArrayList<>();
        accurateIdList.add(request.getOaid());
        accurateIdList.add(request.getOaid2());
        accurateIdList.add(request.getAndroidId());
        accurateIdList.add(request.getAndroidMd5Id());
        accurateIdList.add(request.getCaid());
        accurateIdList.add(request.getCaid2());
        accurateIdList.add(request.getIdfa2());
        log.info("精准匹配列表accurateIdList {} ocpcDeviceId {}", JSONObject.toJSONString(accurateIdList), request.getOcpcDeviceId());
        return accurateIdList.contains(request.getOcpcDeviceId());
    }

    public boolean setOpenidActive(String product, String openId) {
        return setOpenidActive(product, openId, null);
    }

    public boolean setOpenidActive(String product, String openId, String minPlt) {
        if (StringUtils.isNotBlank(product) && StringUtils.isNotBlank(openId)) {
            String wkey = getActiveKey(product, openId, minPlt);
            String s = bpDispenseJedisClusterClient.get(wkey);
            if (StringUtils.isNotBlank(s)) {
                return false;
            } else {
                bpDispenseJedisClusterClient.setex(wkey, DateTimeConstants.SECONDS_PER_DAY * 8, "1");
                return true;
            }
        }
        return false;
    }


    public String getActiveKey(String product, String openId, String minPlt) {
        String wkey = "actWxOpenId:" + product + "@" + openId;
        if (StringUtils.isNotBlank(minPlt)) {
            wkey = wkey + "@" + minPlt;
        }
        return wkey;
    }


    public void setCaid(String product, String userId, String caidJson) {
        bpDispenseJedisClusterClient.setex("caidj:" + product + "@" + userId, DateTimeConstants.SECONDS_PER_HOUR * 12, caidJson);
        log.info("更新caidchche " + "caidj:" + product + "@" + userId + "   " + caidJson);
    }

    public String getCaidJson(String product, String userId) {
        String caidJson = bpDispenseJedisClusterClient.get("caidj:" + product + "@" + userId);
        return caidJson;
    }

    public ToutiaoClick getAccurateClick(UserEventReq userEventReq, boolean getDataFromHbase) {
        if (getDataFromHbase) {
            return hbaseClickService.getAccurateHbaseClickByDevice(userEventReq);
        } else {
            return getAccurateClickByRedis(userEventReq);
        }
    }

    private ToutiaoClick getAccurateClickByRedis(UserEventReq request) {
        ToutiaoClick toutiaoClick = null;
        try {
            if (StringUtils.isBlank(request.getProduct())) {
                log.warn("redisGetClick product is null  {}", JSONObject.toJSONString(request));
                return toutiaoClick;
            }

            //先按设备号查询，ios OcpcDeviceId 代表idfa，android OcpcDeviceId 代表imei，均为精准归因
            if (StringUtils.isNotBlank(request.getOcpcDeviceId()) && !ConstCls.emptyMd5.equals(request.getOcpcDeviceId())) {
                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickDeviceKey(request.getProduct(), request.getOs(), request.getOcpcDeviceId()));
                if (StringUtils.isNotBlank(s)) {
                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                    request.setGuiType(GuiyingType.deive.name);
                    toutiaoClick.setGuiType(GuiyingType.deive.name);
                }
            }

            //如果os未ios则根据caid和idfa进行匹配
            if ("ios".equals(request.getOs().toLowerCase())) {
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid())) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), request.getCaid()));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid())) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), request.getCaid()));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                    }
                }
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getIdfa2()) && !ConstCls.emptyMd5.equals(request.getIdfa2())) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), request.getIdfa2()));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.idfa.name);
                        toutiaoClick.setGuiType(GuiyingType.idfa.name);
                    }
                }

                String localCaid2022 = "";
                String localCaid2023 = "";
                String localCaid2025 = "";
                String caid2022 = "";
                String caid2025 = "";
                String idfaMap = "";
                String source = "";
                // 根据caid取出映射关系
                Map<String,String> localCaidMap =  safeDataRedisService.getLocalCaidMap(request.getCaid());
                if (localCaidMap != null && !localCaidMap.isEmpty()) {
                    localCaid2022 = localCaidMap.get("20220111");
                    localCaid2023 = localCaidMap.get("20230330");
                    localCaid2025 = localCaidMap.get("20250325");
                }

                Map<String,String> caidMap = safeDataRedisService.getCaidMap(request.getCaid());
                if (caidMap != null && !caidMap.isEmpty()) {
                    caid2022 = caidMap.get("20220111");
                    caid2025 = caidMap.get("20250325");
                    idfaMap = caidMap.get("idfa");
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2022)) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), localCaid2022));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2023)) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2023));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2023)) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), localCaid2023));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(localCaid2025)) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), localCaid2025));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "补充任务";
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(caid2022)) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid2022));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "caid_service";
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(caid2025)) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), caid2025));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.caid.name);
                        toutiaoClick.setGuiType(GuiyingType.caid.name);
                        source = "caid_service";
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(idfaMap)) {
                    String[] idfaStr = idfaMap.split(",");
                    for (String idfa : idfaStr) {
                        if (toutiaoClick == null) {
                            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), idfa));
                            if (StringUtils.isNotBlank(s)) {
                                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                                request.setGuiType(GuiyingType.idfa.name);
                                toutiaoClick.setGuiType(GuiyingType.idfa.name);
                                source = "click_event";
                            }
                        } else {
                            break;
                        }
                    }
                }

                if (StringUtils.isNotBlank(source) && toutiaoClick != null) {
                    // 通过Kafka发送数据
                    CaidMappingGyData caidMappingGyData = buildCaidMappingGyData(source,toutiaoClick,idfaMap,request);
                    caidMappingGyData.setDataObtainTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                    kafkaSender.sendCaidMappingGyData(JSONObject.toJSONString(caidMappingGyData));
                }

                // 根据caid映射取出caid2，并寻找点击
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid())) {
                    String caid2 = bpDispenseJedisClusterClient.get("caidMap:" + request.getCaid());
                    if (StringUtils.isNotBlank(caid2)) {
                        String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid2));
                        if (StringUtils.isNotBlank(s)) {
                            toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.caid.name);
                            toutiaoClick.setGuiType(GuiyingType.caid.name);
                        } else {
                            String s2 = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaid2Key(request.getProduct(), request.getOs(), caid2));
                            if (StringUtils.isNotBlank(s2)) {
                                toutiaoClick = JSONObject.parseObject(s2, ToutiaoClick.class);
                                request.setGuiType(GuiyingType.caid.name);
                                toutiaoClick.setGuiType(GuiyingType.caid.name);
                            }
                        }
                    }
                }



                // 若点击查询为null，则根据userId查询caidList
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getUserId()) && !"0".equals(request.getUserId())) {
                    // 查询userId 和 caidList的映射
                    List<String> caidListWithUserId = safeDataRedisService.getCaidListWithUserId(request.getUserId());
                    // 如果caidList的数量大于1 或 caidList只有一个且和请求获取caid不同 则查询点击
                    if (caidListWithUserId.size() > 1 || (caidListWithUserId.size() == 1 && caidListWithUserId.get(0).equals(request.getUserId()))) {
                        // 循环查询点击
                        for (String caid : caidListWithUserId) {
                            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickCaidKey(request.getProduct(), request.getOs(), caid));
                            if (StringUtils.isNotBlank(s)) {
                                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                                request.setGuiType(GuiyingType.caidMaping.name);
                                toutiaoClick.setGuiType(GuiyingType.caidMaping.name);
                                break;
                            }
                        }
                    }
                }


                if (toutiaoClick == null && StringUtils.isNotBlank(request.getCaid()) && StringUtils.isBlank(request.getIdfa2())) {
                    String idfa = redisRelationshipService.getIdfaCaid(request.getCaid());
                    if (StringUtils.isNotBlank(idfa)) {
                        String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), idfa));
                        if (StringUtils.isNotBlank(s)) {
                            toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                            request.setGuiType(GuiyingType.idfa.name);
                            toutiaoClick.setGuiType(GuiyingType.idfa.name);
                        }
                    }
                }

                if (toutiaoClick == null && StringUtils.isNotBlank(request.getUserId()) && !"0".equals(request.getUserId())) {
                    // 查询userId 和 caidList的映射
                    List<String> caidListWithUserId = safeDataRedisService.getCaidListWithUserId(request.getUserId());
                    // 如果caidList的数量大于1 或 caidList只有一个且和请求获取caid不同 则查询点击
                    if (caidListWithUserId.size() > 1 || (caidListWithUserId.size() == 1 && caidListWithUserId.get(0).equals(request.getUserId()))) {
                        // 循环查询点击
                        for (String caid : caidListWithUserId) {
                            String idfa = redisRelationshipService.getIdfaCaid(caid);
                            if (StringUtils.isNotBlank(idfa)) {
                                String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIdfa2Key(request.getProduct(), request.getOs(), idfa));
                                if (StringUtils.isNotBlank(s)) {
                                    toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                                    request.setGuiType(GuiyingType.idfaMaping.name);
                                    toutiaoClick.setGuiType(GuiyingType.idfaMaping.name);
                                    break;
                                }
                            }
                        }
                    }
                }

            } else if ("android".equals(request.getOs().toLowerCase())) {
                //如果os未android则根据oaid和androidId进行匹配
                if (StringUtils.isNotBlank(request.getOaid())) {
                    String rkey = RedisKeyConstants.getClickOaIdKey(request.getProduct(), request.getOs(), request.getOs());
                    String s = bpDispenseJedisClusterClient.get(rkey);
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.oaid.name);
                        toutiaoClick.setGuiType(GuiyingType.oaid.name);
                    }
                }
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getOaid2())) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickOaId2Key(request.getProduct(), request.getOs(), request.getOaid2()));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.oaid.name);
                        toutiaoClick.setGuiType(GuiyingType.oaid.name);

                    }
                }
                if (toutiaoClick == null && StringUtils.isNotBlank(request.getAndroidId())) {
                    String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickAndroidIdKey(request.getProduct(), request.getOs(), request.getAndroidId()));
                    if (StringUtils.isNotBlank(s)) {
                        toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                        request.setGuiType(GuiyingType.androidId.name);
                        toutiaoClick.setGuiType(GuiyingType.androidId.name);
                    }
                }
            }

        } catch (Exception e) {
            log.error("redisGetClick error ", e);
        }
        return toutiaoClick;
    }

    private CaidMappingGyData buildCaidMappingGyData(String source, ToutiaoClick toutiaoClick,String idfa,UserEventReq userEventReq) {
        CaidMappingGyData caidMappingGyData = new CaidMappingGyData();
        caidMappingGyData.setOs(toutiaoClick.getOs());
        caidMappingGyData.setProduct(toutiaoClick.getProduct());
        caidMappingGyData.setUserId(userEventReq.getUserId());
        caidMappingGyData.setSourceDeviceId(userEventReq.getSourceDeviceId());
        caidMappingGyData.setCaid(userEventReq.getCaid());
        caidMappingGyData.setAndroidId(userEventReq.getAndroidId());
        caidMappingGyData.setOaid(userEventReq.getOaid());
        caidMappingGyData.setOaid2(userEventReq.getOaid2());
        caidMappingGyData.setAccountId(toutiaoClick.getAccountId());
        caidMappingGyData.setAccountName(toutiaoClick.getAccountName());
        caidMappingGyData.setDsp(toutiaoClick.getDsp());
        caidMappingGyData.setIdfa(idfa);
        caidMappingGyData.setMappingSource(source);
        return caidMappingGyData;
    }

    public ToutiaoClick getObscureClick(UserEventReq userEventReq, boolean getDataFromHbase) {
        if (getDataFromHbase) {
            return hbaseClickService.getObscureHbaseClickByDevice(userEventReq);
        } else {
            return getObscureClickByRedis(userEventReq);
        }
    }

    private ToutiaoClick getObscureClickByRedis(UserEventReq request) {
        //查询非精准归因。 根据deviceId、macId、ipua、openId、ipua(加密)、ip依次查询
        ToutiaoClick toutiaoClick = null;
        if (StringUtils.isBlank(request.getProduct())) {
            log.warn("obscure redis product is null {}", JSONObject.toJSONString(request.getProduct()));
            return toutiaoClick;
        }

        //根据macId进行查询
        if (toutiaoClick == null && StringUtils.isNotBlank(request.getMac())) {
            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickMacKey(request.getProduct(), request.getOs(), request.getMac()));
            if (StringUtils.isNotBlank(s)) {
                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                request.setGuiType(GuiyingType.mac.name);
                toutiaoClick.setGuiType(GuiyingType.mac.name);
                log.info("新版归因 非精准匹配 mac归因成功 设备 {} " + request.getProduct() + " " + request.getMac() + " " + request.getUserId(), request.getOs());
            }
        }

        //根据ipua进行查询
        if (toutiaoClick == null && StringUtils.isNotBlank(request.concatIpua())) {
            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIpuaKey(request.getProduct(), request.getOs(), request.concatIpua()));
            if (StringUtils.isNotBlank(s)) {
                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                log.info("新版归因 非精准匹配 ipua查归因成功 设备 {} " + request.getProduct() + " " + request.concatIpua() + " " + request.getUserId() + " " + JSON.toJSONString(request), request.getOs());
                request.setGuiType(GuiyingType.ipua.name);
                toutiaoClick.setGuiType(GuiyingType.ipua.name);
            }
        }

        //根据openId进行查询
        if (toutiaoClick == null && StringUtils.isNotBlank(request.getOpenId())) {
            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickOpenKey(request.getProduct(), request.getOpenId()));
            if (StringUtils.isNotBlank(s)) {
                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                request.setGuiType(GuiyingType.openid.name);
                toutiaoClick.setGuiType(GuiyingType.openid.name);
                log.info("新版归因 非精准匹配 openId查归因成功 设备 {} " + request.getProduct() + " " + request.getAndroidId(), request.getOs());
            }
        }

        //根据iunod进行查询
        if (toutiaoClick == null && StringUtils.isNotBlank(request.concatIpuaMd())) {
            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIAKey(request.getProduct(), request.getOs(), request.concatIpuaMd()));
            if (StringUtils.isNotBlank(s)) {
                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                request.setGuiType(GuiyingType.iunod.name);
                toutiaoClick.setGuiType(GuiyingType.iunod.name);
                log.info("新版归因 非精准匹配 iunod 设备 {} " + request.getProduct() + " " + request.concatIpuaMd() + " " + request.getUserId(), request.getOs());
            }
        }

        //根据ipua(加密)进行查询
        if (toutiaoClick == null && "ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.concatIpMd())) {
            String s = bpDispenseJedisClusterClient.get(RedisKeyConstants.getClickIMKey(request.getProduct(), request.getOs(), request.concatIpMd()));
            if (StringUtils.isNotBlank(s)) {
                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                request.setGuiType(GuiyingType.ipmd.name);
                toutiaoClick.setGuiType(GuiyingType.ipmd.name);
                log.info("新版归因 非精准匹配 ipmd 设备 {} " + request.getProduct() + " " + request.concatIpMd() + " " + request.getUserId(), request.getOs());
            }
        }

        //根据ip进行查询
        if (toutiaoClick == null && "ios".equals(request.getOs().toLowerCase()) && StringUtils.isNotBlank(request.concatIp())) {
            String rkey = RedisKeyConstants.getClickIpKey(request.getProduct(), request.getOs(), request.concatIp());
            String s = bpDispenseJedisClusterClient.get(rkey);
            if (StringUtils.isNotBlank(s)) {
                toutiaoClick = JSONObject.parseObject(s, ToutiaoClick.class);
                toutiaoClick.setGuiType(GuiyingType.ip.name);
                request.setGuiType(GuiyingType.ip.name);
                log.info("新版归因 非精准匹配 ip 设备 {} " + request.getProduct() + " " + request.concatIpMd() + " " + request.getUserId(), request.getOs());
            }
        }

        return toutiaoClick;
    }

    public String getClickByTrackId(ToutiaoClick sdkClick, boolean getDataFromHbase) {
        if (getDataFromHbase) {
            return hbaseClickService.getTrackClickByHbase(sdkClick);
        } else {
            return getTrackClickByRedis(sdkClick);
        }
    }

    private String getTrackClickByRedis(ToutiaoClick sdkClick) {

        if (StringUtils.isBlank(sdkClick.getProduct()) || StringUtils.isBlank(sdkClick.getTrackId())) {
            log.warn("trackId为空 {}",JSONObject.toJSONString(sdkClick));
            return null;
        }
       return bpDispenseJedisClusterClient.get(sdkClick.getTrackId());
    }
}
