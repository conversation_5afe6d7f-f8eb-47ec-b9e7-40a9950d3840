package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OcpcGuiying对象", description="")
public class OcpcGuiying implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "主归因click")
    private Long clickId;

    @ApiModelProperty(value = "主DSP")
    private String dsp;

    @ApiModelProperty(value = "次clickIdList")
    private String ciClickIds;

    @ApiModelProperty(value = "次dsps")
    private String ciDsps;

    private Long eventId;

    private String product;

    private String pkgChannel;

    private String ocpcDeviceId;

    private String oaid;

    @ApiModelProperty(value = "导流原始产品")
    private String productSource;

    private String userId;

    private Integer eventType;

    private Date createTime;

    private Date updateTime;


}
