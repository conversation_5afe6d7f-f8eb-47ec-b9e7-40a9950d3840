package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.BaiduAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.*;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Set;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_MULTI_URL;

@Service
@Slf4j
public class AliUserEventService {

    @Autowired
    private BaiduAdvertiserService baiduAdvertiserService;
    @Autowired
    private AlertService alertService;

    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @ApolloJsonValue("${ali.toActive.account:[]}")
    private Set<String> aliToActiveAccount;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {

        try {
            int eventType =  userEventReq.getEventType();
            if (aliToActiveAccount.contains(toutiaoClick.getAccountId())) {
                if (Objects.equals(userEventReq.getEventType(), ToutiaoEventTypeEnum.KEY_EVENT.value)) {
                    eventType = ToutiaoEventTypeEnum.ACTIVATE_APP.value;
                    log.info("阿里假激活账户关键行为 回传激活 {}", toutiaoClick.getAccountId());
                } else {
                    log.info("阿里假激活账户 不回传除关键行为外的事件 {}", toutiaoClick.getAccountId());
                    userEvent.setReqRsp("阿里假激活账户 不回传除关键行为外的事件");
                    return false;
                }
            }
            if (eventType == ToutiaoEventTypeEnum.KEY_EVENT.value) {
                requestAli(toutiaoClick, userEvent, AliEventType.KEY_ACTION, true);
            }
            if (eventType == ToutiaoEventTypeEnum.START_APP.value) {
                requestAli(toutiaoClick, userEvent, AliEventType.START_APP, true);
            }
            if (eventType == ToutiaoEventTypeEnum.ACTIVATE_APP.value) {
                requestAli(toutiaoClick, userEvent, AliEventType.ACTIVATE_APP, true);
            }
        } catch (Exception e) {
            log.error(String.format("阿里关键行为回传异常 userEventReq: %s, toutiaoClick: %s", JSON.toJSONString(userEventReq), JSON.toJSONString(toutiaoClick)), e);
            return false;
        }
        return false;
    }


    /**
     * @param isExtraCallback 是否是额外的回传
     */
    private void requestAli(ToutiaoClick toutiaoClick, UserEvent userEvent, AliEventType aliEventType, boolean isExtraCallback) throws Exception {
        try {
            String callbackUrl = toutiaoClick.getCallbackUrl();
            callbackUrl = callbackUrl+"&type="+aliEventType.value;
            log.info("阿里待回传事件 {} {}", aliEventType, callbackUrl);
            String html = HttpClientUtil.get(HttpConfig.custom().timeout(3000).url(callbackUrl));

            JSONObject jobj = JSON.parseObject(html);
            log.info("阿里回调成功 ", jobj.toString());
            alertService.report(toutiaoClick.getProduct(), toutiaoClick.getAccountId(), "阿里回调成功");
            if (userEvent != null) {
                // 额外的回传通过分隔符累加在后面
                userEvent.setReqUrl(!isExtraCallback ? callbackUrl : userEvent.getReqUrl() + DELIMITER_MULTI_URL + callbackUrl);
                userEvent.setReqRsp(!isExtraCallback ? html : userEvent.getReqRsp() + DELIMITER_MULTI_URL + html);
            }
        } catch (Exception e) {
            log.error("阿里回调失败 ", e);
        }
    }

}
