package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.entity.VivoAdvertiser;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DeviceType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.UUID;

import static com.coohua.core.caf.dispense.constant.BaseConstants.DELIMITER_URL_BODY;

@Component
@Slf4j
public class VivoUserEventService {
    @Autowired
    private AlertService alertService;
    @Autowired
    VivoAdvertiserService vivoAdvertiserService;
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    OcpcEventService ocpcEventService;
    @Autowired
    RedisEventService redisEventService;


    public boolean oldActiveEvent(UserEventReq userEventReq) {
        try {
//            ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEventReq.getEventType());
//            String productName = userEventReq.getProduct();
//            VivoAdvertiser vivoAdvertiser = vivoAdvertiserService.queryByProductName(productName);
//
//            if(vivoAdvertiser!=null && StringUtils.isNotBlank(userEventReq.getPkgChannel()) && userEventReq.getPkgChannel().contains("vivo")){
//                ToutiaoClick toutiaoClick = new ToutiaoClick();
//                toutiaoClick.setAccountId(vivoAdvertiser.getClientId());
//                toutiaoClick.setDsp(DspType.vivo.name);
//                toutiaoClick.setOs("android");
//
//                if(ToutiaoEventTypeEnum.KEY_EVENT.equals(toutiaoEventTypeEnum)){
//                    Boolean isMatch = vivoAdvertiserService.checkActionCount(vivoAdvertiser,userEventReq);
//                    if(isMatch){
//                        sendVivoEvent(toutiaoClick,userEventReq,vivoAdvertiser,toutiaoEventTypeEnum);
//                    }
//                }
//            }
        } catch (Exception e) {
            log.error("", e);
        }
        return false;
    }
    @ApolloJsonValue("${ocpc.vivo.active.accounts:[\"62f3bed13cae692d81d0\"]}")
    public Set<String> activeAccountSet;
    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {
        Response response = null;
        String rspStr = null;
        try {
            ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEventReq.getEventType());
            VivoAdvertiser vivoAdvertiser = vivoAdvertiserService.queryByAdvertiserId(toutiaoClick.getAccountId());
            String uri = "https://marketing-api.vivo.com.cn/openapi/v1/advertiser/behavior/upload";
            String nonce = getNonce();
            String accessToken = vivoAdvertiser.getAccessToken();
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            MediaType mediaType = MediaType.parse("application/json");
            JSONObject jsonObject = new JSONObject();

            jsonObject.put("srcType", "APP");
            jsonObject.put("pkgName", vivoAdvertiser.getPkgName());
            jsonObject.put("srcId", vivoAdvertiser.getSrcId());
            JSONArray jsonArray = new JSONArray();


            JSONObject deviceObject = new JSONObject();
            if(activeAccountSet.contains(vivoAdvertiser.getAdvertiserId())){
                if (ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)) {
                    deviceObject.put("cvType", "ACTIVATION");
                    log.info("vivo加白回传激活 "+vivoAdvertiser.getAdvertiserId()+" "+JSON.toJSONString(userEventReq));
                }else{
                    userEvent.setReqRsp("vivo加白回传激活");
                    return false;
                }
            }else{
                if (ToutiaoEventTypeEnum.KEY_EVENT.equals(toutiaoEventTypeEnum)) {
                    deviceObject.put("cvType", "ACTIVATION");
                } else {
                    userEvent.setReqRsp("vivo非关键行为不回传");
                    return false;
                }
            }

            // 判断归因是根据imei还是oaid
            DeviceType deviceType = redisEventService.checkActiveEventFrom(userEventReq);
            if (deviceType == DeviceType.device) {
                deviceObject.put("userIdType", "IMEI_MD5");
                deviceObject.put("userId", userEventReq.getOcpcDeviceId());
            } else if (deviceType == DeviceType.oaid) {
                deviceObject.put("userIdType", "OAID");
                deviceObject.put("userId", userEventReq.getOaid());
            } else {
                log.info("vivo回传未匹配到imei或oaid {} {} {} ", toutiaoClick.getAccountId(), JSON.toJSONString(toutiaoClick), JSON.toJSONString(userEventReq));
                userEvent.setReqRsp("vivo回传未匹配到imei或oaid");
                return false;
            }

            deviceObject.put("cvTime", System.currentTimeMillis());
//            deviceObject.put("creativeId",toutiaoClick.getCid());
            jsonArray.add(deviceObject);

            jsonObject.put("dataList", jsonArray);

            String url = uri + "?access_token=" + accessToken + "&timestamp=" + System.currentTimeMillis() + "&nonce=" + nonce + "&advertiser_id=" + vivoAdvertiser.getClientId();
            String bodyStr = jsonObject.toJSONString();

            RequestBody body = RequestBody.create(mediaType, bodyStr);
            Request request = new Request.Builder()
                    .url(url)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();
            log.info("vivo回传URL url {} , body {}", url, jsonObject.toJSONString());
            response = client.newCall(request).execute();
            rspStr = response.body().string();
            JSONObject rstJobj = JSON.parseObject(rspStr);
            if(rstJobj.getInteger("code")==0){
                log.info("vivo回传 " + toutiaoEventTypeEnum.desc + " " + rspStr);
                alertService.report(userEventReq.getProduct(), vivoAdvertiser.getClientId(), "vivo回调成功");
            } else {
                log.info("vivo回传失败 {} {} {}", rspStr, url, jsonObject.toJSONString());
                alertService.report(userEventReq.getProduct(), vivoAdvertiser.getClientId(), "vivo回调失败");
            }

            if (userEvent != null) {
                userEvent.setReqUrl(url + DELIMITER_URL_BODY + bodyStr);
                userEvent.setReqRsp(rspStr);
            }
        } catch (Exception e) {
            log.error("vivo回传未知异常 " + JSON.toJSONString(userEventReq), e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return false;
    }

    public static String getNonce() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().substring(0, 12);
    }
}
