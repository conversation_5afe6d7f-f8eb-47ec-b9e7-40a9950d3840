package com.coohua.core.caf.dispense.dsp.entity;

import lombok.Data;

/**
 * (。≖ˇェˇ≖。)
 *
 * @author: zkk
 * DateTime: 2020/8/4 17:38
 */
@Data
public class OcpcDeviceEntity {
	/**
	 * pkgId
	 */
	private Long pkgId;
	private Integer appId;
	private String productName;
	private String deviceId;
	private String os;
	private String oaId;
	private String channel;
	/**
	 * 相差天数
	 */
	private Integer currentDay;
	/**
	 * 用户账户创建时间
	 */
	private Long createTime;
	/**
	 * 用户Id
	 */
	private Long userId;

}
