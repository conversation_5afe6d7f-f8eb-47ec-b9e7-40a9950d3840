package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.service.RedisClickService;
import com.coohua.core.caf.dispense.dto.CaidMappingDto;
import com.coohua.core.caf.dispense.dto.CaidVersionEntity;
import com.coohua.core.caf.dispense.dto.req.CaidReq;
import com.coohua.core.caf.dispense.dto.req.CaidRsp;
import com.coohua.core.caf.dispense.dto.req.IosCaidReq;
import com.coohua.core.caf.dispense.enums.CaidVersionEnum;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.kafka.KafkaSender;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CaidService {
    public static String tencentQAIDVersion = "1005";
    public static String tencentQAIDVersion2 = "1006";
    public static String caidVersion = "20220111";
    public static String caidVersion2 = "20230330";
    public static String caidVersion3 = "20250325";
    @Autowired
    RedisClickService redisClickService;
    //序列化，公钥RSA加密, Base64
    static String PUBK = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrdJmR6plkvl0DN796lWggTOgJ" +
            "N7frk7MsUDzIKeAcjojsDMdgs+Xto9F5TSQp4dV8bD30EjBzbB8fo7SxP9Y6UsaV" +
            "RgRtQYH5rf3omhO9lMlBiZqDeIG0pbY+ZT2Kr6KkcYV21BrmG6S4fNF/366xqkb2" +
            "tXmmQY0HBQ8yX0R41wIDAQAB"; // 分配的API接入的公钥字符串，注意换行符需要剔除

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    @Autowired
    private KafkaSender kafkaSender;

    static byte[] pubKeyBytes = Base64.getDecoder().decode(PUBK);

    public String queryCaid(IosCaidReq iosCaidReq) {
        String caidStr = null;
        try{
            caidStr = sendCaidReq(iosCaidReq);
        }catch (Exception e){

        }
        for(int i=0 ;i<3 && (StringUtils.isBlank(caidStr) || caidStr==null); i++){
            try {
                caidStr = sendCaidReq(iosCaidReq);
                log.info("caid补偿机制开始 " + i + " " + caidStr);
            }catch (Exception e){
                log.warn("caid获取异常 ",e);
            }
        }
        if(StringUtils.isBlank(caidStr)){
            log.error("caid获取异常22 {} carrierInfo {} deviceName {}",iosCaidReq.getProduct(),iosCaidReq.getCarrierInfo(),iosCaidReq.getDeviceName());
        }else{
            log.info("caid获取233 {} carrierInfo {} deviceName {}",caidStr,iosCaidReq.getCarrierInfo(),iosCaidReq.getDeviceName());
        }
        if (StringUtils.isNotBlank(caidStr)) {
            // 通过kafka发送，保存映射
            List<CaidRsp> caidList = JSON.parseArray(caidStr, CaidRsp.class);
            if (caidList!=null && caidList.size()>1) {
                // 分别存储存储caid1找caid2的映射，caid2找caid1的映射
                bpDispenseJedisClusterClient.setex("caidMap:"+caidList.get(0).getCaid(),86400*60,caidList.get(1).getCaid());
                bpDispenseJedisClusterClient.setex("caidMap:"+caidList.get(1).getCaid(),86400*60,caidList.get(0).getCaid());
                Map<String, String> caidMap = caidList.stream().collect(Collectors.toMap(CaidRsp::getVersion, CaidRsp::getCaid));
                CaidMappingDto caidMappingDto = new CaidMappingDto();

                caidMappingDto.setCaid(caidMap.get(CaidVersionEnum.CAID_20230330.getKeyPrefix()));
                caidMappingDto.setCaidType(CaidVersionEnum.CAID_20230330.getCode());
                List<CaidVersionEntity> caidVersionEntities = new ArrayList<>();
                String caid = caidMap.get(CaidVersionEnum.CAID_20220111.getKeyPrefix());
                CaidVersionEntity caidVersionEntity = new CaidVersionEntity();
                if (StringUtils.isNotBlank(caid)) {
                    caidVersionEntity.setCaid(caid);
                    caidVersionEntity.setCaidType(CaidVersionEnum.CAID_20220111.getCode());
                }else {
                    caidVersionEntity.setCaid( caidMap.get(CaidVersionEnum.CAID_20250325.getKeyPrefix()));
                    caidVersionEntity.setCaidType(CaidVersionEnum.CAID_20250325.getCode());
                }
                caidVersionEntities.add(caidVersionEntity);
                caidMappingDto.setCaidVersions(caidVersionEntities);
                caidMappingDto.setSource("caid_service");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String dateFormat = sdf.format(new Date());
                caidMappingDto.setCreateTime(dateFormat);
                kafkaSender.sendCaidMapping(JSONObject.toJSONString(caidMappingDto));
            }
            for (CaidRsp caidRsp : caidList) {
                if (caidRsp.getVersion().equals(caidVersion2)) {
                    return caidRsp.getCaid();
                }
            }
        }
        return null;
    }


    @Autowired
    OcpcSwitcher ocpcSwitcher;
    public void setCaidRedis(String userId,String product,String caid){
        if(StringUtils.isNotBlank(userId) && StringUtils.isNotBlank(caid) && userId.length()>2 && caid.length()>2 && StringUtils.isNotBlank(product)){
            if(ocpcSwitcher.writeRedisSwitch){
                String rkey = getCaidKey(product,userId);
                bpDispenseJedisClusterClient.setex(rkey,DateTimeConstants.SECONDS_PER_DAY * 1,caid);
//                log.info("设置caid成功 "+userId+" "+rkey+" @"+caid);
            }
        }
    }

    private String getCaidKey(String product,String userId){
        String rkey = "caidrd@"+product+"@"+userId;
        return rkey;
    }
    public String getCaidRedis(String userId,String product){
        if(StringUtils.isNotBlank(userId) && userId.length()>2 && StringUtils.isNotBlank(product)){
            if(ocpcSwitcher.writeRedisSwitch){
                String rkey = getCaidKey(product,userId);
                String caid = bpDispenseJedisClusterClient.get(rkey);
                if(StringUtils.isNotBlank(caid)){
                    log.info("补充caid成功  "+userId+" @ "+product+" caid:"+caid);
                    return caid;
                }
            }
        }
        log.info("补充caid失败 "+userId+" KKK"+product);
        return null;
    }
    public String sendCaidReq(IosCaidReq iosCaidReq) {
//        if (StringUtils.isNotBlank(iosCaidReq.getUserId()) && iosCaidReq.getUserId().length() > 6) {
//            String caidJson = redisClickService.getCaidJson(iosCaidReq.getProduct(), iosCaidReq.getUserId());
//            if (StringUtils.isNotBlank(caidJson)) {
//                return caidJson;
//            }
//        }
        ByteArrayOutputStream out = null;
        try {
            // 设备公共信息按照字典结构，特定字段名称存储
//            HashMap<String, String> deviceInfo = new HashMap<String, String>() {
////                {
////                    put("bootTimeInSec", "1595643553");
////                    put("countryCode", "CN");
////                    put("language", "zh-Hans-CN");
////                    put("deviceName", "e910dddb2748c36b47fcde5dd720eec1");
////                    put("systemVersion", "14.0");
////                    put("machine", "iPhone10,3");
////                    put("carrierInfo", "中国移动");
////                    put("memory", "3955589120");
////                    put("disk", "63900340224");
////                    put("sysFileTime", "1595214620.383940");
////                    put("model", "D22AP");
////                    put("timeZone", "28800");
////                }
////            };
            HashMap<String, String> deviceInfo = new HashMap<String, String>() {
                {
                    put("bootTimeInSec", iosCaidReq.getBootTimeInSec());
                    put("countryCode", iosCaidReq.getCountryCode());
                    put("language", iosCaidReq.getLanguage());
                    put("deviceName", iosCaidReq.getDeviceName());
                    put("systemVersion", iosCaidReq.getSystemVersion());
                    put("machine", iosCaidReq.getMachine());
                    put("carrierInfo", iosCaidReq.getCarrierInfo());
                    put("memory", iosCaidReq.getMemory());
                    put("disk", iosCaidReq.getDisk());
                    put("sysFileTime", iosCaidReq.getSysFileTime());
                    put("model", iosCaidReq.getModel());
                    put("timeZone", iosCaidReq.getTimeZone());
                }
            };
            String jsonStr = new Gson().toJson(new HashMap<>(deviceInfo));
            Cipher encryptCipher = Cipher.getInstance("RSA");
            PublicKey publicKey = KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(pubKeyBytes));
            encryptCipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] b = jsonStr.getBytes("UTF-8");
            int inputLen = b.length;
            out = new ByteArrayOutputStream();
            int offSet = 0;
            int MAX_ENCRYPT_BLOCK = 117;
            byte[] cache;
            int i = 0;
            // 对数据分段加密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                    cache = encryptCipher.doFinal(b, offSet, MAX_ENCRYPT_BLOCK);
                } else {
                    cache = encryptCipher.doFinal(b, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * MAX_ENCRYPT_BLOCK;
            }
            byte[] encryptedBytes = out.toByteArray();
            String encryptedDeviceInfo = Base64.getEncoder().encodeToString(encryptedBytes);
            String devId = "10441";

            CaidReq caidReq = new CaidReq();
            caidReq.setDev_id(devId);
            caidReq.setEncrypted_device_info(encryptedDeviceInfo);
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("Content-Type", "application/json");
            String result1 = doPostJson("https://caid.china-caa.org/v1.0/get", JSON.toJSONString(caidReq), headerMap);
            log.info("caid获取结果 result" + result1);
            JSONObject job = JSON.parseObject(result1);
            if (job.getInteger("code") == 0) {
                log.info("caid获取成功 "+result1);
                String decodestr = job.getString("data");
                String drsultStr = dcodeStr(decodestr);
                if (StringUtils.isNotBlank(drsultStr)) {
                    redisClickService.setCaid(iosCaidReq.getProduct(), iosCaidReq.getUserId(), drsultStr);
                    return drsultStr;
                }
            }else{
                log.warn("caiid获取错误 "+result1+" reqis "+JSON.toJSONString(iosCaidReq));
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        return null;
    }

    public String dcodeStr(String dataStr) {
        ByteArrayOutputStream out = null;
        try {
            byte[] dataBase64Decoded = Base64.getDecoder().decode(dataStr);
            Cipher decryptedCipher = Cipher.getInstance("RSA");
            PublicKey publicKey = KeyFactory.getInstance("RSA").generatePublic(new X509EncodedKeySpec(pubKeyBytes));
            decryptedCipher.init(Cipher.DECRYPT_MODE, publicKey);
            int l = dataBase64Decoded.length;
            int offSet = 0;
            int MAX_DECRYPT_BLOCK = 128;
            byte[] cache2;
            int i = 0;
            out = new ByteArrayOutputStream();
            while (l - offSet > 0) {
                if (l - offSet > MAX_DECRYPT_BLOCK) {
                    cache2 = decryptedCipher.doFinal(dataBase64Decoded, offSet, MAX_DECRYPT_BLOCK);
                } else {
                    cache2 = decryptedCipher.doFinal(dataBase64Decoded, offSet, l - offSet);
                }
                out.write(cache2, 0, cache2.length);
                i++;
                offSet = i * MAX_DECRYPT_BLOCK;
            }
            byte[] decryptedBytes = out.toByteArray();
            String caids = new String(decryptedBytes, "UTF-8");
            // caidArray即为最终获得的caid版本和值的数组，结构即为3.1.2中的最终解密后的形式
            log.info(caids);
            return caids;
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        return "";
    }

    public String doPostJson(String url, String json, Map<String, String> headers) {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            // 创建请求内容
            StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);

            if (headers != null) {
                headers.forEach(httpPost::setHeader);
            }

            // 执行http请求
            response = httpClient.execute(httpPost);
            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
        } catch (Exception e) {
            log.error("post请求失败, url:{}, param:{}, header:{}", url, json, JSONObject.toJSONString(headers), e);
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("post连接关闭异常", e);
                }
            }
        }

        return resultString;
    }

    /**
     * 根据版本号获取ksCaid
     * @param caidStr
     * @return
     */
    public static String getKSCaid(String caidStr,String ksCaidVersion) {
        //https://docs.qingque.cn/d/home/<USER>
        //URLEncode("[{\"kenyId\":\"12345\",\"kenyId_MD5\":\"827ccb0eea8a706c4c34a16891f84e7b\",\"version\":20200801},
        //{\"kenyId\":\"67890\",\"kenyId_MD5\":\"1e01ba3e07ac48cbdab2d3284d1dd0fa\",\"version\":20200901}]

        try {
            String decodeCaidStr = URLDecoder.decode(caidStr, "utf-8");
//            log.info("caidclick 快手caid " + caidStr + " 解析后 " + decodeCaidStr);
            if (decodeCaidStr.startsWith("__")) {
                log.info("ks未替换字符串");
                return null;
            }
            JSONArray jsonArray = JSON.parseArray(caidStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject cajob = jsonArray.getJSONObject(i);
                if (ksCaidVersion.equals(cajob.getString("version"))) {
                    String caid = cajob.getString("kenyId");
                    return caid;
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }
        return null;
    }

    /**
     * 获取俩个版本的caid
     * @param caidStr
     * @return
     */
    public static String[] getKSCaid2(String caidStr) {
        String[] caids = new String[2];
        try {
            String decodeCaidStr = URLDecoder.decode(caidStr, "utf-8");
//            log.info("caidclick 快手caid " + caidStr + " 解析后 " + decodeCaidStr);
            if (decodeCaidStr.startsWith("__")) {
                log.info("ks未替换字符串");
                return null;
            }
            JSONArray jsonArray = JSON.parseArray(caidStr);

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject cajob = jsonArray.getJSONObject(i);
                String caid = cajob.getString("kenyId");
                caids[i] = caid;
            }
        } catch (Exception e) {
            log.warn("", e);
        }
        return caids;
    }


    public static String getTTCaid(String caidStr,String version) {
        //https://docs.qingque.cn/d/home/<USER>
        //%5B%7B%22version%22%3A%2220220111%22%2C%22caid%22%3A%22912ec803b2ce49e4a541068d495ab570%22%7D%2C%7B%22version%22%3A%2220211207%22%2C%22caid%22%3A%22e332a76c29654fcb7f6e6b31ced090c7%22%7D%5D
        //{\"kenyId\":\"67890\",\"kenyId_MD5\":\"1e01ba3e07ac48cbdab2d3284d1dd0fa\",\"version\":20200901}]

        try {
            String decodeCaidStr = URLDecoder.decode(caidStr, "utf-8");
//            log.info("caidclick 头条caid3 " + caidStr + " 解析后 " + decodeCaidStr);
            JSONArray jsonArray = JSON.parseArray(decodeCaidStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject cajob = jsonArray.getJSONObject(i);
                if (version.equals(cajob.getString("version"))) {
                    String caid = cajob.getString("caid");
                    return caid;
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }
        return null;
    }

    public static String getTTCaid2(String caid2Str) {
        try {
            String decodeCaid2Str = URLDecoder.decode(caid2Str, "utf-8");
            JSONArray jsonArray = JSON.parseArray(decodeCaid2Str);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject cajob = jsonArray.getJSONObject(i);
                //[{"caid_md5":"912ec803b2ce49e4a541068d495ab570","version":"20230330"},{"caid_md5":"e332a76c29654fcb7f6e6b31ced090c7","version":"20220111"}]
                if (caidVersion.equals(cajob.getString("version"))) {
                    String caid2 = cajob.getString("caid_md5");
                    return caid2;
                }
            }
        } catch (Exception e) {
            log.warn("", e);
        }
        return null;
    }


    public static String getTencentCaid(String caidStr,String caidVersion) {
        try {
            //[{"qaid":"12345","hash_qaid":"123456789012345","version":"1003"},{"qaid":"67890","hash_qaid":"6789523654780214556","version":"1006"}]
            String decodeCaidStr = URLDecoder.decode(caidStr, "utf-8");
//            log.info("caidclick 腾讯caid " + caidStr + " 解析后 " + decodeCaidStr);
            if (decodeCaidStr.startsWith("__")) {
                log.info("ks未替换字符串");
                return null;
            }
            JSONArray jsonArray = JSON.parseArray(caidStr);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject cajob = jsonArray.getJSONObject(i);
                if (caidVersion.equals(cajob.getString("version"))) {
                    String caid = cajob.getString("qaid");
                    return caid;
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    /**
     * 是否禁止激活caid
     * @param caid
     * @return true 是 false 否
     */
    public boolean isNoActiveCaid(String caid) {
        if (StringUtils.isBlank(caid)) {
            return false;
        }
        String iosNoActiveCaidKey = RedisKeyConstants.getIosNoActiveCaid(caid);
        String iosNoActiveCaid = bpDispenseJedisClusterClient.get(iosNoActiveCaidKey);
        return  iosNoActiveCaid !=null && Objects.equals(iosNoActiveCaid, "true");
    }

    /**
     * 灌数
     */
 //   @PostConstruct
    public void test() throws IOException {
        List<String> strings = FileUtils.readLines(new File("/Users/<USER>/Desktop/caid1.txt"), Charset.forName("utf-8"));
        ExecutorService executorService = Executors.newFixedThreadPool(100);
        List<CompletableFuture> taskList = new ArrayList<>();
        AtomicLong al = new AtomicLong(0);
        for (String string : strings) {
            CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                String iosNoActiveCaid = RedisKeyConstants.getIosNoActiveCaid(string);
                bpDispenseJedisClusterClient.set(iosNoActiveCaid, "true");
               // String s = bpDispenseJedisClusterClient.get(iosNoActiveCaid);
                al.incrementAndGet();
            }, executorService);
            taskList.add(task);
        }
        CompletableFuture.allOf(taskList.toArray(new CompletableFuture[0])).join();
        System.out.println("结束 " + al.get());
    }
    public static void main(String[] args) {
        //%5B%7B%22version%22%3A%2220220111%22%2C%22caid%22%3A%22912ec803b2ce49e4a541068d495ab570%22%7D%2C%7B%22version%22%3A%2220211207%22%2C%22caid%22%3A%22e332a76c29654fcb7f6e6b31ced090c7%22%7D%5D
        String str = "%5B%7B%22version%22%3A%2220220111%22%2C%22caid%22%3A%22912ec803b2ce49e4a541068d495ab570%22%7D%2C%7B%22version%22%3A%2220211207%22%2C%22caid%22%3A%22e332a76c29654fcb7f6e6b31ced090c7%22%7D%5D";
        System.out.println(getTTCaid(str,"20220111"));
        //getKSCaid("[{\"version\":\"20220111\",\"kenyId\":\"197fcff1e7a180c6d716986cd036fdb0\",\"kenyId_MD5\":\"9eec7e76d38ca573f38e741888f8b48a\"},{\"version\":\"20211207\",\"kenyId\":\"d244cdeb59226f3bbd0de90a096b83e7\",\"kenyId_MD5\":\"94b29c077db407ba9327426f9ce7aea7\"}]");
    }
}
