package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="CvrProduct对象", description="")
public class CvrProduct implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty(value = "打点名称")
    private String product;
    @ApiModelProperty(value = "中文名称")
    private String productName;
    @ApiModelProperty(value = "平台")
    private String dsp;
    @ApiModelProperty(value = "系统, 同PhoneOsEnum")
    private Integer os;
    @ApiModelProperty(value = "cvr加权数组")
    private String rateArray;
    @ApiModelProperty(value = "倍数n,m")
    private String rateConst;
    @ApiModelProperty(value = "限制数")
    private String rateLimit;
    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;
}
