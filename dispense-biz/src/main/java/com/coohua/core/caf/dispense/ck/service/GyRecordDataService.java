package com.coohua.core.caf.dispense.ck.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.ck.entity.GyRecordDataLocal;
import com.coohua.core.caf.dispense.ck.mapper.GyRecordDataMapper;
import com.coohua.core.caf.dispense.dsp.entity.OcpcPayReq;
import com.coohua.core.caf.dispense.dsp.mapper.OcpcPayReqMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class GyRecordDataService extends ServiceImpl<GyRecordDataMapper, GyRecordDataLocal> {
    @Autowired
    GyRecordDataMapper gyRecordDataMapper;

    public void saveGyRecordDataBatch(List<GyRecordDataLocal> gyRecordDatas) {
        //DataSourceContextHolder.setDataSource(targetDb);
        this.saveBatch(gyRecordDatas);
        //gyRecordDataMapper.insertBatch(gyRecordDatas);
    }
}
