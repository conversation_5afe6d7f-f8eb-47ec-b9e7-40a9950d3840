package com.coohua.core.caf.dispense.dto.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/7/6
 **/
@Data
public class VivoClickReqBody {

    private String requestId;//请求id
    private String adId;//广告(创意)ID
    private String imei;//(设备ID md5 值)
    private String clickTime;//点击时间
    private String ua;//UserAgent
    private String ip;//客户端请求的ip地址
    private String oaid;//设备标识,明文（64位字符串）；Android 10 及以上版本返回；（支持安卓10以下下发oaid，需与商务接口人联系，配置白名单） 备注：目前有oaid的设备覆盖在安卓9以上，安卓9以下设备仅有imei
    private String creativeId;//创意id
    private Integer mediaType;//计划类型；0-商店、1-非商店、2-联盟
    private String advertiserId;//广告主账号id
    private String advertiserName;//广告账户名称
    private Integer placeType;//广告位类型
    private String advertisementId;//广告id
    private String adName;//广告名称
    private String groupId;//广告组ID
    private String groupName;//广告组名称
    private String campaignId;//计划ID
    private String campaignName;//计划名称


}
