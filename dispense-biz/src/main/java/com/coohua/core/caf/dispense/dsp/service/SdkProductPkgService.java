package com.coohua.core.caf.dispense.dsp.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.Product;
import com.coohua.core.caf.dispense.dsp.entity.SdkProductPkg;
import com.coohua.core.caf.dispense.dsp.mapper.ProductMapper;
import com.coohua.core.caf.dispense.dsp.mapper.SdkProductPkgMapper;
import com.coohua.core.caf.dispense.enums.DELFLAG;
import com.coohua.core.caf.dispense.redis.SafeDataRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SdkProductPkgService extends ServiceImpl<SdkProductPkgMapper, SdkProductPkg> {

    private static Map<String, SdkProductPkg> productCnMap = new HashMap<>();

    @PostConstruct
    @Scheduled(fixedDelay = 1000 * 60)
    public void reloadConfig() {
        List<SdkProductPkg> list = lambdaQuery().eq(SdkProductPkg::getDelFlag, DELFLAG.weishanchu.value).list();

        productCnMap = list.stream().collect(Collectors.toMap(
                sdkProductPkg -> sdkProductPkg.getAppId() + "_" + sdkProductPkg.getOs(),
                sdkProductPkg -> sdkProductPkg,
                (existing, replacement) -> existing,
                HashMap::new
        ));
        log.info("reload config success productCnMap {}", productCnMap);
    }


    public SdkProductPkg getProductPkg(String key) {
        return productCnMap.get(key);
    }
}
