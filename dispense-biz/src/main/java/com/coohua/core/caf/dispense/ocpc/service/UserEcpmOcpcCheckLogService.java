package com.coohua.core.caf.dispense.ocpc.service;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dsp.entity.AuthToutiaoAdvertiser;
import com.coohua.core.caf.dispense.dsp.entity.Product;
import com.coohua.core.caf.dispense.dsp.service.ProductService;
import com.coohua.core.caf.dispense.ocpc.entity.UserEcpmOcpcCheckLog;
import com.coohua.core.caf.dispense.ocpc.mapper.UserEcpmOcpcCheckLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class UserEcpmOcpcCheckLogService extends ServiceImpl<UserEcpmOcpcCheckLogMapper, UserEcpmOcpcCheckLog> {

    @Autowired
    private ProductService productService;

    public void saveToutiaoNoData(List<AuthToutiaoAdvertiser> noDataList, Date start) {
        if(CollectionUtils.isNotEmpty(noDataList)) {
            try {
                List<UserEcpmOcpcCheckLog> saveList = new ArrayList<>();
                for (AuthToutiaoAdvertiser advertiser : noDataList) {
                    UserEcpmOcpcCheckLog checkLog = new UserEcpmOcpcCheckLog();
                    checkLog.setDsp("toutiao");
                    checkLog.setProductName(advertiser.getProductName());
                    Product product = productService.getByCnName(advertiser.getProductName());
                    checkLog.setName(product.getName());
                    checkLog.setAccountId(advertiser.getAdvertiserId().toString());
                    checkLog.setCreateTime(start);
                    checkLog.setNatureActiveCount(0);
                    checkLog.setNatureKeyCount(0);
                    checkLog.setNatureNotCallback(0);
                    checkLog.setNatureQueryCallback(0);
                    checkLog.setHourActiveCount(0);
                    checkLog.setHourKeyCount(0);
                    checkLog.setHourNotCallback(0);
                    checkLog.setHourQueryCallback(0);
                    saveList.add(checkLog);
                }
                this.saveBatch(saveList);
            }catch (Exception ex) {
                log.error("saveToutiaoNoData err, data:{}", JSONObject.toJSONString(noDataList), ex);
            }
        }
    }

    public void saveQueryRes(String dsp, String productName, String name, String accountId, Date start, Integer natureActiveCount, Integer natureKeyCount, Integer natureNotCallback, Integer natureQueryCallback,
                             Integer hourActiveCount, Integer hourKeyCount, Integer hourNotCallback, Integer hourQueryCallback) {
        UserEcpmOcpcCheckLog checkLog = new UserEcpmOcpcCheckLog();
        try{

            checkLog.setDsp(dsp);
            checkLog.setProductName(productName);
            checkLog.setName(name);
            checkLog.setAccountId(accountId);
            checkLog.setCreateTime(start);
            checkLog.setNatureActiveCount(natureActiveCount);
            checkLog.setNatureKeyCount(natureKeyCount);
            checkLog.setNatureNotCallback(natureNotCallback);
            checkLog.setNatureQueryCallback(natureQueryCallback);
            checkLog.setHourActiveCount(hourActiveCount);
            checkLog.setHourKeyCount(hourKeyCount);
            checkLog.setHourNotCallback(hourNotCallback);
            checkLog.setHourQueryCallback(hourQueryCallback);
            save(checkLog);
        }catch (Exception ex) {
            log.error("saveQueryRes err, data:{}", JSONObject.toJSONString(checkLog), ex);
        }
    }
}
