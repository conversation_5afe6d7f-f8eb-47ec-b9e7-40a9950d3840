package com.coohua.core.caf.dispense.dsp.entity;

public class EventEntity {
    // 基础事件
    private String event;
    private String project;
    private String distinct_id;
    private EventEntityProperty properties;

    private String reason;

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getDistinct_id() {
        return distinct_id;
    }

    public void setDistinct_id(String distinct_id) {
        this.distinct_id = distinct_id;
    }

    public EventEntityProperty getProperties() {
        return properties;
    }

    public void setProperties(EventEntityProperty properties) {
        this.properties = properties;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
