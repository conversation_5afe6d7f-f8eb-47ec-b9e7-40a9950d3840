package com.coohua.core.caf.dispense.ocpc.service;

import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcLockinfo;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcLockinfoMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-12-26
*/
@Service
public class OcpcLockinfoService extends ServiceImpl<OcpcLockinfoMapper, OcpcLockinfo> {
    @Autowired
    OcpcSwitcher ocpcSwitcher;
    @Autowired
    OcpcTableService ocpcTableService;
    public void saveLockInfo(UserEventReq userEventReq, UserActive userActive,ToutiaoClick toutiaoClick,String dsp,String remark){
        try {
            boolean isSaveChl = StringUtils.isNotBlank(userEventReq.getPkgChannel()) && ocpcSwitcher.vivoLcProjectNames.contains(userEventReq.getProduct());
            if(isSaveChl){
                ocpcTableService.createIfNot(OcpcTableService.lockInfo);
                Date date = new Date();
                OcpcLockinfo ocpcLockinfo = new OcpcLockinfo();
                ocpcLockinfo.setProduct(userEventReq.getProduct());
                ocpcLockinfo.setAndroidId(userEventReq.getAndroidId());
                ocpcLockinfo.setCaid(userEventReq.getCaid());
                ocpcLockinfo.setCreateTime(date);
                ocpcLockinfo.setIdfa2(userEventReq.getIdfa2());
                ocpcLockinfo.setIp(userEventReq.getIp());
                ocpcLockinfo.setModel(userEventReq.getModel());
                ocpcLockinfo.setOaid(userEventReq.getOaid());
                ocpcLockinfo.setOaid2(userEventReq.getOaid2());
                ocpcLockinfo.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
                ocpcLockinfo.setSourceDeviceId(userEventReq.getSourceDeviceId());
                ocpcLockinfo.setSourceOaid(userEventReq.getSourceOaid());
                ocpcLockinfo.setEventTypeName(userEventReq.getPkgChannel());
                ocpcLockinfo.setUa(userEventReq.getUa());
                ocpcLockinfo.setMacId(userEventReq.getMac());
                ocpcLockinfo.setUpdateTime(date);
                ocpcLockinfo.setOs(userEventReq.getOs());
                ocpcLockinfo.setRemark(remark);
                ocpcLockinfo.setDsp(dsp);

                if(toutiaoClick!=null){
                    if(StringUtils.isBlank(dsp)){
                        ocpcLockinfo.setDsp(toutiaoClick.getDsp());
                    }
                    ocpcLockinfo.setGyType(userEventReq.getGuiType());
                    ocpcLockinfo.setAccountId(toutiaoClick.getAccountId());

                    if(userActive!=null){
                        Date createTime = userActive.getCreateTime();
                        Long mins = (createTime.getTime()-toutiaoClick.getCreateTime().getTime())/ DateTimeConstants.MILLIS_PER_MINUTE;
                        ocpcLockinfo.setRemark(mins+"");
                    }
                }
                save(ocpcLockinfo);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }
}
