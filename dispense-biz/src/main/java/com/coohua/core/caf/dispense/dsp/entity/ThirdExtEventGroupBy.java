package com.coohua.core.caf.dispense.dsp.entity;

import com.coohua.core.caf.dispense.enums.DspType;
import lombok.Data;

/**
 * 衍生事件分类
 * <AUTHOR>
 */
@Data
public class ThirdExtEventGroupBy {

    private String dsp;

    private String eventTypes;

    public ThirdExtEventGroupBy(ThirdExtEvent extEvent) {
        this.dsp = extEvent.getDsp();
        this.eventTypes = extEvent.getEventTypes();
    }

    public ThirdExtEventGroupBy(DspType dsp, String eventTypes) {
        this.dsp = dsp.value;
        this.eventTypes = eventTypes;
    }
}
