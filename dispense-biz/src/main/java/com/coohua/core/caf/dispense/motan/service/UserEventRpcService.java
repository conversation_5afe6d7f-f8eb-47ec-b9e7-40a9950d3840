package com.coohua.core.caf.dispense.motan.service;

import com.coohua.user.event.api.dto.QueryRequest;
import com.coohua.user.event.api.remote.rpc.UserAdEcpmRpc;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class UserEventRpcService {
    @MotanReferer(basicReferer = "user-eventBasicRefererConfigBean")
    private UserAdEcpmRpc userAdEcpmRpc;

    public String queryUserECPMBatch(String os, Integer appId, List<Long> userIdList){
        QueryRequest queryRequest = new QueryRequest();
        queryRequest.setAppId(appId);
        queryRequest.setOs(os);
        queryRequest.setUserIdList(userIdList);
        return userAdEcpmRpc.queryUserECPMBatch(queryRequest);
    }
}
