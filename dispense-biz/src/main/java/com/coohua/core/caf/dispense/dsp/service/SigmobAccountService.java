package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.SigmobExtApolloConfig;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.mapper.SigmobAccountMapper;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.*;
import com.coohua.core.caf.dispense.hbase.HbaseEventService;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.coohua.core.caf.dispense.constant.BaseConstants.*;
import static com.coohua.core.caf.dispense.utils.OcpcNewCallbackRuleValidateUtil.*;
import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR>
 * @Date 2024/11/26 11:12
 * @Description : sigmob广告主实现类
 */
@Service
@Slf4j
public class SigmobAccountService extends ServiceImpl<SigmobAccountMapper, SigmobAccount> implements IService<SigmobAccount> {

    @Autowired
    OcpcEventService ocpcEventService;

    @Autowired
    HbaseEventService hbaseEventService;

    @Autowired
    SigmobExtApolloConfig extApolloConfig;

    @Autowired
    ThirdExtEventService thirdExtEventService;

    @Autowired
    AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    private Map<String, SigmobAccount> accountActionMap = Maps.newHashMap();

    private Map<String, List<AccountAction>> appMap = Maps.newHashMap();


    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void initAccountActionConfig() {
        List<SigmobAccount> list = this.lambdaQuery()
                .isNotNull(SigmobAccount::getAppName)
                .isNotNull(SigmobAccount::getAdvertiserId)
                .eq(SigmobAccount::getDelFlag, DELFLAG.weishanchu.value)
                .list();

        accountActionMap = list
                .stream()
                .collect(Collectors.toMap(SigmobAccount::getAppProductName, Function.identity(), (a, b) -> a));

        appMap = list.stream()
                .collect(Collectors.groupingBy(SigmobAccount::getAppName,
                        Collectors.collectingAndThen(toList(), advertisers -> advertisers.stream()
                                .flatMap(this::convertAction)
                                .filter(Objects::nonNull)
                                .collect(toList()))));
    }

    public Map<String, List<AccountAction>> queryActionConfig() {
        return appMap;
    }

    private Stream<AccountAction> convertAction(SigmobAccount sigmobAccount) {
        try {

            // 无配置
            if (StringUtils.isBlank(sigmobAccount.getEventValues()) || StringUtils.isBlank(sigmobAccount.getEventTypes())) {
                return Stream.empty();
            }

            return SigmobAccountService.buildEventStandardConsideringMultiEcpm(sigmobAccount.getEventTypes(),
                    sigmobAccount.getEventValues(), new BigInteger(sigmobAccount.getAdvertiserId()));

        } catch (Exception e) {
            log.error("更新sigmob产品回传规则出现异常 ", e);
            log.error("配置有问题的sigmob产品 {}", JSON.toJSONString(sigmobAccount));
        }

        return null;
    }

    public static Stream<AccountAction> buildEventStandardConsideringMultiEcpm(String eventTypesText, String eventValuesText,
                                                                               BigInteger advertiserId) {

        String[] eventTypes = StringUtils.splitByWholeSeparator(eventTypesText, ",");
        String[] eventValues = StringUtils.splitByWholeSeparator(eventValuesText, ",");

        // 多arpu格式，走新逻辑
        if (OcpcNewCallbackRuleValidateUtil.isMultiArpu(eventTypesText, eventValuesText)) {
            return buildAccountActionForMultiArpu(advertiserId, eventValues);
        }

        int configNumber = StringUtils.countMatches(eventValues[0], MULTI_ECPM_SEPARATOR) + 1;

        List<Map<String, String>> configs = IntStream.range(0, configNumber)
                .mapToObj(index -> Maps.<String, String>newHashMap())
                .collect(toList());

        for (int index = 0; index < eventTypes.length; index++) {
            String[] eventValuePieces = StringUtils.splitByWholeSeparator(eventValues[index], MULTI_ECPM_SEPARATOR);

            for (int valueIndex = 0; valueIndex < configNumber; valueIndex++) {
                configs.get(valueIndex).put(eventTypes[index], eventValuePieces[valueIndex]);
            }
        }

        return configs.stream()
                .map(config -> new AccountAction(advertiserId, config));
    }

    private static Stream<AccountAction> buildAccountActionForMultiArpu(BigInteger advertiserId, String[] eventValues) {

        // 多arpu 格式 : 1,2 -> 8/10/10,1.5/2.5/5,30/60/1440  (次数1/次数2/次数3,arpu1/arpu2/arpu3,累计时间1/累计时间2/累计时间3)
        // 以下为 8/10/10,1.5/2.5/5,30/60/1440 拆分后的值
        String[] nums = StringUtils.splitByWholeSeparator(eventValues[0], MULTI_ECPM_SEPARATOR);
        String[] arpus = StringUtils.splitByWholeSeparator(eventValues[1], MULTI_ECPM_SEPARATOR);
        String[] times = StringUtils.splitByWholeSeparator(eventValues[2], MULTI_ECPM_SEPARATOR);

        int configNumber = arpus.length;

        List<AccountAction> configs = IntStream.range(0, configNumber)
                .mapToObj(index -> new AccountAction(advertiserId))
                .collect(toList());

        for (int valueIndex = 0; valueIndex < configNumber; valueIndex++) {
            AccountAction accountActionIndex = configs.get(valueIndex);
            accountActionIndex.getEventConfigurations().put("1", nums[valueIndex]);
            accountActionIndex.getEventConfigurations().put("2", arpus[valueIndex]);
            accountActionIndex.setMaxAccumulatePeriod(Integer.valueOf(times[valueIndex]));
        }

        return configs.stream();

    }

    public Boolean checkActionCount(ToutiaoClick toutiaoClick, UserEventReq userEventReq) {
        try {

            // 包装类型防空指针
            boolean matchDefaultConfig = userEventReq.getActionDefault() != null && userEventReq.getActionDefault();

            String advertiserId = toutiaoClick.getAccountId();
            SigmobAccount sigmobAccount = accountActionMap.get(toutiaoClick.getProduct());
            // 若无配置或未配置行为参数 仅当默认值时上报
            if (sigmobAccount == null) {
                return matchDefaultConfig;
            }

            BooleanSupplier firstTimeJudge = () -> authToutiaoAdvertiserService.isFirstBackRequest(userEventReq);

            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());

            // 命中默认规则，那么新旧值都传，此时先验新值，再验旧值
            if (matchDefaultConfig) {
                return matchNewRule(userEventReq, sigmobAccount.getEventTypes(),
                        sigmobAccount.getEventValues(), firstTimeJudge, dspType.value, sigmobAccount.getAppName(),
                        advertiserId, null);
            }

            // 非默认规则，那么新旧版本会分别回传，单次只校验一种配置即可
            // 新版本配置
            if (userEventReq.getActionValues() != null) {
                return matchNewRule(userEventReq, sigmobAccount.getEventTypes(),
                        sigmobAccount.getEventValues(), firstTimeJudge, dspType.value, sigmobAccount.getAppName(),
                        advertiserId, null);
            }

        } catch (Exception e) {
            log.error("Sigmob执行checkActionCount失败:", e);
        }

        return false;
    }


    public Map<String, SigmobAccount> getAccountActionMap() {
        return accountActionMap;
    }
}
