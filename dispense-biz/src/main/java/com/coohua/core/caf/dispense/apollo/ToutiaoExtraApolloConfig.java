package com.coohua.core.caf.dispense.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 关键行为衍生事件相关apollo配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ToutiaoExtraApolloConfig {

    /**
     * 关键行额外回传到注册和下单 是否对全部advertiserId生效
     */
    @Value("${ocpc.toutiao.extra.enable.all:false}")
    public boolean toutiaoExtraEnableAll;

    /**
     * 关键行额外回传到注册和下单 生效的advertiserId列表
     */
    @ApolloJsonValue("${ocpc.toutiao.extra.enable.advertisers:[1719291750329357]}")
    public Set<Long> toutiaoExtraEnableAdvertiserIds;

    /**
     * 关键行额外回传到 arpu次留 是否对全部advertiserId生效
     */
    @Value("${ocpc.toutiao.extra.arpu0.enable.all:false}")
    public boolean toutiaoExtraArpu0EnableAll;

    /**
     * 关键行额外回传到 arpu次留 生效的advertiserId列表
     */
    @ApolloJsonValue("${ocpc.toutiao.extra.arpu0.enable.advertisers:[]}")
    public Set<Long> toutiaoExtraArpu0EnableAdvertiserIds;


}
