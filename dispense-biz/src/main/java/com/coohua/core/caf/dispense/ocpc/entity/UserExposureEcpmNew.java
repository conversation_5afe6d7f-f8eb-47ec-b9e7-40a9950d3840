package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

@Data
public class UserExposureEcpmNew {

    private String id;
    private Date dataDate;
    private Long userId;
    private String product;
    private String os;
    private String deviceId;
    private String oaid;
    private String caid;
    private String imei;
    private Integer pv;
    private Double ecpm;
    private Integer status;
    private String remark;
    private Date sendTime;
    private Date updateTime;

    //非表内数据，同步event_type的active
    @TableField(exist = false)
    private Date activeTime;
    @TableField(exist = false)
    private boolean valid;
}
