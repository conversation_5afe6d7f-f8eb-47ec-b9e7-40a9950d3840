package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OcpcCallbackConf对象", description="")
public class OcpcCallbackConf implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "pkgId")
    private Long pkgId;

    @ApiModelProperty(value = "广告主账户ID")
    private String accountId;

    @ApiModelProperty(value = "平台Id")
    private Long platformId;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "激活行为")
    private String activeMatchExpression;

    @ApiModelProperty(value = "次留行为")
    private String ciliuMatchExpression;

    @ApiModelProperty(value = "关键行为")
    private String behaviorMathExpression;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识")
    private Integer delFlag;

    @ApiModelProperty(value = "创建人id")
    private Long createId;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "修改人id")
    private Long updateId;

    @ApiModelProperty(value = "操作人")
    private String updateName;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "对应click和event里的product")
    private String product;


}
