package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;

@Setter
@Getter
@ToString
@Accessors(chain = true)
@ApiModel(value="ToutiaoSdkGyRetry对象", description="")
public class ToutiaoSdkGyRetry {
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer appId;

    private String userId;

    private String product;

    private String os;

    private String ocpcDeviceId;

    private String sourceDeviceId;

    private String oaid;

    private String caid;

    private String androidId;

    private String ip;

    private String model;

    private String originalAndroidId;

    private String idfv;

    private String pkgChannel;

    private String sourceType;

    private String timerDelay;

    private String ua;

    private Integer eventType;

    private Integer retryCount;

    private Date createTime;

    private Date updateTime;

    private Integer delFlag;
}
