package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.ocpc.entity.UserActive;
import com.coohua.core.caf.dispense.utils.HBaseUtils;
import com.coohua.core.caf.dispense.utils.MD5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.util.Bytes;
import org.joda.time.DateTimeConstants;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class HbaseLockUserActiveService {
    @Resource
    private Connection hbaseConnection;

    static String userActByDevice = "userActByDevice";

    public void createOcpcTable() {
        try {
            HBaseUtils.initHadoopTable(hbaseConnection, userActByDevice, 365*DateTimeConstants.SECONDS_PER_DAY);
        }catch (Exception e){
            log.error("",e);
        }
    }

    public UserActive queryDeviceActive(UserEventReq userEventReq){
        String hbaseValue = null;
        if ("ios".equalsIgnoreCase(userEventReq.getOs())){
            String caidKey = getCaidKey(userEventReq.getCaid(), userEventReq.getProduct(), userEventReq.getOs());
            if (StringUtils.isNotBlank(caidKey)){
                hbaseValue = HBaseUtils.searchDataFromHadoop(hbaseConnection, userActByDevice, caidKey);
            }
        }else {
            String oaidKey = getOaidKey(userEventReq.getOaid(),userEventReq.getProduct(),userEventReq.getOs());
            if(StringUtils.isNotBlank(oaidKey)){
                hbaseValue = HBaseUtils.searchDataFromHadoop(hbaseConnection, userActByDevice, oaidKey);
            }
            if(StringUtils.isBlank(hbaseValue)){
                String imeiKey = getImeiKey(userEventReq.getOcpcDeviceId(),userEventReq.getProduct(),userEventReq.getOs());
                if(StringUtils.isNotBlank(imeiKey)){
                    hbaseValue = HBaseUtils.searchDataFromHadoop(hbaseConnection, userActByDevice, imeiKey);
                }
            }
        }

        UserActive userActive = null;
        if(StringUtils.isNotBlank(hbaseValue)){
            userActive = JSON.parseObject(hbaseValue,UserActive.class);
        }
        return userActive;
    }


    public void saveHbaseUserActByDevice(UserActive userActive) {
        if ("ios".equalsIgnoreCase(userActive.getOs())) {
            String caidKey = getCaidKey(userActive.getOaid(), userActive.getProduct(), userActive.getOs());
            if (StringUtils.isNotBlank(caidKey)) {
                try {
                    boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userActByDevice, caidKey, Bytes.toBytes(JSON.toJSONString(userActive)));
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        } else {
            String oaidKey = getOaidKey(userActive.getOaid(), userActive.getProduct(), userActive.getOs());
            if (StringUtils.isNotBlank(oaidKey)) {
                try {
                    boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userActByDevice, oaidKey, Bytes.toBytes(JSON.toJSONString(userActive)));
                } catch (Exception e) {
                    log.error("", e);
                }

            }

            String imeyKey = getImeiKey(userActive.getImei(), userActive.getProduct(), userActive.getOs());
            if (StringUtils.isNotBlank(imeyKey)) {
                try {
                    boolean success = HBaseUtils.saveToHadoop(hbaseConnection, userActByDevice, imeyKey, Bytes.toBytes(JSON.toJSONString(userActive)));
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
    }

    private String getImeiKey(String imei,String product,String os){
        if(StringUtils.isNotBlank(imei) && imei.length()>4){
            return "imei@"+ MD5Utils.getMd5Sum(imei) +"@"+product+"@"+os;
        }
        return null;
    }

    private String getOaidKey(String oaid,String product,String os){
        if(StringUtils.isNotBlank(oaid) && oaid.length()>4){
            return "oaid@"+oaid+"@"+product+"@"+os;
        }
        return null;
    }


    private String getCaidKey(String caid, String product, String os) {
        if(StringUtils.isNotBlank(caid) && caid.length()>4){
            return "caid@"+caid+"@"+product+"@"+os;
        }
        return null;
    }

}
