package com.coohua.core.caf.dispense.ocpc.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.apollo.ExtEventApolloConfig;
import com.coohua.core.caf.dispense.apollo.OcpcSwitcher;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.dsp.entity.*;
import com.coohua.core.caf.dispense.dsp.service.*;
import com.coohua.core.caf.dispense.dsp.service.wmin.TTDaRenWxService;
import com.coohua.core.caf.dispense.dsp.service.wmin.TTWxService;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.GuiyingType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.hbase.*;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcClickMapperExt;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcEventMapper;
import com.coohua.core.caf.dispense.redis.PayEventService;
import com.coohua.core.caf.dispense.redis.RedisEventService;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.Resource;
import java.util.*;

import static com.coohua.core.caf.dispense.constant.BaseConstants.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-29
 */
@Service
@Slf4j
public class OcpcEventService extends ServiceImpl<OcpcEventMapper, OcpcEvent> {
    @Resource(name = "dspToOcpcEevent")
    ThreadPoolTaskExecutor poolTaskExecutor;
    @Autowired
    OcpcClickMapperExt ocpcClickMapperExt;
    @Autowired
    private ToutiaoClickService toutiaoClickService;
    @Autowired
    private OcpcClickService ocpcClickService;

    @Autowired
    private OcpcEventMapper ocpcEventMapper;
    @Autowired
    TencentUserActionService tencentUserActionService;
    @Autowired
    KuaishouUserEventService kuaishouUserEventService;
    @Autowired
    SogouUserEventService sogouUserEventService;
    @Autowired
    BaiduUserEventService baiduUserEventService;
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    TianweiUserEventService tianweiUserEventService;
    @Autowired
    private DspUserEventService dspEventService;
    @Autowired
    private InnerUserEventService innerUserEventService;
    @Autowired
    private SigmobUserEventService sigmobUserEventService;
    @Autowired
    private UserEventFailedService userEventFailedService;

    @Resource
    private HBaseUserChannelService hBaseUserChannelService;

    @Autowired
    OppoUserEventService oppoUserEventService;
    @Autowired
    VivoUserEventService vivoUserEventService;
    @Autowired
    IqiyiUserEventService iqiyiUserEventService;
    @Autowired
    XiaomiUserEventService xiaomiUserEventService;
    @Autowired
    XimalayaUserEventService ximalayaUserEventService;

    /**
     * 优先归因到头条
     */
    @ApolloJsonValue("${priority.toutiao.app:[]}")
    private Set<String> priorityToutiaoApp;

    @Value("${priority.dsp:toutiao}")
    private String priorityDsp;
    @Value("${replace.all.click.toutiao:false}")
    private Boolean openAllReplace;

    @ApolloJsonValue("${ replace.toutiao.app:[\"huankuaizou\"]}")
    private Set<String> replaceClickToutiaoApp;
    /**
     * 关闭补偿激活
     */
    @Value("${need.active.close:true}")
    private Boolean closeNeedActive;
    @Autowired
    OcpcGuiyingService ocpcGuiyingService;
    @Autowired
    private AuthToutiaoAdvertiserService authToutiaoAdvertiserService;

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    @Autowired
    ThirdExtEventService thirdExtEventService;


    /**
     * 启用分布式锁保证回传不重复的产品打点名称集合
     */
    @ApolloJsonValue("${enable.lock.products:[\"\"]}")
    private Set<String> enableLockProducts;

    /**
     * 是否启用分布式锁抢占结果
     */
    @Value("${apply.lock.result:false}")
    private boolean applyLockResult;

    @Autowired
    ExtEventApolloConfig extEventApolloConfig;

    @Autowired
    ApRedisService apRedisService;
    @Autowired
    private AuthKuaishouAppService authKuaishouAppService;

    /**
     * dspUserEvent 向ocpcEvent 双写
     * 同事冗余写入clickId
     *
     * @param toutiaoClick
     */
    public void dspInsertEvent(ToutiaoClick toutiaoClick, UserEvent userEvent) {
        poolTaskExecutor.execute(() -> {
            try {
                Integer appId = ProductCache.getAppId(toutiaoClick.getProduct());
                if (appId == null) {
                    log.error("产品不存在{}", toutiaoClick.getProduct());
                    appId = -1;
                }
                ToutiaoClick ocpcClick = new ToutiaoClick();
                BeanUtils.copyProperties(toutiaoClick, ocpcClick);
//                Long id = ocpcClickMapperExt.dspInsertIntoClick(ocpcClick, appId);
                ocpcClick.setId(null);
                ocpcClickService.saveClick(ocpcClick);
                OcpcEvent ocpcEvent = new OcpcEvent();
                BeanUtils.copyProperties(userEvent, ocpcEvent);
                ocpcEvent.setSourceDeviceId(userEvent.getSourceDeviceId());
                ocpcEvent.setClickId(ocpcClick.getId());
                ocpcEvent.setId(null);
                ocpcEvent.setClickTime(toutiaoClick.getCreateTime());
                ocpcEvent.setCallbackUrl(toutiaoClick.getCallbackUrl());
                ocpcEvent.setAppId(appId);
                save(ocpcEvent);
                log.info("双写ocpcEvent成功 appId-{},userEvent-{}", appId, ocpcEvent.getId());
            } catch (Exception e) {
                log.error("双写ocpcEvent成功异常", e);
            }
        });
    }

    @Autowired
    RedisClickService redisClickService;
    @Autowired
    UserEventActiveService userEventActiveService;

    /**
     * 根据dsp 排序，第一个应该保留 根据最近归因算法，一个dsp只归因一个click事件
     *
     * @param guiyClickList
     * @return
     */
    public List<ToutiaoClick> getDspClickList(List<ToutiaoClick> guiyClickList) {
        if (CollectionUtils.isEmpty(guiyClickList) || guiyClickList.size() == 1) {
            return guiyClickList;
        }
        Map<String, ToutiaoClick> dmap = new HashMap<String, ToutiaoClick>();
        if (guiyClickList != null && guiyClickList.size() > 0) {
            for (int i = guiyClickList.size() - 1; i >= 0; i--) {
                dmap.put(guiyClickList.get(i).getDsp(), guiyClickList.get(i));
            }
        }
        return new ArrayList<>(dmap.values());
    }

    public List<ToutiaoClick> sortClickList(List<ToutiaoClick> clickList) {
        try {
            Collections.sort(clickList, (t1, t2) -> {
                if (!CollectionUtils.isEmpty(priorityToutiaoApp) && priorityToutiaoApp.contains(t1.getProduct())) {
                    if (Objects.equals(t1.getDsp(), t2.getDsp())) {
                        return t2.getCreateTime().compareTo(t1.getCreateTime());
                    }
                    if (t1.getDsp().equalsIgnoreCase(priorityDsp)) {
                        return -1;
                    }
                    if (t2.getDsp().equalsIgnoreCase(priorityDsp)) {
                        return 1;
                    }
                }
                return t2.getCreateTime().compareTo(t1.getCreateTime());
            });
        } catch (Exception e) {
            log.error("sort exp!", e);
        }
        return clickList;
    }

    @Autowired
    OcpcSwitcher ocpcSwitcher;

    private boolean lockUid(UserEventReq userEventReq) {
        try {
            // 要使用分布式锁保证回传顺序执行
            boolean locked = tryGetDistributedLock(RedisKeyConstants.getReportLockKey(userEventReq.getProduct(), userEventReq.getUserId(),
                    userEventReq.getEventType()), "1", 10000);

            if (!locked) {
                log.warn("上报并发抢占资格失败 {} {} {}", userEventReq.getProduct(), userEventReq.getUserId(), userEventReq.getEventType());
                if (applyLockResult) {
                    log.warn("抢占资格失败取消上报 {} {} {}", userEventReq.getProduct(), userEventReq.getUserId(), userEventReq.getEventType());
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("分布式锁抢占出现异常 ", e);
        }
        return false;
    }

    @Autowired
    OcpcEventColdService ocpcEventColdService;
    @Autowired
    TTWxService ttWxService;
    @Autowired
    TTDaRenWxService ttdaRenWxService;
    @Autowired
    AliUserEventService aliUserEventService;
    @Autowired
    CaidService caidService;

    public boolean guiyingDspClick(ToutiaoClick toutiaoClick, UserEventReq userEventReq, boolean isDirectMatch) {
        boolean isActive = false;
        if (toutiaoClick != null) {
            DspType dspType = DspType.getDspType(toutiaoClick.getDsp());
            String reqUrl = "";
            if (DspType.KUAISHOU.equals(dspType)) {
                // kuaishou dsp 特殊处理
                reqUrl = toutiaoClick.getCallbackUrl() + "&event_type=" + userEventReq.getEventType() + "&event_time=" + System.currentTimeMillis();
                if (!isDirectMatch) {
                    reqUrl = reqUrl + "&is_direct_match=false";
                }
            }
            if ((Objects.equals(userEventReq.getAppId(), 1201) || Objects.equals(userEventReq.getAppId(), 1172)) && Objects.equals(userEventReq.getOs().toLowerCase(), "ios")
                    && ToutiaoEventTypeEnum.ACTIVATE_APP.value.equals(userEventReq.getEventType()) && caidService.isNoActiveCaid(userEventReq.getCaid())) {
                log.info("指定caid 不上报激活事件 {} {}", userEventReq.getAppId(), userEventReq.getCaid());
                return true;
            }
            if (lockUid(userEventReq)) {
                return true;
            }

            UserEvent userEvent = saveUserEvent(toutiaoClick, userEventReq, reqUrl, "");
            if (DspType.TOUTIAO.equals(dspType)) {
                toutiaoCallService.touTiaoActive(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.GUANGDIANTONG.equals(dspType)) {
                isActive = tencentUserActionService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.KUAISHOU.equals(dspType)) {
                isActive = kuaishouUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent, isDirectMatch);
            } else if (DspType.SOGOU.equals(dspType)) {
                isActive = sogouUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.isBaidu(dspType.value)) {
                isActive = baiduUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.INNER_VIDEO.equals(dspType)) {
                innerUserEventService.activeVideoEvent(userEventReq, toutiaoClick);
            } else if (DspType.INNER_DRAINAGE.equals(dspType)) {
                innerUserEventService.activeDrainageEvent(userEventReq, toutiaoClick);
            } else if (DspType.INNER_OLD_PULL_NEW.equals(dspType)) {
                innerUserEventService.activeOldPullEvent(userEventReq, toutiaoClick);
            } else if (DspType.KUAISHOU_CLJX.equals(dspType)) {
                isActive = kuaishouUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent, true);
            } else if (DspType.OPPO.equals(dspType)) {
                isActive = oppoUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.vivo.equals(dspType)) {
                isActive = vivoUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.IQIYI.equals(dspType)) {
                isActive = iqiyiUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.XIAOMI.equals(dspType)) {
                isActive = xiaomiUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.XIMALAYA.equals(dspType)) {
                isActive = ximalayaUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.TTWX.equals(dspType)) {
                isActive = ttWxService.sendWxEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.TTWMINDR.equals(dspType)) {
                isActive = ttdaRenWxService.sendDRWxEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.ALI.equals(dspType)) {
                isActive = aliUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else if (DspType.SIGMOB.equals(dspType)) {
                isActive = sigmobUserEventService.activeEvent(userEventReq, toutiaoClick, userEvent);
            } else {
                log.warn("无法识别的dsp平台");
            }
            if (userEvent != null) {
                updateUserEvent(userEvent);
                userEventFailedService.insertUserEventFailed(userEvent, toutiaoClick);
//                ocpcEventColdService.insertColdEvent(userEvent);
            }

        } else {
            UserEvent userEvent = saveUserEvent(toutiaoClick, userEventReq, "", "");
            log.warn("无法找到 click 事件");
        }
        return isActive;
    }

    @Autowired
    BigclickService bigclickService;

    @Autowired
    RedisEventService redisEventService;
    @Autowired
    HbaseEventService hbaseEventService;

    public UserEvent saveUserEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq, String reqUrl, String rspStr) {
        return saveUserEvent(toutiaoClick, userEventReq, reqUrl, rspStr, null, null);
    }

    @Autowired
    HbaseUserActiveService hbaseUserActiveService;
    @Resource(name = "ocpcTo8Eevent")
    ThreadPoolTaskExecutor ocpcTo8Eevent;
    @Autowired
    PayEventService payEventService;
    @Autowired
    HbaseRtaService hbaseRtaService;

    /**
     * @param extEvents 衍生事件
     */
    private UserEvent saveUserEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq, String reqUrl, String rspStr, ThirdExtEvent currentExtEvent, List<ThirdExtEvent> extEvents) {
        try {
            UserEvent userEvent = new UserEvent();
            Date date = new Date();
            userEvent.setAppId(userEventReq.getAppId());
            userEvent.setCreateTime(date);
            userEvent.setUpdateTime(date);
            userEvent.setUserId(userEventReq.getUserId());
            userEvent.setEventType(userEventReq.getEventType());
            userEvent.setProduct(userEventReq.getProduct());
            userEvent.setChannel(userEventReq.getPkgChannel());
            userEvent.setOpenId(userEventReq.getOpenId());
            userEvent.setWAppId(userEventReq.getWAppId());
            userEvent.setCaid(userEventReq.getCaid());

            if (StringUtils.equalsIgnoreCase(userEventReq.getOs(), "android")) {
                String channel = userEventReq.getPkg_channel();
                if (StringUtils.isBlank(channel)) {
                    channel = userEventReq.getPkgChannel();
                }
                userEvent.setOpenId(channel);
            }
            // 记录触发关键行为回传时的上报值
            if (StringUtils.isNotBlank(userEventReq.getActionValues())) {
                userEvent.setRemark(userEventReq.getActionValues() + ";" + userEventReq.getAccumulateDuration());
            }
            if (StringUtils.isNotBlank(toutiaoClick.getAccountId())) {
                payEventService.setPayKeyEventRemark(toutiaoClick, userEventReq, userEvent);
            }

            if (toutiaoClick != null) {
                userEvent.setClickId(toutiaoClick.getId());
                userEvent.setAccountId(toutiaoClick.getAccountId());
                userEvent.setAccountName(toutiaoClick.getAidName());
                userEvent.setDsp(toutiaoClick.getDsp());
                userEvent.setMacId(userEventReq.getMac());
                userEvent.setCallbackUrl(toutiaoClick.getCallbackUrl());
                userEvent.setAndroidId(toutiaoClick.getAndroidId());
                if (StringUtils.isNotBlank(toutiaoClick.getAidName())) {
                    String uvt = userEvent.getRemark();
                    if (StringUtils.isBlank(uvt)) {
                        uvt = "";
                    }
                    userEvent.setRemark(uvt + " " + toutiaoClick.getAidName());
                }
                userEvent.setMid(toutiaoClick.getMid());


            } else {
                userEvent.setLastRecallTime(new Date());
            }
            userEvent.setOaid(userEventReq.getOaid());
            userEvent.setOaid2(userEventReq.getOaid2());
            userEvent.setIdfa2(userEventReq.getIdfa2());
            userEvent.setIp(userEventReq.getIp());
            userEvent.setUa(userEventReq.getUa());
            userEvent.setModel(userEventReq.getModel());
            userEvent.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
            userEvent.setOs(userEventReq.getOs());
            userEvent.setReqUrl(reqUrl);
            userEvent.setReqRsp(rspStr);

            if (StringUtils.isNotBlank(userEventReq.getUarpuGy()) || StringUtils.isNotBlank(userEventReq.getUarpuRmk())) {
                log.info("arpu波动替换成功 " + userEventReq.getUarpuGy() + "@" + userEventReq.getUarpuRmk());
                userEvent.setRemark("" + userEvent.getRemark() + " llop " + userEventReq.getUarpuRmk() + " " + userEventReq.getUarpuGy());
            }
            if (userEventReq.isTimerDelay()) {
                userEvent.setRemark(userEvent.getRemark() + "  timerDelay");
            }
            if (StringUtils.isNotBlank(userEventReq.getSourceOaid())) {
                userEvent.setSourceOaid(userEventReq.getSourceOaid());
            }
            if (StringUtils.isNotBlank(userEventReq.getSourceDeviceId())) {
                userEvent.setSourceDeviceId(userEventReq.getSourceDeviceId());
            }
            if (ToutiaoEventTypeEnum.PURCHASE.equals(userEvent.getEventType())) {
                if (userEventReq.getPayAmount() == null) {
                    userEventReq.setPayAmount(0);
                }
                userEvent.setCost(new Double(userEventReq.getPayAmount()));
            }
            UserEventService.setPlanIdAndCreativeId(toutiaoClick, userEvent);

            try {
                ToutiaoEventTypeEnum toutiaoEventTypeEnum = ToutiaoEventTypeEnum.getStatus(userEventReq.getEventType());

                // oppo和vivo渠道，激活事件存imei时以点击事件中的imei为准
                checkUseClickImei(toutiaoClick, userEventReq, userEvent, toutiaoEventTypeEnum);

                OcpcEvent ocpcEvent = new OcpcEvent();
                BeanUtils.copyProperties(userEvent, ocpcEvent);
                ocpcEvent.setCreateTime(new Date());
                ocpcEvent.setUpdateTime(new Date());
                if (ToutiaoEventTypeEnum.PURCHASE.equals(userEvent.getEventType())) {
                    ocpcEvent.setRemark("" + userEventReq.getPayAmount());
                }
                if (toutiaoClick != null) {
                    ocpcEvent.setClickTime(toutiaoClick.getCreateTime());
                    ocpcEvent.setClickIdT(toutiaoClick.getClickId());
                } else {
                    ocpcEvent.setClickTime(new Date());
                }
                ocpcEvent.setMacId(userEventReq.getMac());
                ocpcEvent.setGyType(toutiaoClick.getGuiType());
                if (ocpcSwitcher.writeOcpcEventSwitch) {
                    if (ocpcSwitcher.sysWriteOcpcEventSwitch) {
                        ocpcTo8Eevent.execute(() -> {
                            int ocnum = ocpcEventMapper.insert(ocpcEvent);
                            if (ocnum > 0 && ocpcSwitcher.logOpen) {
                                log.info("异步插入ocpcEvent " + ocpcEvent.getOcpcDeviceId() + " 成功 产品为：" + ocpcEvent.getProduct() + "");
                            }
                        });
                    } else {
                        int ocnum = ocpcEventMapper.insert(ocpcEvent);
                        if (ocnum > 0 && ocpcSwitcher.logOpen) {
                            log.info("插入ocpcEvent " + ocpcEvent.getOcpcDeviceId() + " 成功 产品为：" + ocpcEvent.getProduct() + "");
                        }
                    }
                }
                userEvent.setId(ocpcEvent.getId());

                if (ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)) {
                    if (toutiaoClick != null) {
                        ocpcEvent.setCallbackUrl(toutiaoClick.getCallbackUrl());
                        // 上报激活行为时额外存储用户渠道信息
                        hBaseUserChannelService.saveUserChannel(userEventReq.getUserId(), userEventReq.getAppId(),
                                toutiaoClick.getDsp(), toutiaoClick.getOs());

                        hbaseUserActiveService.saveUserEvent(userEvent);
                    }
                    if (StringUtils.isNotBlank(toutiaoClick.getAccountId()) && payEventService.isPayAccount(toutiaoClick)) {
                        redisEventService.redisInsertEvent(ocpcEvent, 7);
                    } else {
                        redisEventService.redisInsertEvent(ocpcEvent);
                    }

                    hbaseEventService.syncSaveUserActive(ocpcEvent);

                    new Thread(() -> {
                        // 快手达人数据额外存储到redis中
                        if (ocpcSwitcher.saveKsdrSwithcer && "kuaishou".equals(ocpcEvent.getDsp())) {
                            // 查询当前账户是否是快手达人
                            AuthKuaishouApp authKuaishouApp = authKuaishouAppService.getAuthKuaishouAppByAdvertiserId(ocpcEvent.getAccountId());
                            if (authKuaishouApp != null && authKuaishouApp.getAccountType() == 2) {
                                apRedisService.saveKsDrData(ocpcEvent.getAppId(), ocpcEvent.getOs(), Long.valueOf(ocpcEvent.getUserId()), "ksdr");
                            }
                        }
                    }).start();
                }

                if (ToutiaoEventTypeEnum.KEY_EVENT == toutiaoEventTypeEnum) {
                    if (toutiaoClick != null) {
                        ocpcEvent.setCallbackUrl(toutiaoClick.getCallbackUrl());
                    }
                    redisEventService.saveKeyEvent(ocpcEvent);
                    if (ocpcSwitcher.writeHbaseSwitch) {
                        hbaseEventService.saveKeyEvent(ocpcEvent);
                        hbaseRtaService.saveUserKeyEvent(ocpcEvent);
                    }
                }

                // 判断是否是关键行为衍生事件
                if (toutiaoEventTypeEnum.isKeyActionExtEvent()) {
                    if (toutiaoClick != null) {
                        ocpcEvent.setCallbackUrl(toutiaoClick.getCallbackUrl());
                    }
                    if (currentExtEvent != null) {
                        boolean isGaConvert = currentExtEvent.getIsGaConvert() != null && currentExtEvent.getIsGaConvert().equals(1L);
                        userEvent.setRemark((isGaConvert ? 1 : 0) + ";"
                                + currentExtEvent.getEventValues() + ";"
                                + ObjectUtils.defaultIfNull(userEventReq.getActionValues(), ""));
                    } else {
                        userEvent.setRemark("noClick;" + ObjectUtils.defaultIfNull(userEventReq.getActionValues(), ""));
                    }
                    if (!CollectionUtils.isEmpty(extEvents)) {
                        // 记录上传的衍生事件
                        ocpcEvent.setExtEvents(extEvents);
                        redisEventService.saveKeyActionExtEvent(ocpcEvent);
                        if (ocpcSwitcher.writeHbaseSwitch) {
                            hbaseEventService.saveKeyActionExtEvent(ocpcEvent);
                        }
                    }
                }

                dspEventService.ocpcInsertEvent(toutiaoClick, userEvent);
            } catch (Exception e) {
                log.error("", e);
            }

            return userEvent;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    private void checkUseClickImei(ToutiaoClick toutiaoClick, UserEventReq userEventReq, UserEvent userEvent, ToutiaoEventTypeEnum toutiaoEventTypeEnum) {
        // 这两种渠道下，激活事件存imei时，若点击与上报的imei不同，使用点击事件里的imei值
        if (DspType.needCallbackDeviceId(toutiaoClick.getDsp())) {
            // 仅限激活
            if (ToutiaoEventTypeEnum.ACTIVATE_APP.equals(toutiaoEventTypeEnum)) {
                // 点击事件中imei与上报的imei不同时，以点击事件中的为准
                if (!Objects.equals(userEventReq.getOcpcDeviceId(), toutiaoClick.getOcpcDeviceId())) {
                    userEvent.setOcpcDeviceId(toutiaoClick.getOcpcDeviceId());
                }
            }
        }
    }

    public void updateUserEvent(UserEvent userEvent) {
        if (userEvent.getId() != null && userEvent.getId() > 0) {
            try {
                OcpcEvent ocpcEvent = new OcpcEvent();
                BeanUtils.copyProperties(userEvent, ocpcEvent);
                ocpcEventMapper.updateById(ocpcEvent);
            } catch (Exception e) {
                log.error("回调后重写到ocpcEvent异常", e);
            }
        }
    }

    /**
     * redis 分布式锁
     *
     * @param lockKey         锁定Key
     * @param requestId       请求ID
     * @param millsExpireTime 过期毫秒数
     * @return
     */
    public boolean tryGetDistributedLock(String lockKey, String requestId, int millsExpireTime) {
        String result = bpDispenseJedisClusterClient.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, millsExpireTime);
        return LOCK_SUCCESS.equals(result);
    }

    public void unlockDistributedLock(String lockKey) {
        bpDispenseJedisClusterClient.del(lockKey);
    }

    public void callbackExtEvent(DspType dspType, ToutiaoClick toutiaoClick, UserEventReq userEventReq, List<ThirdExtEvent> extEventList, OcpcEvent ocpcEvent) {

        if (CollectionUtils.isEmpty(extEventList)) {
            log.info("已回传过所有匹配的衍生事件 用户-{} 产品-{} 配置-{}", userEventReq.getUserId(), userEventReq.getProduct(), userEventReq.getActionValues());
            return;
        }

        if (ocpcEvent != null && !Objects.equals(dspType.value, ocpcEvent.getDsp())) {
            log.info("激活事件与衍生事件dsp不匹配 上报:{} 点击:{} 激活:{} ", JSON.toJSONString(userEventReq), JSON.toJSONString(toutiaoClick), JSON.toJSONString(ocpcEvent));
            return;
        }

        // 时间保持一致
        long callbackMillisecond = System.currentTimeMillis();
        long callbackSecond = callbackMillisecond / 1000;

        // 当前已回传过的所有衍生事件
        List<ThirdExtEvent> allExtEvents = getAllExtEvents(ocpcEvent);

        for (ThirdExtEvent thirdExtEvent : extEventList) {

            allExtEvents.add(thirdExtEvent);

            // 记录用户关键行为衍生事件
            UserEventReq userEventReqCopy = copyUserEventReq(userEventReq, thirdExtEvent);

            String rkey = userEventReq.getProduct() + "@" + userEventReq.getOs() + "@" + userEventReq.getUserId() + "@" + thirdExtEvent.getDsp() + "@" + thirdExtEvent.getDepth();
            String rval = bpDispenseJedisClusterClient.get(rkey);
            if (StringUtils.isBlank(rval)) {
                UserEvent userExtEvent = saveUserEvent(toutiaoClick, userEventReqCopy, "", "", thirdExtEvent, allExtEvents);
                bpDispenseJedisClusterClient.setex(rkey, DateTimeConstants.SECONDS_PER_DAY * 1, "1");
                // 向三方发送回调请求
                if (dspType == DspType.TOUTIAO) {
                    toutiaoCallService.touTiaoExtEvent(userEventReqCopy, toutiaoClick, userExtEvent, thirdExtEvent, callbackSecond);
                } else if (dspType == DspType.KUAISHOU) {
                    kuaishouUserEventService.callbackExtDefine(userEventReqCopy, toutiaoClick, userExtEvent, thirdExtEvent, callbackMillisecond);
                } else if (dspType == DspType.GUANGDIANTONG) {
                    tencentUserActionService.callbackExtEvent(userEventReqCopy, toutiaoClick, userExtEvent, thirdExtEvent, callbackSecond);
                } else if (dspType == DspType.BAIDUSEM || dspType == DspType.BAIDUFEED) {
                    baiduUserEventService.callbackExtEvent(userEventReqCopy, toutiaoClick, userExtEvent, thirdExtEvent, callbackSecond);
                }

                if (userExtEvent != null && userExtEvent.getId() != null && userExtEvent.getId() > 0 && ocpcSwitcher.writeOcpcEventSwitch) {
                    updateUserEvent(userExtEvent);
                }

                if (ocpcSwitcher.writeUserEventFailedSwitch) {
                    userEventFailedService.insertUserEventFailed(userExtEvent, toutiaoClick);
                }
            } else {
                if (ocpcSwitcher.logOpen) {
                    log.info("衍生事件重复2 " + rkey + " " + rval);
                }
            }

        }
    }

    private List<ThirdExtEvent> getAllExtEvents(OcpcEvent ocpcEvent) {
        List<ThirdExtEvent> allExtEvents;
        if (ocpcEvent != null && !CollectionUtils.isEmpty(ocpcEvent.getExtEvents())) {
            allExtEvents = ocpcEvent.getExtEvents();
        } else {
            allExtEvents = new ArrayList<>();
        }
        return allExtEvents;
    }


    private UserEventReq copyUserEventReq(UserEventReq userEventReq, ThirdExtEvent thirdExtEvent) {
        UserEventReq userEventReqCopy = new UserEventReq();
        BeanUtils.copyProperties(userEventReq, userEventReqCopy);
        userEventReqCopy.setEventType(thirdExtEvent.getToutiaoEventType().intValue());
        return userEventReqCopy;
    }
}
