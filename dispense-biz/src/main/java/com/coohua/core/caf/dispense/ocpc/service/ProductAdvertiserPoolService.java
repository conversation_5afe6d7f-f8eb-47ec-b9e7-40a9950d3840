package com.coohua.core.caf.dispense.ocpc.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.constant.RedisKeyConstants;
import com.coohua.core.caf.dispense.ocpc.entity.ProductAdvertiserPool;
import com.coohua.core.caf.dispense.ocpc.mapper.ProductAdvertiserPoolMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ProductAdvertiserPoolService extends ServiceImpl<ProductAdvertiserPoolMapper, ProductAdvertiserPool> {

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;

    /**
     * 是否为有效的cvr账户
     * @param product
     * @param accountId
     * @return
     */
    public boolean isValid(String product, String accountId, String os) {
        String key = RedisKeyConstants.getCvrValidProductAccount(product, accountId, os);
        String value = bpDispenseJedisClusterClient.get(key);
        if(null == value) {
            boolean exists = lambdaQuery().eq(ProductAdvertiserPool::getProduct, product).eq(ProductAdvertiserPool::getAccountId, accountId).eq(ProductAdvertiserPool::getCvrStatus, 1).count() > 0;
            //半个5min
            bpDispenseJedisClusterClient.setex(key, 150, exists ? "1" : "0");
            return exists;
        }
        return Objects.equals(value, "1");
    }

    public void updateAccountStatusByIds(List<ProductAdvertiserPool> accountList, Integer cvrStatus) {
        if(CollectionUtils.isNotEmpty(accountList)) {
            this.lambdaUpdate().set(ProductAdvertiserPool::getCvrStatus, cvrStatus).in(ProductAdvertiserPool::getId, accountList.stream().map(ProductAdvertiserPool::getId).collect(Collectors.toList())).update();
        }
    }
}
