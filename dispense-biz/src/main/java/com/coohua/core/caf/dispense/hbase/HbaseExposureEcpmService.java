package com.coohua.core.caf.dispense.hbase;

import com.alibaba.fastjson.JSON;
import com.coohua.core.caf.dispense.dsp.entity.ExpousureEcpm;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class HbaseExposureEcpmService {
    @Resource
    private Connection hbaseConnection;

    static String tableName = "ExposureEcpm";

    public static final String EXPOSURE_ECPM = "ex:ecpm:%s:%s:%s";


    public static String getHbaseKey(String product, String os, String userId) {
        String key = String.format(EXPOSURE_ECPM, product, os, userId);
        return key;
    }

    public ExpousureEcpm getUserExpousureEcpm(String product, String os, String userId) {
        String key = getHbaseKey(product, os, userId);
        ExpousureEcpm expousureEcpm = new ExpousureEcpm();
        try (Table table = hbaseConnection.getTable(TableName.valueOf(tableName))) {
            Get get = new Get(Bytes.toBytes(key));
            Result res = table.get(get);
            String s = HbaseUtils.getCellValStr(res);
            if (StringUtils.isNotBlank(s)) {
                expousureEcpm = JSON.parseObject(s, ExpousureEcpm.class);
            }
        } catch (Exception e) {
            log.error("get ExpousureEcpm error ", e);
        }
        return expousureEcpm;
    }


    public boolean isStopOcpc(String product, String os, String userId) {
        ExpousureEcpm userExpousureEcpm = getUserExpousureEcpm(product, os, userId);
        return userExpousureEcpm.getStopOcpc();
    }
}

