package com.coohua.core.caf.dispense.enums;

public enum DeviceType {
    userId("userId","userId"),//	        用户id
    device("de","de"),//	         30天内第一次打开APP
    idfa2("idfa2","idfa2"), // idfa加md5
    oaid("oa","oa"),
    oaid2("oa2","oa2"), // oaid加md5
    mac("mac","mac"),////   用户点击广告后注册成为APP的新用户
    androidId("androidId","androidId"),
    ipua("ipua","ipua"),
    openid("openid","openid"),
    caid("caid","caid"),
    caid2("caid2","caid2"),
            ;
    public String value;
    public String name;

    DeviceType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static DeviceType getDspType(String value) {
        if (value != null) {
            DeviceType[] otypes = DeviceType.values();
            for (DeviceType memberType : otypes) {
                if (value.equals(memberType.value)) {
                    return memberType;
                }
            }
        }
        return null;
    }
}
