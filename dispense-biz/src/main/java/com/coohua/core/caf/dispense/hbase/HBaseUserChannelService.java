package com.coohua.core.caf.dispense.hbase;

import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.mapper.OcpcEventMapper;
import com.coohua.core.caf.dispense.utils.HBaseUtils;
import com.coohua.core.caf.dispense.utils.IterateUtils;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.coohua.core.caf.dispense.constant.BaseConstants.COMMA;

/**
 * <AUTHOR>
 * @date 2021/8/19
 */
@Slf4j
@Component
public class HBaseUserChannelService implements InitializingBean {

    /**
     * 用户HBase连接
     */
    @Resource
    private Connection userHadoopConnection;

    @Resource
    private OcpcEventMapper ocpcEventMapper;

    /**
     * 用户渠道信息表前缀
     */
    private static final String USER_CHANNEL_TABLE_PREFIX = "user_channel_";

    /**
     * 记录用户渠道
     *
     * @param userId 用户id
     * @param appId  产品id
     * @param dsp    平台
     * @param os     操作系统
     */
    public void saveUserChannel(String userId, Integer appId, String dsp, String os) {

        if (appId==null || appId == 0) {
            return;
        }

        String tableName = USER_CHANNEL_TABLE_PREFIX + appId;

        String value = dsp + COMMA + os;

        boolean success = HBaseUtils.saveToHadoop(userHadoopConnection, tableName, userId, Bytes.toBytes(value));

        if (!success) {
            log.warn("用户渠道数据记录失败 {} {} {}", appId, userId, value);
        }

    }

    private static final Map<String, Integer> PRODUCT_APP_MAPPER = ImmutableMap.<String, Integer>builder()
            .put("xingfunongyuan", 484)
            .put("duoduohuayuan", 407)
            .put("molihuayuan", 487)
            .put("bendilaoxiangqun", 492)
            .put("yeyedehuayuan", 427)
            .put("youyoutianyuan", 493)
            .put("kaixinzhuangyuan", 481)
            .put("wuyouxiaoyuan", 446)
            .build();

    @Override
    public void afterPropertiesSet() throws Exception {

//        CompletableFuture.runAsync(() -> {
//
//            IterateUtils.iterateMySqlByIdAndLimit(0L,
//                    id -> ocpcEventMapper.selectActivateEventByIdLimit(id, 1000),
//                    OcpcEvent::getId,
//                    list -> {
//                        int count = 0;
//                        for (OcpcEvent ocpcEvent : list) {
//                            if (StringUtils.isBlank(ocpcEvent.getUserId()) || StringUtils.isBlank(ocpcEvent.getDsp())) {
//                                continue;
//                            }
//
//                            Integer appId = PRODUCT_APP_MAPPER.get(ocpcEvent.getProduct());
//                            if (appId != null) {
//                                count++;
//                                saveUserChannel(ocpcEvent.getUserId(), appId, ocpcEvent.getDsp(), ocpcEvent.getOs());
//                            }
//                        }
//                        if (count > 0) {
//                            log.info("激活用户补数成功 {} 结束id是 {}", count, list.get(list.size() - 1).getId());
//                        }
//                    });
//
//        }).whenComplete((result, e) -> {
//            if (e != null) {
//                log.error("激活用户补数出现异常 ", e);
//            }
//        });

    }

}
