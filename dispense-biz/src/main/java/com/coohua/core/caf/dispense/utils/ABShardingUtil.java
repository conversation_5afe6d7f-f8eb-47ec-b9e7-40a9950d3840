package com.coohua.core.caf.dispense.utils;

import com.google.common.hash.Hashing;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;

public class ABShardingUtil {

    private static final int[] WEIGHTS = {10, 10, 80};
    private static final int TOTAL_WEIGHT = 100;

    // 使用 LongAdder 代替 AtomicLong
    private static final Map<Integer, LongAdder> groupCounters = new ConcurrentHashMap<>();

    static {
        for (int i = 0; i < WEIGHTS.length; i++) {
            groupCounters.put(i, new LongAdder());
        }
    }

    // 哈希函数
    private static int hash(String deviceId) {
        return Hashing.murmur3_32().hashUnencodedChars(deviceId).asInt() & 0x7FFFFFFF; // 取正数
    }

    // 分流方法
    public static int shard(String deviceId) {
        int hashValue = hash(deviceId);
        int mod = hashValue % TOTAL_WEIGHT;

        int accumulatedWeight = 0;
        for (int i = 0; i < WEIGHTS.length; i++) {
            accumulatedWeight += WEIGHTS[i];
            if (mod < accumulatedWeight) {
//                groupCounters.get(i).increment();
                return i;
            }
        }

        // 默认返回最后一组
        return WEIGHTS.length - 1;
    }
}
