package com.coohua.core.caf.dispense.dsp.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.HttpClientUtil;
import com.arronlong.httpclientutil.common.HttpConfig;
import com.arronlong.httpclientutil.exception.HttpProcessException;
import com.coohua.core.caf.dispense.dsp.entity.CheckClickRsp;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventMapper;
import com.coohua.core.caf.dispense.enums.OcpcSourceType;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import com.coohua.core.caf.dispense.ocpc.service.OcpcEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.util.Date;

@Service
@Slf4j
public class SogouUserEventService {

    public final static String akey = "6b8a759bfc83936254fad9fdafa36e23";
    @Autowired
    private AlertService alertService;
    @Autowired
    private OcpcEventService ocpcEventService;
    @Autowired
    private DspUserEventService dspEventService;

    public boolean activeEvent(UserEventReq userEventReq, ToutiaoClick toutiaoClick, UserEvent userEvent) {

        String reqUrl = null;
        String html = null;
        if (toutiaoClick != null) {
            String callbackUrl = toutiaoClick.getCallbackUrl();
            String url = callbackUrl.replace("{{ATYPE}}", "105");
            StringBuilder stringBuilder = new StringBuilder(url);
            String urlAkey = stringBuilder.append(akey).toString();
            // 计算 sign 值
            String sign = md5Func(urlAkey);
            reqUrl = new StringBuilder(url)
                    .append("&sign=")
                    .append(sign).toString();
            log.info("sogou final callback url-{}", reqUrl);
            try {
                html = HttpClientUtil.get(HttpConfig.custom().timeout(3000).url(reqUrl));
            } catch (HttpProcessException e) {
                log.error("httpclient call failure");
                return false;
            }
            JSONObject jobj = JSON.parseObject(html);
            int code = jobj.getInteger("status");
            if(userEvent!=null){
                userEvent.setReqRsp(html);
                userEvent.setReqUrl(reqUrl);
            }
            if (code == 200) {
                log.info("sogou 回调成功");
            } else {
                log.info("sogou 回调失败");
                return false;
            }
        }
        return true;
    }

    public static String md5Func(String c_password) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(c_password.getBytes());
            String md5 = new BigInteger(1, md.digest()).toString(16);
            return fillMD5(md5);
        } catch (Exception e) {
            throw new RuntimeException("MD5 加密错误：" + e.getMessage(), e);
        }
    }

    // 将 16 位数转为 32 位
    private static String fillMD5(String md5) {
        return md5.length() == 32 ? md5 : fillMD5("0" + md5);
    }

}
