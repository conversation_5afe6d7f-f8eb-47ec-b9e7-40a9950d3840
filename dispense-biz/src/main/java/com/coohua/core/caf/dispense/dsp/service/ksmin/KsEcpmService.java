package com.coohua.core.caf.dispense.dsp.service.ksmin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.arronlong.httpclientutil.common.HttpHeader;
import com.coohua.core.caf.dispense.dsp.entity.AuthKuaishouApp;
import com.coohua.core.caf.dispense.dsp.service.AuthKuaishouAdvertiserService;
import com.coohua.core.caf.dispense.dsp.service.ToutiaoCallService;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.ocpc.entity.ByteEcpm;
import com.coohua.core.caf.dispense.ocpc.service.ByteEcpmService;
import com.coohua.core.caf.dispense.utils.DateUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import top.zrbcool.pepper.boot.jediscluster.JedisClusterClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class KsEcpmService {
    private String queryEcpmUrl = "https://ad.e.kuaishou.com/rest/openapi/gw/dsp/v1/report/ecpm_report";
    @Autowired
    ToutiaoCallService toutiaoCallService;
    @Autowired
    AuthKuaishouAdvertiserService authKuaishouAdvertiserService;
    @ApolloJsonValue("${ks.min.appids:[\"kljyds2-ks718042667811618720\"]}")
    public Set<String> ksPrdAppidSet;

    private String getAppId(String product){
        Map<String,String> appMap = ksPrdAppidSet.stream().collect(Collectors.toMap(s->s.split("-")[0], s->s.split("-")[1]));
        return appMap.get(product);
    }
    public Pair<Long,Long> getEcmp(String userId, String product, String openId, String appid, String accountId, String dateHour){
        List<ByteEcpm> dlist = new ArrayList();
        String reqUrl = queryEcpmUrl;
        CloseableHttpClient client = toutiaoCallService.getHttpClient();
        Date eventDate = DateUtils.parse(dateHour,DateUtils.PATTERN_YMD);
        Long allCount = 0l;
        Long totalCost = 0l;
        try {
            AuthKuaishouApp authKuaishouApp = authKuaishouAdvertiserService.getByAccountId(accountId);

            Header[] headers = HttpHeader.custom()
                    .other("Access-Token", authKuaishouApp.getAccessToken())
                    .other("Content-Type", "application/json")
                    .build();
            String appIdKs = getAppId(product);

            List<String> dliist = new ArrayList<>();
            dliist.add(openId);

            JSONObject body = new JSONObject()
                    .fluentPut("advertiser_id", accountId)
                    .fluentPut("app_id", appIdKs)
                    .fluentPut("open_id", dliist)
                    .fluentPut("page", 1)
                    .fluentPut("page_size", 500)
                    .fluentPut("data_hour", dateHour)
                    ;

            String bdJson = body.toJSONString();
            String rsp = toutiaoCallService.resendUrlPost(reqUrl,bdJson,client,headers);
            JSONObject jsonObject  = JSON.parseObject(rsp);
            JSONObject dataObj  = jsonObject.getJSONObject("data");

            allCount = dataObj.getLong("total_count");
            Integer errNo = jsonObject.getInteger("code");
            if(!errNo.equals(0)){
                //错误码为 ******** 重新请求
                log.info("快手小游戏 报错 "+userId+" "+rsp );
            }
            log.info("ksmin请求ecpm "+userId+" reqUrl "+reqUrl+" "+rsp );
            JSONArray jsonArray = dataObj.getJSONArray("details");
            if(jsonArray!=null){
                for(int i=0;i<jsonArray.size();i++){
                    ByteEcpm  byteAdEcpm = new ByteEcpm();
                    JSONObject adJobj  =  jsonArray.getJSONObject(i);
                    Double cost = adJobj.getDouble("cost"); // 1/10000 元
                    totalCost = totalCost + cost.longValue();
                    String openIdTr = adJobj.getString("open_id");
                    byteAdEcpm.setId(product+"@"+getIncsNum()+"");
                    byteAdEcpm.setCost(cost);
                    byteAdEcpm.setOpenId(openIdTr);
                    byteAdEcpm.setUserId(userId);
                    byteAdEcpm.setProduct(product);
                    byteAdEcpm.setDsp(DspType.KSMIN.name);
                    byteAdEcpm.setEventDate(eventDate);
                    Date date = new Date();
                    byteAdEcpm.setCreateTime(date);
                    byteAdEcpm.setUpdateTime(date);
                    dlist.add(byteAdEcpm);
                }
            }

            if(dlist.size()>0){
                saveEptd(product,openId,eventDate,dlist);
            }
        }catch (Exception e){
            log.error("ksmin报错 ",e);
        }
        Pair<Long,Long> pairExposure = new Pair<>(allCount,totalCost);
        return pairExposure;
    }

    @Autowired
    @Qualifier("bp-dispenseJedisClusterClient")
    private JedisClusterClient bpDispenseJedisClusterClient;
    private Long getIncsNum(){
        Long icid = bpDispenseJedisClusterClient.incrBy("ksminecpm",1);
        return icid;
    }
    @Resource(name = "minEcpmToDb")
    ThreadPoolTaskExecutor minEcpmToDb;
    @Autowired
    ByteEcpmService byteEcpmService;
    public void saveEptd(String product,String openId,Date eventDate,List<ByteEcpm> dlist){
        minEcpmToDb.execute(()-> {
            try {
                byteEcpmService.delEcpms(product,openId,eventDate);
                byteEcpmService.saveBatch(dlist);
                log.info("ksmin 保存ecpm "+openId+" "+dlist.size());
            }catch (Exception e){
                log.error("",e);
            }
        });
    }
}
