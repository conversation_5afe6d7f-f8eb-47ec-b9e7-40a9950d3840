package com.coohua.core.caf.dispense.dsp.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="OppoAdvertiser对象", description="")
public class OppoAdvertiser implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "产品打点名")
    private String productName;

    @ApiModelProperty(value = "产品名")
    private String appName;

    @ApiModelProperty(value = "广告主id")
    private String advertiserId;

    @ApiModelProperty(value = "广告主名称")
    private String advertiserName;

    @ApiModelProperty(value = "组别")
    private String groupName;

    @ApiModelProperty(value = "登录账户")
    private String loginAccount;

    @ApiModelProperty(value = "登录密码")
    private String loginPassword;

    @ApiModelProperty(value = "API-ID")
    private String api_id;

    @ApiModelProperty(value = "API-KEY")
    private String api_key;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "关键行为类型")
    private String eventTypes;

    @ApiModelProperty(value = "关键行为数值")
    private String eventValues;

    @ApiModelProperty(value = "包名")
    private String pkg;

    private Integer delFlag;

    @ApiModelProperty(value = "创建人")
    private String createName;

    private Date createTime;

    @ApiModelProperty(value = "修改人")
    private String updateName;

    private Date updateTime;

}
