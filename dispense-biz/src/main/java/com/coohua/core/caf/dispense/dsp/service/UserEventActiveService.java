package com.coohua.core.caf.dispense.dsp.service;

import cn.hutool.core.thread.ThreadUtil;
import com.coohua.core.caf.dispense.cache.ProductCache;
import com.coohua.core.caf.dispense.component.ThreadPollComponent;
import com.coohua.core.caf.dispense.dsp.entity.KuaishouClick;
import com.coohua.core.caf.dispense.dsp.entity.ToutiaoClick;
import com.coohua.core.caf.dispense.dsp.entity.UserEvent;
import com.coohua.core.caf.dispense.dsp.entity.UserEventActive;
import com.coohua.core.caf.dispense.dsp.mapper.UserEventActiveMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.coohua.core.caf.dispense.dto.req.UserEventReq;
import com.coohua.core.caf.dispense.enums.DspType;
import com.coohua.core.caf.dispense.enums.ToutiaoEventTypeEnum;
import com.coohua.core.caf.dispense.ocpc.entity.OcpcEvent;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-17
 */
@Service
public class UserEventActiveService extends ServiceImpl<UserEventActiveMapper, UserEventActive> {
    static ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory("createUserAcitveDspPool",null,false);

    static ExecutorService createUserAcitveDspPool = new ThreadPoolExecutor(
            5,
            40,
            60,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(2000),
            threadFactory,
            new ThreadPollComponent.RejectedLogAbortPolicy("dspToActiveEvent"));

    public void saveUserEvent(ToutiaoClick toutiaoClick, UserEventReq userEventReq, String reqUrl, String rspStr) {
        try {
            if (userEventReq.getEventType() == ToutiaoEventTypeEnum.ACTIVATE_APP.value) {
                createUserAcitveDspPool.submit(() -> {
                    try {
                        UserEventActive userEvent = new UserEventActive();
                        Integer appId = ProductCache.getAppId(toutiaoClick.getProduct());
                        if (appId == null) {
                            appId = -1;
                        }
                        userEvent.setAppId(appId);
                        userEvent.setCreateTime(new Date());
                        userEvent.setUpdateTime(new Date());
                        userEvent.setUserId(userEventReq.getUserId());
                        userEvent.setEventType(userEventReq.getEventType());
                        userEvent.setProduct(userEventReq.getProduct());
                        if (toutiaoClick != null) {
                            userEvent.setClickId(toutiaoClick.getId());
                            userEvent.setAccountId(toutiaoClick.getAccountId());
                            userEvent.setAccountName(toutiaoClick.getAidName());
                            userEvent.setDsp(toutiaoClick.getDsp());
                            if (DspType.KUAISHOU.name.equals(toutiaoClick.getDsp())) {
                                //get ideaday
                                userEvent.setPlanId(toutiaoClick.getGid() + "");
                            } else if (DspType.TOUTIAO.name.equals(toutiaoClick.getDsp())) {
                                userEvent.setPlanId(toutiaoClick.getPid() + "");
                                userEvent.setCreativeId(toutiaoClick.getCid());
                            }
                        }
                        userEvent.setOaid(userEventReq.getOaid());
                        userEvent.setOcpcDeviceId(userEventReq.getOcpcDeviceId());
                        userEvent.setOs(userEventReq.getOs());
                        userEvent.setReqUrl(reqUrl);
                        userEvent.setReqRsp(rspStr);
                        save(userEvent);
                    }catch (Exception e){
                        log.error("",e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("", e);
        }
    }
}
