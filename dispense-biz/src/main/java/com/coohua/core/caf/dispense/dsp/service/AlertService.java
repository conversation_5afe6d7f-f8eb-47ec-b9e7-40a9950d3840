package com.coohua.core.caf.dispense.dsp.service;

import com.pepper.metrics.core.AlertProfiler;
import com.pepper.metrics.core.AlertStats;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @desc
 * @date 2020/6/10
 **/
@Service
@Slf4j
public class AlertService {

    private AlertStats lertStats;
    private AlertStats lertStatsDel;
    //    @PostConstruct
    public void init() {
        try {
            lertStats = AlertProfiler.Builder.builder()
                    .name("dispense")
                    .rule("dispense_rule_001")
                    .create();

        }catch (Exception e){
            log.warn(e.getMessage());
        }

        try {
            lertStatsDel = AlertProfiler.Builder.builder()
                    .name("dispense-job")
                    .rule("dispense_rule_001")
                    .create();

        }catch (Exception e){
            log.warn(" alertserviceinit ",e.getMessage());
        }
    }

    public void reportDelData(String product, String tableName, String type) {
        try {
            lertStatsDel.incrementAndGet(
                    "产品", product,
                    "表名", tableName,
                    "类型", type
            );
        }catch (Exception e){
            log.warn(e.getMessage());
        }
    }

    public void report(String product, String account, String type) {
        try {
            lertStats.incrementAndGet(
                    "产品", product,
                    "账户", account,
                    "类型", type
            );
        }catch (Exception e){
            log.warn("alertreport "+product+" "+account+" "+type,e.getMessage());
        }
    }
}
