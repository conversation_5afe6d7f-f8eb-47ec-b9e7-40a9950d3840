package com.coohua.core.caf.dispense.ocpc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 广点通用户每次看广告时，回传广告变现价值
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class TencentLtv implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "应用ID")
    private Integer appId;

    @ApiModelProperty(value = "用户ID")
    @TableField("userId")
    private String userId;

    @ApiModelProperty(value = "原始deviceId")
    private String sourceDeviceId;

    @ApiModelProperty(value = "原始oaId")
    private String sourceOaid;

    private String product;

    private String os;

    private String ocpcDeviceId;

    private String oaid;

    @ApiModelProperty(value = "clickId")
    private Long clickId;

    @ApiModelProperty(value = "accountId")
    private String accountId;

    private String reqUrl;

    private String reqBody;

    private String reqRsp;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    private String callbackUrl;

    /**
     * 目前用做记录关键行为回传触发时的值
     */
    private String remark;

    /**
     * 单次ecpm(单位:分)
     */
    private Integer ecpm;

    /**
     * 事件发生时间
     */
    private Date eventTime;



}
