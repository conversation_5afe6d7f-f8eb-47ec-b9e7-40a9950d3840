package com.coohua.core.caf.dispense.apollo;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Set;

/**
 * 关键行为衍生事件相关apollo配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ExtEventApolloConfig {

    /**
     * 关键行为衍生事件回传 是否对全部产品生效
     */
    @Value("${ocpc.ext.event.enable.all:false}")
    public boolean extEventEnableAll;

    /**
     * 关键行为衍生事件回传 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.ext.event.enable.products:[\"\"]}")
    public Set<String> extEventEnableProducts;

    /**
     * 全部使用分布式锁保证回传不重复
     */
    @Value("${lock.ext.event.all.enable:true}")
    public boolean lockExtEventAllEnable;

    /**
     * 启用分布式锁保证回传不重复的产品打点名称集合
     */
    @ApolloJsonValue("${lock.ext.event.enable.products:[\"\"]}")
    public Set<String> lockExtEventEnableProducts;

    /**
     * 适用的账户 eventTypes范围
     */
    @ApolloJsonValue("${ocpc.ext.event.enable.eventTypes:[\"1,2\"]}")
    public Set<String> extEventEnableEventTypes;

    /**
     * 关键行为衍生事件回传 是否对全部账户生效
     */
    @Value("${ocpc.ext.event.enable.account.all:false}")
    public boolean extEventEnableAllAccount;

    /**
     * 关键行为衍生事件回传 生效的账户id列表
     */
    @ApolloJsonValue("${ocpc.ext.event.accounts:[]}")
    public Set<String> extEventEnableAccounts;

    /**
     * 关键行为衍生事件回传(1,3类型) 是否对全部产品生效
     */
    @Value("${ocpc.ext.event.1_3.enable.all:false}")
    public boolean extEventEnableAllFor_1_3;

    /**
     * 关键行为衍生事件回传(1,3类型) 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.ext.event.1_3.enable.products:[\"yydxny\"]}")
    public Set<String> extEventEnableProductsFor_1_3;

    /**
     * 关键行为衍生事件回传 v2链路-事件管理 是否对全部产品生效
     */
    @Value("${ocpc.ext.event.v2.enable.all.product:false}")
    public boolean extEventV2EnableAllProduct;

    /**
     * 关键行为衍生事件回传 v2链路-事件管理 生效的产品列表 (产品英文名)
     */
    @ApolloJsonValue("${ocpc.ext.event.v2.enable.products:[]}")
    public Set<String> extEventV2EnableProducts;

    /**
     * 关键行为衍生事件回传 v2链路-事件管理 是否对全部账户生效
     */
    @Value("${ocpc.ext.event.v2.enable.account.all:false}")
    public boolean extEventV2EnableAllAccount;

    /**
     * 关键行为衍生事件回传 v2链路-事件管理 生效的账户id列表
     */
    @ApolloJsonValue("${ocpc.ext.event.v2.accounts:[]}")
    public Set<String> extEventV2EnableAccounts;

}
